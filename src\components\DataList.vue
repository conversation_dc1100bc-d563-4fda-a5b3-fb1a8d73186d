<template>
  <div class="data-list-container">
    <!-- 功能操作区 -->
    <div class="actions">
      <!-- 使用 flat-pickr 作为日期选择器 -->
      <!-- 它看起来像一个输入框，点击即可弹出日历 -->
      <flat-pickr
          v-model="selectedDate"
          :config="flatpickrConfig"
          class="form-control"
          placeholder="选择日期"
          @on-change="handleDateChange"
      />
      <span class="date-display">当前数据日期：{{ formattedDate }}</span>
    </div>

    <!-- 表格区域 (这里可以用 Bootstrap 的表格样式) -->
    <table class="table table-striped" v-if="!loading">
      <thead>
      <tr>
        <th>ID</th>
        <th>名称</th>
        <th>数值</th>
        <th>记录日期</th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="item in tableData" :key="item.id">
        <td>{{ item.id }}</td>
        <td>{{ item.name }}</td>
        <td>{{ item.value }}</td>
        <td>{{ item.date }}</td>
      </tr>
      </tbody>
    </table>
    <div v-if="loading" class="text-center my-3">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>

    <!-- 分页区域 (使用 BootstrapVueNext 的分页组件) -->
    <div class="pagination-container">
      <b-pagination
          v-model="currentPage"
          :total-rows="totalItems"
          :per-page="pageSize"
          aria-controls="my-table"
      ></b-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable */
import { ref, watch, onMounted, computed } from 'vue';
import axios from 'axios'; // 使用 axios

// 1. 引入 flatpickr 组件和样式
import flatPickr from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';
import {BPagination} from "bootstrap-vue-next";


// --- 状态定义 ---

const selectedDate = ref(new Date());
const tableData = ref([]);
const loading = ref(false);
const pageSize = 10;
const currentPage = ref(1); // v-model 会自动更新这个值
const totalItems = ref(0);

// flatpickr 的配置对象
const flatpickrConfig = {
  dateFormat: 'Y-m-d', // 日期格式
  altInput: true,      // 显示一个对用户友好的格式
  altFormat: 'Y 年 m 月 d 日',
};

// --- 逻辑函数 ---

// 统一的数据获取函数
const fetchData = async () => {
  loading.value = true;
  try {
    // 格式化日期以发送给后端，这里使用你已有的 date-fns 会更健壮
    // import { format } from 'date-fns';
    // const formattedApiDate = format(selectedDate.value, 'yyyy-MM-dd');
    const formattedApiDate = selectedDate.value.toISOString().split('T')[0]; // 简单格式化

    // 使用 axios 发送请求
    const response = await axios.get('/api/data', {
      params: {
        date: formattedApiDate,
        page: currentPage.value,
        size: pageSize,
      },
    });
    // 假设后端返回的数据结构是 { data: [...], total: 100 }
    tableData.value = response.data.data;
    totalItems.value = response.data.total;
  } catch (error) {
    console.error("数据获取失败:", error);
  } finally {
    loading.value = false;
  }
};

// 日期选择器的 @on-change 事件处理器
const handleDateChange = (selectedDates) => {
  // 当日期改变时，flatpickr 会调用这个函数
  // v-model 已经更新了 selectedDate.value
  // 我们需要重置页码并获取数据
  console.log('日期已更改，重置页码并获取新数据。');
  currentPage.value = 1;
  fetchData();
};

// --- 计算属性 ---
const formattedDate = computed(() => {
  if (!selectedDate.value) return '未选择';
  const date = new Date(selectedDate.value);
  return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
});

// --- 侦听器 ---
// 只需要侦听 currentPage 的变化即可
// 日期变化由 @on-change 事件处理了，逻辑更清晰
watch(currentPage, () => {
  console.log('页码已更改，获取新一页数据。');
  fetchData();
});

// --- 生命周期钩子 ---
onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.data-list-container {
  padding: 20px;
}
.actions {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 15px; /* 元素间距 */
}
.date-display {
  color: #606266;
  font-size: 14px;
}
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
/* 让 flatpickr 输入框宽度自适应 */
.form-control {
  width: auto;
}
</style>
