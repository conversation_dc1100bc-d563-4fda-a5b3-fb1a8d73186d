<template>
  <BreadCrumb pageTitle="商户管理"/>
  
  <!-- 操作栏 -->
  <div class="d-flex justify-content-between align-items-center mb-25">
    <div class="refresh">
      <button
          class="transition bg-transparent p-0 border-0"
          @click="refreshStoreList"
          aria-label="刷新">
        <i class="flaticon-refresh"></i>
      </button>
    </div>
    
    <!-- 搜索框 -->
    <div class="search-box">
      <div class="input-group" style="width: 300px;">
        <input 
          type="text" 
          class="form-control" 
          placeholder="搜索门店名称或地址..."
          v-model="searchKeyword"
          @input="handleSearch"
        >
        <span class="input-group-text">
          <i class="flaticon-search"></i>
        </span>
      </div>
    </div>
  </div>

  <!-- 加载状态 -->
  <div v-if="isLoading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">加载中...</span>
    </div>
  </div>

  <!-- 错误状态 -->
  <div v-else-if="error" class="alert alert-danger" role="alert">
    <i class="flaticon-warning me-2"></i>
    {{ error }}
  </div>

  <!-- 门店列表 -->
  <div v-else class="store-management-container">
    <!-- 统计信息 -->
    <div class="stats-row mb-25">
      <div class="row">
        <div class="col-md-3">
          <div class="stats-card">
            <div class="stats-icon">
              <i class="flaticon-store text-primary"></i>
            </div>
            <div class="stats-content">
              <h3>{{ totalStores }}</h3>
              <p>总门店数</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="stats-card">
            <div class="stats-icon">
              <i class="flaticon-check text-success"></i>
            </div>
            <div class="stats-content">
              <h3>{{ activeStores }}</h3>
              <p>营业中</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="stats-card">
            <div class="stats-icon">
              <i class="flaticon-pause text-warning"></i>
            </div>
            <div class="stats-content">
              <h3>{{ inactiveStores }}</h3>
              <p>暂停营业</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="stats-card">
            <div class="stats-icon">
              <i class="flaticon-filter text-info"></i>
            </div>
            <div class="stats-content">
              <h3>{{ filteredStores.length }}</h3>
              <p>当前显示</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 门店卡片列表 -->
    <div class="stores-grid">
      <div class="row">
        <div class="col-lg-4 col-md-6 mb-25" v-for="store in filteredStores" :key="store.storeId">
          <div class="store-card card border-0 rounded-3 bg-white shadow-sm letter-spacing transition">
            <div class="card-body p-20">
              <!-- 门店头部信息 -->
              <div class="store-header d-flex justify-content-between align-items-start mb-15">
                <div class="store-title">
                  <h5 class="card-title fw-bold mb-5">{{ store.storeName }}</h5>
                  <span class="store-status badge" :class="getStatusClass(store.status)">
                    {{ getStatusText(store.status) }}
                  </span>
                </div>
                <div class="store-type">
                  <span class="badge bg-light text-dark">{{ store.storeType || '标准店' }}</span>
                </div>
              </div>

              <!-- 门店详细信息 -->
              <div class="store-info">
                <div class="info-item mb-10">
                  <i class="flaticon-location text-muted me-2"></i>
                  <span class="text-muted small">{{ store.storeAddress }}</span>
                </div>
                <div class="info-item mb-10">
                  <i class="flaticon-phone text-muted me-2"></i>
                  <span class="text-muted small">{{ store.contactPhone || '暂无联系电话' }}</span>
                </div>
                <div class="info-item mb-10">
                  <i class="flaticon-clock text-muted me-2"></i>
                  <span class="text-muted small">{{ store.businessHours || '营业时间待更新' }}</span>
                </div>
                <div class="info-item mb-15">
                  <i class="flaticon-info text-muted me-2"></i>
                  <span class="text-muted small">{{ store.storeDescription || '暂无描述' }}</span>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="store-actions">
                <div class="row g-2">
                  <div class="col-6">
                    <router-link
                      :to="{
                        name: 'StoreDetailPage',
                        params: { id: store.storeId }
                      }"
                      class="btn btn-outline-primary btn-sm w-100 text-decoration-none"
                    >
                      <i class="flaticon-eye me-1"></i>
                      查看详情
                    </router-link>
                  </div>
                  <div class="col-6">
                    <button 
                      class="btn btn-outline-secondary btn-sm w-100"
                      @click="viewStoreStats(store)"
                    >
                      <i class="flaticon-chart me-1"></i>
                      数据统计
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredStores.length === 0 && !isLoading" class="empty-state text-center py-5">
      <i class="flaticon-search display-1 text-muted mb-3"></i>
      <h4 class="text-muted">未找到匹配的门店</h4>
      <p class="text-muted">请尝试调整搜索条件或刷新页面</p>
      <button class="btn btn-primary" @click="clearSearch">
        <i class="flaticon-refresh me-2"></i>
        清除搜索
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import BreadCrumb from '@/components/layouts/BreadCrumb.vue'
import { getStoreList } from '@/utils/api/store'
import type { storeDetail } from '@/types/store'

// 响应式数据
const stores = ref<storeDetail[]>([])
const isLoading = ref(false)
const error = ref<string | null>(null)
const searchKeyword = ref('')

const router = useRouter()

// 计算属性
const filteredStores = computed(() => {
  if (!searchKeyword.value.trim()) {
    return stores.value
  }
  
  const keyword = searchKeyword.value.toLowerCase().trim()
  return stores.value.filter(store => 
    store.storeName.toLowerCase().includes(keyword) ||
    store.storeAddress.toLowerCase().includes(keyword) ||
    (store.storeDescription && store.storeDescription.toLowerCase().includes(keyword))
  )
})

const totalStores = computed(() => stores.value.length)
const activeStores = computed(() => 
  stores.value.filter(store => 
    store.status === 'OPEN' || store.status === 'open' || store.status === '营业中'
  ).length
)
const inactiveStores = computed(() => totalStores.value - activeStores.value)

// 方法
const refreshStoreList = async () => {
  isLoading.value = true
  error.value = null
  
  try {
    console.log('🔄 [商户管理] 开始获取门店列表')
    const response = await getStoreList({ pageNum: 1, pageSize: 9999 })
    stores.value = response.rows || []
    console.log(`✅ [商户管理] 门店列表获取成功: ${stores.value.length}个门店`)
  } catch (err) {
    console.error('❌ [商户管理] 获取门店列表失败:', err)
    error.value = '获取门店列表失败，请稍后重试'
    stores.value = []
  } finally {
    isLoading.value = false
  }
}

const handleSearch = () => {
  // 搜索是通过计算属性自动处理的，这里可以添加防抖逻辑
  console.log(`🔍 [商户管理] 搜索关键词: ${searchKeyword.value}`)
}

const clearSearch = () => {
  searchKeyword.value = ''
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'OPEN':
    case 'open':
    case '营业中':
      return 'bg-success'
    case 'CLOSED':
    case 'closed':
    case '已关闭':
      return 'bg-danger'
    case 'MAINTENANCE':
    case 'maintenance':
    case '维护中':
      return 'bg-warning'
    default:
      return 'bg-secondary'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'OPEN':
    case 'open':
      return '营业中'
    case 'CLOSED':
    case 'closed':
      return '已关闭'
    case 'MAINTENANCE':
    case 'maintenance':
      return '维护中'
    default:
      return status || '未知状态'
  }
}

const viewStoreStats = (store: storeDetail) => {
  console.log(`📊 [商户管理] 查看门店统计: ${store.storeName}`)
  router.push({
    name: 'AllStoresDashboard',
    query: { storeId: store.storeId }
  })
}

// 生命周期
onMounted(() => {
  refreshStoreList()
})
</script>

<style scoped>
.store-management-container {
  min-height: 400px;
}

.stats-row .stats-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  transition: transform 0.2s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
}

.stats-icon {
  font-size: 2.5rem;
  margin-right: 15px;
}

.stats-content h3 {
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
  color: var(--bs-dark);
}

.stats-content p {
  margin: 0;
  color: var(--bs-secondary);
  font-size: 0.9rem;
}

.store-card {
  transition: all 0.3s ease;
  border: 1px solid #e9ecef !important;
}

.store-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.store-header .store-title h5 {
  color: var(--bs-dark);
  font-size: 1.1rem;
}

.store-info .info-item {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  line-height: 1.4;
}

.store-info .info-item i {
  width: 16px;
  font-size: 0.8rem;
}

.store-actions .btn {
  font-size: 0.8rem;
  padding: 6px 12px;
}

.search-box .input-group-text {
  background-color: #f8f9fa;
  border-left: none;
}

.search-box .form-control {
  border-right: none;
}

.search-box .form-control:focus {
  box-shadow: none;
  border-color: #ced4da;
}

.refresh button {
  font-size: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  border-radius: 8px;
  box-sizing: border-box;
  margin: 5px 22px 5px 5px;
}

.refresh button:hover {
  color: var(--splash-primary-color);
}

.refresh button:active {
  transform: scale(0.85);
}

.empty-state {
  background: white;
  border-radius: 12px;
  padding: 60px 20px;
  margin: 40px 0;
}
</style>
