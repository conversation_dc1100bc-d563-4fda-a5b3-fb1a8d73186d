<template>
  <div class="product-item-management p-20">
    <!-- 搜索和操作区域 -->
    <div class="d-flex justify-content-between align-items-center mb-20">
      <div class="search-controls d-flex gap-3">
        <div class="search-input">
          <input 
            v-model="searchForm.searchValue" 
            type="text" 
            class="form-control" 
            placeholder="搜索商品名称"
            @input="handleSearch"
          />
        </div>
        <div class="store-filter">
          <select v-model="searchForm.storeId" class="form-select" @change="handleSearch">
            <option value="">全部门店</option>
            <option v-for="store in storeList" :key="store.storeId" :value="store.storeId">
              {{ store.storeName }}
            </option>
          </select>
        </div>
        <div class="prototype-filter">
          <select v-model="searchForm.drinkPrototype" class="form-select" @change="handleSearch">
            <option value="">全部原型</option>
            <option v-for="prototype in prototypeList" :key="prototype.prototypeId" :value="prototype.prototypeId">
              {{ prototype.prototypeName }}
            </option>
          </select>
        </div>
        <div class="status-filter">
          <select v-model="searchForm.status" class="form-select" @change="handleSearch">
            <option value="">全部状态</option>
            <option value="1">上架</option>
            <option value="0">下架</option>
          </select>
        </div>
        <button @click="resetSearch" class="btn btn-outline-secondary">
          重置
        </button>
      </div>
      <div class="action-buttons">
        <button @click="showAddModal" class="btn btn-primary">
          新增商品
        </button>
        <button @click="exportData" class="btn btn-outline-secondary ms-2">
          导出
        </button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3 text-muted">正在加载数据...</p>
      </div>

      <!-- 数据表格 -->
      <div v-else class="table-responsive">
        <table class="table table-hover mb-0">
          <thead class="table-light">
            <tr>
              <th width="60">序号</th>
              <th width="150">商品名称</th>
              <th width="120">所属门店</th>
              <th width="100">销售价格</th>
              <th width="120">IoT工作流ID</th>
              <th width="80">状态</th>
              <th>自定义选项</th>
              <th width="150">更新时间</th>
              <th width="150">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in productList" :key="item.drinkId">
              <td>{{ (pagination.pageNum - 1) * pagination.pageSize + index + 1 }}</td>
              <td>
                <div class="d-flex align-items-center">
                  <img 
                    v-if="item.drinkPrototypeInfo?.prototypeImage" 
                    :src="item.drinkPrototypeInfo.prototypeImage" 
                    alt="商品图片" 
                    class="product-image me-2"
                  />
                  <div>
                    <div class="fw-bold text-dark">{{ item.drinkPrototypeInfo?.prototypeName || '未知商品' }}</div>
                    <small class="text-muted">商品ID: {{ item.drinkId }} | 原型ID: {{ item.drinkPrototype }}</small>
                  </div>
                </div>
              </td>
              <td>
                <span class="badge bg-info">{{ item.storeInfo?.storeName || '未知门店' }}</span>
              </td>
              <td>
                <span class="fw-bold text-success">¥{{ item.productPrice }}</span>
              </td>
              <td>
                <code class="small">{{ item.targetId || '未设置' }}</code>
              </td>
              <td>
                <span 
                  :class="item.status === '1' ? 'badge bg-success' : 'badge bg-danger'"
                >
                  {{ item.status === '1' ? '上架' : '下架' }}
                </span>
              </td>
              <td>
                <span v-if="item.options" class="text-truncate d-inline-block" style="max-width: 150px;" :title="item.options">
                  {{ item.options }}
                </span>
                <span v-else class="text-muted">无自定义选项</span>
              </td>
              <td>{{ formatDate(item.updateTime) }}</td>
              <td>
                <div class="btn-group btn-group-sm">
                  <button @click="showEditModal(item)" class="btn btn-outline-primary">
                    编辑
                  </button>
                  <button @click="deleteProduct(item)" class="btn btn-outline-danger">
                    删除
                  </button>
                </div>
              </td>
            </tr>
            <tr v-if="productList.length === 0">
              <td colspan="9" class="text-center py-4 text-muted">
                暂无数据
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.total > 0" class="d-flex justify-content-between align-items-center mt-20">
        <div class="text-muted">
          共 {{ pagination.total }} 条记录，第 {{ pagination.pageNum }} / {{ Math.ceil(pagination.total / pagination.pageSize) }} 页
        </div>
        <nav>
          <ul class="pagination mb-0">
            <li class="page-item" :class="{ disabled: pagination.pageNum <= 1 }">
              <button class="page-link" @click="changePage(pagination.pageNum - 1)">上一页</button>
            </li>
            <li 
              v-for="page in getPageNumbers()" 
              :key="page" 
              class="page-item" 
              :class="{ active: page === pagination.pageNum }"
            >
              <button class="page-link" @click="changePage(page)">{{ page }}</button>
            </li>
            <li class="page-item" :class="{ disabled: pagination.pageNum >= Math.ceil(pagination.total / pagination.pageSize) }">
              <button class="page-link" @click="changePage(pagination.pageNum + 1)">下一页</button>
            </li>
          </ul>
        </nav>
      </div>
    </div>

    <!-- 新增/编辑模态框 -->
    <div class="modal fade" id="productModal" tabindex="-1" ref="productModalRef">
      <div class="modal-dialog modal-xl">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">{{ isEdit ? '编辑商品' : '新增商品' }}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="submitForm">
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label class="form-label">饮品原型 <span class="text-danger">*</span></label>
                  <select v-model="formData.drinkPrototype" class="form-select" required>
                    <option value="">请选择饮品原型</option>
                    <option v-for="prototype in prototypeList" :key="prototype.prototypeId" :value="prototype.prototypeId">
                      {{ prototype.prototypeName }}
                    </option>
                  </select>
                </div>
                <div class="col-md-6 mb-3">
                  <label class="form-label">所属门店 <span class="text-danger">*</span></label>
                  <select v-model="formData.storeId" class="form-select" required>
                    <option value="">请选择门店</option>
                    <option v-for="store in storeList" :key="store.storeId" :value="store.storeId">
                      {{ store.storeName }}
                    </option>
                  </select>
                </div>
                <div class="col-md-6 mb-3">
                  <label class="form-label">销售价格 <span class="text-danger">*</span></label>
                  <div class="input-group">
                    <span class="input-group-text">¥</span>
                    <input 
                      v-model="formData.productPrice" 
                      type="number" 
                      step="0.01"
                      min="0"
                      class="form-control" 
                      placeholder="请输入销售价格"
                      required
                    />
                  </div>
                </div>
                <div class="col-md-6 mb-3">
                  <label class="form-label">IoT工作流ID <span class="text-danger">*</span></label>
                  <input 
                    v-model="formData.targetId" 
                    type="text" 
                    class="form-control" 
                    placeholder="请输入IoT设备工作流ID"
                    required
                  />
                  <div class="form-text">用于IoT设备制作此商品的工作流标识</div>
                </div>
                <div class="col-md-6 mb-3">
                  <label class="form-label">商品状态</label>
                  <select v-model="formData.status" class="form-select">
                    <option value="1">上架</option>
                    <option value="0">下架</option>
                  </select>
                </div>
                <div class="col-12 mb-3">
                  <label class="form-label">自定义选项 (JSON格式)</label>
                  <textarea 
                    v-model="formData.options" 
                    class="form-control" 
                    rows="4"
                    placeholder='如：{"size": "large", "temperature": "hot", "sugar": "normal"}'
                  ></textarea>
                  <div class="form-text">请输入有效的JSON格式，定义商品的规格、配料等自定义选项</div>
                </div>
                <div class="col-12 mb-3">
                  <label class="form-label">备注</label>
                  <textarea 
                    v-model="formData.remark" 
                    class="form-control" 
                    rows="2"
                    placeholder="请输入备注信息"
                  ></textarea>
                </div>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" @click="submitForm" class="btn btn-primary" :disabled="submitting">
              <span v-if="submitting" class="spinner-border spinner-border-sm me-2"></span>
              {{ isEdit ? '更新' : '创建' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { format } from 'date-fns'
import { Modal } from 'bootstrap'
import {
  getProductList,
  createProduct,
  updateProduct,
  deleteProduct as apiDeleteProduct,
  getPrototypeDetailById,
  type ProductQueryParams
} from '@/utils/api/product'
import { getStoreList } from '@/utils/api/store'
import { getDrinkPrototypeList, type PrototypeQueryParams } from '@/utils/api/drink'
import { storeDetail, drinkPrototype, drinkProduct } from '@/types'

// 数据状态
const loading = ref(false)
const submitting = ref(false)
const productList = ref<drinkProduct[]>([])
const storeList = ref<storeDetail[]>([])
const prototypeList = ref<drinkPrototype[]>([])
const isEdit = ref(false)
const productModalRef = ref<HTMLElement>()
let productModal: Modal | null = null

// 搜索表单
const searchForm = ref({
  searchValue: '',
  storeId: '',
  drinkPrototype: '',
  status: ''
})

// 分页信息
const pagination = ref({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 表单数据
const formData = ref({
  drinkId: null,
  drinkPrototype: '',
  storeId: '',
  productPrice: '',
  targetId: '',
  status: '1',
  options: '',
  remark: ''
})

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return format(new Date(dateString), 'yyyy-MM-dd HH:mm')
}

// 获取门店列表
const fetchStoreList = async () => {
  try {
    console.log('🔄 [商品管理] 开始加载门店列表...')
    const response = await getStoreList({ pageNum: 1, pageSize: 1000 })

    console.log('📦 [商品管理] 门店API响应:', response)

    // 根据实际API响应，门店的status='0'表示营业中（与商品状态不同）
    const allStores = response.rows || []
    console.log('🔍 [商品管理] 门店状态分析:', allStores.map(s => `${s.storeId}:${s.storeName}:status=${s.status}`))

    // 先尝试筛选status='1'的门店，如果没有则使用status='0'的门店
    let activeStores = allStores.filter((item: storeDetail) => item.status === '1')
    if (activeStores.length === 0) {
      console.log('⚠️ [商品管理] 没有找到status=1的门店，使用status=0的门店')
      activeStores = allStores.filter((item: storeDetail) => item.status === '0')
    }
    storeList.value = activeStores

    console.log(`✅ [商品管理] 门店列表加载成功: 总共${allStores.length}个门店, 营业中${storeList.value.length}个门店`)
    console.log('🏪 [商品管理] 营业门店列表:', storeList.value.map(s => `${s.storeId}:${s.storeName}`))
  } catch (error) {
    console.error('❌ [商品管理] 获取门店列表失败:', error)
    storeList.value = []
  }
}

// 获取原型列表
const fetchPrototypeList = async () => {
  try {
    console.log('🔄 [商品管理] 开始加载原型列表...')
    const params: PrototypeQueryParams = {
      pageNum: 1,
      pageSize: 1000,
      status: '1'
    }
    const response = await getDrinkPrototypeList(params)

    prototypeList.value = response.rows || []
    console.log(`✅ [商品管理] 原型列表加载成功: ${prototypeList.value.length}个原型`)
    console.log('🎯 [商品管理] 原型列表:', prototypeList.value.map(p => `${p.prototypeId}:${p.prototypeName}`))
  } catch (error) {
    console.error('❌ [商品管理] 获取原型列表失败:', error)
    prototypeList.value = []
  }
}

// 获取商品列表
const fetchProductList = async () => {
  loading.value = true
  try {
    const params: ProductQueryParams = {
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize,
      storeId: searchForm.value.storeId ? parseInt(searchForm.value.storeId) : undefined,
      drinkPrototype: searchForm.value.drinkPrototype ? parseInt(searchForm.value.drinkPrototype) : undefined,
      status: searchForm.value.status || undefined,
      productName: searchForm.value.searchValue || undefined
    }

    console.log('📤 [商品管理] API请求参数:', params)
    const response = await getProductList(params)
    console.log('📥 [商品管理] API响应:', { total: response.total, count: response.rows?.length })
    const products = response.rows || []

    console.log(`🔄 [商品管理] 开始补充${products.length}个商品的详细信息...`)

    // 创建门店和原型映射表，避免重复API调用
    const storeMap = new Map()
    const prototypeMap = new Map()

    // 收集需要查询的唯一ID
    const uniqueStoreIds = [...new Set(products.map((p: { storeId: number; [key: string]: unknown }) => p.storeId))]
    const uniquePrototypeIds = [...new Set(products.map((p: { drinkPrototype: number; [key: string]: unknown }) => p.drinkPrototype))]

    console.log(`📊 [商品管理] 需要查询 ${uniqueStoreIds.length} 个门店, ${uniquePrototypeIds.length} 个原型`)

    // 批量获取门店信息
    await Promise.all(
      uniqueStoreIds.map(async (storeId) => {
        try {
          const storeDetail = await getStoreByStoreId(storeId)
          storeMap.set(storeId, storeDetail?.data?.storeName || storeDetail?.storeName || '未知门店')
        } catch (error) {
          console.warn(`⚠️ [商品管理] 获取门店${storeId}信息失败:`, error)
          storeMap.set(storeId, '未知门店')
        }
      })
    )

    // 批量获取原型信息
    await Promise.all(
      uniquePrototypeIds.map(async (prototypeId) => {
        try {
          const prototypeDetail = await getPrototypeDetailById(prototypeId)
          prototypeMap.set(prototypeId, {
            prototypeName: prototypeDetail?.prototypeName || '未知原型',
            prototypeImage: prototypeDetail?.prototypeImage
          })
        } catch (error) {
          console.warn(`⚠️ [商品管理] 获取原型${prototypeId}信息失败:`, error)
          prototypeMap.set(prototypeId, { prototypeName: '未知原型', prototypeImage: null })
        }
      })
    )

    // 使用映射表补充商品信息
    const enrichedProducts = products.map((product: { storeId: number; drinkPrototype: number; [key: string]: unknown }) => {
      const storeInfo = storeMap.get(product.storeId) || '未知门店'
      const prototypeInfo = prototypeMap.get(product.drinkPrototype) || { prototypeName: '未知原型', prototypeImage: null }

      return {
        ...product,
        storeInfo: {
          storeName: storeInfo
        },
        drinkPrototypeInfo: {
          prototypeName: prototypeInfo.prototypeName,
          prototypeImage: prototypeInfo.prototypeImage
        }
      }
    })

    productList.value = enrichedProducts
    pagination.value.total = response.total || 0

    console.log(`✅ [商品管理] 商品列表加载成功: ${productList.value.length}个商品，详细信息补充完成`)
  } catch (error) {
    console.error('❌ [商品管理] 获取商品列表失败:', error)
    productList.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  console.log('🔍 [商品管理] 执行搜索，条件:', searchForm.value)
  pagination.value.pageNum = 1
  fetchProductList()
}

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    searchValue: '',
    storeId: '',
    drinkPrototype: '',
    status: ''
  }
  pagination.value.pageNum = 1
  fetchProductList()
}

// 分页处理
const changePage = (page: number) => {
  if (page < 1 || page > Math.ceil(pagination.value.total / pagination.value.pageSize)) return
  pagination.value.pageNum = page
  fetchProductList()
}

// 获取页码数组
const getPageNumbers = () => {
  const totalPages = Math.ceil(pagination.value.total / pagination.value.pageSize)
  const current = pagination.value.pageNum
  const pages = []
  
  const start = Math.max(1, current - 2)
  const end = Math.min(totalPages, current + 2)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
}

// 显示新增模态框
const showAddModal = () => {
  isEdit.value = false
  formData.value = {
    drinkId: null,
    drinkPrototype: '',
    storeId: '',
    productPrice: '',
    targetId: '',
    status: '1',
    options: '',
    remark: ''
  }
  productModal?.show()
}

// 显示编辑模态框
const showEditModal = (item: drinkProduct) => {
  isEdit.value = true
  formData.value = {
    drinkId: item.drinkId,
    drinkPrototype: item.drinkPrototype,
    storeId: item.storeId,
    productPrice: item.productPrice,
    targetId: item.targetId || '',
    status: item.status,
    options: item.options || '',
    remark: item.remark || ''
  }
  productModal?.show()
}

// 提交表单
const submitForm = async () => {
  if (!formData.value.drinkPrototype) {
    alert('请选择饮品原型')
    return
  }
  
  if (!formData.value.storeId) {
    alert('请选择所属门店')
    return
  }
  
  if (!formData.value.productPrice) {
    alert('请输入销售价格')
    return
  }
  
  if (!formData.value.targetId) {
    alert('请输入IoT工作流ID')
    return
  }
  
  // 验证JSON格式
  if (formData.value.options) {
    try {
      JSON.parse(formData.value.options)
    } catch (error) {
      alert('自定义选项必须是有效的JSON格式')
      return
    }
  }
  
  submitting.value = true
  try {
    if (isEdit.value) {
      await updateProduct(formData.value as drinkProduct) // 使用API工具函数
      console.log(`✅ [商品管理] 商品更新成功: ${formData.value.drinkPrototype}`)
    } else {
      await createProduct({
        drinkPrototype: parseInt(formData.value.drinkPrototype),
        storeId: parseInt(formData.value.storeId),
        productPrice: formData.value.productPrice,
        targetId: formData.value.targetId,
        status: formData.value.status,
        options: formData.value.options,
        remark: formData.value.remark
      })
      console.log(`✅ [商品管理] 商品创建成功: ${formData.value.drinkPrototype}`)
    }

    productModal?.hide()
    fetchProductList()
    alert(isEdit.value ? '更新成功' : '创建成功')
  } catch (error) {
    console.error('❌ [商品管理] 提交失败:', error)
    alert('操作失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 删除商品
const deleteProduct = async (item: drinkProduct) => {
  if (!confirm(`确定要删除商品"${item.drinkPrototypeInfo?.prototypeName}"吗？`)) return

  try {
    await apiDeleteProduct(item.drinkId) // 使用API工具函数
    console.log(`✅ [商品管理] 商品删除成功: ${item.drinkPrototypeInfo?.prototypeName}`)
    fetchProductList()
    alert('删除成功')
  } catch (error) {
    console.error('❌ [商品管理] 删除失败:', error)
    alert('删除失败，请重试')
  }
}

// 导出数据
const exportData = async () => {
  try {
    const params = { ...searchForm.value }
    await serviceAxios.post('/manager/product/export', {}, { params })
    alert('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    alert('导出失败，请重试')
  }
}

// 初始化
onMounted(async () => {
  await nextTick()
  if (productModalRef.value) {
    productModal = new Modal(productModalRef.value)
  }

  console.log('🚀 [商品管理] 开始初始化...')

  // 确保门店和原型数据先加载完成
  await fetchStoreList()
  await fetchPrototypeList()

  // 验证数据加载状态
  if (storeList.value.length === 0) {
    console.error('❌ [商品管理] 门店数据加载失败，门店筛选将不可用')
  }

  if (prototypeList.value.length === 0) {
    console.error('❌ [商品管理] 原型数据加载失败，原型筛选将不可用')
  }

  // 然后加载商品列表
  await fetchProductList()

  console.log(`✅ [商品管理] 初始化完成: ${storeList.value.length}个门店, ${prototypeList.value.length}个原型`)
  console.log('🔍 [商品管理] 当前搜索条件:', searchForm.value)
})
</script>

<style scoped>
.product-item-management {
  background: #fff;
}

.search-controls {
  flex-wrap: wrap;
}

.search-input {
  min-width: 200px;
}

.store-filter,
.prototype-filter,
.status-filter {
  min-width: 120px;
}

.product-image {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

.table th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
}

.table td {
  vertical-align: middle;
}

.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
}

@media (max-width: 768px) {
  .search-controls {
    flex-direction: column;
    gap: 10px !important;
  }
  
  .search-input,
  .store-filter,
  .prototype-filter,
  .status-filter {
    min-width: auto;
    width: 100%;
  }
  
  .action-buttons {
    margin-top: 10px;
  }
}
</style>
