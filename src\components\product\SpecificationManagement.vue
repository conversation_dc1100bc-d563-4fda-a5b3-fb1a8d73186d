<template>
  <div class="specification-management p-20">
    <!-- 页面标题 -->
    <div class="page-header mb-20">
      <h4 class="mb-0">规格管理</h4>
      <p class="text-muted mb-0">管理商品规格配置，支持JSON格式的规格定义</p>
    </div>

    <!-- 原型选择区域 -->
    <div class="prototype-selector mb-20">
      <div class="d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center gap-3">
          <label class="form-label mb-0 fw-bold">选择原型:</label>
          <select v-model="selectedPrototypeId" class="form-select" style="width: 300px;" @change="handlePrototypeChange">
            <option value="">请选择原型</option>
            <option v-for="prototype in prototypeList" :key="prototype.prototypeId" :value="prototype.prototypeId">
              {{ prototype.prototypeName }}
            </option>
          </select>
          <button v-if="selectedPrototypeId" @click="refreshSpecification" class="btn btn-outline-secondary">
            <i class="flaticon-refresh me-2"></i>
            刷新
          </button>
        </div>
        <div class="action-buttons" v-if="selectedPrototypeId">
          <button @click="validateJson" class="btn btn-outline-info">
            <i class="flaticon-check me-2"></i>
            验证JSON
          </button>
          <button @click="saveSpecification" class="btn btn-primary ms-2" :disabled="saving">
            <i class="flaticon-save me-2"></i>
            {{ saving ? '保存中...' : '保存配置' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 原型信息卡片 -->
    <div v-if="selectedPrototype" class="prototype-info-card mb-20">
      <div class="card border-0 bg-light">
        <div class="card-body p-15">
          <div class="row align-items-center">
            <div class="col-md-8">
              <h6 class="mb-1">{{ selectedPrototype.prototypeName }}</h6>
              <p class="text-muted mb-0 small">
                <i class="flaticon-info me-1"></i>{{ selectedPrototype.prototypeDescription || '暂无描述' }}
              </p>
            </div>
            <div class="col-md-4 text-end">
              <span :class="selectedPrototype.status === '1' ? 'badge bg-success' : 'badge bg-danger'">
                {{ selectedPrototype.status === '1' ? '启用' : '停用' }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 规格配置内容 -->
    <div v-if="selectedPrototypeId" class="specification-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3 text-muted">正在加载规格配置...</p>
      </div>

      <!-- 配置编辑器 -->
      <div v-else class="row">
        <!-- JSON编辑器 -->
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h6 class="mb-0">JSON配置编辑器</h6>
              <small class="text-muted">编辑原型的默认选项配置</small>
            </div>
            <div class="card-body">
              <div class="mb-3">
                <label class="form-label">默认选项配置 (JSON格式)</label>
                <textarea 
                  v-model="jsonConfig" 
                  class="form-control font-monospace"
                  rows="20"
                  placeholder="请输入JSON格式的规格配置..."
                  :class="{ 'is-invalid': jsonError }"
                ></textarea>
                <div v-if="jsonError" class="invalid-feedback">
                  {{ jsonError }}
                </div>
              </div>
              
              <!-- 快速模板 -->
              <div class="mb-3">
                <label class="form-label">快速模板</label>
                <div class="btn-group w-100" role="group">
                  <button @click="loadTemplate('basic')" class="btn btn-outline-secondary btn-sm">基础模板</button>
                  <button @click="loadTemplate('coffee')" class="btn btn-outline-secondary btn-sm">咖啡模板</button>
                  <button @click="loadTemplate('tea')" class="btn btn-outline-secondary btn-sm">茶饮模板</button>
                  <button @click="loadTemplate('juice')" class="btn btn-outline-secondary btn-sm">果汁模板</button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 预览区域 -->
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h6 class="mb-0">配置预览</h6>
              <small class="text-muted">实时预览JSON配置的结构</small>
            </div>
            <div class="card-body">
              <div v-if="parsedConfig" class="specification-preview">
                <!-- 尺寸选项 -->
                <div v-if="parsedConfig.sizes" class="mb-3">
                  <h6 class="text-primary">尺寸选项</h6>
                  <div class="row">
                    <div v-for="size in parsedConfig.sizes" :key="size.id" class="col-6 mb-2">
                      <div class="border rounded p-2">
                        <strong>{{ size.name }}</strong>
                        <div class="small text-muted">{{ size.description }}</div>
                        <div class="small">价格调整: {{ size.priceAdjustment > 0 ? '+' : '' }}{{ size.priceAdjustment }}元</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 温度选项 -->
                <div v-if="parsedConfig.temperatures" class="mb-3">
                  <h6 class="text-primary">温度选项</h6>
                  <div class="d-flex flex-wrap gap-2">
                    <span v-for="temp in parsedConfig.temperatures" :key="temp.id" class="badge bg-secondary">
                      {{ temp.name }}
                    </span>
                  </div>
                </div>

                <!-- 甜度选项 -->
                <div v-if="parsedConfig.sweetness" class="mb-3">
                  <h6 class="text-primary">甜度选项</h6>
                  <div class="d-flex flex-wrap gap-2">
                    <span v-for="sweet in parsedConfig.sweetness" :key="sweet.id" class="badge bg-info">
                      {{ sweet.name }}
                    </span>
                  </div>
                </div>

                <!-- 配料选项 -->
                <div v-if="parsedConfig.toppings" class="mb-3">
                  <h6 class="text-primary">配料选项</h6>
                  <div class="row">
                    <div v-for="topping in parsedConfig.toppings" :key="topping.id" class="col-12 mb-1">
                      <div class="d-flex justify-content-between align-items-center">
                        <span>{{ topping.name }}</span>
                        <span class="text-muted small">+{{ topping.price }}元</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div v-else-if="jsonError" class="text-danger">
                <i class="flaticon-warning me-2"></i>
                JSON格式错误，无法预览
              </div>
              
              <div v-else class="text-muted">
                <i class="flaticon-info me-2"></i>
                请输入有效的JSON配置以查看预览
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state text-center py-5">
      <i class="flaticon-settings display-1 text-muted"></i>
      <h5 class="mt-3 text-muted">请选择一个原型开始配置规格</h5>
      <p class="text-muted">选择原型后，您可以编辑该原型的默认选项配置</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { 
  getDrinkPrototypeList,
  getPrototypeDetail,
  updatePrototype,
  type PrototypeQueryParams 
} from '@/utils/api/drink'
import { drinkPrototype } from '@/types/drink'

// 数据状态
const loading = ref(false)
const saving = ref(false)
const selectedPrototypeId = ref('')
const prototypeList = ref<drinkPrototype[]>([])
const selectedPrototype = ref<drinkPrototype | null>(null)
const jsonConfig = ref('')
const jsonError = ref('')

// 计算属性：解析后的配置
const parsedConfig = computed(() => {
  if (!jsonConfig.value.trim()) return null
  
  try {
    const parsed = JSON.parse(jsonConfig.value)
    jsonError.value = ''
    return parsed
  } catch (error) {
    jsonError.value = '无效的JSON格式'
    return null
  }
})

// 获取原型列表
const fetchPrototypeList = async () => {
  try {
    const params: PrototypeQueryParams = {
      pageNum: 1,
      pageSize: 1000,
      status: '1'
    }
    const response = await getDrinkPrototypeList(params)
    prototypeList.value = response.rows || []
  } catch (error) {
    console.error('获取原型列表失败:', error)
    prototypeList.value = []
  }
}

// 获取原型详情
const fetchPrototypeDetail = async () => {
  if (!selectedPrototypeId.value) return
  
  loading.value = true
  try {
    const prototype = await getPrototypeDetail(Number(selectedPrototypeId.value))
    selectedPrototype.value = prototype
    jsonConfig.value = prototype.defaultOptions || ''
  } catch (error) {
    console.error('获取原型详情失败:', error)
    selectedPrototype.value = null
    jsonConfig.value = ''
  } finally {
    loading.value = false
  }
}

// 原型切换处理
const handlePrototypeChange = () => {
  if (selectedPrototypeId.value) {
    fetchPrototypeDetail()
  } else {
    selectedPrototype.value = null
    jsonConfig.value = ''
  }
}

// 刷新规格配置
const refreshSpecification = () => {
  fetchPrototypeDetail()
}

// 验证JSON
const validateJson = () => {
  if (!jsonConfig.value.trim()) {
    alert('请输入JSON配置')
    return
  }
  
  try {
    JSON.parse(jsonConfig.value)
    alert('JSON格式验证通过！')
    jsonError.value = ''
  } catch (error) {
    const errorMsg = `JSON格式错误: ${error.message}`
    alert(errorMsg)
    jsonError.value = errorMsg
  }
}

// 保存规格配置
const saveSpecification = async () => {
  if (!selectedPrototype.value) return
  
  // 验证JSON格式
  try {
    JSON.parse(jsonConfig.value)
  } catch (error) {
    alert('JSON格式错误，请检查后重试')
    return
  }
  
  saving.value = true
  try {
    const updatedPrototype = {
      ...selectedPrototype.value,
      defaultOptions: jsonConfig.value
    }
    
    await updatePrototype(updatedPrototype)
    alert('规格配置保存成功！')
  } catch (error) {
    console.error('保存规格配置失败:', error)
    alert('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

// 加载模板
const loadTemplate = (templateType: string) => {
  const templates = {
    basic: {
      sizes: [
        { id: 1, name: "小杯", description: "360ml", priceAdjustment: 0 },
        { id: 2, name: "中杯", description: "480ml", priceAdjustment: 3 },
        { id: 3, name: "大杯", description: "600ml", priceAdjustment: 6 }
      ],
      temperatures: [
        { id: 1, name: "热饮" },
        { id: 2, name: "温饮" },
        { id: 3, name: "冰饮" }
      ]
    },
    coffee: {
      sizes: [
        { id: 1, name: "小杯", description: "360ml", priceAdjustment: 0 },
        { id: 2, name: "中杯", description: "480ml", priceAdjustment: 3 },
        { id: 3, name: "大杯", description: "600ml", priceAdjustment: 6 }
      ],
      temperatures: [
        { id: 1, name: "热饮" },
        { id: 2, name: "冰饮" }
      ],
      sweetness: [
        { id: 1, name: "无糖" },
        { id: 2, name: "三分糖" },
        { id: 3, name: "五分糖" },
        { id: 4, name: "七分糖" },
        { id: 5, name: "全糖" }
      ],
      toppings: [
        { id: 1, name: "珍珠", price: 3 },
        { id: 2, name: "椰果", price: 2 },
        { id: 3, name: "布丁", price: 4 }
      ]
    },
    tea: {
      sizes: [
        { id: 1, name: "小杯", description: "360ml", priceAdjustment: 0 },
        { id: 2, name: "中杯", description: "480ml", priceAdjustment: 3 },
        { id: 3, name: "大杯", description: "600ml", priceAdjustment: 6 }
      ],
      temperatures: [
        { id: 1, name: "热饮" },
        { id: 2, name: "温饮" },
        { id: 3, name: "冰饮" }
      ],
      sweetness: [
        { id: 1, name: "无糖" },
        { id: 2, name: "少糖" },
        { id: 3, name: "半糖" },
        { id: 4, name: "正常糖" }
      ],
      toppings: [
        { id: 1, name: "珍珠", price: 3 },
        { id: 2, name: "红豆", price: 2 },
        { id: 3, name: "仙草", price: 2 },
        { id: 4, name: "芋圆", price: 4 }
      ]
    },
    juice: {
      sizes: [
        { id: 1, name: "小杯", description: "300ml", priceAdjustment: 0 },
        { id: 2, name: "中杯", description: "450ml", priceAdjustment: 5 },
        { id: 3, name: "大杯", description: "600ml", priceAdjustment: 8 }
      ],
      temperatures: [
        { id: 1, name: "常温" },
        { id: 2, name: "冰饮" }
      ],
      toppings: [
        { id: 1, name: "椰果", price: 2 },
        { id: 2, name: "芦荟", price: 3 }
      ]
    }
  }
  
  const template = templates[templateType]
  if (template) {
    jsonConfig.value = JSON.stringify(template, null, 2)
  }
}

// 初始化
onMounted(() => {
  fetchPrototypeList()
})
</script>

<style scoped>
.specification-management {
  background: #fff;
}

.prototype-selector {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.prototype-info-card .card {
  border-radius: 8px;
}

.specification-preview {
  max-height: 500px;
  overflow-y: auto;
}

.font-monospace {
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
}

.empty-state {
  padding: 60px 20px;
}

.card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-header {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.specification-preview .badge {
  margin: 2px;
}

.specification-preview h6 {
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 5px;
  margin-bottom: 10px;
}
</style>
