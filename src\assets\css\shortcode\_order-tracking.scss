.order-tracking-box {
    .card-body {
        .track-order-list {
            display: flex;
            flex-wrap: wrap;
            margin: {
                top: 60px;
                bottom: 60px;
            };
            li {
                flex: 1 0 0%;
                z-index: 1;
                
                &::before {
                    left: 0;
                    right: 0;
                    top: 12px;
                    content: '';
                    z-index: -1;
                    margin-left: 6px;
                    position: absolute;
                    border: {
                        style: dashed;
                        left-width: 0;
                        top-width: 1px;
                        right-width: 0;
                        bottom-width: 0;
                        color: var(--splash-primary-color);
                    };
                }
                .dot {
                    width: 25px;
                    height: 25px;
                    position: relative;
                    border: 1px solid var(--splash-primary-color);

                    &::before {
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        content: '';
                        margin: 5px;
                        display: none;
                        position: absolute;
                        border-radius: 50%;
                        background: var(--splash-primary-color);
                    }
                }
                &.active {
                    .dot {
                        &::before {
                            display: block;
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width : 767px) {

    .order-tracking-box {
        .card-body {
            .track-order-list {
                flex-wrap: unset;
                display: block;
                margin: {
                    top: 20px;
                    bottom: 20px;
                };
                li {
                    flex: unset;
                    padding: {
                        left: 30px;
                        top: 15px;
                    };
                    &::before {
                        top: 0;
                        bottom: 0;
                        right: auto;
                        margin-left: 12px;
                        border: {
                            left-width: 1px;
                            top-width: 0;
                        };
                    }
                    .dot {
                        margin-left: -30px;
                    }
                    &:first-child {
                        padding-top: 0;
                    }
                }
            }
        }
    }
    
}

@media only screen and (min-width : 768px) and (max-width : 991px) {

    .order-tracking-box {
        .card-body {
            .track-order-list {
                margin: {
                    top: 35px;
                    bottom: 35px;
                };
            }
        }
    }

}