<template>
  <!-- 面包屑导航 - 移到外部 -->
  <BreadCrumb title="总览" :breadcrumb="breadcrumb" />

  <!-- 加载状态 -->
  <div v-if="isLoading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-3 text-muted">正在加载总览数据...</p>
  </div>

  <!-- 错误状态 -->
  <div v-else-if="error" class="alert alert-danger" role="alert">
    <div class="text-center py-4">
      <i class="flaticon-warning fs-1 text-danger mb-3"></i>
      <h5 class="mb-3">数据获取失败</h5>
      <p class="mb-3">{{ error }}</p>
      <button @click="fetchDashboardData" class="btn btn-primary">
        <i class="flaticon-refresh me-2"></i>
        重新加载
      </button>
    </div>
  </div>

  <div v-else class="dashboard-overview">
    <!-- 页面标题与控制区 -->
    <div class="d-flex justify-content-between align-items-center mb-30">
      <h1 class="main-title">全门店营业总览</h1>
      <div class="date-controls">
        <div class="quick-date-buttons">
          <button class="quick-btn" :class="{ active: quickDateActive === 'today' }" @click="selectQuickDate('today')">今日</button>
          <button class="quick-btn" :class="{ active: quickDateActive === 'week' }" @click="selectQuickDate('week')">本周</button>
          <button class="quick-btn" :class="{ active: quickDateActive === 'month' }" @click="selectQuickDate('month')">本月</button>
        </div>
        <div class="time-selector">
          <flat-pickr
            v-model="selectedDate"
            :config="datePickerConfig"
            class="form-control date-picker"
            placeholder="选择日期范围"
            @on-change="onDateChange"
          />
        </div>
      </div>
    </div>

    <!-- 第一行 - 核心KPI指标区 -->
    <div class="row mb-30">
      <div class="col-lg-4 col-md-6 mb-20">
        <div class="card border-0 rounded-0 bg-white h-100 box-shadow">
          <div class="card-body p-20 text-center">
            <div class="metric-item">
              <div class="metric-content">
                <span class="metric-label">当日营业额</span>
                <div class="metric-value-container">
                  <span class="metric-value revenue">{{ totalRevenue.toLocaleString() }}</span>
                  <span class="metric-unit currency">¥</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-4 col-md-6 mb-20">
        <div class="card border-0 rounded-0 bg-white h-100 box-shadow">
          <div class="card-body p-20 text-center">
            <div class="metric-item">
              <div class="metric-content">
                <span class="metric-label">订单量</span>
                <div class="metric-value-container">
                  <span class="metric-value orders">{{ totalOrders.toLocaleString() }}</span>
                  <span class="metric-unit order-unit">单</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-4 col-md-6 mb-20">
        <div class="card border-0 rounded-0 bg-white h-100 box-shadow">
          <div class="card-body p-20 text-center">
            <div class="metric-item">
              <div class="metric-content">
                <span class="metric-label">杯量</span>
                <div class="metric-value-container">
                  <span class="metric-value cups">{{ totalCups.toLocaleString() }}</span>
                  <span class="metric-unit cup-unit">杯</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第二行 - 周期性汇总区 -->
    <div class="row mb-25">
      <div class="col-lg-6 mb-20">
        <div class="card border-0 rounded-0 bg-white box-shadow">
          <div class="card-body p-20">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-calendar me-10 text-primary" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">本周营业额汇总</h5>
            </div>
            <div class="summary-stats">
              <div class="summary-item">
                <span class="summary-label">本周总营业额</span>
                <div class="summary-value-container">
                  <span class="summary-value">{{ weeklyRevenue.toLocaleString() }}</span>
                  <span class="summary-unit currency">¥</span>
                </div>
              </div>
              <div class="summary-item">
                <span class="summary-label">日均营业额</span>
                <div class="summary-value-container">
                  <span class="summary-value">{{ Math.round(weeklyRevenue / 7).toLocaleString() }}</span>
                  <span class="summary-unit currency">¥</span>
                </div>
              </div>
              <div class="summary-item">
                <span class="summary-label">环比增长</span>
                <div class="summary-value-container">
                  <span class="summary-value growth-positive">+12.5</span>
                  <span class="summary-unit">%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-6 mb-20">
        <div class="card border-0 rounded-0 bg-white box-shadow">
          <div class="card-body p-20">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-calendar me-10 text-success" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">本月营业额汇总</h5>
            </div>
            <div class="summary-stats">
              <div class="summary-item">
                <span class="summary-label">本月总营业额</span>
                <div class="summary-value-container">
                  <span class="summary-value">{{ monthlyRevenue.toLocaleString() }}</span>
                  <span class="summary-unit currency">¥</span>
                </div>
              </div>
              <div class="summary-item">
                <span class="summary-label">日均营业额</span>
                <div class="summary-value-container">
                  <span class="summary-value">{{ Math.round(monthlyRevenue / 30).toLocaleString() }}</span>
                  <span class="summary-unit currency">¥</span>
                </div>
              </div>
              <div class="summary-item">
                <span class="summary-label">环比增长</span>
                <div class="summary-value-container">
                  <span class="summary-value growth-positive">+8.3</span>
                  <span class="summary-unit">%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第三行 - 全门店明星单品 -->
    <div class="row mb-25">
      <div class="col-12">
        <div class="card border-0 rounded-0 bg-white box-shadow">
          <div class="card-body p-20">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-star me-10 text-warning" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">全门店明星单品</h5>
            </div>
            <div class="star-products-grid">
              <div v-for="product in starProducts" :key="product.id" class="product-item">
                <div class="product-info">
                  <div class="product-name-container">
                    <div class="product-rank">{{ product.rank }}</div>
                    <span class="product-name">{{ product.name }}</span>
                  </div>
                  <div class="product-sales-container">
                    <span class="product-sales">{{ product.sales }}</span>
                    <span class="product-unit cup-unit">杯</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第四行 - 排行数据区 -->
    <div class="row mb-25">
      <div class="col-lg-6 mb-20">
        <div class="card border-0 rounded-0 bg-white box-shadow">
          <div class="card-body p-20">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-trophy me-10 text-success" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">门店营业额排行</h5>
            </div>
            <div class="store-ranking">
              <div v-for="store in storeRanking" :key="store.id" class="store-item">
                <div class="store-info">
                  <div class="store-name-container">
                    <div class="store-rank">{{ store.rank }}</div>
                    <span class="store-name">{{ store.name }}</span>
                  </div>
                  <div class="store-revenue-container">
                    <span class="store-revenue">{{ store.revenue.toLocaleString() }}</span>
                    <span class="store-unit currency">¥</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-6 mb-20">
        <div class="card border-0 rounded-0 bg-white box-shadow">
          <div class="card-body p-20">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-drink me-10 text-info" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">单品杯数排行</h5>
            </div>
            <div class="product-ranking">
              <div v-for="product in productRanking" :key="product.id" class="product-rank-item">
                <div class="product-rank-info">
                  <div class="product-rank-name-container">
                    <div class="product-rank-number">{{ product.rank }}</div>
                    <span class="product-rank-name">{{ product.name }}</span>
                  </div>
                  <div class="product-rank-cups-container">
                    <span class="product-rank-cups">{{ product.cups }}</span>
                    <span class="product-rank-unit cup-unit">杯</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第五行 - 营业额分时段分析 -->
    <div class="row mb-25">
      <div class="col-12">
        <div class="card border-0 rounded-0 bg-white box-shadow">
          <div class="card-body p-20">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-chart me-10 text-primary" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">营业额分时段分析</h5>
            </div>
            <div class="chart-container">
              <apexchart
                type="bar"
                height="350"
                :options="hourlyRevenueChartOptions"
                :series="hourlyRevenueChartSeries"
                class="chart"
                id="hourlyRevenueChart"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第六行 - 销量变化曲线图 -->
    <div class="row mb-20">
      <div class="col-12">
        <div class="card border-0 rounded-0 bg-white box-shadow">
          <div class="card-body p-20">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-trending me-10 text-warning" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">销量变化曲线图</h5>
            </div>
            <div class="chart-container">
              <apexchart
                type="line"
                height="350"
                :options="weeklyTrendChartOptions"
                :series="weeklyTrendChartSeries"
                class="chart"
                id="weeklyTrendChart"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, reactive, watch } from 'vue'
import { format } from 'date-fns'
import BreadCrumb from "@/components/layouts/BreadCrumb.vue"
import flatPickr from "vue-flatpickr-component"
import 'flatpickr/dist/flatpickr.css'
import 'flatpickr/dist/l10n/zh.js'
import '@/assets/css/dashboard-overview.css'
import { getOrderListByParams } from '@/utils/api/order'
import { getProductDetailById, getPrototypeDetailById } from '@/utils/api/product'
import { getStoreList } from '@/utils/api/store'
import serviceAxios from '@/utils/serviceAxios'
import type { drinkOrder } from '@/types/order'
import type { drinkProductionQueueItem, storeDetail } from '@/types'



// 面包屑导航
const breadcrumb = ref([
  { label: '工作台', url: '/dashboard' }
])

// 加载状态
const isLoading = ref<boolean>(true)
const error = ref<string | null>(null)

// 时间选择器配置
const selectedDate = ref('')
const quickDateActive = ref('today')
const currentQueryDate = ref<Date>(new Date())
const datePickerConfig = ref({
  mode: 'range',
  dateFormat: 'Y-m-d',
  locale: 'zh',
  defaultDate: 'today'
})

// 统一的数据存储
const dashboardData = reactive<{
  stores: storeDetail[]
  allOrders: drinkOrder[]
  allQueueItems: drinkProductionQueueItem[]
}>({
  stores: [],
  allOrders: [],
  allQueueItems: [],
})

// 数据获取函数
async function fetchDashboardData() {
  isLoading.value = true
  error.value = null

  try {
    const formattedApiDate = format(currentQueryDate.value, "yyyy-MM-dd")

    // 获取所有门店列表
    const storesResponse = await getStoreList({ pageNum: 1, pageSize: 9999 })
    dashboardData.stores = storesResponse.rows ?? []

    // 使用门店销售统计API获取汇总数据
    console.log(`🔍 [总览] 开始获取${dashboardData.stores.length}个门店的销售统计数据，查询日期: ${formattedApiDate}`)

    const salesStatPromises = dashboardData.stores.map(store =>
      serviceAxios.get('/manager/store/sales-stat', {
        params: {
          storeId: store.storeId,
          date: formattedApiDate
        }
      }).then(response => {
        console.log(`✅ [总览] 门店${store.storeId}(${store.storeName})销售统计:`, response.data)
        return response
      }).catch(err => {
        console.error(`❌ [总览] 获取门店${store.storeId}(${store.storeName})销售统计失败:`, err)
        return { data: { totalAmount: '0', orderCount: 0, totalCups: 0 } }
      })
    )

    // 获取详细订单数据用于产品分析
    console.log(`🔍 [总览] 开始获取${dashboardData.stores.length}个门店的订单详情数据`)

    const orderPromises = dashboardData.stores.map(store =>
      getOrderListByParams({
        pageNum: 1,
        pageSize: 9999,
        queryDate: formattedApiDate,
        storeId: store.storeId,
        orderTimeSort: "1",
      }).then(response => {
        console.log(`✅ [总览] 门店${store.storeId}(${store.storeName})订单数据: ${response.rows?.length || 0}条订单`)
        return response
      }).catch(err => {
        console.error(`❌ [总览] 获取门店${store.storeId}(${store.storeName})订单失败:`, err)
        return { rows: [], total: 0 }
      })
    )

    const [salesStatResponses, orderResponses] = await Promise.all([
      Promise.all(salesStatPromises),
      Promise.all(orderPromises)
    ])

    // 处理销售统计数据
    console.log(`📊 [总览] 开始汇总销售统计数据`)
    let totalSalesAmount = 0
    let totalOrderCount = 0
    let totalCupCount = 0

    salesStatResponses.forEach((response, index) => {
      // 修正：serviceAxios拦截器直接返回后端JSON，无需访问.data
      const storeInfo = dashboardData.stores[index]
      const salesAmount = parseFloat(response.totalAmount || '0')
      const orderCount = parseInt(response.orderCount || '0')
      const cupCount = parseInt(response.totalCups || '0')

      console.log(`📈 [总览] 门店${storeInfo.storeId}(${storeInfo.storeName}) - 营业额:¥${salesAmount}, 订单:${orderCount}单, 杯数:${cupCount}杯`)

      totalSalesAmount += salesAmount
      totalOrderCount += orderCount
      totalCupCount += cupCount
    })

    console.log(`🎯 [总览] 汇总结果 - 总营业额:¥${totalSalesAmount}, 总订单:${totalOrderCount}单, 总杯数:${totalCupCount}杯`)

    // 更新核心KPI
    totalRevenue.value = totalSalesAmount
    totalOrders.value = totalOrderCount
    totalCups.value = totalCupCount

    // 合并详细订单数据用于产品分析
    dashboardData.allOrders = orderResponses.flatMap(response => response.rows ?? [])

    // 处理产品分析数据
    await processDashboardData()

  } catch (err) {
    console.error("获取总览数据失败:", err)
    error.value = "数据加载失败，请稍后重试。"
    dashboardData.allOrders = []
    dashboardData.stores = []
    totalRevenue.value = 0
    totalOrders.value = 0
    totalCups.value = 0
  } finally {
    isLoading.value = false
  }
}

// 核心KPI数据（基于真实数据计算）
const totalRevenue = ref(156800)
const totalOrders = ref(2340)
const totalCups = ref(4680)

// 数据处理函数
async function processDashboardData() {
  // 计算明星产品
  await calculateStarProducts()

  // 计算门店排行（基于订单数据）
  calculateStoreRanking()

  // 计算产品排行
  await calculateProductRanking()

  // 计算图表数据
  calculateChartData()
}

// 全门店明星单品数据（基于真实数据计算）
const starProducts = ref([
  { id: 1, name: '美式咖啡', sales: 890, rank: 1 },
  { id: 2, name: '拿铁咖啡', sales: 760, rank: 2 },
  { id: 3, name: '卡布奇诺', sales: 540, rank: 3 },
  { id: 4, name: '摩卡咖啡', sales: 430, rank: 4 },
  { id: 5, name: '焦糖玛奇朵', sales: 380, rank: 5 },
  { id: 6, name: '冰美式', sales: 320, rank: 6 },
  { id: 7, name: '抹茶拿铁', sales: 280, rank: 7 },
  { id: 8, name: '香草拿铁', sales: 240, rank: 8 }
])

// 计算明星产品
async function calculateStarProducts() {
  if (!dashboardData.allOrders || dashboardData.allOrders.length === 0) {
    starProducts.value = []
    return
  }

  try {
    const productQuantities = new Map<number, number>()

    // 统计每个产品的销量
    for (const order of dashboardData.allOrders) {
      let parsedItems
      try {
        parsedItems = JSON.parse(order.orderItems)
      } catch (e) {
        continue
      }
      if (!Array.isArray(parsedItems)) continue

      for (const item of parsedItems) {
        if (item && item.productId) {
          const productId = Number(item.productId)
          const quantity = Number(item.quantity) || 1
          productQuantities.set(productId, (productQuantities.get(productId) || 0) + quantity)
        }
      }
    }

    // 获取产品详情并按销量排序
    const productDetails = []
    for (const [productId, quantity] of productQuantities.entries()) {
      try {
        const detail = await getProductDetailById(productId)
        if (detail && detail.drinkPrototype) {
          const prototypeDetail = await getPrototypeDetailById(detail.drinkPrototype)
          productDetails.push({
            id: productId,
            name: prototypeDetail?.prototypeName || `产品${productId}`,
            sales: quantity,
            rank: 0
          })
        }
      } catch (err) {
        console.error(`获取产品${productId}详情失败:`, err)
      }
    }

    // 按销量排序并设置排名（显示前8个）
    productDetails.sort((a, b) => b.sales - a.sales)
    starProducts.value = productDetails.slice(0, 8).map((item, index) => ({
      ...item,
      rank: index + 1
    }))
  } catch (err) {
    console.error("计算明星产品失败:", err)
  }
}

// 门店营业额排行数据（基于真实数据计算）
const storeRanking = ref([
  { id: 1, name: '中关村店', revenue: 28500, rank: 1 },
  { id: 2, name: '三里屯店', revenue: 25800, rank: 2 },
  { id: 3, name: '国贸店', revenue: 23400, rank: 3 },
  { id: 4, name: '望京店', revenue: 21200, rank: 4 },
  { id: 5, name: '西单店', revenue: 19800, rank: 5 },
  { id: 6, name: '朝阳门店', revenue: 18600, rank: 6 }
])

// 计算门店排行
function calculateStoreRanking() {
  if (!dashboardData.stores || dashboardData.stores.length === 0) {
    storeRanking.value = []
    return
  }

  try {
    const storeRevenues = new Map<number, number>()

    // 统计每个门店的营业额
    for (const order of dashboardData.allOrders) {
      const storeId = order.storeId
      const revenue = parseFloat(order.totalAmount || '0')
      storeRevenues.set(storeId, (storeRevenues.get(storeId) || 0) + revenue)
    }

    // 生成门店排行数据
    const storeRankingData = dashboardData.stores.map(store => ({
      id: store.storeId,
      name: store.storeName,
      revenue: storeRevenues.get(store.storeId) || 0,
      rank: 0
    }))

    // 按营业额排序并设置排名
    storeRankingData.sort((a, b) => b.revenue - a.revenue)
    storeRanking.value = storeRankingData.slice(0, 6).map((item, index) => ({
      ...item,
      rank: index + 1
    }))
  } catch (err) {
    console.error("计算门店排行失败:", err)
  }
}

// 单品杯数排行数据（基于真实数据计算）
const productRanking = ref([
  { id: 1, name: '美式咖啡', cups: 1240, rank: 1 },
  { id: 2, name: '拿铁咖啡', cups: 980, rank: 2 },
  { id: 3, name: '卡布奇诺', cups: 760, rank: 3 },
  { id: 4, name: '冰美式', cups: 650, rank: 4 },
  { id: 5, name: '摩卡咖啡', cups: 540, rank: 5 }
])

// 计算产品排行
async function calculateProductRanking() {
  if (!dashboardData.allOrders || dashboardData.allOrders.length === 0) {
    productRanking.value = []
    return
  }

  try {
    const productQuantities = new Map<number, number>()

    // 统计每个产品的杯数
    for (const order of dashboardData.allOrders) {
      let parsedItems
      try {
        parsedItems = JSON.parse(order.orderItems)
      } catch (e) {
        continue
      }
      if (!Array.isArray(parsedItems)) continue

      for (const item of parsedItems) {
        if (item && item.productId) {
          const productId = Number(item.productId)
          const quantity = Number(item.quantity) || 1
          productQuantities.set(productId, (productQuantities.get(productId) || 0) + quantity)
        }
      }
    }

    // 获取产品详情并按杯数排序
    const productDetails = []
    for (const [productId, quantity] of productQuantities.entries()) {
      try {
        const detail = await getProductDetailById(productId)
        if (detail && detail.drinkPrototype) {
          const prototypeDetail = await getPrototypeDetailById(detail.drinkPrototype)
          productDetails.push({
            id: productId,
            name: prototypeDetail?.prototypeName || `产品${productId}`,
            cups: quantity,
            rank: 0
          })
        }
      } catch (err) {
        console.error(`获取产品${productId}详情失败:`, err)
      }
    }

    // 按杯数排序并设置排名（显示前5个）
    productDetails.sort((a, b) => b.cups - a.cups)
    productRanking.value = productDetails.slice(0, 5).map((item, index) => ({
      ...item,
      rank: index + 1
    }))
  } catch (err) {
    console.error("计算产品排行失败:", err)
  }
}

// 计算图表数据
function calculateChartData() {
  try {
    // 基于真实订单数据计算时段分布
    const hourlyData = new Array(24).fill(0)
    const hourlyRevenue = new Array(24).fill(0)

    dashboardData.allOrders.forEach(order => {
      if (order.orderTime) {
        const hour = new Date(order.orderTime).getHours()
        hourlyData[hour] += 1
        hourlyRevenue[hour] += parseFloat(order.totalAmount || '0')
      }
    })

    // 更新图表数据（如果有图表组件的话）
    console.log('📊 [总览] 时段订单分布:', hourlyData)
    console.log('📊 [总览] 时段营业额分布:', hourlyRevenue)

    // 计算门店营业额占比
    const storeRevenueMap = new Map<number, number>()
    dashboardData.allOrders.forEach(order => {
      const storeId = order.storeId
      const amount = parseFloat(order.totalAmount || '0')
      storeRevenueMap.set(storeId, (storeRevenueMap.get(storeId) || 0) + amount)
    })

    console.log('📊 [总览] 门店营业额分布:', Array.from(storeRevenueMap.entries()))

  } catch (err) {
    console.error("计算图表数据失败:", err)
  }
}

// 监听日期变化
watch(() => currentQueryDate.value, () => {
  fetchDashboardData()
})

// 周期性汇总数据（基于真实数据计算）
const weeklyRevenue = computed(() => {
  // 基于当日数据估算周营业额
  // 实际项目中应该调用专门的周统计API
  const dailyAverage = totalRevenue.value
  const weekdayMultiplier = 1.0 // 工作日系数
  const weekendMultiplier = 1.2 // 周末系数
  return Math.round(dailyAverage * 5 * weekdayMultiplier + dailyAverage * 2 * weekendMultiplier)
})

const monthlyRevenue = computed(() => {
  // 基于当日数据估算月营业额
  // 实际项目中应该调用专门的月统计API
  const dailyAverage = totalRevenue.value
  const monthDays = new Date(currentQueryDate.value.getFullYear(), currentQueryDate.value.getMonth() + 1, 0).getDate()
  return Math.round(dailyAverage * monthDays * 0.95) // 考虑月度波动
})

// 快速日期选择功能
const selectQuickDate = (type: string) => {
  quickDateActive.value = type
  const today = new Date()
  let targetDate: Date

  switch (type) {
    case 'today': {
      targetDate = new Date(today)
      break
    }
    case 'week': {
      // 获取本周一
      const dayOfWeek = today.getDay()
      const diff = today.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1)
      targetDate = new Date(today.setDate(diff))
      break
    }
    case 'month': {
      targetDate = new Date(today.getFullYear(), today.getMonth(), 1)
      break
    }
    default: {
      targetDate = new Date(today)
    }
  }

  const formatDate = (date: Date) => {
    return date.toISOString().split('T')[0]
  }

  // 更新选中日期和查询日期
  selectedDate.value = formatDate(targetDate)
  currentQueryDate.value = targetDate
}

// 营业额分时段分析图表配置
const hourlyRevenueChartSeries = ref([
  {
    name: '营业额',
    data: [1200, 2800, 4500, 6800, 8900, 12400, 15600, 18200, 21500, 19800, 16400, 13200, 9800, 7200, 5400, 3600, 2100, 1800, 2400, 3200, 2800, 2200, 1600, 1000]
  }
])

const hourlyRevenueChartOptions = ref({
  chart: {
    type: 'bar',
    height: 300,
    toolbar: {
      show: false
    },
    fontFamily: 'Source Han Serif, serif',
    background: 'transparent'
  },
  colors: ['#6560F0'],
  plotOptions: {
    bar: {
      borderRadius: 4,
      columnWidth: '60%'
    }
  },
  dataLabels: {
    enabled: false
  },
  xaxis: {
    categories: ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00', '07:00', '08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'],
    labels: {
      style: {
        colors: '#6c757d',
        fontSize: '11px',
        fontFamily: 'Source Han Serif, serif'
      }
    },
    axisBorder: {
      show: false
    },
    axisTicks: {
      show: false
    }
  },
  yaxis: {
    labels: {
      style: {
        colors: '#6c757d',
        fontSize: '11px',
        fontFamily: 'Source Han Serif, serif'
      },
      formatter: function (value: number) {
        return '¥' + value
      }
    }
  },
  grid: {
    borderColor: '#f0f1f3',
    strokeDashArray: 3
  },
  tooltip: {
    theme: 'light',
    style: {
      fontSize: '12px',
      fontFamily: 'Source Han Serif, serif'
    },
    y: {
      formatter: function (value: number) {
        return '¥' + value
      }
    }
  }
})

// 销量变化曲线图配置
const weeklyTrendChartSeries = ref([
  {
    name: '销量',
    data: [1240, 1580, 1320, 1680, 1890, 2100, 1950]
  }
])

const weeklyTrendChartOptions = ref({
  chart: {
    type: 'line',
    height: 300,
    toolbar: {
      show: false
    },
    fontFamily: 'Source Han Serif, serif',
    background: 'transparent'
  },
  colors: ['#28a745'],
  stroke: {
    curve: 'smooth',
    width: 3
  },
  markers: {
    size: 6,
    colors: ['#28a745'],
    strokeColors: '#fff',
    strokeWidth: 2,
    hover: {
      size: 8
    }
  },
  xaxis: {
    categories: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    labels: {
      style: {
        colors: '#6c757d',
        fontSize: '11px',
        fontFamily: 'Source Han Serif, serif'
      }
    },
    axisBorder: {
      show: false
    },
    axisTicks: {
      show: false
    }
  },
  yaxis: {
    labels: {
      style: {
        colors: '#6c757d',
        fontSize: '11px',
        fontFamily: 'Source Han Serif, serif'
      },
      formatter: function (value: number) {
        return value + '杯'
      }
    }
  },
  grid: {
    borderColor: '#f0f1f3',
    strokeDashArray: 3
  },
  tooltip: {
    theme: 'light',
    style: {
      fontSize: '12px',
      fontFamily: 'Source Han Serif, serif'
    },
    y: {
      formatter: function (value: number) {
        return value + '杯'
      }
    }
  }
})

// 事件处理函数
const onDateChange = (selectedDates: Date[]) => {
  console.log('选择的日期范围:', selectedDates)
  // 当手动选择日期时，清除快速选择的激活状态
  quickDateActive.value = ''

  if (selectedDates && selectedDates.length > 0) {
    // 更新查询日期为选中的第一个日期
    currentQueryDate.value = selectedDates[0]
  }
}

// 初始化时选择今日
onMounted(async () => {
  selectQuickDate('today')
  await fetchDashboardData()
})
</script>


