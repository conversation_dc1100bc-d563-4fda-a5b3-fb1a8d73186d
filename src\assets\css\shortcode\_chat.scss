.chat-sidebar {
    .card-body {
        padding: 15px;

        .search-box {
            margin-bottom: 15px;

            .form-control {
                background: #F5F4FA;
                padding: {
                    top: 14px;
                    bottom: 14px;
                };
            }
            button {
                top: 50%;
                right: 20px;
                line-height: 1;
                margin-top: 1px;
                font-size: 17px;
                position: absolute;
                transform: translateY(-50%);
            }
        }
        .chat-tabs {
            display: flex;
            flex-wrap: wrap;

            a {
                flex: 1 0 0%;
                border-radius: 4px;
                padding: 10px 15px;
                color: var(--splash-muted-color);
                border: 1px solid rgba(101, 96, 240, 0.3);
                margin: {
                    left: 2px;
                    right: 2px;
                };
                &:hover, &.active {
                    background-color: var(--splash-primary-color);
                    border-color: var(--splash-primary-color);
                    color: var(--splash-white-color);
                }
                &:first-child {
                    margin-left: 0;
                }
                &:last-child {
                    margin-right: 0;
                }
            }
        }
        .create-new-group-btn {
            margin: {
                top: 20px;
                left: -15px;
                right: -15px;
            };
            button {
                padding: {
                    top: 17px;
                    left: 15px;
                    bottom: 17px;
                    right: 35px;
                };
                i {
                    top: 50%;
                    line-height: 1;
                    font-size: 14px;
                    position: absolute;
                    transform: translateY(-50%);
                    margin: {
                        left: 8px;
                        top: 1.5px;
                    };
                }
            }
        }
        .chat-users-list {
            height: 705px;
            overflow: hidden;
            overflow-y: scroll;
            margin: {
                top: 30px;
                left: -15px;
                right: -15px;
            };
            .single-user-item {
                z-index: 1;
                cursor: pointer;
                border-bottom: 1px dashed #d9e9ef;
                padding: {
                    top: 20px;
                    left: 15px;
                    right: 45px;
                    bottom: 20px;
                };
                .title {
                    margin-bottom: 2px;
                }
                .dot-badge {
                    top: 50%;
                    right: 15px;
                    width: 23px;
                    height: 23px;
                    line-height: 23px;
                    position: absolute;
                    transform: translateY(-50%);
                }
                &:first-child {
                    border-top: 1px dashed #d9e9ef;
                }
                &::before {
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    opacity: 0;
                    z-index: -1;
                    margin: 4px;
                    content: '';
                    visibility: hidden;
                    position: absolute;
                    background: #efeef9;
                    transition: var(--transition);
                }
                &:hover, &.active {
                    &::before {
                        opacity: 1;
                        visibility: visible;
                    }
                }
            }
            &::-webkit-scrollbar {
                -webkit-appearance: none;

                &:vertical {
                    width: 5px;
                }
                &:horizontal {
                    height: 5px;
                }
            }
            &::-webkit-scrollbar-thumb {
                border-radius: 50px;
                background: #6D6C7D;
            }
            &::-webkit-scrollbar-track {
                background: #DCDCE3;
            }
            .box {
                border-bottom: 1px dashed #d9e9ef;
                padding-bottom: 15px;
                margin-top: 30px;
                padding: {
                    left: 15px;
                    right: 15px;
                };
                span {
                    width: 40px;
                    height: 40px;
                    line-height: 37px;
                    background: #F2F1F9;
                    border: 1px solid var(--splash-primary-color);
                }
                &:first-child {
                    margin-top: 0;
                }
            }
        }
    }
}
.chat-card {
    .card-head {
        .users-list {
            div {
                width: 40px;
                height: 40px;
                margin-right: -15px;
                border: 2px solid var(--splash-white-color);
                filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));

                &:last-child {
                    margin-right: 0;
                }
            }
        }
    }
    .card-body {
        .chat-list {
            height: 640px;
            overflow: hidden;
            overflow-y: scroll;
            padding-right: 25px;

            .chat-item {
                margin-bottom: 10px;
                padding-left: 60px;
                max-width: 700px;

                .user {
                    position: absolute;
                    left: 0;
                    top: 0;
                }
                .message {
                    margin-bottom: 10px;
                    padding-right: 70px;
                    display: inline-block;

                    .inner {
                        padding: 20px;
                        background: #F3F2FA;
                        border-radius: 0 10px 10px 10px;

                        p {
                            margin-bottom: 12px;
                            color: #72708A;

                            &:last-child {
                                margin-bottom: 0;
                            }
                        }
                    }
                    .dropdown {
                        top: 50%;
                        z-index: 2;
                        right: 40px;
                        position: absolute;
                        transform: translateY(-50%);
                    }
                    .emoji {
                        right: 0;
                        top: 50%;
                        margin-top: 3px;
                        position: absolute;
                        transform: translateY(-50%);

                        button {
                            font-size: 22px;
                            color: var(--splash-muted-color);
        
                            &:hover {
                                color: var(--splash-primary-color);
                            }
                        }
                    }
                    &:last-child {
                        margin-bottom: 0;
                    }
                }
                &.right {
                    padding-left: 0;
                    text-align: end;
                    margin-left: auto;

                    .message {
                        padding: {
                            right: 0;
                            left: 70px;
                        };
                        .inner {
                            border-radius: 10px 10px 0px 10px;
                            background: var(--splash-primary-color);
    
                            p {
                                color: #F1F0F5;
                            }
                        }
                        .dropdown {
                            right: auto;
                            left: 35px;
                        }
                        .emoji {
                            right: auto;
                            left: 0;
                        }
                    }
                }
                &:last-child {
                    margin-bottom: 0;
                }
                &.info {
                    max-width: 100%;
                    z-index: 1;
                    margin: {
                        top: 30px;
                        bottom: 30px;
                    };
                    &::before {
                        left: 0;
                        top: 50%;
                        right: 0;
                        z-index: -1;
                        height: 1px;
                        content: '';
                        position: absolute;
                        transform: translateY(-50%);
                        border-bottom: 1px dashed #D2CFE4;
                    }
                }
            }
            &::-webkit-scrollbar {
                -webkit-appearance: none;

                &:vertical {
                    width: 5px;
                }
                &:horizontal {
                    height: 5px;
                }
            }
            &::-webkit-scrollbar-thumb {
                border-radius: 50px;
                background: #6D6C7D;
            }
            &::-webkit-scrollbar-track {
                background: #DCDCE3;
            }
        }
        .write-your-message {
            padding-right: 220px;
            margin-top: 25px;

            .write-message {
                padding: 16px 20px 16px 50px;
                background: #F5F4FA;
                border-radius: 10px;

                .input-message {
                    border: none;
                    background-color: transparent;
                    border-left: 1px solid #A4A3B0;
                    padding: {
                        top: 3px;
                        left: 11px;
                        bottom: 3px;
                        right: 10px;
                    };
                    &::placeholder {
                        color: #8E8DA1;
                        transition: var(--transition);
                    }
                    &:focus {
                        &::placeholder {
                            color: transparent;
                        }
                    }
                }
                button {
                    top: 50%;
                    left: 15px;
                    font-size: 25px;
                    margin-top: 3px;
                    position: absolute;
                    transform: translateY(-50%);
                    color: var(--splash-muted-color);

                    &:hover {
                        color: var(--splash-primary-color);
                    }
                }
            }
            .buttons-list {
                top: 50%;
                right: 0;
                position: absolute;
                transform: translateY(-50%);

                button {
                    width: 59px;
                    height: 59px;
                    font-size: 22px;
                    margin-right: 10px;
                    border-radius: 10px;
                    background: #F5F4FA;
                    color: var(--splash-muted-color);

                    i {
                        left: 0;
                        right: 0;
                        top: 50%;
                        line-height: 1;
                        margin-top: 1px;
                        position: absolute;
                        transform: translateY(-50%);
                    }
                    &:last-child {
                        margin-right: 0;
                    }
                    &.active, &:hover {
                        background-color: var(--splash-success-color);
                        color: var(--splash-white-color);
                    }
                }
            }
        }
    }
    &.chat-group-card {
        .card-body {
            .chat-list {
                height: 718px;
            }
        }
    }
}

// Dark Mode
.dark {

    .chat-sidebar {
        .card-body {
            .search-box {
                .form-control {
                    background: var(--splash-black-color);
                }
            }
            .chat-tabs {
                a {
                    color: #BCBBC7;
                    
                    &:hover, &.active {
                        color: var(--splash-white-color);
                    }
                }
            }
            .chat-users-list {
                .single-user-item {
                    border-bottom-color: #45445e;
                    
                    &:first-child {
                        border-top-color: #45445e;
                    }
                    &::before {
                        background: var(--splash-black-color);
                    }
                }
                &::-webkit-scrollbar-thumb {
                    background: #6D6C7D;
                }
                &::-webkit-scrollbar-track {
                    background: #0D0C1D;
                }
                .box {
                    border-bottom-color: #45445e;
                    
                    span {
                        background: var(--splash-black-color);
                    }
                }
            }
        }
    }
    .chat-card {
        .card-head {
            .users-list {
                div {
                    border-color: #45445e;
                }
            }
        }
        .card-body {
            .chat-list {
                .chat-item {
                    .message {
                        .inner {
                            background: var(--splash-black-color);
    
                            p {
                                color: #BCBBC7;
                            }
                        }
                        .emoji {
                            button {
                                color: #BCBBC7;
            
                                &:hover {
                                    color: var(--splash-primary-color);
                                }
                            }
                        }
                    }
                    &.right {
                        .message {
                            .inner {
                                background: var(--splash-primary-color);
        
                                p {
                                    color: #F1F0F5;
                                }
                            }
                        }
                    }
                    &.info {
                        &::before {
                            border-bottom-color: #45445e;
                        }
                    }
                }
                &::-webkit-scrollbar-thumb {
                    background: #6D6C7D;
                }
                &::-webkit-scrollbar-track {
                    background: #0D0C1D;
                }
            }
            .write-your-message {
                .write-message {
                    background: var(--splash-black-color);
    
                    .input-message {
                        border-left-color: #45445e;
                        
                        &::placeholder {
                            color: #BCBBC7;
                        }
                        &:focus {
                            &::placeholder {
                                color: transparent;
                            }
                        }
                    }
                    button {
                        color: #BCBBC7;
    
                        &:hover {
                            color: var(--splash-primary-color);
                        }
                    }
                }
                .buttons-list {
                    button {
                        background: var(--splash-black-color);
                        color: #BCBBC7;
    
                        &.active, &:hover {
                            background-color: var(--splash-success-color);
                            color: var(--splash-white-color);
                        }
                    }
                }
            }
        }
    }

}

@media only screen and (max-width : 767px) {

    .chat-sidebar {
        .card-body {
            .search-box {
                .form-control {
                    padding: {
                        top: 13px;
                        bottom: 13px;
                    };
                }
                button {
                    right: 15px;
                    font-size: 15px;
                }
            }
            .chat-tabs {
                a {
                    padding: 8px 15px;
                }
            }
            .create-new-group-btn {
                margin-top: 15px;

                button {
                    padding: {
                        top: 15px;
                        bottom: 15px;
                    };
                }
            }
            .chat-users-list {
                height: 500px;
                margin: {
                    top: 15px;
                    left: -15px;
                    right: -15px;
                };
                .single-user-item {
                    padding: {
                        top: 15px;
                        left: 15px;
                        right: 35px;
                        bottom: 15px;
                    };
                    .dot-badge {
                        right: 12px;
                        width: 20px;
                        height: 20px;
                        line-height: 20px;
                    }
                    &::before {
                        margin: 3px;
                    }
                }
                .box {
                    margin-top: 20px;
                }
            }
        }
    }
    .chat-card {
        .card-body {
            .chat-list {
                height: 500px;
                padding-right: 10px;
    
                .chat-item {
                    padding-left: 0;
                    max-width: 100%;
    
                    .user {
                        position: relative;
                        margin-bottom: 11px;
                    }
                    .message {
                        padding-right: 0;
    
                        .inner {
                            padding: 12px;
                        }
                        .dropdown {
                            top: 0;
                            right: 0;
                            position: relative;
                            display: inline-block;
                            transform: translateY(0);
                        }
                        .emoji {
                            top: 4px;
                            right: 0;
                            margin-top: 5px;
                            position: relative;
                            display: inline-block;
                            transform: translateY(0);
                            margin: {
                                top: 0;
                                left: 5px;
                            };
                        }
                    }
                    &.right {
                        margin-left: 0;
    
                        .message {
                            padding: {
                                right: 0;
                                left: 0;
                            };
                            .dropdown {
                                right: 0;
                                left: 0;
                            }
                            .emoji {
                                right: 0;
                                left: 0;
                            }
                        }
                    }
                    &.info {
                        margin: {
                            top: 10px;
                            bottom: 10px;
                        };
                    }
                }
            }
            .write-your-message {
                padding-right: 0;
                margin-top: 15px;
    
                .write-message {
                    padding: 15px 15px 15px 40px;
    
                    .input-message {
                        padding: {
                            top: 2px;
                            bottom: 2px;
                        };
                    }
                    button {
                        left: 10px;
                        font-size: 20px;
                    }
                }
                .buttons-list {
                    top: 0;
                    margin-top: 12px;
                    position: relative;
                    transform: translateY(0);
    
                    button {
                        width: 50px;
                        height: 50px;
                        font-size: 20px;
                        margin-right: 5px;
    
                        i {
                            margin-top: 1px;
                        }
                    }
                }
            }
        }
        &.chat-group-card {
            .card-body {
                .chat-list {
                    height: 500px;
                }
            }
        }
    }

}

@media only screen and (min-width : 768px) and (max-width : 991px) {

    .chat-sidebar {
        .card-body {
            .create-new-group-btn {
                margin-top: 15px;
            }
            .chat-users-list {
                height: 550px;
                margin-top: 15px;
            }
        }
    }
    .chat-card {
        .card-body {
            .chat-list {
                height: 600px;
    
                .chat-item {
                    max-width: 600px;
                }
            }
        }
        &.chat-group-card {
            .card-body {
                .chat-list {
                    height: 600px;
                }
            }
        }
    }

}

@media only screen and (min-width : 992px) and (max-width : 1199px) {

    .chat-sidebar {
        .card-body {
            .chat-users-list {
                .single-user-item {
                    .title {
                        span {
                            display: block;
                            margin: {
                                left: 0 !important;
                                bottom: 3px;
                                top: 3px;
                            };
                        }
                    }
                }
            }
        }
    }
    .chat-card {
        .card-body {
            .chat-list {
                .chat-item {
                    max-width: 545px;
                }
            }
        }
    }

}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {

    .chat-sidebar {
        .card-body {
            .chat-users-list {
                .single-user-item {
                    .title {
                        span {
                            display: block;
                            margin: {
                                left: 0 !important;
                                bottom: 3px;
                                top: 3px;
                            };
                        }
                    }
                }
            }
        }
    }
    
}