.edit-course-card {
    .input-group {
        .input-group-text {
            padding: 10px 18px;
            border-right: none;
            background: #F5F4FA;
            border-color: #dedee4;
        }
        .form-control {
            border-left: none;
        }
    }
    .file-upload {
        border: 1px solid #dedee4;
        padding: 55px 15px;

        i {
            line-height: 1;
            font-size: 35px;
            margin-bottom: 5px;
            color: var(--splash-primary-color);
        }
        span {
            span {
                &::before {
                    left: 0;
                    right: 0;
                    height: 1px;
                    content: '';
                    bottom: -2px;
                    position: absolute;
                    background: var(--splash-black-color);
                }
            }
        }
        input {
            cursor: pointer;
        }
    }
    .members-list {
        div {
            margin: {
                top: 10px;
                right: 5px;
            };
        }
        button {
            font-size: 8px;
            margin-left: 3px;

            &:hover {
                color: red;
            }
        }
    }
    button {
        span {
            line-height: 1.3;

            &::before {
                left: 0;
                right: 0;
                bottom: 0;
                height: 1px;
                content: '';
                position: absolute;
                transition: var(--transition);
                background: var(--splash-danger-color);
            }
        }
    }
    .ql-container {
        min-height: 150px;
        height: auto;
    }
    .accordion {
        .accordion-item {
            margin-bottom: 10px;

            .accordion-button {
                padding: 15px 50px 15px 15px;
                box-shadow: 0px 4px 34px rgba(101, 96, 240, 0.1);

                .number {
                    width: 30px;
                    height: 30px;
                    line-height: 30px;
                    margin-right: 20px;
                }
                ul {
                    li {
                        margin-right: 15px;
                        padding-left: 20px;

                        i {
                            left: 0;
                            top: 50%;
                            line-height: 1;
                            margin-top: .5px;
                            position: absolute;
                            transform: translateY(-50%);
                        }
                        &:last-child {
                            margin-right: 0;
                        }
                    }
                }
                &::after {
                    top: 50%;
                    right: 15px;
                    position: absolute;
                    transform: translateY(-50%) rotate(180deg);
                }
                &.collapsed {
                    &::after {
                        transform: translateY(-50%);
                    }
                }
            }
            .accordion-body {
                padding: 0 0 15px;

                ul {
                    li {
                        border-bottom: 1px dashed #D2CFE4;
                        padding: 20px;

                        a {
                            padding-left: 28px;

                            i {
                                left: 0;
                                top: 50%;
                                line-height: 1;
                                position: absolute;
                                transform: translateY(-50%);
                            }
                        }
                    }
                }
            }
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}

// Dark Mode
.dark {
    .edit-course-card {
        .input-group {
            .input-group-text {
                background: var(--splash-black-color);
                border-color: #45445e;
            }
        }
        .file-upload {
            border-color: #45445e;
    
            span {
                span {
                    &::before {
                        background: var(--splash-white-color);
                    }
                }
            }
        }
        .accordion {
            .accordion-item {
                .accordion-button {
                    box-shadow: unset;
                    border-bottom: 1px dashed #45445e !important;

                    &.bg-white {
                        background-color: var(--splash-black-color) !important;
                    }
                }
                .accordion-body {
                    ul {
                        li {
                            border-bottom-color: #45445e;
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width : 767px) {

    .edit-course-card {
        .accordion {
            .accordion-item {
                .accordion-button {
                    padding-right: 30px;

                    .number {
                        width: 25px;
                        height: 25px;
                        line-height: 25px;
                        margin-right: 10px;
                    }
                }
                .accordion-body {
                    ul {
                        li {
                            padding: 15px;

                            a {
                                padding-left: 25px;
                            }
                        }
                    }
                }
            }
        }
    }
    
}