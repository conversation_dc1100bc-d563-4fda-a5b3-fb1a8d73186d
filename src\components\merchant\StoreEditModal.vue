<template>
  <!-- 背景遮罩 -->
  <div
    v-if="visible"
    class="modal-backdrop fade show"
    @click="closeModal"
  ></div>

  <!-- 模态框 -->
  <div
    class="modal fade"
    :class="{ show: visible }"
    :style="{ display: visible ? 'block' : 'none' }"
    id="storeEditModal"
    tabindex="-1"
    aria-labelledby="storeEditModalLabel"
    :aria-hidden="!visible"
  >
    <div class="modal-dialog modal-lg" @click.stop>
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="storeEditModalLabel">
            {{ isEdit ? '编辑门店' : '新增门店' }}
          </h5>
          <button type="button" class="btn-close" @click="closeModal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="handleSubmit">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="storeName" class="form-label">门店名称 <span class="text-danger">*</span></label>
                <input
                  type="text"
                  class="form-control"
                  id="storeName"
                  v-model="formData.storeName"
                  required
                  placeholder="请输入门店名称"
                />
              </div>
              <div class="col-md-6 mb-3">
                <label for="contactPhone" class="form-label">联系电话 <span class="text-danger">*</span></label>
                <input
                  type="tel"
                  class="form-control"
                  id="contactPhone"
                  v-model="formData.contactPhone"
                  required
                  placeholder="请输入联系电话"
                />
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="storeType" class="form-label">门店类型</label>
                <select class="form-select" id="storeType" v-model="formData.storeType">
                  <option value="">请选择门店类型</option>
                  <option value="直营店">直营店</option>
                  <option value="加盟店">加盟店</option>
                </select>
              </div>
              <div class="col-md-6 mb-3">
                <label for="status" class="form-label">营业状态 <span class="text-danger">*</span></label>
                <select class="form-select" id="status" v-model="formData.status" required>
                  <option value="1">营业中</option>
                  <option value="0">暂停营业</option>
                </select>
              </div>
            </div>

            <div class="mb-3">
              <label for="storeAddress" class="form-label">门店地址 <span class="text-danger">*</span></label>
              <input
                type="text"
                class="form-control"
                id="storeAddress"
                v-model="formData.storeAddress"
                required
                placeholder="请输入门店地址"
              />
            </div>



            <div class="mb-3">
              <label for="businessHours" class="form-label">营业时间</label>
              <input
                type="text"
                class="form-control"
                id="businessHours"
                v-model="formData.businessHours"
                placeholder="例如：09:00-22:00"
              />
            </div>

            <div class="mb-3">
              <label for="storeDescription" class="form-label">门店描述</label>
              <textarea
                class="form-control"
                id="storeDescription"
                rows="3"
                v-model="formData.storeDescription"
                placeholder="请输入门店描述"
              ></textarea>
            </div>

            <div class="mb-3">
              <label for="remark" class="form-label">备注</label>
              <textarea
                class="form-control"
                id="remark"
                rows="2"
                v-model="formData.remark"
                placeholder="请输入备注信息"
              ></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="closeModal">取消</button>
          <button type="button" class="btn btn-primary" @click="handleSubmit" :disabled="loading">
            <span v-if="loading" class="spinner-border spinner-border-sm me-2" role="status"></span>
            {{ isEdit ? '更新' : '创建' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { createMerchantStore, updateMerchantStore, type StoreFormData, type StoreInfo } from '@/utils/api/merchant'

// Props
interface Props {
  visible: boolean
  storeData?: StoreInfo | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  storeData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 状态
const loading = ref(false)
const isEdit = ref(false)

// 表单数据
const formData = reactive<StoreFormData>({
  storeName: '',
  storeAddress: '',
  contactPhone: '',
  status: '1',
  storeDescription: '',
  storeType: '',
  businessHours: '',
  remark: ''
})

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    storeName: '',
    storeAddress: '',
    contactPhone: '',
    status: '1',
    storeDescription: '',
    storeType: '',
    businessHours: '',
    remark: ''
  })
}

// 监听门店数据变化
watch(() => props.storeData, (newData) => {
  if (newData) {
    isEdit.value = true
    Object.assign(formData, {
      storeId: newData.storeId,
      storeName: newData.storeName,
      storeAddress: newData.storeAddress,
      contactPhone: newData.contactPhone,
      status: newData.status,
      storeDescription: newData.storeDescription || '',
      storeType: newData.storeType || '',
      businessHours: newData.businessHours || '',
      remark: newData.remark || ''
    })
  } else {
    isEdit.value = false
    resetForm()
  }
}, { immediate: true })

// 提交表单
const handleSubmit = async () => {
  loading.value = true
  try {
    if (isEdit.value) {
      await updateMerchantStore(formData)
      alert('门店更新成功')
    } else {
      await createMerchantStore(formData)
      alert('门店创建成功')
    }
    
    emit('success')
    emit('update:visible', false)
  } catch (error) {
    console.error('操作失败:', error)
    alert(isEdit.value ? '门店更新失败' : '门店创建失败')
  } finally {
    loading.value = false
  }
}

// 关闭模态框
const closeModal = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.form-label {
  font-weight: 500;
  color: #495057;
}

.text-danger {
  color: #dc3545 !important;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}
</style>
