.modal {
    --bs-modal-width: 785px;
    --bs-modal-border-radius: 0;
}
.modal-content {
    border: none;
}
.modal-header {
    border-bottom: 1px dashed #d9e9ef;
}

// Dark Mode
.dark {
    .modal {
        --bs-modal-bg: var(--splash-black-color);

        .modal-header {
            border-bottom-color: #45445e;
        }
        .btn-close {
            filter: invert(1);
        }
        .modal-footer {
            border-color: #45445e;
        }
    }
    .offcanvas {
        color: var(--splash-white-color);
        background-color: var(--splash-black-color);
    }
}

@media only screen and (min-width : 576px) and (max-width : 767px) {

    .modal-dialog {
        max-width: 540px;
    }

}