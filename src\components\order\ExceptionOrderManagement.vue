<template>
  <div class="exception-order-management">
    <!-- 页面标题和统计 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2 class="mb-0">异常订单处理</h2>
      <div class="d-flex gap-2">
        <button @click="refreshStatistics" class="btn btn-outline-info" :disabled="loading">
          <i class="flaticon-refresh me-2"></i>刷新统计
        </button>
        <button @click="fetchExceptionOrders" class="btn btn-primary" :disabled="loading">
          <i class="flaticon-refresh me-2"></i>刷新列表
        </button>
      </div>
    </div>

    <!-- 异常统计卡片 -->
    <div class="row g-3 mb-4" v-if="statistics">
      <div class="col-md-2">
        <div class="card bg-danger text-white">
          <div class="card-body text-center">
            <h4>{{ statistics.totalExceptions }}</h4>
            <p class="mb-0">总异常订单</p>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="card bg-warning text-white">
          <div class="card-body text-center">
            <h4>{{ statistics.unhandledCount }}</h4>
            <p class="mb-0">待处理</p>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="card bg-success text-white">
          <div class="card-body text-center">
            <h4>{{ statistics.handledCount }}</h4>
            <p class="mb-0">已处理</p>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="card bg-info text-white">
          <div class="card-body text-center">
            <h4>{{ statistics.timeoutProduction }}</h4>
            <p class="mb-0">制作超时</p>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="card bg-secondary text-white">
          <div class="card-body text-center">
            <h4>{{ statistics.timeoutPickup }}</h4>
            <p class="mb-0">取餐超时</p>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="card bg-dark text-white">
          <div class="card-body text-center">
            <h4>{{ statistics.paymentFailed }}</h4>
            <p class="mb-0">支付失败</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="card mb-4">
      <div class="card-body">
        <div class="row g-3">
          <!-- 异常类型筛选 -->
          <div class="col-md-3">
            <select v-model="searchForm.exceptionType" class="form-select" @change="handleSearch">
              <option value="">全部异常类型</option>
              <option value="timeout_production">制作超时</option>
              <option value="timeout_pickup">取餐超时</option>
              <option value="payment_failed">支付失败</option>
              <option value="production_failed">制作失败</option>
              <option value="system_error">系统错误</option>
            </select>
          </div>
          
          <!-- 处理状态筛选 -->
          <div class="col-md-2">
            <select v-model="searchForm.isHandled" class="form-select" @change="handleSearch">
              <option value="">全部状态</option>
              <option value="false">待处理</option>
              <option value="true">已处理</option>
            </select>
          </div>
          
          <!-- 门店筛选 -->
          <div class="col-md-2">
            <select v-model="searchForm.storeId" class="form-select" @change="handleSearch">
              <option value="">全部门店</option>
              <option v-for="store in storeList" :key="store.storeId" :value="store.storeId">
                {{ store.storeName }}
              </option>
            </select>
          </div>
          
          <!-- 日期筛选 -->
          <div class="col-md-2">
            <input 
              type="date" 
              v-model="searchForm.queryDate" 
              class="form-control" 
              @change="handleSearch"
            />
          </div>
          
          <!-- 搜索框 -->
          <div class="col-md-3">
            <div class="input-group">
              <input
                type="text"
                class="form-control"
                placeholder="搜索订单号、手机号..."
                v-model="searchForm.searchValue"
                @keyup.enter="handleSearch"
              />
              <button class="btn btn-outline-secondary" type="button" @click="handleSearch">
                <i class="flaticon-search-interface-symbol"></i>
              </button>
            </div>
          </div>
        </div>
        
        <div class="row g-3 mt-2">
          <!-- 批量操作 -->
          <div class="col-md-3">
            <div class="input-group">
              <select v-model="batchAction" class="form-select">
                <option value="">选择批量操作</option>
                <option value="remake">批量重新制作</option>
                <option value="refund">批量退款</option>
                <option value="cancel">批量取消</option>
                <option value="contact">批量联系客户</option>
              </select>
              <button 
                @click="handleBatchAction" 
                class="btn btn-warning" 
                :disabled="!batchAction || selectedOrders.length === 0"
              >
                执行
              </button>
            </div>
          </div>
          
          <!-- 重置按钮 -->
          <div class="col-md-2">
            <button @click="resetSearch" class="btn btn-outline-secondary w-100">
              <i class="flaticon-refresh me-2"></i>重置
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 异常订单列表 -->
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          异常订单列表
          <span class="badge bg-danger ms-2">{{ pagination.total }}</span>
        </h5>
      </div>
      <div class="card-body">
        <!-- 加载状态 -->
        <div v-if="loading" class="text-center py-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
          <p class="mt-2 text-muted">正在加载异常订单...</p>
        </div>

        <!-- 异常订单表格 -->
        <div v-else-if="exceptionOrderList.length > 0" class="table-responsive">
          <table class="table table-hover">
            <thead class="table-light">
              <tr>
                <th>
                  <input 
                    type="checkbox" 
                    @change="toggleSelectAll" 
                    :checked="isAllSelected"
                    class="form-check-input"
                  />
                </th>
                <th>订单号</th>
                <th>异常类型</th>
                <th>异常原因</th>
                <th>异常时间</th>
                <th>超时时长</th>
                <th>门店</th>
                <th>金额</th>
                <th>处理状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="order in exceptionOrderList" :key="order.orderId">
                <td>
                  <input 
                    type="checkbox" 
                    :value="order.orderId" 
                    v-model="selectedOrders"
                    class="form-check-input"
                  />
                </td>
                <td>
                  <div>
                    <strong>{{ order.orderId }}</strong>
                    <br>
                    <small class="text-muted">{{ order.orderReqSeq }}</small>
                  </div>
                </td>
                <td>
                  <span :class="getExceptionTypeClass(order.exceptionType)">
                    {{ getExceptionTypeName(order.exceptionType) }}
                  </span>
                </td>
                <td>
                  <small>{{ order.exceptionReason }}</small>
                </td>
                <td>
                  <small>{{ formatDateTime(order.exceptionTime) }}</small>
                </td>
                <td>
                  <span v-if="order.timeoutMinutes" class="badge bg-warning">
                    {{ order.timeoutMinutes }}分钟
                  </span>
                  <span v-else>-</span>
                </td>
                <td>
                  <small>{{ getStoreName(order.storeId) }}</small>
                </td>
                <td>
                  <strong class="text-primary">¥{{ order.totalAmount }}</strong>
                </td>
                <td>
                  <span v-if="order.isHandled" class="badge bg-success">
                    已处理
                    <br>
                    <small>{{ getActionName(order.handledAction) }}</small>
                  </span>
                  <span v-else class="badge bg-warning">待处理</span>
                </td>
                <td>
                  <div class="btn-group-vertical btn-group-sm">
                    <button 
                      @click="viewOrderDetail(order)" 
                      class="btn btn-outline-info btn-sm"
                    >
                      详情
                    </button>
                    <button 
                      v-if="!order.isHandled"
                      @click="showHandleModal(order)" 
                      class="btn btn-outline-warning btn-sm"
                    >
                      处理
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 空状态 -->
        <div v-else class="text-center py-5">
          <i class="flaticon-check-mark text-success" style="font-size: 3rem;"></i>
          <h5 class="mt-3">暂无异常订单</h5>
          <p class="text-muted">当前没有需要处理的异常订单</p>
        </div>

        <!-- 分页 -->
        <nav v-if="pagination.total > 0" class="mt-4">
          <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted">
              显示第 {{ (pagination.pageNum - 1) * pagination.pageSize + 1 }} - 
              {{ Math.min(pagination.pageNum * pagination.pageSize, pagination.total) }} 条，
              共 {{ pagination.total }} 条记录
            </div>
            <div class="btn-group">
              <button 
                @click="changePage(pagination.pageNum - 1)" 
                :disabled="pagination.pageNum <= 1"
                class="btn btn-outline-primary"
              >
                上一页
              </button>
              <button 
                @click="changePage(pagination.pageNum + 1)" 
                :disabled="pagination.pageNum >= Math.ceil(pagination.total / pagination.pageSize)"
                class="btn btn-outline-primary"
              >
                下一页
              </button>
            </div>
          </div>
        </nav>
      </div>
    </div>

    <!-- 异常订单处理弹窗 -->
    <ExceptionHandleModal
      v-if="showHandleModalFlag"
      :order="selectedOrder"
      @close="showHandleModalFlag = false"
      @handled="onOrderHandled"
    />

    <!-- 订单详情弹窗 -->
    <OrderDetailModal
      v-if="showDetailModalFlag"
      :order="selectedOrder"
      @close="showDetailModalFlag = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { format } from 'date-fns'
import { 
  getExceptionOrders, 
  getExceptionStatistics,
  batchHandleExceptionOrders,
  type ExceptionOrder,
  type ExceptionType,
  type ExceptionAction,
  type OrderQueryParams
} from '@/utils/api/order'
import { getStoreList } from '@/utils/api/store'
import type { storeDetail } from '@/types'
import ExceptionHandleModal from './ExceptionHandleModal.vue'
import OrderDetailModal from './OrderDetailModal.vue'

// 数据状态
const loading = ref(false)
const exceptionOrderList = ref<ExceptionOrder[]>([])
const storeList = ref<storeDetail[]>([])
const statistics = ref<any>(null)
const selectedOrders = ref<string[]>([])
const batchAction = ref('')

// 弹窗状态
const showHandleModalFlag = ref(false)
const showDetailModalFlag = ref(false)
const selectedOrder = ref<ExceptionOrder | null>(null)

// 搜索表单
const searchForm = ref({
  exceptionType: '',
  isHandled: '',
  storeId: '',
  queryDate: '',
  searchValue: ''
})

// 分页信息
const pagination = ref({
  pageNum: 1,
  pageSize: 20,
  total: 0
})

// 计算属性
const isAllSelected = computed(() => {
  return exceptionOrderList.value.length > 0 && 
         selectedOrders.value.length === exceptionOrderList.value.length
})

// 获取异常订单列表
const fetchExceptionOrders = async () => {
  loading.value = true
  try {
    const params: OrderQueryParams = {
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize,
      ...searchForm.value
    }
    
    const response = await getExceptionOrders(params)
    exceptionOrderList.value = response.rows || []
    pagination.value.total = response.total || 0
    
    console.log(`✅ [异常订单] 加载成功: ${exceptionOrderList.value.length}个异常订单`)
  } catch (error) {
    console.error('❌ [异常订单] 获取异常订单列表失败:', error)
    exceptionOrderList.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

// 获取门店列表
const fetchStoreList = async () => {
  try {
    const response = await getStoreList({ pageNum: 1, pageSize: 1000 })
    storeList.value = (response.rows || []).filter((store: storeDetail) => store.status === '0')
  } catch (error) {
    console.error('❌ [异常订单] 获取门店列表失败:', error)
  }
}

// 获取异常统计
const refreshStatistics = async () => {
  try {
    const storeId = searchForm.value.storeId ? Number(searchForm.value.storeId) : undefined
    const date = searchForm.value.queryDate || undefined
    statistics.value = await getExceptionStatistics(storeId, date)
  } catch (error) {
    console.error('❌ [异常订单] 获取异常统计失败:', error)
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.value.pageNum = 1
  fetchExceptionOrders()
  refreshStatistics()
}

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    exceptionType: '',
    isHandled: '',
    storeId: '',
    queryDate: '',
    searchValue: ''
  }
  handleSearch()
}

// 分页处理
const changePage = (page: number) => {
  pagination.value.pageNum = page
  fetchExceptionOrders()
}

// 全选/取消全选
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedOrders.value = []
  } else {
    selectedOrders.value = exceptionOrderList.value.map(order => order.orderId)
  }
}

// 批量操作
const handleBatchAction = async () => {
  if (!batchAction.value || selectedOrders.value.length === 0) return
  
  const action = batchAction.value as ExceptionAction
  const remark = `批量${getActionName(action)}`
  
  try {
    await batchHandleExceptionOrders(selectedOrders.value, action, remark)
    console.log(`✅ [异常订单] 批量${getActionName(action)}成功`)
    
    // 刷新列表
    selectedOrders.value = []
    batchAction.value = ''
    fetchExceptionOrders()
    refreshStatistics()
  } catch (error) {
    console.error(`❌ [异常订单] 批量${getActionName(action)}失败:`, error)
  }
}

// 显示处理弹窗
const showHandleModal = (order: ExceptionOrder) => {
  selectedOrder.value = order
  showHandleModalFlag.value = true
}

// 查看订单详情
const viewOrderDetail = (order: ExceptionOrder) => {
  selectedOrder.value = order
  showDetailModalFlag.value = true
}

// 订单处理完成回调
const onOrderHandled = () => {
  fetchExceptionOrders()
  refreshStatistics()
}

// 工具函数
const getExceptionTypeName = (type: ExceptionType): string => {
  const names = {
    timeout_production: '制作超时',
    timeout_pickup: '取餐超时',
    payment_failed: '支付失败',
    production_failed: '制作失败',
    system_error: '系统错误'
  }
  return names[type] || type
}

const getExceptionTypeClass = (type: ExceptionType): string => {
  const classes = {
    timeout_production: 'badge bg-warning',
    timeout_pickup: 'badge bg-info',
    payment_failed: 'badge bg-danger',
    production_failed: 'badge bg-secondary',
    system_error: 'badge bg-dark'
  }
  return classes[type] || 'badge bg-secondary'
}

const getActionName = (action?: ExceptionAction): string => {
  if (!action) return ''
  const names = {
    remake: '重新制作',
    refund: '退款',
    cancel: '取消订单',
    contact: '联系客户',
    complete: '手动完成'
  }
  return names[action] || action
}

const getStoreName = (storeId: number): string => {
  const store = storeList.value.find(s => s.storeId === storeId)
  return store?.storeName || `门店${storeId}`
}

const formatDateTime = (dateTime: string): string => {
  if (!dateTime) return ''
  return format(new Date(dateTime), 'MM-dd HH:mm')
}

// 初始化
onMounted(async () => {
  console.log('🚀 [异常订单] 开始初始化...')
  await fetchStoreList()
  await fetchExceptionOrders()
  await refreshStatistics()
  console.log('✅ [异常订单] 初始化完成')
})
</script>

<style scoped>
.exception-order-management {
  padding: 1rem;
}

.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.table th {
  font-weight: 600;
  border-bottom: 2px solid #dee2e6;
}

.btn-group-vertical .btn {
  margin-bottom: 2px;
}

.badge {
  font-size: 0.75em;
}
</style>
