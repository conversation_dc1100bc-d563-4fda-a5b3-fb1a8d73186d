.product-details-box {
    .card-body {
        padding: 30px;

        .productDetailsSwiper {
            height: 600px;

            img {
                -o-object-fit: cover;
                object-fit: cover;
                display: block;
                height: 100%;
                width: 100%;
            }
        }
        .productDetailsThumbSwiper {
            height: 600px;
            padding-right: 50px;

            img {
                -o-object-fit: cover;
                object-fit: cover;
                display: block;
                height: 100%;
                width: 100%;
            }
        }
        .product-details-content {
            margin-left: -40px;

            h3 {
                margin-bottom: 12px;
                line-height: 1.3;
                font-size: 24px;
            }
            .reviews {
                .rating {
                    color: #F3C44C;
                    i {
                        margin-right: 4px;
                    }
                }
            }
            p {
                margin-bottom: 15px;

                &:last-child {
                    margin-bottom: 0;
                }
            }
            .info {
                margin: {
                    top: 20px;
                    bottom: 20px;
                };
                li {
                    margin-bottom: 5px;

                    span {
                        width: 100px;
                    }
                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
            .box {
                padding: 15px;
                margin-bottom: 15px;
                border: 1px solid #DEDEE4;

                p {
                    a {
                        &::before {
                            left: 0;
                            right: 0;
                            bottom: 0;
                            height: 1px;
                            content: '';
                            position: absolute;
                            background: var(--splash-black-color);
                        }
                    }
                }
            }
            .buttons-list {
                margin-top: 20px;

                .input-counter {
                    .number-counter {
                        position: relative;
                        width: 135px;

                        input {
                            border: 1px solid var(--splash-primary-color);
                            color: var(--splash-black-color);
                            -moz-appearance: textfield;
                            background: #F3F2FA;
                            border-radius: 4px;
                            text-align: center;
                            display: block;
                            height: 43px;
                            width: 100%;
                            font: {
                                size: 15px;
                                weight: 700;
                            };
                            &::placeholder {
                                color: var(--splash-black-color);
                            }
                            &::-webkit-outer-spin-button, &::-webkit-inner-spin-button {
                                -webkit-appearance: none;
                                margin: 0;
                            }
                        }
                        button {
                            color: var(--splash-black-color);
                            background-color: transparent;
                            transition: var(--transition);
                            font-weight: 900;
                            position: absolute;
                            font-size: 11px;
                            line-height: 1;
                            left: 20px;
                            padding: 0;
                            border: 0;
                            bottom: 0;
                            top: 0;

                            i {
                                position: relative;
                                top: 1px;
                            }
                            &:last-child {
                                left: auto;
                                right: 20px;
                            }
                            &:hover {
                                color: var(--splash-primary-color);
                            }
                        }
                    }
                }
                .fav-btn {
                    color: #8E8DA1;
                    padding-left: 24px;

                    i {
                        left: 0;
                        top: 50%;
                        line-height: 1;
                        font-size: 18px;
                        color: #EF2929;
                        position: absolute;
                        transform: translateY(-50%);
                    }
                    span {
                        &::before {
                            left: 0;
                            right: 0;
                            bottom: 0;
                            height: 1px;
                            content: '';
                            position: absolute;
                            background: #8E8DA1;
                        }
                    }
                }
            }
        }
        .product-details-tabs {
            .nav {
                &.nav-tabs {
                    background: var(--splash-white-color);
                    box-shadow: 0px 4px 34px rgba(101, 96, 240, 0.1);
                    margin: {
                        top: 50px;
                        bottom: 40px;
                    };
                    .nav-item {
                        margin-right: 15px;

                        .nav-link {
                            padding: 20px 30px;
                            margin-bottom: 0;
                            color: #8E8DA1;
                            font-size: 18px;

                            &::before {
                                left: 0;
                                width: 0;
                                bottom: 0;
                                content: '';
                                height: 3px;
                                position: absolute;
                                transition: var(--transition);
                                background: var(--splash-primary-color);
                            }
                            &:hover, &.active {
                                color: var(--splash-black-color);

                                &::before {
                                    width: 100%;
                                }
                            }
                        }
                    }
                }
            }
            .tab-content {
                .content {
                    padding-right: 30px;

                    p {
                        margin-bottom: 20px;
    
                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                    ul {
                        margin: {
                            top: 25px;
                            bottom: 25px;
                        };
                        li {
                            margin-bottom: 13px;
                            padding-left: 18px;
    
                            &::before {
                                left: 0;
                                top: 50%;
                                width: 10px;
                                content: '';
                                height: 10px;
                                border-radius: 50%;
                                position: absolute;
                                transform: translateY(-50%);
                                background: var(--splash-primary-color);
                            }
                            &:last-child {
                                margin-bottom: 0;
                            }
                        }
                    }
                }
                .card {
                    margin: {
                        left: auto;
                        right: auto;
                    };
                }
                .product-reviews {
                    .reviews {
                        .rating {
                            color: #F3C44C;
                        }
                    }
                }
                .rating-count {
                    .progress {
                        height: 7px;
                    }
                }
                .reviews-list {
                    margin: {
                        top: 25px;
                        bottom: -25px;
                    };
                    .rating {
                        line-height: 1;
                        color: #F3C44C;
                    }
                }
            }
        }
    }
}

// Dark Mode
.dark {
    .product-details-box {
        .card-body {
            .product-details-content {
                .box {
                    border-color: #45445e;
    
                    p {
                        a {
                            &::before {
                                background: var(--splash-white-color);
                            }
                        }
                    }
                }
                .buttons-list {
                    .input-counter {
                        .number-counter {
                            input {
                                color: var(--splash-white-color);
                                background: var(--splash-black-color);
                                
                                &::placeholder {
                                    color: var(--splash-white-color);
                                }
                            }
                            button {
                                color: var(--splash-white-color);
    
                                &:hover {
                                    color: var(--splash-primary-color);
                                }
                            }
                        }
                    }
                    .fav-btn {
                        color: #BCBBC7;
    
                        span {
                            &::before {
                                background: #45445e;
                            }
                        }
                    }
                }
            }
            .product-details-tabs {
                .nav {
                    &.nav-tabs {
                        background: var(--splash-black-color);
                        box-shadow: unset;
                        
                        .nav-item {
                            .nav-link {
                                color: #BCBBC7;
    
                                &:hover, &.active {
                                    color: var(--splash-white-color);
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width : 767px) {

    .product-details-box {
        .card-body {
            padding: 15px;
    
            .productDetailsSwiper {
                height: auto;
            }
            .productDetailsThumbSwiper {
                height: auto;
                padding-right: 0;
                margin-top: 15px;
            }
            .product-details-content {
                margin: {
                    left: 0;
                    top: 15px;
                };
                h3 {
                    margin-bottom: 10px;
                    font-size: 19px;

                    br {
                        display: none;
                    }
                }
                .info {
                    margin: {
                        top: 12px;
                        bottom: 12px;
                    };
                    li {
                        span {
                            width: 80px;
                        }
                    }
                }
                .box {
                    padding: 12px;
                }
                .buttons-list {
                    margin-top: 12px;
    
                    .input-counter {
                        .number-counter {
                            width: 120px;
    
                            input {
                                height: 41px;
                            }
                            button {
                                left: 15px;
                                
                                &:last-child {
                                    right: 15px;
                                }
                            }
                        }   
                    }
                }
            }
            .product-details-tabs {
                .nav {
                    &.nav-tabs {
                        margin: {
                            top: 20px;
                            bottom: 25px;
                        };
                        .nav-item {
                            margin-right: 5px;
    
                            .nav-link {
                                padding: 12px 20px;
                                font-size: 15px;
                            }
                        }
                    }
                }
                .tab-content {
                    .content {
                        padding-right: 0;
    
                        p {
                            margin-bottom: 15px;
                        }
                        ul {
                            margin: {
                                top: 15px;
                                bottom: 15px;
                            };
                            li {
                                margin-bottom: 12px;
                            }
                        }
                    }
                    .card {
                        margin-top: 20px;
                    }
                }
            }
        }
    }

}

@media only screen and (min-width : 576px) and (max-width : 767px) {

    .product-details-box {
        .card-body {
            padding: 20px;
        }
    }

}

@media only screen and (min-width : 768px) and (max-width : 991px) {

    .product-details-box {
        .card-body {
            padding: 25px;
    
            .productDetailsSwiper {
                height: 500px;
            }
            .productDetailsThumbSwiper {
                height: 500px;
                padding-right: 0;
            }
            .product-details-content {
                margin: {
                    left: 0;
                    top: 20px;
                };
                h3 {
                    margin-bottom: 10px;
                    font-size: 21px;

                    br {
                        display: none;
                    }
                }
                .info {
                    margin: {
                        top: 15px;
                        bottom: 15px;
                    };
                }
                .buttons-list {
                    margin-top: 15px;
                }
            }
            .product-details-tabs {
                .nav {
                    &.nav-tabs {
                        margin: {
                            top: 30px;
                            bottom: 30px;
                        };
                        .nav-item {
                            margin-right: 10px;
    
                            .nav-link {
                                padding: 15px 30px;
                                font-size: 16px;
                            }
                        }
                    }
                }
                .tab-content {
                    .content {
                        padding-right: 0;
    
                        p {
                            margin-bottom: 15px;
                        }
                        ul {
                            margin: {
                                top: 15px;
                                bottom: 15px;
                            };
                            li {
                                margin-bottom: 12px;
                            }
                        }
                    }
                    .card {
                        margin-top: 25px;
                    }
                }
            }
        }
    }

}

@media only screen and (min-width : 992px) and (max-width : 1199px) {

    .product-details-box {
        .card-body {
            .productDetailsThumbSwiper {
                padding-right: 40px;
            }
            .product-details-content {
                margin-left: -40px;
    
                h3 {
                    font-size: 21px;

                    br {
                        display: none;
                    }
                }
                .info {
                    margin: {
                        top: 15px;
                        bottom: 15px;
                    };
                }
                .box {
                    padding: 13px;
                }
                .buttons-list {
                    margin-top: 15px;
    
                    .input-counter {
                        .number-counter {
                            width: 100px;
    
                            button {
                                left: 12px;
    
                                &:last-child {
                                    right: 12px;
                                }
                            }
                        }   
                    }
                    .fav-btn {
                        padding-left: 20px;
    
                        i {
                            font-size: 16px;
                        }
                    }
                }
            }
            .product-details-tabs {
                .nav {
                    &.nav-tabs {
                        margin: {
                            top: 40px;
                            bottom: 30px;
                        };
                        .nav-item {
                            .nav-link {
                                padding: 15px 30px;
                                font-size: 17px;
                            }
                        }
                    }
                }
                .tab-content {
                    .content {
                        padding-right: 0;
    
                        p {
                            margin-bottom: 15px;
                        }
                        ul {
                            margin: {
                                top: 20px;
                                bottom: 20px;
                            };
                            li {
                                margin-bottom: 12px;
                            }
                        }
                    }
                }
            }
        }
    }

}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {

    .product-details-box {
        .card-body {
            .productDetailsThumbSwiper {
                padding-right: 35px;
            }
            .product-details-content {
                margin-left: -40px;
    
                h3 {
                    font-size: 22px;
                    margin-bottom: 10px;

                    br {
                        display: none;
                    }
                }
                .info {
                    margin: {
                        top: 15px;
                        bottom: 15px;
                    };
                }
                .box {
                    padding: 13px;
                }
                .buttons-list {
                    margin-top: 15px;
    
                    .input-counter {
                        .number-counter {
                            width: 100px;
    
                            button {
                                left: 15px;
                                
                                &:last-child {
                                    left: auto;
                                    right: 15px;
                                }
                            }
                        }   
                    }
                    .fav-btn {
                        padding-left: 20px;
    
                        i {
                            font-size: 15px;
                        }
                    }
                }
            }
            .product-details-tabs {
                .nav {
                    &.nav-tabs {
                        margin: {
                            top: 40px;
                            bottom: 35px;
                        };
                        .nav-item {
                            .nav-link {
                                padding: 18px 30px;
                            }
                        }
                    }
                }
                .tab-content {
                    .content {
                        padding-right: 0;
    
                        p {
                            margin-bottom: 15px;
                        }
                        ul {
                            margin: {
                                top: 20px;
                                bottom: 20px;
                            };
                        }
                    }
                }
            }
        }
    }

}

@media only screen and (min-width: 1600px) {

    .product-details-box {
        .card-body {
            padding: 50px;
    
            .productDetailsSwiper {
                height: 725px;
            }
            .productDetailsThumbSwiper {
                height: 725px;
                padding-right: 90px;
            }
            .product-details-content {
                margin-left: -10px;
    
                .info {
                    margin: {
                        top: 25px;
                        bottom: 25px;
                    };
                }
                .box {
                    padding: 18px;
                    margin-bottom: 18px;
                }
                .buttons-list {
                    margin-top: 25px;
                }
            }
            .product-details-tabs {
                .tab-content {
                    padding-left: 30px;
    
                    .content {
                        padding-right: 100px;
                    }
                    .card {
                        max-width: 370px;
                    }
                }
            }
        }
    }

}