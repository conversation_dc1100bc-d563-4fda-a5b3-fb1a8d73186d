import serviceAxios from '@/utils/serviceAxios'

// 系统配置接口
export interface SystemConfig {
  configId?: number
  configName: string
  configKey: string
  configValue: string
  configType: string // Y系统内置 N非系统内置
  createTime?: string
  updateTime?: string
  remark?: string
}

// 系统设置数据接口
export interface SystemSettings {
  systemName: string
  version: string
  sessionTimeout: number
  minPasswordLength: number
  enableRegistration: boolean
  enableEmailNotification: boolean
  enableCaptcha?: boolean
  maxLoginAttempts?: number
  lockoutDuration?: number
  enableAuditLog?: boolean
  dataRetentionDays?: number
  backupFrequency?: string
  maintenanceMode?: boolean
  systemLogo?: string
  systemDescription?: string
  contactEmail?: string
  contactPhone?: string
}

// 系统监控数据接口
export interface SystemMonitor {
  cpuUsage: number
  memoryUsage: number
  diskUsage: number
  networkIn: number
  networkOut: number
  onlineUsers: number
  totalRequests: number
  errorRate: number
  responseTime: number
  uptime: string
  lastUpdateTime: string
}

/**
 * ==================== 系统配置 API ====================
 */

/**
 * 获取系统配置列表
 */
export function getSystemConfigList(params?: {
  pageNum?: number
  pageSize?: number
  configName?: string
  configKey?: string
  configType?: string
}): Promise<{
  total: number
  rows: SystemConfig[]
}> {
  return serviceAxios.get('/system/config/list', { params })
}

/**
 * 获取系统配置详细信息
 */
export function getSystemConfig(configId: number): Promise<SystemConfig> {
  return serviceAxios.get(`/system/config/${configId}`)
}

/**
 * 根据参数键名查询参数值
 */
export function getConfigByKey(configKey: string): Promise<string> {
  return serviceAxios.get(`/system/config/configKey/${configKey}`)
}

/**
 * 新增系统配置
 */
export function createSystemConfig(data: SystemConfig): Promise<void> {
  return serviceAxios.post('/system/config', data)
}

/**
 * 修改系统配置
 */
export function updateSystemConfig(data: SystemConfig): Promise<void> {
  return serviceAxios.put('/system/config', data)
}

/**
 * 删除系统配置
 */
export function deleteSystemConfig(configId: number): Promise<void> {
  return serviceAxios.delete(`/system/config/${configId}`)
}

/**
 * 批量删除系统配置
 */
export function deleteSystemConfigs(configIds: number[]): Promise<void> {
  return serviceAxios.delete(`/system/config/${configIds.join(',')}`)
}

/**
 * 刷新系统配置缓存
 */
export function refreshConfigCache(): Promise<void> {
  return serviceAxios.delete('/system/config/refreshCache')
}

/**
 * ==================== 系统设置 API ====================
 */

/**
 * 获取系统设置
 */
export function getSystemSettings(): Promise<SystemSettings> {
  return serviceAxios.get('/system/settings')
}

/**
 * 更新系统设置
 */
export function updateSystemSettings(settings: Partial<SystemSettings>): Promise<void> {
  return serviceAxios.put('/system/settings', settings)
}

/**
 * 重置系统设置为默认值
 */
export function resetSystemSettings(): Promise<void> {
  return serviceAxios.post('/system/settings/reset')
}

/**
 * ==================== 系统监控 API ====================
 */

/**
 * 获取系统监控数据
 */
export function getSystemMonitor(): Promise<SystemMonitor> {
  return serviceAxios.get('/system/monitor')
}

/**
 * 获取系统运行状态
 */
export function getSystemStatus(): Promise<{
  status: 'running' | 'maintenance' | 'error'
  uptime: string
  version: string
  environment: string
}> {
  return serviceAxios.get('/system/status')
}

/**
 * 获取在线用户数量
 */
export function getOnlineUserCount(): Promise<number> {
  return serviceAxios.get('/system/online/count')
}

/**
 * 获取系统日志
 */
export function getSystemLogs(params?: {
  pageNum?: number
  pageSize?: number
  logLevel?: string
  startTime?: string
  endTime?: string
  keyword?: string
}): Promise<{
  total: number
  rows: Array<{
    logId: number
    logLevel: string
    logMessage: string
    logTime: string
    userId?: number
    userName?: string
    ipAddress?: string
    userAgent?: string
  }>
}> {
  return serviceAxios.get('/system/logs', { params })
}

/**
 * 清理系统日志
 */
export function clearSystemLogs(beforeDate?: string): Promise<void> {
  const params = beforeDate ? { beforeDate } : {}
  return serviceAxios.delete('/system/logs/clear', { params })
}

/**
 * ==================== 系统维护 API ====================
 */

/**
 * 进入维护模式
 */
export function enterMaintenanceMode(reason?: string): Promise<void> {
  return serviceAxios.post('/system/maintenance/enter', { reason })
}

/**
 * 退出维护模式
 */
export function exitMaintenanceMode(): Promise<void> {
  return serviceAxios.post('/system/maintenance/exit')
}

/**
 * 系统备份
 */
export function createSystemBackup(): Promise<{
  backupId: string
  backupPath: string
  backupSize: number
  createTime: string
}> {
  return serviceAxios.post('/system/backup/create')
}

/**
 * 获取备份列表
 */
export function getBackupList(): Promise<Array<{
  backupId: string
  backupName: string
  backupSize: number
  createTime: string
  status: string
}>> {
  return serviceAxios.get('/system/backup/list')
}

/**
 * 恢复系统备份
 */
export function restoreSystemBackup(backupId: string): Promise<void> {
  return serviceAxios.post(`/system/backup/restore/${backupId}`)
}

/**
 * 删除系统备份
 */
export function deleteSystemBackup(backupId: string): Promise<void> {
  return serviceAxios.delete(`/system/backup/${backupId}`)
}

/**
 * ==================== 系统动态 API ====================
 */

// 系统动态接口
export interface SystemActivity {
  activityId: number
  activityType: 'order' | 'device' | 'system' | 'store' | 'user'
  title: string
  description?: string
  relatedId?: number
  relatedType?: string
  userId?: number
  userName?: string
  createTime: string
  icon?: string
  color?: string
}

/**
 * 获取系统动态列表
 */
export function getSystemActivities(params?: {
  pageNum?: number
  pageSize?: number
  activityType?: string
  startTime?: string
  endTime?: string
}): Promise<{
  total: number
  rows: SystemActivity[]
}> {
  return serviceAxios.get('/system/activities', { params })
}

/**
 * 获取最新系统动态
 */
export function getRecentActivities(limit: number = 10): Promise<SystemActivity[]> {
  return serviceAxios.get('/system/activities/recent', {
    params: { limit }
  })
}

/**
 * 创建系统动态记录
 */
export function createSystemActivity(data: {
  activityType: string
  title: string
  description?: string
  relatedId?: number
  relatedType?: string
}): Promise<void> {
  return serviceAxios.post('/system/activities', data)
}

/**
 * 删除系统动态记录
 */
export function deleteSystemActivity(activityId: number): Promise<void> {
  return serviceAxios.delete(`/system/activities/${activityId}`)
}

/**
 * 批量删除系统动态记录
 */
export function deleteSystemActivities(activityIds: number[]): Promise<void> {
  return serviceAxios.delete(`/system/activities/${activityIds.join(',')}`)
}

/**
 * 清理过期的系统动态记录
 */
export function cleanExpiredActivities(beforeDate: string): Promise<void> {
  return serviceAxios.delete('/system/activities/clean', {
    params: { beforeDate }
  })
}

/**
 * ==================== 营收统计 API ====================
 */

// 营收统计数据接口
export interface RevenueStats {
  totalRevenue: number
  totalOrders: number
  averageOrderValue: number
  revenueGrowth: number
  orderGrowth: number
  topStores: Array<{
    storeId: number
    storeName: string
    revenue: number
    orders: number
  }>
  topProducts: Array<{
    productId: number
    productName: string
    revenue: number
    quantity: number
  }>
  hourlyRevenue: Array<{
    hour: number
    revenue: number
    orders: number
  }>
  dailyRevenue: Array<{
    date: string
    revenue: number
    orders: number
  }>
}

// 营收查询参数
export interface RevenueQueryParams {
  startDate?: string
  endDate?: string
  storeId?: number
  groupBy?: 'hour' | 'day' | 'week' | 'month'
  includeDetails?: boolean
}

/**
 * 获取营收统计数据
 */
export function getRevenueStats(params: RevenueQueryParams): Promise<RevenueStats> {
  return serviceAxios.get('/system/revenue/stats', { params })
}

/**
 * 获取总营收（快速查询）
 */
export function getTotalRevenue(params: {
  date?: string
  storeId?: number
}): Promise<{
  totalRevenue: number
  totalOrders: number
  averageOrderValue: number
}> {
  return serviceAxios.get('/system/revenue/total', { params })
}

/**
 * 获取营收趋势数据
 */
export function getRevenueTrend(params: {
  startDate: string
  endDate: string
  storeId?: number
  groupBy: 'hour' | 'day' | 'week' | 'month'
}): Promise<Array<{
  period: string
  revenue: number
  orders: number
  growth: number
}>> {
  return serviceAxios.get('/system/revenue/trend', { params })
}

/**
 * 获取门店营收排行
 */
export function getStoreRevenueRanking(params: {
  date?: string
  limit?: number
}): Promise<Array<{
  storeId: number
  storeName: string
  revenue: number
  orders: number
  rank: number
}>> {
  return serviceAxios.get('/system/revenue/store-ranking', { params })
}

/**
 * 获取商品营收排行
 */
export function getProductRevenueRanking(params: {
  date?: string
  storeId?: number
  limit?: number
}): Promise<Array<{
  productId: number
  productName: string
  revenue: number
  quantity: number
  rank: number
}>> {
  return serviceAxios.get('/system/revenue/product-ranking', { params })
}

/**
 * 获取营收对比数据
 */
export function getRevenueComparison(params: {
  currentDate: string
  compareDate: string
  storeId?: number
}): Promise<{
  current: {
    revenue: number
    orders: number
  }
  compare: {
    revenue: number
    orders: number
  }
  growth: {
    revenueGrowth: number
    orderGrowth: number
  }
}> {
  return serviceAxios.get('/system/revenue/comparison', { params })
}
