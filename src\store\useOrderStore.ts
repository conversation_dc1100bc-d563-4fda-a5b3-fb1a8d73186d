import {defineStore} from 'pinia';
import {ref} from "vue";
import type {drinkOrderList} from "@/types/order"; // 假设 drinkOrderList 是一个订单对象的数组类型，例如 Order[]
import {getOrderListByStoreId} from "@/utils/api/order";

export const useOrderStore = defineStore('order', () => {
    // State: 用于存储订单列表的 ref
    const orders = ref<drinkOrderList>([]);

    /**
     * @description 从API获取订单列表并更新 state
     */
    async function fetchOrdersByStore(storeId: number) {
        try {
            const res = await getOrderListByStoreId(
                storeId,
                {
                    pageNum: 1,
                    pageSize: 10,
                });
            // 使用 '?? []' 来防止当 res.rows 为 null 或 undefined 时程序报错。
            orders.value = res.rows ?? [];
            console.log("订单数据已成功加载！");

        } catch (err) {
            console.error('订单数据获取失败', err);
            // 如果请求失败，清空数组以防止页面显示旧的、过时的数据。
            orders.value = [];
        }
    }
    /**
     * @description 从API获取订单列表并更新 state
     */
    async function fetchTodayOrder(storeId: number) {
        try {
            const res = await getOrderListByStoreId(
                storeId,
                {
                    pageNum: 1,
                    pageSize: 10,
                });
            // 使用 '?? []' 来防止当 res.rows 为 null 或 undefined 时程序报错。
            orders.value = res.rows ?? [];
            console.log("订单数据已成功加载！");

        } catch (err) {
            console.error('订单数据获取失败', err);
            // 如果请求失败，清空数组以防止页面显示旧的、过时的数据。
            orders.value = [];
        }
    }
    return {
        orders,      // 导出的订单列表 state
        fetchOrdersByStore, // 导出的获取订单列表的 action
        fetchTodayOrder,
    }
}, {
    persist: {
        key: 'order-data',
        storage: localStorage,
    }, // pinia-plugin-persistedstate 为当前store实现持久化
});
