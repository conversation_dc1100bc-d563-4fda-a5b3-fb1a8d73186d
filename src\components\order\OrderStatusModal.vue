<template>
  <div class="modal fade" id="orderStatusModal" tabindex="-1" aria-labelledby="orderStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="orderStatusModalLabel">修改订单状态</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div v-if="currentOrder">
            <!-- 当前订单信息 -->
            <div class="alert alert-light mb-4">
              <div class="row">
                <div class="col-md-6">
                  <strong>订单号:</strong> #{{ currentOrder.orderId }}
                </div>
                <div class="col-md-6">
                  <strong>流水号:</strong> {{ currentOrder.orderReqSeq }}
                </div>
              </div>
              <div class="row mt-2">
                <div class="col-md-6">
                  <strong>当前状态:</strong> 
                  <span :class="getOrderStatusClass(currentOrder.orderStatus)">
                    {{ getOrderStatusText(currentOrder.orderStatus) }}
                  </span>
                </div>
                <div class="col-md-6">
                  <strong>订单金额:</strong> ¥{{ currentOrder.totalAmount }}
                </div>
              </div>
            </div>

            <!-- 状态选择 -->
            <div class="mb-3">
              <label for="newStatus" class="form-label">选择新状态 <span class="text-danger">*</span></label>
              <select 
                id="newStatus" 
                v-model="newStatus" 
                class="form-select"
                :class="{ 'is-invalid': !newStatus && showValidation }"
              >
                <option value="">请选择状态</option>
                <option 
                  v-for="status in availableStatuses" 
                  :key="status.value" 
                  :value="status.value"
                  :disabled="!canTransitionTo(currentOrder.orderStatus, status.value)"
                >
                  {{ status.label }}
                  <span v-if="!canTransitionTo(currentOrder.orderStatus, status.value)"> (不可选)</span>
                </option>
              </select>
              <div v-if="!newStatus && showValidation" class="invalid-feedback">
                请选择要修改的状态
              </div>
            </div>

            <!-- 状态说明 -->
            <div v-if="newStatus" class="alert alert-info">
              <h6 class="alert-heading">状态说明</h6>
              <p class="mb-0">{{ getStatusDescription(newStatus) }}</p>
            </div>

            <!-- 备注 -->
            <div class="mb-3">
              <label for="statusRemark" class="form-label">状态修改备注</label>
              <textarea 
                id="statusRemark" 
                v-model="statusRemark" 
                class="form-control" 
                rows="3"
                placeholder="请输入状态修改的原因或备注（可选）"
              ></textarea>
            </div>

            <!-- 状态流转规则提示 -->
            <div class="alert alert-warning">
              <h6 class="alert-heading">
                <i class="flaticon-warning me-2"></i>状态流转规则
              </h6>
              <ul class="mb-0">
                <li>待支付 → 已支付、已取消</li>
                <li>已支付 → 制作中、已取消</li>
                <li>制作中 → 待取餐、已取消</li>
                <li>待取餐 → 已完成</li>
                <li>已完成、已取消状态不可修改</li>
              </ul>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button 
            type="button" 
            class="btn btn-primary" 
            @click="confirmStatusChange"
            :disabled="!newStatus || submitting"
          >
            <span v-if="submitting" class="spinner-border spinner-border-sm me-2" role="status"></span>
            确认修改
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { updateOrderStatus } from '@/utils/api/order'
import type { drinkOrder } from '@/types/order'

// Emits
const emit = defineEmits<{
  statusChanged: [orderId: string, newStatus: string]
}>()

// 数据状态
const currentOrder = ref<drinkOrder | null>(null)
const newStatus = ref('')
const statusRemark = ref('')
const submitting = ref(false)
const showValidation = ref(false)

// 可用状态列表
const availableStatuses = [
  { value: '0', label: '待支付' },
  { value: '1', label: '已支付' },
  { value: '2', label: '制作中' },
  { value: '3', label: '待取餐' },
  { value: '4', label: '已完成' },
  { value: '5', label: '已取消' }
]

// 状态流转规则
const statusTransitions: Record<string, string[]> = {
  '0': ['1', '5'], // 待支付 → 已支付、已取消
  '1': ['2', '5'], // 已支付 → 制作中、已取消
  '2': ['3', '5'], // 制作中 → 待取餐、已取消
  '3': ['4'],      // 待取餐 → 已完成
  '4': [],         // 已完成 → 无
  '5': []          // 已取消 → 无
}

// 显示状态修改弹窗
const showModal = (order: drinkOrder) => {
  currentOrder.value = order
  newStatus.value = ''
  statusRemark.value = ''
  showValidation.value = false
  console.log('显示状态修改弹窗:', order)
}

// 判断是否可以转换到目标状态
const canTransitionTo = (currentStatus: string, targetStatus: string) => {
  if (currentStatus === targetStatus) return false
  return statusTransitions[currentStatus]?.includes(targetStatus) || false
}

// 获取订单状态文本
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '0': '待支付',
    '1': '已支付',
    '2': '制作中',
    '3': '待取餐',
    '4': '已完成',
    '5': '已取消'
  }
  return statusMap[status] || '未知状态'
}

// 获取订单状态样式
const getOrderStatusClass = (status: string) => {
  const classMap: Record<string, string> = {
    '0': 'badge bg-warning',
    '1': 'badge bg-info',
    '2': 'badge bg-primary',
    '3': 'badge bg-success',
    '4': 'badge bg-secondary',
    '5': 'badge bg-danger'
  }
  return classMap[status] || 'badge bg-light'
}

// 获取状态描述
const getStatusDescription = (status: string) => {
  const descriptions: Record<string, string> = {
    '0': '订单已创建，等待用户完成支付',
    '1': '用户支付成功，订单将被推送到门店制作队列',
    '2': '门店已接单，正在制作饮品',
    '3': '所有饮品制作完成，系统将生成取餐码并通知用户',
    '4': '用户已成功取餐，订单流程结束',
    '5': '订单已取消，可能是用户主动取消或支付超时'
  }
  return descriptions[status] || ''
}

// 确认状态修改
const confirmStatusChange = async () => {
  if (!newStatus.value) {
    showValidation.value = true
    return
  }

  if (!currentOrder.value) return

  submitting.value = true
  try {
    console.log(`🔄 [状态修改] 修改订单${currentOrder.value.orderId}状态: ${currentOrder.value.orderStatus} → ${newStatus.value}`)
    
    await updateOrderStatus(parseInt(currentOrder.value.orderId), newStatus.value)
    
    // 如果有备注，更新订单备注
    if (statusRemark.value.trim()) {
      // TODO: 调用更新订单备注的API
      console.log('添加状态修改备注:', statusRemark.value)
    }
    
    console.log('✅ [状态修改] 订单状态修改成功')
    
    // 通知父组件状态已修改
    emit('statusChanged', currentOrder.value.orderId, newStatus.value)
    
    // 关闭弹窗
    const modal = bootstrap.Modal.getInstance(document.getElementById('orderStatusModal')!)
    modal?.hide()
    
  } catch (error) {
    console.error('❌ [状态修改] 订单状态修改失败:', error)
    alert('状态修改失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  showModal
})
</script>

<style scoped>
.alert-heading {
  margin-bottom: 0.5rem;
}

.badge {
  font-size: 0.75rem;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}
</style>
