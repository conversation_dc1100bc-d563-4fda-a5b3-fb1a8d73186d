.project-card {
    .card-body {
        .progress {
            height: 9px;
        }
        .info {
            li {
                margin-bottom: 10px;

                span {
                    width: 85px;
                }
                .users-list {
                    div {
                        width: 33px;
                        height: 33px;
                        margin-right: -10px;
                        border: 2px solid var(--splash-white-color);
                        filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));
            
                        &:last-child {
                            margin-right: 0;
                        }
                    }
                }
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
        .features-list {
            li {
                margin-bottom: 12px;
                padding-left: 18px;

                &::before {
                    left: 0;
                    top: 50%;
                    content: '';
                    width: 10px;
                    height: 10px;
                    position: absolute;
                    border-radius: 50%;
                    transform: translateY(-50%);
                    background: var(--splash-primary-color);
                }
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
}