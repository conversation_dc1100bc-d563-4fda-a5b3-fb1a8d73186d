<template>
  <BreadCrumb pageTitle="商品管理"/>
  
  <!-- 操作栏 -->
  <div class="d-flex justify-content-between align-items-center mb-25">
    <div class="d-flex align-items-center">
      <div class="refresh me-3">
        <button
            class="transition bg-transparent p-0 border-0"
            @click="refreshProductList"
            aria-label="刷新">
          <i class="flaticon-refresh"></i>
        </button>
      </div>
      
      <!-- 分类筛选 -->
      <div class="category-filter">
        <select class="form-select" v-model="selectedCategory" @change="handleCategoryChange">
          <option value="">全部分类</option>
          <option v-for="category in categories" :key="category.id" :value="category.id">
            {{ category.name }}
          </option>
        </select>
      </div>
    </div>
    
    <div class="d-flex align-items-center">
      <!-- 搜索框 -->
      <div class="search-box me-3">
        <div class="input-group" style="width: 300px;">
          <input 
            type="text" 
            class="form-control" 
            placeholder="搜索商品名称..."
            v-model="searchKeyword"
            @input="handleSearch"
          >
          <span class="input-group-text">
            <i class="flaticon-search"></i>
          </span>
        </div>
      </div>
      
      <!-- 新增商品按钮 -->
      <button class="btn btn-primary" @click="showCreateProductModal = true">
        <i class="flaticon-plus me-1"></i>
        新增商品
      </button>
    </div>
  </div>

  <!-- 统计卡片 -->
  <div class="stats-row mb-25">
    <div class="row">
      <div class="col-md-3">
        <div class="stats-card">
          <div class="stats-icon">
            <i class="flaticon-coffee-cup text-primary"></i>
          </div>
          <div class="stats-content">
            <h3>{{ totalProducts }}</h3>
            <p>总商品数</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card">
          <div class="stats-icon">
            <i class="flaticon-check text-success"></i>
          </div>
          <div class="stats-content">
            <h3>{{ activeProducts }}</h3>
            <p>在售商品</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card">
          <div class="stats-icon">
            <i class="flaticon-pause text-warning"></i>
          </div>
          <div class="stats-content">
            <h3>{{ inactiveProducts }}</h3>
            <p>下架商品</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card">
          <div class="stats-icon">
            <i class="flaticon-filter text-info"></i>
          </div>
          <div class="stats-content">
            <h3>{{ filteredProducts.length }}</h3>
            <p>当前显示</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 商品列表 -->
  <div class="product-management-container">
    <div class="card border-0 rounded-3 bg-white shadow-sm">
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table table-hover mb-0">
            <thead class="table-light">
              <tr>
                <th style="width: 80px;">图片</th>
                <th>商品名称</th>
                <th>分类</th>
                <th>价格</th>
                <th>状态</th>
                <th>销量</th>
                <th>库存</th>
                <th>创建时间</th>
                <th style="width: 120px;">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="product in filteredProducts" :key="product.id">
                <td>
                  <div class="product-image">
                    <img :src="product.image" :alt="product.name" class="rounded">
                  </div>
                </td>
                <td>
                  <div class="product-info">
                    <h6 class="mb-1">{{ product.name }}</h6>
                    <small class="text-muted">{{ product.description }}</small>
                  </div>
                </td>
                <td>
                  <span class="badge bg-light text-dark">{{ product.categoryName }}</span>
                </td>
                <td>
                  <span class="fw-bold text-primary">¥{{ product.price }}</span>
                </td>
                <td>
                  <span class="badge" :class="getStatusClass(product.status)">
                    {{ getStatusText(product.status) }}
                  </span>
                </td>
                <td>{{ product.salesCount }}</td>
                <td>
                  <span :class="product.stock < 10 ? 'text-danger fw-bold' : ''">
                    {{ product.stock }}
                  </span>
                </td>
                <td>{{ formatDate(product.createTime) }}</td>
                <td>
                  <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" @click="editProduct(product)" title="编辑">
                      <i class="flaticon-edit"></i>
                    </button>
                    <button 
                      class="btn" 
                      :class="product.status === 'active' ? 'btn-outline-warning' : 'btn-outline-success'"
                      @click="toggleProductStatus(product)"
                      :title="product.status === 'active' ? '下架' : '上架'"
                    >
                      <i :class="product.status === 'active' ? 'flaticon-pause' : 'flaticon-play'"></i>
                    </button>
                    <button class="btn btn-outline-danger" @click="deleteProduct(product)" title="删除">
                      <i class="flaticon-delete"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- 空状态 -->
        <div v-if="filteredProducts.length === 0" class="empty-state text-center py-5">
          <i class="flaticon-coffee-cup display-1 text-muted mb-3"></i>
          <h4 class="text-muted">暂无商品数据</h4>
          <p class="text-muted">请添加商品或调整筛选条件</p>
          <button class="btn btn-primary" @click="showCreateProductModal = true">
            <i class="flaticon-plus me-2"></i>
            添加第一个商品
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 新增/编辑商品模态框 -->
  <div class="modal fade" :class="{ show: showCreateProductModal }" :style="{ display: showCreateProductModal ? 'block' : 'none' }" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{{ editingProduct ? '编辑商品' : '新增商品' }}</h5>
          <button type="button" class="btn-close" @click="closeProductModal"></button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="saveProduct">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label class="form-label">商品名称 <span class="text-danger">*</span></label>
                <input type="text" class="form-control" v-model="productForm.name" required>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label">商品分类 <span class="text-danger">*</span></label>
                <select class="form-select" v-model="productForm.categoryId" required>
                  <option value="">请选择分类</option>
                  <option v-for="category in categories" :key="category.id" :value="category.id">
                    {{ category.name }}
                  </option>
                </select>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label">商品价格 <span class="text-danger">*</span></label>
                <div class="input-group">
                  <span class="input-group-text">¥</span>
                  <input type="number" class="form-control" v-model="productForm.price" step="0.01" min="0" required>
                </div>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label">库存数量</label>
                <input type="number" class="form-control" v-model="productForm.stock" min="0">
              </div>
              <div class="col-12 mb-3">
                <label class="form-label">商品描述</label>
                <textarea class="form-control" rows="3" v-model="productForm.description" placeholder="请输入商品描述"></textarea>
              </div>
              <div class="col-12 mb-3">
                <label class="form-label">商品图片URL</label>
                <input type="url" class="form-control" v-model="productForm.image" placeholder="请输入图片链接">
              </div>
              <div class="col-12 mb-3">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="productStatus" v-model="productForm.isActive">
                  <label class="form-check-label" for="productStatus">
                    立即上架销售
                  </label>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="closeProductModal">取消</button>
          <button type="button" class="btn btn-primary" @click="saveProduct">
            {{ editingProduct ? '更新' : '创建' }}
          </button>
        </div>
      </div>
    </div>
  </div>
  <div v-if="showCreateProductModal" class="modal-backdrop fade show"></div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue'
import BreadCrumb from '@/components/layouts/BreadCrumb.vue'
import {
  getProductList,
  createProduct,
  updateProduct,
  deleteProduct as apiDeleteProduct,
  type ProductQueryParams
} from '@/utils/api/product'
import type { drinkProduct } from '@/types/drink'

// 接口定义
interface Product {
  id: number
  name: string
  description: string
  categoryId: number
  categoryName: string
  price: number
  image: string
  status: 'active' | 'inactive'
  salesCount: number
  stock: number
  createTime: Date
}

interface Category {
  id: number
  name: string
  description: string
}

// 响应式数据
const products = ref<Product[]>([])
const categories = ref<Category[]>([
  { id: 1, name: '咖啡类', description: '各种咖啡饮品' },
  { id: 2, name: '茶饮类', description: '茶类饮品' },
  { id: 3, name: '果汁类', description: '鲜榨果汁' },
  { id: 4, name: '甜品类', description: '各种甜品' },
  { id: 5, name: '轻食类', description: '简餐轻食' }
])

const searchKeyword = ref('')
const selectedCategory = ref('')
const showCreateProductModal = ref(false)
const editingProduct = ref<Product | null>(null)
const isLoading = ref(false)
const error = ref('')

// 分页数据
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 表单数据
const productForm = reactive({
  name: '',
  description: '',
  categoryId: '',
  price: 0,
  image: '',
  stock: 100,
  isActive: true
})

// 计算属性
const filteredProducts = computed(() => {
  let filtered = products.value

  // 按分类筛选
  if (selectedCategory.value) {
    filtered = filtered.filter(product => product.categoryId === parseInt(selectedCategory.value))
  }

  // 按关键词搜索
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.toLowerCase().trim()
    filtered = filtered.filter(product => 
      product.name.toLowerCase().includes(keyword) ||
      product.description.toLowerCase().includes(keyword)
    )
  }

  return filtered
})

const totalProducts = computed(() => products.value.length)
const activeProducts = computed(() => products.value.filter(p => p.status === 'active').length)
const inactiveProducts = computed(() => products.value.filter(p => p.status === 'inactive').length)

// 方法
const refreshProductList = () => {
  console.log('🔄 [商品管理] 刷新商品列表')
  loadProductData()
}

const loadProductData = async () => {
  isLoading.value = true
  error.value = ''

  try {
    const params: ProductQueryParams = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      productName: searchKeyword.value || undefined
    }

    const response = await getProductList(params)

    // 转换API数据为UI数据格式
    products.value = (response.rows || []).map(apiProduct => ({
      id: apiProduct.drinkId,
      name: apiProduct.drinkPrototypeInfo?.prototypeName || `商品${apiProduct.drinkId}`,
      description: apiProduct.drinkPrototypeInfo?.prototypeDescription || '暂无描述',
      categoryId: apiProduct.drinkPrototypeInfo?.drinkGroupId || 1,
      categoryName: apiProduct.drinkPrototypeInfo?.drinkGroupInfo?.groupName || '未分类',
      price: parseFloat(apiProduct.productPrice || '0'),
      image: apiProduct.drinkPrototypeInfo?.prototypeImage || 'https://via.placeholder.com/100x100?text=商品图片',
      status: apiProduct.status === '1' ? 'active' : 'inactive',
      salesCount: Math.floor(Math.random() * 200) + 50, // 模拟销量数据，实际应该从统计API获取
      stock: Math.floor(Math.random() * 50) + 10, // 模拟库存数据，实际应该从库存API获取
      createTime: apiProduct.createTime ? new Date(apiProduct.createTime) : new Date()
    }))

    pagination.total = response.total || 0

    console.log(`✅ [商品管理] 商品数据加载完成: ${products.value.length}个商品`)

  } catch (err) {
    console.error('❌ [商品管理] 获取商品数据失败:', err)
    error.value = '获取商品数据失败'
    products.value = []
    pagination.total = 0
  } finally {
    isLoading.value = false
  }
}

const handleSearch = () => {
  console.log(`🔍 [商品管理] 搜索关键词: ${searchKeyword.value}`)
  pagination.pageNum = 1 // 重置到第一页
  loadProductData()
}

const handleCategoryChange = () => {
  console.log(`📂 [商品管理] 分类筛选: ${selectedCategory.value}`)
  pagination.pageNum = 1 // 重置到第一页
  loadProductData()
}

const getStatusClass = (status: string) => {
  return status === 'active' ? 'bg-success' : 'bg-secondary'
}

const getStatusText = (status: string) => {
  return status === 'active' ? '在售' : '下架'
}

const formatDate = (date: Date) => {
  return date.toLocaleDateString('zh-CN')
}

const editProduct = (product: Product) => {
  editingProduct.value = product
  Object.assign(productForm, {
    name: product.name,
    description: product.description,
    categoryId: product.categoryId.toString(),
    price: product.price,
    image: product.image,
    stock: product.stock,
    isActive: product.status === 'active'
  })
  showCreateProductModal.value = true
}

const toggleProductStatus = (product: Product) => {
  const newStatus = product.status === 'active' ? 'inactive' : 'active'
  const action = newStatus === 'active' ? '上架' : '下架'
  
  if (confirm(`确定要${action}商品 "${product.name}" 吗？`)) {
    product.status = newStatus
    console.log(`✅ [商品管理] 商品${action}成功: ${product.name}`)
  }
}

const deleteProduct = (product: Product) => {
  if (confirm(`确定要删除商品 "${product.name}" 吗？此操作不可恢复。`)) {
    const index = products.value.findIndex(p => p.id === product.id)
    if (index > -1) {
      products.value.splice(index, 1)
      console.log(`✅ [商品管理] 商品删除成功: ${product.name}`)
    }
  }
}

const saveProduct = () => {
  if (!productForm.name || !productForm.categoryId || !productForm.price) {
    alert('请填写必填信息')
    return
  }

  const categoryName = categories.value.find(c => c.id === parseInt(productForm.categoryId))?.name || ''

  if (editingProduct.value) {
    // 编辑模式
    Object.assign(editingProduct.value, {
      name: productForm.name,
      description: productForm.description,
      categoryId: parseInt(productForm.categoryId),
      categoryName: categoryName,
      price: productForm.price,
      image: productForm.image,
      stock: productForm.stock,
      status: productForm.isActive ? 'active' : 'inactive'
    })
    console.log(`✅ [商品管理] 商品更新成功: ${productForm.name}`)
  } else {
    // 新增模式
    const newProduct: Product = {
      id: Date.now(),
      name: productForm.name,
      description: productForm.description,
      categoryId: parseInt(productForm.categoryId),
      categoryName: categoryName,
      price: productForm.price,
      image: productForm.image || 'https://via.placeholder.com/100x100?text=商品图片',
      status: productForm.isActive ? 'active' : 'inactive',
      salesCount: 0,
      stock: productForm.stock,
      createTime: new Date()
    }
    products.value.unshift(newProduct)
    console.log(`✅ [商品管理] 商品创建成功: ${productForm.name}`)
  }

  closeProductModal()
}

const closeProductModal = () => {
  showCreateProductModal.value = false
  editingProduct.value = null
  
  // 重置表单
  Object.assign(productForm, {
    name: '',
    description: '',
    categoryId: '',
    price: 0,
    image: '',
    stock: 100,
    isActive: true
  })
}

// 生命周期
onMounted(() => {
  loadProductData()
})
</script>

<style scoped>
.product-management-container {
  min-height: 400px;
}

.stats-row .stats-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  transition: transform 0.2s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
}

.stats-icon {
  font-size: 2.5rem;
  margin-right: 15px;
}

.stats-content h3 {
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
  color: var(--bs-dark);
}

.stats-content p {
  margin: 0;
  color: var(--bs-secondary);
  font-size: 0.9rem;
}

.product-image img {
  width: 60px;
  height: 60px;
  object-fit: cover;
}

.product-info h6 {
  color: var(--bs-dark);
  font-size: 0.95rem;
}

.product-info small {
  font-size: 0.8rem;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.table th {
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
  font-size: 0.9rem;
}

.table td {
  vertical-align: middle;
  font-size: 0.9rem;
}

.btn-group-sm .btn {
  padding: 4px 8px;
  font-size: 0.8rem;
}

.search-box .input-group-text {
  background-color: #f8f9fa;
  border-left: none;
}

.search-box .form-control {
  border-right: none;
}

.search-box .form-control:focus {
  box-shadow: none;
  border-color: #ced4da;
}

.category-filter .form-select {
  width: 150px;
  font-size: 0.9rem;
}

.refresh button {
  font-size: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  border-radius: 8px;
  box-sizing: border-box;
}

.refresh button:hover {
  color: var(--splash-primary-color);
}

.refresh button:active {
  transform: scale(0.85);
}

.empty-state {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 60px 20px;
  margin: 40px 0;
}

.modal.show {
  background-color: rgba(0, 0, 0, 0.5);
}
</style>
