<template>
  <div class="store-menu-management p-20">
    <!-- 门店选择区域 -->
    <div class="store-selector mb-20">
      <div class="d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center gap-3">
          <label class="form-label mb-0 fw-bold">选择门店:</label>
          <select v-model="selectedStoreId" class="form-select" style="width: 300px;" @change="handleStoreChange">
            <option value="">请选择门店</option>
            <option v-for="store in storeList" :key="store.storeId" :value="store.storeId">
              {{ store.storeName }}
            </option>
          </select>
          <button v-if="selectedStoreId" @click="refreshStoreMenu" class="btn btn-outline-secondary">
            刷新菜单
          </button>
        </div>
        <div class="action-buttons" v-if="selectedStoreId">
          <button @click="showAddProductModal" class="btn btn-primary">
            添加商品
          </button>
        </div>
      </div>
    </div>

    <!-- 门店信息卡片 -->
    <div v-if="selectedStore" class="store-info-card mb-20">
      <div class="card border-0 bg-light">
        <div class="card-body p-15">
          <div class="row align-items-center">
            <div class="col-md-8">
              <h6 class="mb-1">{{ selectedStore.storeName }}</h6>
              <p class="text-muted mb-0 small">
                <i class="flaticon-location me-1"></i>{{ selectedStore.storeAddress || '地址未设置' }}
                <span class="ms-3"><i class="flaticon-phone me-1"></i>{{ selectedStore.storePhone || '电话未设置' }}</span>
              </p>
            </div>
            <div class="col-md-4 text-end">
              <span :class="selectedStore.status === '1' ? 'badge bg-success' : 'badge bg-danger'">
                {{ selectedStore.status === '1' ? '营业中' : '暂停营业' }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 菜单内容 -->
    <div v-if="selectedStoreId">
      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3 text-muted">正在加载门店菜单...</p>
      </div>

      <!-- 菜单数据 -->
      <div v-else-if="menuData.length > 0">
        <div class="menu-categories">
          <div v-for="category in menuData" :key="category?.groupId || 0" class="category-section mb-30">
            <!-- 分类标题 -->
            <div class="category-header d-flex justify-content-between align-items-center mb-15">
              <h5 class="mb-0">
                <i class="flaticon-category me-2 text-primary"></i>
                {{ category?.groupName || '未知分类' }}
                <span class="badge bg-secondary ms-2">{{ category?.prototypes?.length || 0 }} 个原型</span>
              </h5>
            </div>

            <!-- 原型列表 -->
            <div v-if="category?.prototypes && category.prototypes.length > 0" class="prototypes-grid">
              <div v-for="prototype in category.prototypes" :key="prototype?.prototypeId || 0" class="prototype-card">
                <div class="card border-0 shadow-sm h-100">
                  <div class="card-body p-15">
                    <div class="d-flex align-items-start">
                      <img 
                        v-if="prototype.prototypeImage" 
                        :src="prototype.prototypeImage" 
                        alt="原型图片" 
                        class="prototype-image me-3"
                      />
                      <div class="flex-grow-1">
                        <h6 class="mb-1">{{ prototype?.prototypeName || '未知原型' }}</h6>
                        <p class="text-muted small mb-2">{{ prototype?.prototypeDescription || '暂无描述' }}</p>
                        
                        <!-- 商品列表 -->
                        <div v-if="prototype?.products && prototype.products.length > 0" class="products-list">
                          <div class="small text-muted mb-1">商品 ({{ prototype.products.length }}个):</div>
                          <div v-for="product in prototype?.products || []" :key="product?.drinkId || 0" class="product-item d-flex justify-content-between align-items-center mb-1">
                            <div class="d-flex align-items-center">
                              <span :class="product?.status === '1' ? 'badge bg-success' : 'badge bg-danger'" style="font-size: 0.7rem;">
                                {{ product?.status === '1' ? '上架' : '下架' }}
                              </span>
                              <span class="ms-2 small">¥{{ product?.productPrice || '0.00' }}</span>
                              <span v-if="product?.targetId" class="ms-2 text-muted" style="font-size: 0.7rem;">
                                工作流: {{ product.targetId }}
                              </span>

                            </div>
                            <div class="btn-group btn-group-sm">
                              <button @click="product && toggleProductStatus(product)" class="btn btn-outline-warning btn-sm" :title="product?.status === '1' ? '下架商品' : '上架商品'">
                                {{ product?.status === '1' ? '下架' : '上架' }}
                              </button>
                              <button @click="product && editProduct(product)" class="btn btn-outline-primary btn-sm">
                                编辑
                              </button>
                              <button @click="product && deleteProductItem(product)" class="btn btn-outline-danger btn-sm">
                                删除
                              </button>
                            </div>
                          </div>
                        </div>
                        <div v-else class="text-muted small">
                          暂无商品
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="text-muted text-center py-3">
              该分类下暂无原型
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state text-center py-5">
        <i class="flaticon-menu text-muted" style="font-size: 4rem;"></i>
        <h5 class="text-muted mt-3">该门店暂无菜单数据</h5>
        <p class="text-muted">您可以通过手动添加商品来创建菜单</p>
        <button @click="showAddProductModal" class="btn btn-primary">
          添加商品
        </button>
      </div>
    </div>

    <!-- 未选择门店状态 -->
    <div v-else class="no-store-selected text-center py-5">
      <i class="flaticon-store text-muted" style="font-size: 4rem;"></i>
      <h5 class="text-muted mt-3">请选择门店</h5>
      <p class="text-muted">选择门店后可以查看和管理该门店的菜单</p>
    </div>


    <!-- 添加商品模态框 -->
    <div class="modal fade" id="addProductModal" tabindex="-1" ref="addProductModalRef">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">添加商品到门店</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="addProductToStore">
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label class="form-label">选择原型 <span class="text-danger">*</span></label>
                  <select v-model="addProductForm.drinkPrototype" class="form-select" required>
                    <option value="">请选择饮品原型</option>
                    <option v-for="prototype in allPrototypes" :key="prototype?.prototypeId" :value="prototype?.prototypeId">
                      {{ prototype?.prototypeName || '未知原型' }}
                    </option>
                  </select>
                </div>
                <div class="col-md-6 mb-3">
                  <label class="form-label">销售价格 <span class="text-danger">*</span></label>
                  <div class="input-group">
                    <span class="input-group-text">¥</span>
                    <input 
                      v-model="addProductForm.productPrice" 
                      type="number" 
                      step="0.01"
                      min="0"
                      class="form-control" 
                      placeholder="请输入销售价格"
                      required
                    />
                  </div>
                </div>
                <div class="col-12 mb-3">
                  <label class="form-label">IoT工作流ID <span class="text-danger">*</span></label>
                  <input 
                    v-model="addProductForm.targetId" 
                    type="text" 
                    class="form-control" 
                    placeholder="请输入IoT设备工作流ID"
                    required
                  />
                </div>
                <div class="col-12 mb-3">
                  <label class="form-label">自定义选项配置</label>
                  <ProductOptionsEditor ref="addProductOptionsRef" v-model="addProductForm.options" />
                </div>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" @click="addProductToStore" class="btn btn-primary" :disabled="addingProduct">
              <span v-if="addingProduct" class="spinner-border spinner-border-sm me-2"></span>
              添加商品
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑商品模态框 -->
    <div class="modal fade" id="editProductModal" tabindex="-1" ref="editProductModalRef">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">编辑商品</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="updateProduct">
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label class="form-label">原型名称</label>
                  <input
                    :value="editProductForm.prototypeName"
                    type="text"
                    class="form-control"
                    readonly
                    disabled
                  />
                  <small class="text-muted">原型不可修改</small>
                </div>
                <div class="col-md-6 mb-3">
                  <label class="form-label">销售价格 <span class="text-danger">*</span></label>
                  <div class="input-group">
                    <span class="input-group-text">¥</span>
                    <input
                      v-model="editProductForm.productPrice"
                      type="number"
                      step="0.01"
                      min="0"
                      class="form-control"
                      placeholder="请输入销售价格"
                      required
                    />
                  </div>
                </div>
                <div class="col-12 mb-3">
                  <label class="form-label">IoT工作流ID <span class="text-danger">*</span></label>
                  <input
                    v-model="editProductForm.targetId"
                    type="text"
                    class="form-control"
                    placeholder="请输入IoT设备工作流ID"
                    required
                  />
                </div>
                <div class="col-12 mb-3">
                  <label class="form-label">商品状态</label>
                  <div class="form-check form-switch">
                    <input
                      v-model="editProductForm.isActive"
                      type="checkbox"
                      class="form-check-input"
                      id="editProductStatus"
                    >
                    <label class="form-check-label" for="editProductStatus">
                      {{ editProductForm.isActive ? '上架销售' : '下架停售' }}
                    </label>
                  </div>
                </div>
                <div class="col-12 mb-3">
                  <label class="form-label">自定义选项配置</label>
                  <ProductOptionsEditor ref="editProductOptionsRef" v-model="editProductForm.options" />
                </div>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" @click="updateProduct" class="btn btn-primary" :disabled="updatingProduct">
              <span v-if="updatingProduct" class="spinner-border spinner-border-sm me-2"></span>
              保存修改
            </button>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from 'vue'
import { Modal } from 'bootstrap'
import { getStoreList } from '@/utils/api/store'
import {
  getDrinkPrototypeList,
  type PrototypeQueryParams
} from '@/utils/api/drink'
import {
  getStoreMenu,
  createProduct,
  deleteProduct,
  changeProductStatus
} from '@/utils/api/product'
import { storeDetail, drinkPrototype, drinkProduct } from '@/types'
import ProductOptionsEditor from './ProductOptionsEditor.vue'

// 数据状态
const loading = ref(false)
const addingProduct = ref(false)
const updatingProduct = ref(false)
const selectedStoreId = ref('')
const storeList = ref<storeDetail[]>([])
const menuData = ref<Array<{
  groupId: number
  groupName: string
  prototypes?: Array<{
    prototypeId: number
    prototypeName: string
    prototypeImage?: string
    prototypeDescription?: string
    products?: drinkProduct[]
  }>
}>>([])
const allPrototypes = ref<drinkPrototype[]>([])

// 模态框引用
const addProductModalRef = ref<HTMLElement>()
const editProductModalRef = ref<HTMLElement>()
let addProductModal: Modal | null = null
let editProductModal: Modal | null = null

// ProductOptionsEditor 组件引用
const addProductOptionsRef = ref()
const editProductOptionsRef = ref()

// 添加商品表单
const addProductForm = ref({
  drinkPrototype: '',
  productPrice: '',
  targetId: '',
  options: ''
})

// 编辑商品表单
const editProductForm = ref({
  drinkId: null as number | null,
  prototypeName: '',
  productPrice: '',
  targetId: '',
  options: '',
  isActive: true
})

// 计算属性：当前选择的门店
const selectedStore = computed(() => {
  return storeList.value.find((store: storeDetail) => store.storeId == selectedStoreId.value)
})

// 获取门店列表
const fetchStoreList = async () => {
  try {
    const response = await getStoreList({ pageNum: 1, pageSize: 1000 })
    storeList.value = response.rows || []
  } catch (error) {
    console.error('获取门店列表失败:', error)
    storeList.value = []
  }
}

// 获取所有原型
const fetchAllPrototypes = async () => {
  try {
    const params: PrototypeQueryParams = {
      pageNum: 1,
      pageSize: 1000,
      status: '1'
    }
    const response = await getDrinkPrototypeList(params)
    allPrototypes.value = response.rows || []
  } catch (error) {
    console.error('获取原型列表失败:', error)
    allPrototypes.value = []
  }
}

// 获取门店菜单数据
const fetchStoreMenu = async () => {
  if (!selectedStoreId.value) return

  loading.value = true
  try {
    console.log(`🔄 [门店菜单] 开始获取门店 ${selectedStoreId.value} 的菜单数据...`)

    const response = await getStoreMenu(Number(selectedStoreId.value), { status: '1' })
    const products = response.rows || []

    console.log(`✅ [门店菜单] 获取到 ${products.length} 个商品`)
    console.log('🍹 [门店菜单] 商品数据:', products)

    // 按分类和原型分组商品数据
    menuData.value = await groupProductsByCategory(products)

    console.log(`📊 [门店菜单] 分组后的菜单数据:`, menuData.value)
  } catch (error) {
    console.error('❌ [门店菜单] 获取门店菜单失败:', error)
    menuData.value = []
  } finally {
    loading.value = false
  }
}

// 辅助函数：按分类分组商品
const groupProductsByCategory = async (products: drinkProduct[]) => {
  if (!products || products.length === 0) {
    console.log('📝 [门店菜单] 没有商品数据需要分组')
    return []
  }

  // 获取所有原型信息，用于分组
  await fetchAllPrototypes()

  // 获取分类信息
  const { getEnabledDrinkGroups } = await import('@/utils/api/drink')
  const groupsResponse = await getEnabledDrinkGroups()
  const categories = groupsResponse.rows || []

  console.log('📂 [门店菜单] 可用分类:', categories.map(c => `${c.groupId}:${c.groupName}`))

  // 创建分类映射
  const categoryMap = new Map()
  categories.forEach(category => {
    categoryMap.set(category.groupId, {
      groupId: category.groupId,
      groupName: category.groupName,
      prototypes: []
    })
  })

  // 创建原型映射
  const prototypeMap = new Map()
  allPrototypes.value.forEach(prototype => {
    if (prototype && prototype.prototypeId && !prototypeMap.has(prototype.prototypeId)) {
      prototypeMap.set(prototype.prototypeId, {
        prototypeId: prototype.prototypeId,
        prototypeName: prototype.prototypeName || '未知原型',
        prototypeImage: prototype.prototypeImage,
        prototypeDescription: prototype.prototypeDescription,
        drinkGroupId: prototype.drinkGroupId,
        products: []
      })
    }
  })

  // 将商品分配到对应的原型
  products.forEach(product => {
    if (!product || !product.drinkPrototype) {
      console.warn(`⚠️ [门店菜单] 商品数据无效:`, product)
      return
    }

    const prototypeId = product.drinkPrototype
    if (prototypeId && prototypeMap.has(prototypeId)) {
      const prototype = prototypeMap.get(prototypeId)
      if (prototype) {
        prototype.products.push({
          ...product,
          // 添加原型名称用于显示
          prototypeName: prototype.prototypeName
        })
      }
    } else {
      console.warn(`⚠️ [门店菜单] 商品 ${product.drinkId} 的原型 ${prototypeId} 未找到`)
    }
  })

  // 将原型分配到对应的分类
  prototypeMap.forEach(prototype => {
    if (prototype && prototype.products && prototype.products.length > 0) {
      const categoryId = prototype.drinkGroupId
      if (categoryId && categoryMap.has(categoryId)) {
        const category = categoryMap.get(categoryId)
        if (category && category.prototypes) {
          category.prototypes.push(prototype)
        }
      } else {
        // 如果分类不存在，创建一个未知分类
        if (!categoryMap.has(0)) {
          categoryMap.set(0, {
            groupId: 0,
            groupName: '未分类',
            prototypes: []
          })
        }
        const uncategorized = categoryMap.get(0)
        if (uncategorized && uncategorized.prototypes) {
          uncategorized.prototypes.push(prototype)
        }
      }
    }
  })

  // 转换为数组并过滤空分类
  const result = Array.from(categoryMap.values()).filter(category =>
    category && category.prototypes && category.prototypes.length > 0
  )

  console.log('🎯 [门店菜单] 分组完成:', result.map(c =>
    `${c?.groupName || '未知'}(${c?.prototypes?.length || 0}个原型, ${c?.prototypes?.reduce((sum, p) => sum + (p?.products?.length || 0), 0) || 0}个商品)`
  ))

  return result
}

// 门店切换处理
const handleStoreChange = () => {
  if (selectedStoreId.value) {
    fetchStoreMenu()
  } else {
    menuData.value = []
  }
}

// 刷新门店菜单
const refreshStoreMenu = () => {
  fetchStoreMenu()
}

// 显示添加商品模态框
const showAddProductModal = () => {
  addProductForm.value = {
    drinkPrototype: '',
    productPrice: '',
    targetId: '',
    options: ''
  }
  addProductModal?.show()
}



// 添加商品到门店
const addProductToStore = async () => {
  if (!addProductForm.value.drinkPrototype || !addProductForm.value.productPrice || !addProductForm.value.targetId) {
    alert('请填写必填字段')
    return
  }

  // 在保存前，手动触发 ProductOptionsEditor 更新数据
  if (addProductOptionsRef.value) {
    addProductOptionsRef.value.updateParentData()
  }

  // 验证选项格式（简单字符串，无需特殊验证）
  // options 现在是简单的点分隔字符串，如："正常冰.少糖.大杯"
  
  addingProduct.value = true
  try {
    const productData = {
      drinkPrototype: Number(addProductForm.value.drinkPrototype),
      storeId: Number(selectedStoreId.value),
      productPrice: addProductForm.value.productPrice,
      targetId: addProductForm.value.targetId,
      status: '1',
      options: addProductForm.value.options
    }

    await createProduct(productData)

    addProductModal?.hide()
    fetchStoreMenu()
    alert('商品添加成功')
  } catch (error) {
    console.error('添加商品失败:', error)
    alert('添加商品失败，请重试')
  } finally {
    addingProduct.value = false
  }
}

// 编辑商品
const editProduct = (product: drinkProduct) => {
  // 查找原型名称
  const prototype = allPrototypes.value.find(p => p.prototypeId === product.drinkPrototype)

  editProductForm.value = {
    drinkId: product.drinkId,
    prototypeName: prototype?.prototypeName || '未知原型',
    productPrice: product.productPrice || '',
    targetId: product.targetId || '',
    options: product.options || product.option || '',
    isActive: product.status === '1'
  }

  editProductModal?.show()
}

// 删除商品
const deleteProductItem = async (product: drinkProduct) => {
  if (!confirm(`确定要删除商品吗？`)) return

  try {
    await deleteProduct(product.drinkId)
    fetchStoreMenu()
    alert('删除成功')
  } catch (error) {
    console.error('删除失败:', error)
    alert('删除失败，请重试')
  }
}

// 切换商品状态
const toggleProductStatus = async (product: drinkProduct) => {
  const newStatus = product.status === '1' ? '0' : '1'
  const action = newStatus === '1' ? '上架' : '下架'

  if (!confirm(`确定要${action}该商品吗？`)) return

  try {
    await changeProductStatus(product.drinkId, newStatus)
    fetchStoreMenu()
    alert(`${action}成功`)
  } catch (error) {
    console.error(`${action}失败:`, error)
    alert(`${action}失败，请重试`)
  }
}



// 更新商品
const updateProduct = async () => {
  if (!editProductForm.value.drinkId || !editProductForm.value.productPrice || !editProductForm.value.targetId) {
    alert('请填写必填字段')
    return
  }

  // 在保存前，手动触发 ProductOptionsEditor 更新数据
  if (editProductOptionsRef.value) {
    editProductOptionsRef.value.updateParentData()
  }

  // 验证选项格式（简单字符串，无需特殊验证）
  // options 现在是简单的点分隔字符串，如："正常冰.少糖.大杯"

  updatingProduct.value = true
  try {
    const { updateProduct: updateProductApi } = await import('@/utils/api/product')

    const productData = {
      drinkId: editProductForm.value.drinkId,
      productPrice: editProductForm.value.productPrice,
      targetId: editProductForm.value.targetId,
      status: editProductForm.value.isActive ? '1' : '0',
      options: editProductForm.value.options
    }

    await updateProductApi(productData)

    editProductModal?.hide()
    fetchStoreMenu()
    alert('商品更新成功')
  } catch (error) {
    console.error('更新商品失败:', error)
    alert('更新商品失败，请重试')
  } finally {
    updatingProduct.value = false
  }
}

// 初始化
onMounted(async () => {
  await nextTick()
  if (addProductModalRef.value) {
    addProductModal = new Modal(addProductModalRef.value)
  }
  if (editProductModalRef.value) {
    editProductModal = new Modal(editProductModalRef.value)
  }

  await fetchStoreList()
  await fetchAllPrototypes()
})
</script>

<style scoped>
.store-menu-management {
  background: #fff;
}

.store-selector {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.store-info-card .card {
  border-radius: 8px;
}

.category-header {
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 10px;
}

.prototypes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.prototype-card .card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.prototype-card .card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

.prototype-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
}

.product-item {
  padding: 5px 0;
  border-bottom: 1px solid #f0f1f3;
}

.product-item:last-child {
  border-bottom: none;
}

.empty-state,
.no-store-selected {
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
}

@media (max-width: 768px) {
  .prototypes-grid {
    grid-template-columns: 1fr;
  }
  
  .store-selector .d-flex {
    flex-direction: column;
    gap: 15px;
  }
  
  .action-buttons {
    margin-top: 10px;
  }
}
</style>
