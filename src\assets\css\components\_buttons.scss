.default-btn {
    background-color: var(--splash-primary-color);

    &:hover {
        background-color: var(--splash-primary-active-color);
    }
}
.default-outline-btn {
    border: 1px solid rgba(101, 96, 240, 0.3);
}
.card-btn {
    color: var(--splash-secondary-color);
    border: 1px solid #d6d4ec;
    border-radius: 4px;

    &:hover {
        background-color: var(--splash-primary-color) !important;
        border-color: var(--splash-primary-color);
        color: var(--splash-white-color);
    }
    &.active {
        background-color: var(--splash-white-color) !important;
        border-color: var(--splash-primary-color);
        color: var(--splash-primary-color);
    }
}
.card-link-btn {
    line-height: 1.3;

    &::before {
        left: 0;
        right: 0;
        bottom: 0;
        height: 1px;
        content: '';
        position: absolute;
        transition: var(--transition);
        background: var(--splash-primary-color);
    }
    &:hover {
        &::before {
            transform: scaleX(0);
        }
    }
}
.card-dot-btn {
    &.dropdown-toggle {
        margin-right: -7px;
        color: #A09FB0;
        font-size: 20px;
        
        &::after {
            display: none;
        }
        &:hover {
            color: var(--splash-primary-color);
        }
    }
}
.btn {
    --bs-btn-padding-x: 35px;
    --bs-btn-padding-y: 11px;
    --bs-btn-font-size: 16px;
    --bs-btn-font-weight: 500;
}
.btn-primary {
    --bs-btn-bg: var(--splash-primary-color);
    --bs-btn-border-color: var(--splash-primary-color);
    --bs-btn-hover-bg: var(--splash-primary-active-color);
    --bs-btn-hover-border-color: var(--splash-primary-active-color);
    --bs-btn-active-bg: var(--splash-primary-active-color);
    --bs-btn-active-border-color: var(--splash-primary-active-color);
    --bs-btn-disabled-border-color: var(--splash-primary-color);
}
.btn-link {
    --bs-btn-color: var(--splash-primary-color);
    --bs-btn-hover-color: var(--splash-primary-active-color);
    --bs-btn-active-color: var(--splash-primary-active-color);
}
.btn-outline-primary {
    --bs-btn-color: var(--splash-primary-color);
    --bs-btn-border-color: var(--splash-primary-color);
    --bs-btn-hover-bg: var(--splash-primary-color);
    --bs-btn-hover-border-color: var(--splash-primary-color);
    --bs-btn-active-bg: var(--splash-primary-color);
    --bs-btn-active-border-color: var(--splash-primary-color);
    --bs-btn-disabled-color: var(--splash-primary-color);
    --bs-btn-disabled-border-color: var(--splash-primary-color);

    &.bg-white {
        &:hover {
            background-color: var(--splash-primary-color) !important;
        }
    }
}
.btn-sm {
    --bs-btn-padding-y: 6px;
    --bs-btn-padding-x: 18px;
    --bs-btn-font-size: 14px;
}
.btn-outline-light {
    --bs-btn-color: var(--splash-black-color);
}
.btn-group-lg>.btn, .btn-lg {
    --bs-btn-font-size: 1.25rem;
}
.btn-group-sm>.btn, .btn-sm {
    --bs-btn-font-size: 0.875rem;
}

// Dark Mode
.dark {

    .card-btn {
        color: var(--splash-white-color);
        border-color: #45445e;
    
        &:hover {
            background-color: var(--splash-primary-color) !important;
            border-color: var(--splash-primary-color);
            color: var(--splash-white-color);
        }
        &.active {
            background-color: var(--splash-black-color) !important;
            border-color: var(--splash-black-color);
            color: var(--splash-primary-color);
        }
    }
    .card-dot-btn {
        &.dropdown-toggle {
            color: #BCBBC7;
        }
    }
    .btn-light {
        color: var(--splash-black-color);
    }
    .btn-close {
        filter: invert(1);
    }

}