<template>
  <div class="order-report-analysis p-20">
    <!-- 统一筛选器 -->
    <div class="report-filters mb-30">
      <div class="row">
        <div class="col-lg-4 col-md-6 mb-15">
          <label class="form-label">时间范围</label>
          <select v-model="filters.timeRange" class="form-select" @change="applyFilters">
            <option value="today">今日</option>
            <option value="week">本周</option>
            <option value="month">本月</option>
            <option value="quarter">本季度</option>
            <option value="custom">自定义</option>
          </select>
        </div>
        <div class="col-lg-3 col-md-6 mb-15">
          <label class="form-label">门店</label>
          <select v-model="filters.storeId" class="form-select" @change="applyFilters">
            <option value="">全部门店</option>
            <option v-for="store in storeList" :key="store.storeId" :value="store.storeId">
              {{ store.storeName }}
            </option>
          </select>
        </div>
        <div class="col-lg-3 col-md-6 mb-15">
          <label class="form-label">商品分类</label>
          <select v-model="filters.categoryId" class="form-select" @change="applyFilters">
            <option value="">全部分类</option>
            <option v-for="category in categoryList" :key="category.groupId" :value="category.groupId">
              {{ category.groupName }}
            </option>
          </select>
        </div>
        <div class="col-lg-2 col-md-6 mb-15">
          <label class="form-label">订单状态</label>
          <select v-model="filters.orderStatus" class="form-select" @change="applyFilters">
            <option value="">全部状态</option>
            <option value="completed">已完成</option>
            <option value="cancelled">已取消</option>
            <option value="refunded">已退款</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="text-center py-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
      </div>
      <p class="mt-2 text-muted">正在获取订单数据...</p>
    </div>

    <!-- 错误提示 -->
    <div v-else-if="error" class="alert alert-danger" role="alert">
      <i class="flaticon-warning me-2"></i>
      {{ error }}
      <button @click="fetchOrderData" class="btn btn-outline-danger btn-sm ms-2">重试</button>
    </div>

    <!-- 订单概览指标 -->
    <div v-else class="order-metrics mb-30">
      <div class="row">
        <div class="col-lg-3 col-md-6 mb-20">
          <div class="metric-card">
            <div class="metric-icon">
              <i class="flaticon-shopping-cart text-primary"></i>
            </div>
            <div class="metric-content">
              <h3 class="metric-value">{{ orderMetrics.totalOrders }}</h3>
              <p class="metric-label">订单总数</p>
              <span class="metric-change positive">+{{ orderMetrics.orderGrowth }}%</span>
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-20">
          <div class="metric-card">
            <div class="metric-icon">
              <i class="flaticon-money text-success"></i>
            </div>
            <div class="metric-content">
              <h3 class="metric-value">¥{{ orderMetrics.totalRevenue }}</h3>
              <p class="metric-label">总营收</p>
              <span class="metric-change positive">+{{ orderMetrics.revenueGrowth }}%</span>
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-20">
          <div class="metric-card">
            <div class="metric-icon">
              <i class="flaticon-user text-info"></i>
            </div>
            <div class="metric-content">
              <h3 class="metric-value">¥{{ orderMetrics.avgOrderValue }}</h3>
              <p class="metric-label">客单价</p>
              <span class="metric-change negative">{{ orderMetrics.avgOrderChange }}%</span>
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-20">
          <div class="metric-card">
            <div class="metric-icon">
              <i class="flaticon-percentage text-warning"></i>
            </div>
            <div class="metric-content">
              <h3 class="metric-value">{{ orderMetrics.completionRate }}%</h3>
              <p class="metric-label">完成率</p>
              <span class="metric-change positive">+{{ orderMetrics.completionChange }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表分析 -->
    <div class="row">
      <!-- 订单量趋势 -->
      <div class="col-lg-8 mb-30">
        <div class="chart-card">
          <div class="chart-header">
            <h6 class="chart-title">订单量趋势分析</h6>
          </div>
          <div class="chart-container">
            <apexchart
              v-if="chartsReady"
              type="line"
              height="280"
              :options="orderTrendChartOptions"
              :series="orderTrendChartSeries"
              class="chart"
            />
            <div v-else class="chart-loading">
              <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">图表加载中...</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 订单时段分布 -->
      <div class="col-lg-4 mb-30">
        <div class="chart-card">
          <div class="chart-header">
            <h6 class="chart-title">订单时段分布</h6>
          </div>
          <div class="chart-container">
            <apexchart
              v-if="chartsReady"
              type="donut"
              height="280"
              :options="timeDistributionChartOptions"
              :series="timeDistributionChartSeries"
              class="chart"
            />
            <div v-else class="chart-loading">
              <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">图表加载中...</p>
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { format } from 'date-fns'
import serviceAxios from '@/utils/serviceAxios'
import type { drinkOrder } from '@/types/order'
import type { storeDetail } from '@/types/store'

// 加载状态和错误处理
const isLoading = ref(false)
const error = ref<string | null>(null)
const chartsReady = ref(false)

// 筛选器
const filters = ref({
  timeRange: 'today',
  storeId: '',
  categoryId: '',
  orderStatus: ''
})

// 数据状态
const storeList = ref<Array<storeDetail>>([])
const categoryList = ref<Array<{ groupId: number; groupName: string }>>([])
const rawOrderData = ref<Array<drinkOrder>>([])

// 订单指标
const orderMetrics = ref({
  totalOrders: '0',
  orderGrowth: '0',
  totalRevenue: '0',
  revenueGrowth: '0',
  avgOrderValue: '0',
  avgOrderChange: '0',
  completionRate: '0',
  completionChange: '0'
})



// 数据处理工具函数
const processOrderData = {
  // 按时间聚合订单数据
  aggregateByTime: (orders: drinkOrder[], timeUnit: 'hour' | 'day' | 'week' | 'month') => {
    const aggregated: Record<string, { count: number; revenue: number }> = {}

    orders.forEach(order => {
      if (!order.orderTime) return

      let key: string
      const orderDate = new Date(order.orderTime)

      switch (timeUnit) {
        case 'hour': {
          key = format(orderDate, 'HH:00')
          break
        }
        case 'day': {
          key = format(orderDate, 'yyyy-MM-dd')
          break
        }
        case 'week': {
          const weekStart = new Date(orderDate)
          weekStart.setDate(orderDate.getDate() - orderDate.getDay())
          key = format(weekStart, 'yyyy-MM-dd')
          break
        }
        case 'month': {
          key = format(orderDate, 'yyyy-MM')
          break
        }
        default: {
          key = format(orderDate, 'yyyy-MM-dd')
        }
      }

      if (!aggregated[key]) {
        aggregated[key] = { count: 0, revenue: 0 }
      }

      aggregated[key].count++
      aggregated[key].revenue += parseFloat(order.totalAmount || '0')
    })

    return aggregated
  },

  // 计算增长率
  calculateGrowthRate: (current: number, previous: number): string => {
    if (previous === 0) return '0'
    const rate = ((current - previous) / previous) * 100
    return rate > 0 ? `+${rate.toFixed(1)}` : rate.toFixed(1)
  },

  // 解析订单商品
  parseOrderItems: (orderItems: string) => {
    try {
      return JSON.parse(orderItems)
    } catch (error) {
      console.warn('解析订单商品失败:', error)
      return []
    }
  },

  // 统计商品销量
  aggregateProductSales: (orders: drinkOrder[]) => {
    const productSales: Record<string, { name: string; count: number }> = {}

    orders.forEach(order => {
      if (!order.orderItems) return

      const items = processOrderData.parseOrderItems(order.orderItems)
      items.forEach((item: Record<string, unknown>) => {
        const productName = String(item.productName || item.drinkName || `商品${item.productId || item.drinkId || ''}`)
        const quantity = parseInt(String(item.quantity || item.drinkQuantity)) || 1

        if (!productSales[productName]) {
          productSales[productName] = { name: productName, count: 0 }
        }
        productSales[productName].count += quantity
      })
    })

    return Object.values(productSales).sort((a, b) => b.count - a.count)
  },

  // 数据验证和格式化
  validateAndFormatData: {
    // 验证订单数据
    validateOrder: (order: drinkOrder): boolean => {
      return !!(order && order.orderId && order.totalAmount)
    },

    // 格式化金额
    formatAmount: (amount: string | number): number => {
      const num = typeof amount === 'string' ? parseFloat(amount) : amount
      return isNaN(num) ? 0 : Math.round(num * 100) / 100
    },

    // 格式化日期
    formatDate: (dateStr: string): Date | null => {
      if (!dateStr) return null
      const date = new Date(dateStr)
      return isNaN(date.getTime()) ? null : date
    },

    // 确保数组数据的一致性
    ensureArrayConsistency: (labels: string[], data: number[]): { labels: string[], data: number[] } => {
      const minLength = Math.min(labels.length, data.length)
      return {
        labels: labels.slice(0, minLength),
        data: data.slice(0, minLength)
      }
    }
  }
}



// 应用筛选器
const applyFilters = () => {
  console.log('应用筛选器:', filters.value)
  fetchOrderData()
}



// 订单趋势图配置
const orderTrendChartSeries = ref([
  {
    name: '订单数量',
    data: [] // 初始为空，等待真实数据加载
  }
])

const orderTrendChartOptions = ref({
  chart: {
    type: 'line',
    height: 280,
    toolbar: {
      show: false
    },
    fontFamily: 'Source Han Serif, serif',
    background: 'transparent',
    animations: {
      enabled: true,
      easing: 'easeinout',
      speed: 800
    },
    dropShadow: {
      enabled: true,
      color: '#000',
      top: 18,
      left: 7,
      blur: 10,
      opacity: 0.1
    }
  },
  colors: ['#667eea'],
  fill: {
    type: 'gradient',
    gradient: {
      shade: 'light',
      type: 'vertical',
      shadeIntensity: 0.5,
      gradientToColors: ['#764ba2'],
      inverseColors: false,
      opacityFrom: 0.8,
      opacityTo: 0.1,
      stops: [0, 100]
    }
  },
  stroke: {
    curve: 'smooth',
    width: 4,
    lineCap: 'round'
  },
  dataLabels: {
    enabled: false
  },
  markers: {
    size: 6,
    colors: ['#667eea'],
    strokeColors: '#fff',
    strokeWidth: 2,
    hover: {
      size: 8
    }
  },
  xaxis: {
    categories: [], // 初始为空，等待真实数据加载
    labels: {
      style: {
        colors: '#64748b',
        fontSize: '13px',
        fontFamily: 'Source Han Serif, serif',
        fontWeight: 500
      }
    },
    axisBorder: {
      show: false
    },
    axisTicks: {
      show: false
    }
  },
  yaxis: {
    labels: {
      style: {
        colors: '#64748b',
        fontSize: '13px',
        fontFamily: 'Source Han Serif, serif',
        fontWeight: 500
      },
      formatter: function (value: number) {
        return value.toLocaleString()
      }
    }
  },
  grid: {
    borderColor: '#e2e8f0',
    strokeDashArray: 0,
    xaxis: {
      lines: {
        show: false
      }
    },
    yaxis: {
      lines: {
        show: true
      }
    },
    padding: {
      top: 10,
      right: 15,
      bottom: 15, /* 为底部坐标轴标签留出空间 */
      left: 10
    }
  },
  tooltip: {
    theme: 'light',
    style: {
      fontSize: '14px',
      fontFamily: 'Source Han Serif, serif'
    },
    custom: function({series, seriesIndex, dataPointIndex, w}) {
      return '<div class="custom-tooltip">' +
        '<div class="tooltip-title">' + w.globals.labels[dataPointIndex] + '</div>' +
        '<div class="tooltip-content">' +
        '<span class="tooltip-label">订单数量:</span> ' +
        '<span class="tooltip-value">' + series[seriesIndex][dataPointIndex].toLocaleString() + '</span>' +
        '</div>' +
        '</div>'
    }
  }
})

// 时段分布图配置
const timeDistributionChartSeries = ref([0, 0, 0, 0, 0, 0]) // 初始为0，等待真实数据加载

const timeDistributionChartOptions = ref({
  chart: {
    type: 'donut',
    height: 280,
    fontFamily: 'Source Han Serif, serif',
    animations: {
      enabled: true,
      easing: 'easeinout',
      speed: 800
    }
  },
  labels: ['早餐(7-10)', '上午(10-12)', '午餐(12-14)', '下午(14-17)', '晚餐(17-20)', '夜宵(20-22)'],
  colors: ['#667eea', '#f093fb', '#4facfe', '#43e97b', '#ffecd2', '#ff9a9e'],
  legend: {
    position: 'bottom',
    fontSize: '14px',
    fontFamily: 'Source Han Serif, serif',
    fontWeight: 500,
    labels: {
      colors: '#374151'
    },
    markers: {
      width: 12,
      height: 12,
      radius: 6
    }
  },
  plotOptions: {
    pie: {
      donut: {
        size: '65%',
        labels: {
          show: true,
          name: {
            show: true,
            fontSize: '16px',
            fontFamily: 'Source Han Serif, serif',
            fontWeight: 600,
            color: '#374151'
          },
          value: {
            show: true,
            fontSize: '24px',
            fontFamily: 'Source Han Serif, serif',
            fontWeight: 700,
            color: '#1f2937',
            formatter: function (val: string) {
              return val + '%'
            }
          },
          total: {
            show: true,
            showAlways: false,
            label: '订单分布',
            fontSize: '16px',
            fontFamily: 'Source Han Serif, serif',
            fontWeight: 600,
            color: '#374151'
          }
        }
      }
    }
  },
  dataLabels: {
    enabled: false
  },
  tooltip: {
    theme: 'light',
    style: {
      fontSize: '14px',
      fontFamily: 'Source Han Serif, serif'
    },
    custom: function({series, seriesIndex, w}) {
      return '<div class="custom-tooltip">' +
        '<div class="tooltip-title">' + w.globals.labels[seriesIndex] + '</div>' +
        '<div class="tooltip-content">' +
        '<span class="tooltip-label">占比:</span> ' +
        '<span class="tooltip-value">' + series[seriesIndex] + '%</span>' +
        '</div>' +
        '</div>'
    }
  },
  states: {
    hover: {
      filter: {
        type: 'lighten',
        value: 0.15
      }
    },
    active: {
      allowMultipleDataPointsSelection: false,
      filter: {
        type: 'darken',
        value: 0.35
      }
    }
  }
})





// 更新订单趋势图
const updateOrderTrendChart = async () => {
  try {
    // 这些变量在switch语句中会被使用，所以保留声明

    // 根据时间筛选器自动选择合适的时间单位
    switch (filters.value.timeRange) {
      case 'today':
        // 获取今天的订单，按小时统计
        await updateHourlyOrderTrend()
        return

      case 'week':
        // 获取最近7天的订单
        await updateDailyOrderTrend()
        return

      case 'month':
        // 获取最近4周的订单
        await updateWeeklyOrderTrend()
        return

      case 'quarter':
        // 获取最近6个月的订单
        await updateMonthlyOrderTrend()
        return

      default:
        // 默认显示最近7天
        await updateDailyOrderTrend()
    }
  } catch (error) {
    console.error('更新订单趋势图失败:', error)
    // API失败时清空数据，不使用假数据
    orderTrendChartOptions.value.xaxis.categories = []
    orderTrendChartSeries.value = [{ name: '订单数量', data: [] }]
  }
}

// 按小时统计订单趋势
const updateHourlyOrderTrend = async () => {
  const today = format(new Date(), 'yyyy-MM-dd')
  const response = await serviceAxios.get('/manager/order/list', {
    params: {
      pageNum: 1,
      pageSize: 9999,
      queryDate: today
    }
  })

  const orders = response.rows || []
  const hourlyStats: Record<number, number> = {}

  // 初始化24小时数据
  for (let i = 0; i < 24; i++) {
    hourlyStats[i] = 0
  }

  // 统计每小时订单数
  orders.forEach((order: drinkOrder) => {
    if (order.orderTime) {
      const hour = new Date(order.orderTime).getHours()
      hourlyStats[hour]++
    }
  })

  // 选择主要营业时间段
  const labels = ['08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00']
  const data = [8, 10, 12, 14, 16, 18, 20].map(hour => hourlyStats[hour] || 0)

  orderTrendChartOptions.value.xaxis.categories = labels
  orderTrendChartSeries.value = [{
    name: '订单数量',
    data: data
  }]
}

// 按天统计订单趋势
const updateDailyOrderTrend = async () => {
  const dailyStats: Record<string, number> = {}
  const labels: string[] = []

  // 获取最近7天数据
  for (let i = 6; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    const dateStr = format(date, 'yyyy-MM-dd')
    const dayName = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.getDay()]

    labels.push(dayName)

    try {
      const response = await serviceAxios.get('/manager/order/list', {
        params: {
          pageNum: 1,
          pageSize: 9999,
          queryDate: dateStr
        }
      })
      dailyStats[dayName] = response.rows?.length || 0
    } catch (error) {
      dailyStats[dayName] = 0
    }
  }

  const data = labels.map(label => dailyStats[label] || 0)

  orderTrendChartOptions.value.xaxis.categories = labels
  orderTrendChartSeries.value = [{
    name: '订单数量',
    data: data
  }]
}

// 按周统计订单趋势
const updateWeeklyOrderTrend = async () => {
  const weeklyStats: number[] = []
  const labels: string[] = []

  // 获取最近4周数据
  for (let i = 3; i >= 0; i--) {
    const endDate = new Date()
    endDate.setDate(endDate.getDate() - i * 7)
    const startDate = new Date(endDate)
    startDate.setDate(startDate.getDate() - 6)

    labels.push(`第${4-i}周`)

    let weekTotal = 0
    // 统计这一周的订单
    for (let j = 0; j < 7; j++) {
      const date = new Date(startDate)
      date.setDate(date.getDate() + j)
      const dateStr = format(date, 'yyyy-MM-dd')

      try {
        const response = await serviceAxios.get('/manager/order/list', {
          params: {
            pageNum: 1,
            pageSize: 9999,
            queryDate: dateStr
          }
        })
        weekTotal += response.rows?.length || 0
      } catch (error) {
        // 忽略错误，继续统计
      }
    }
    weeklyStats.push(weekTotal)
  }

  orderTrendChartOptions.value.xaxis.categories = labels
  orderTrendChartSeries.value = [{
    name: '订单数量',
    data: weeklyStats
  }]
}

// 按月统计订单趋势
const updateMonthlyOrderTrend = async () => {
  const monthlyStats: number[] = []
  const labels: string[] = []

  // 获取最近6个月数据
  for (let i = 5; i >= 0; i--) {
    const date = new Date()
    date.setMonth(date.getMonth() - i)
    const monthName = `${date.getMonth() + 1}月`
    labels.push(monthName)

    // 获取该月的第一天和最后一天
    const firstDay = new Date(date.getFullYear(), date.getMonth(), 1)
    const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0)

    let monthTotal = 0
    // 统计整个月的订单（简化处理，实际应该用时间范围查询）
    try {
      const response = await serviceAxios.get('/manager/order/list', {
        params: {
          pageNum: 1,
          pageSize: 9999,
          orderTimeBegin: format(firstDay, 'yyyy-MM-dd 00:00:00'),
          orderTimeEnd: format(lastDay, 'yyyy-MM-dd 23:59:59')
        }
      })
      monthTotal = response.rows?.length || 0
    } catch (error) {
      monthTotal = 0
    }

    monthlyStats.push(monthTotal)
  }

  orderTrendChartOptions.value.xaxis.categories = labels
  orderTrendChartSeries.value = [{
    name: '订单数量',
    data: monthlyStats
  }]
}



// 更新时段分布图
const updateTimeDistributionChart = async () => {
  try {
    // 获取今天的订单数据
    const today = format(new Date(), 'yyyy-MM-dd')
    const response = await serviceAxios.get('/manager/order/list', {
      params: {
        pageNum: 1,
        pageSize: 9999,
        queryDate: today
      }
    })

    const orders = response.rows || []

    // 定义时段
    const timeSlots = {
      '早餐(7-10)': { start: 7, end: 10, count: 0 },
      '上午(10-12)': { start: 10, end: 12, count: 0 },
      '午餐(12-14)': { start: 12, end: 14, count: 0 },
      '下午(14-17)': { start: 14, end: 17, count: 0 },
      '晚餐(17-20)': { start: 17, end: 20, count: 0 },
      '夜宵(20-22)': { start: 20, end: 22, count: 0 }
    }

    // 统计各时段订单数
    orders.forEach((order: drinkOrder) => {
      if (order.orderTime) {
        const hour = new Date(order.orderTime).getHours()

        Object.values(timeSlots).forEach(slot => {
          if (hour >= slot.start && hour < slot.end) {
            slot.count++
          }
        })
      }
    })

    // 转换为百分比
    const totalOrders = orders.length
    const percentages = Object.values(timeSlots).map(slot => {
      return totalOrders > 0 ? Math.round((slot.count / totalOrders) * 100) : 0
    })

    timeDistributionChartSeries.value = percentages

  } catch (error) {
    console.error('更新时段分布图失败:', error)
    // API失败时清空数据，不使用假数据
    timeDistributionChartSeries.value = [0, 0, 0, 0, 0, 0]
  }
}





// 获取日期范围
const getDateRange = () => {
  const today = new Date()
  let startDate = ''
  const endDate = format(today, 'yyyy-MM-dd')

  switch (filters.value.timeRange) {
    case 'today':
      startDate = format(today, 'yyyy-MM-dd')
      break
    case 'week': {
      const weekStart = new Date(today)
      weekStart.setDate(weekStart.getDate() - 7)
      startDate = format(weekStart, 'yyyy-MM-dd')
      break
    }
    case 'month': {
      const monthStart = new Date(today)
      monthStart.setDate(monthStart.getDate() - 30)
      startDate = format(monthStart, 'yyyy-MM-dd')
      break
    }
    case 'quarter': {
      const quarterStart = new Date(today)
      quarterStart.setDate(quarterStart.getDate() - 90)
      startDate = format(quarterStart, 'yyyy-MM-dd')
      break
    }
    default:
      startDate = format(today, 'yyyy-MM-dd')
  }

  return { startDate, endDate }
}

// 获取订单数据
const fetchOrderData = async () => {
  if (isLoading.value) return

  isLoading.value = true
  error.value = null

  try {
    const { startDate } = getDateRange()

    // 并行获取所有需要的数据
    const [
      ordersResponse,
      storesResponse,
      categoriesResponse
    ] = await Promise.all([
      // 获取订单数据
      serviceAxios.get('/manager/order/list', {
        params: {
          pageNum: 1,
          pageSize: 9999,
          queryDate: startDate,
          storeId: filters.value.storeId || undefined
        }
      }),
      // 获取门店列表
      serviceAxios.get('/manager/store/list', {
        params: {
          pageNum: 1,
          pageSize: 9999
        }
      }),
      // 获取分类列表
      serviceAxios.get('/manager/group/list', {
        params: {
          pageNum: 1,
          pageSize: 9999
        }
      }).catch(() => ({ rows: [] }))
    ])

    // 处理门店数据
    storeList.value = storesResponse.rows || []

    // 处理分类数据
    categoryList.value = (categoriesResponse.rows || []).map((item: Record<string, unknown>) => ({
      groupId: item.groupId as number,
      groupName: item.groupName as string
    }))

    // 处理订单数据
    const orders = ordersResponse.rows || []

    // 验证和过滤有效订单
    const validOrders = orders.filter((order: drinkOrder) =>
      processOrderData.validateAndFormatData.validateOrder(order)
    )

    rawOrderData.value = validOrders

    // 应用筛选器
    let filteredOrders = validOrders

    if (filters.value.orderStatus) {
      const statusMap: Record<string, string> = {
        'completed': '4',
        'cancelled': '5',
        'refunded': '5' // 假设退款也是取消状态
      }
      filteredOrders = filteredOrders.filter((order: drinkOrder) =>
        order.orderStatus === statusMap[filters.value.orderStatus]
      )
    }

    // 不再需要orderList，因为已删除详细订单表格

    // 计算订单指标
    const totalOrders = filteredOrders.length
    const totalRevenue = filteredOrders.reduce((sum: number, order: drinkOrder) => {
      return sum + processOrderData.validateAndFormatData.formatAmount(order.totalAmount || '0')
    }, 0)

    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0

    const completedOrders = filteredOrders.filter((order: drinkOrder) =>
      order.orderStatus === '4'
    ).length
    const completionRate = totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0

    // 计算增长率（与昨天对比）
    let orderGrowth = '0'
    let revenueGrowth = '0'
    let avgOrderChange = '0'
    let completionChange = '0'

    try {
      // 获取昨天的数据进行对比
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      const yesterdayStr = format(yesterday, 'yyyy-MM-dd')

      const yesterdayResponse = await serviceAxios.get('/manager/order/list', {
        params: {
          pageNum: 1,
          pageSize: 9999,
          queryDate: yesterdayStr,
          storeId: filters.value.storeId || undefined
        }
      })

      const yesterdayOrders = (yesterdayResponse.rows || []).filter((order: drinkOrder) =>
        processOrderData.validateAndFormatData.validateOrder(order)
      )
      const yesterdayTotalOrders = yesterdayOrders.length
      const yesterdayTotalRevenue = yesterdayOrders.reduce((sum: number, order: drinkOrder) => {
        return sum + processOrderData.validateAndFormatData.formatAmount(order.totalAmount || '0')
      }, 0)
      const yesterdayAvgOrderValue = yesterdayTotalOrders > 0 ? yesterdayTotalRevenue / yesterdayTotalOrders : 0

      const yesterdayCompletedOrders = yesterdayOrders.filter((order: drinkOrder) =>
        order.orderStatus === '4'
      ).length
      const yesterdayCompletionRate = yesterdayTotalOrders > 0 ? (yesterdayCompletedOrders / yesterdayTotalOrders) * 100 : 0

      // 计算增长率
      orderGrowth = processOrderData.calculateGrowthRate(totalOrders, yesterdayTotalOrders)
      revenueGrowth = processOrderData.calculateGrowthRate(totalRevenue, yesterdayTotalRevenue)
      avgOrderChange = processOrderData.calculateGrowthRate(avgOrderValue, yesterdayAvgOrderValue)
      completionChange = processOrderData.calculateGrowthRate(completionRate, yesterdayCompletionRate)

    } catch (error) {
      console.warn('获取昨天数据失败，无法计算增长率:', error)
    }

    // 更新订单指标
    orderMetrics.value = {
      totalOrders: totalOrders.toLocaleString(),
      orderGrowth, // 真实的增长率数据
      totalRevenue: totalRevenue.toLocaleString(),
      revenueGrowth, // 真实的增长率数据
      avgOrderValue: avgOrderValue.toFixed(1),
      avgOrderChange, // 真实的增长率数据
      completionRate: completionRate.toFixed(1),
      completionChange // 真实的增长率数据
    }

    // 更新图表数据
    updateOrderTrendChart()
    updateTimeDistributionChart()

  } catch (err) {
    console.error('获取订单数据失败:', err)
    error.value = '获取订单数据失败，请稍后重试'
  } finally {
    isLoading.value = false
  }
}



// 初始化
onMounted(async () => {
  // 等待 DOM 完全渲染
  await nextTick()

  // 延迟一小段时间确保容器元素已准备好
  setTimeout(() => {
    chartsReady.value = true
  }, 100)

  // 获取数据
  fetchOrderData()
})
</script>

<style scoped>
.order-report-analysis {
  background: #fff;
}

.report-filters {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.metric-card {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.metric-icon {
  margin-right: 15px;
}

.metric-icon i {
  font-size: 2.5rem;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 5px;
  color: #2c3e50;
}

.metric-label {
  color: #6c757d;
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.metric-change {
  font-size: 0.8rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
}

.metric-change.positive {
  color: #28a745;
  background: #d4edda;
}

.metric-change.negative {
  color: #dc3545;
  background: #f8d7da;
}

.chart-card {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  height: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e9ecef;
}

.chart-title {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0;
}

.chart-controls {
  display: flex;
  gap: 5px;
}

.chart-container {
  position: relative;
  height: 320px; /* 增加高度以容纳280px的图表 + 额外空间 */
  overflow: visible; /* 确保内容不被裁剪 */
  padding-bottom: 10px; /* 为图表底部留出空间 */
}

/* 确保ApexCharts图表完整显示 */
.chart-container .chart {
  width: 100% !important;
  height: 100% !important;
}

.chart-container .apexcharts-canvas {
  overflow: visible !important;
}

.chart-container .apexcharts-legend {
  position: relative !important;
  bottom: auto !important;
}



.chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 320px; /* 与chart-container高度保持一致 */
}

.chart-loading .spinner-border {
  width: 2rem;
  height: 2rem;
}

/* 自定义工具提示样式 */
:global(.custom-tooltip) {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

:global(.custom-tooltip .tooltip-title) {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
  margin-bottom: 6px;
}

:global(.custom-tooltip .tooltip-content) {
  display: flex;
  align-items: center;
  gap: 8px;
}

:global(.custom-tooltip .tooltip-label) {
  font-size: 13px;
  color: #6b7280;
}

:global(.custom-tooltip .tooltip-value) {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
}

@media (max-width: 768px) {
  .report-filters .col-lg-3 {
    margin-bottom: 10px;
  }

  .order-metrics .col-lg-3 {
    margin-bottom: 15px;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  /* 移动端图表容器调整 */
  .chart-container {
    height: 300px; /* 移动端稍微减少高度 */
  }

  .chart-loading {
    min-height: 300px;
  }
}

/* 小屏幕进一步优化 */
@media (max-width: 576px) {
  .chart-container {
    height: 280px; /* 小屏幕进一步减少高度 */
    padding-bottom: 5px;
  }

  .chart-loading {
    min-height: 280px;
  }

  .chart-card {
    padding: 15px; /* 减少内边距 */
  }

  .chart-header {
    margin-bottom: 15px;
  }
}
</style>
