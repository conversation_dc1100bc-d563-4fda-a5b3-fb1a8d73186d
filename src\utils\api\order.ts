import serviceAxios from "@/utils/serviceAxios";
import {drinkOrder} from "@/types/order"
import type {queryPageParams} from "@/types/queryPageParams";
import type {TableDataInfo} from "@/types/tableDataInfo";

// 订单查询参数接口（根据API文档完善）
export interface OrderQueryParams {
    pageNum: number;
    pageSize: number;
    queryDate?: string;
    orderTimeBegin?: string;
    orderTimeEnd?: string;
    paymentTimeBegin?: string;
    paymentTimeEnd?: string;
    orderId?: number;
    orderReqSeq?: string;
    storeId?: number;
    userPhone?: string;
    orderStatus?: string;
    orderSource?: string;
    orderTimeSort?: string;
}

// 订单状态枚举
export enum OrderStatus {
    PENDING_PAYMENT = '0',  // 待支付
    PAID = '1',            // 已支付
    IN_PRODUCTION = '2',   // 制作中
    READY_FOR_PICKUP = '3', // 待取餐
    COMPLETED = '4',       // 已完成
    CANCELLED = '5'        // 已取消
}

// 订单来源枚举
export enum OrderSource {
    WECHAT = 'wx',        // 微信小程序
    PAD = 'pad'           // 线下点单屏
}

// 获取订单列表
export function getOrderList(params: OrderQueryParams): Promise<TableDataInfo<drinkOrder>> {
    return serviceAxios.get("/manager/order/list", {params});
}

// 根据门店ID获取订单列表
export function getOrderListByStoreId(storeId: number, params: queryPageParams): Promise<TableDataInfo<drinkOrder>> {
    return serviceAxios.get(`/manager/order/store/${storeId}`, { params });
}

// 订单详情API响应类型
export interface OrderDetailResponse {
    code: number;
    msg: string;
    data: drinkOrder;
}

// 获取订单详情
export function getOrderDetail(orderId: number): Promise<OrderDetailResponse> {
    return serviceAxios.get(`/manager/order/${orderId}`);
}

// 根据订单流水号获取订单
export function getOrderByReqSeq(orderReqSeq: string): Promise<drinkOrder> {
    return serviceAxios.get(`/manager/order/reqSeq/${orderReqSeq}`);
}

// 根据用户手机号获取订单列表
export function getOrdersByUserPhone(userPhone: string, params: queryPageParams): Promise<TableDataInfo<drinkOrder>> {
    return serviceAxios.get(`/manager/order/user/${userPhone}`, { params });
}

// 修改订单状态
export function updateOrderStatus(orderId: number, orderStatus: string): Promise<void> {
    return serviceAxios.put(`/manager/order/${orderId}/status/${orderStatus}`);
}

// 修改订单信息
export function updateOrder(orderData: Partial<drinkOrder>): Promise<void> {
    return serviceAxios.put('/manager/order', orderData);
}

// 删除订单
export function deleteOrders(orderIds: string): Promise<void> {
    return serviceAxios.delete(`/manager/order/${orderIds}`);
}

// 导出订单列表
export function exportOrderList(params: OrderQueryParams): Promise<Blob> {
    return serviceAxios.get('/manager/order/export', {
        params,
        responseType: 'blob'
    });
}

// 异常订单类型枚举
export enum ExceptionType {
    TIMEOUT_PRODUCTION = 'timeout_production',  // 制作超时
    TIMEOUT_PICKUP = 'timeout_pickup',         // 取餐超时
    PAYMENT_FAILED = 'payment_failed',         // 支付失败
    PRODUCTION_FAILED = 'production_failed',   // 制作失败
    SYSTEM_ERROR = 'system_error'              // 系统错误
}

// 异常订单处理动作枚举
export enum ExceptionAction {
    REMAKE = 'remake',           // 重新制作
    REFUND = 'refund',          // 退款
    CANCEL = 'cancel',          // 取消订单
    CONTACT_CUSTOMER = 'contact', // 联系客户
    MANUAL_COMPLETE = 'complete'  // 手动完成
}

// 异常订单接口
export interface ExceptionOrder extends drinkOrder {
    exceptionType: ExceptionType;
    exceptionReason: string;
    exceptionTime: string;
    timeoutMinutes?: number;
    isHandled: boolean;
    handledBy?: string;
    handledTime?: string;
    handledAction?: ExceptionAction;
    handledRemark?: string;
}

// 异常订单处理参数
export interface HandleExceptionParams {
    orderId: string;
    action: ExceptionAction;
    remark?: string;
    refundAmount?: number;
}

// 获取异常订单列表
export function getExceptionOrders(params: OrderQueryParams): Promise<TableDataInfo<ExceptionOrder>> {
    return serviceAxios.get("/manager/order/exception/list", {params});
}

// 获取超时订单列表
export function getTimeoutOrders(params: OrderQueryParams): Promise<TableDataInfo<ExceptionOrder>> {
    return serviceAxios.get("/manager/order/timeout/list", {params});
}

// 处理异常订单
export function handleExceptionOrder(params: HandleExceptionParams): Promise<void> {
    return serviceAxios.post('/manager/order/exception/handle', params);
}

// 批量处理异常订单
export function batchHandleExceptionOrders(orderIds: string[], action: ExceptionAction, remark?: string): Promise<void> {
    return serviceAxios.post('/manager/order/exception/batch-handle', {
        orderIds,
        action,
        remark
    });
}

// 获取订单异常统计
export function getExceptionStatistics(storeId?: number, date?: string): Promise<{
    totalExceptions: number;
    timeoutProduction: number;
    timeoutPickup: number;
    paymentFailed: number;
    productionFailed: number;
    systemError: number;
    handledCount: number;
    unhandledCount: number;
}> {
    return serviceAxios.get('/manager/order/exception/statistics', {
        params: { storeId, date }
    });
}

// 兼容旧接口
export interface drinkOrderParams {
    pageNum: number,
    pageSize: number,
    queryDate?: string;
    storeId?: number;
    orderTimeSort?: string;
}

export function getOrderListByParams(params: drinkOrderParams): Promise<TableDataInfo<drinkOrder>> {
    return serviceAxios.get(`/manager/order/list`, { params });
}