<template>
  <div class="production-queue-management">
    <!-- 页面标题和控制 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h2 class="mb-0">制作队列管理</h2>
        <p class="text-muted mb-0">实时监控和管理所有订单的制作流程</p>
      </div>
      <div class="d-flex gap-2">
        <button @click="toggleAutoRefresh" :class="autoRefreshClass" :disabled="loading">
          <i class="flaticon-refresh me-2"></i>
          {{ autoRefresh ? '停止自动刷新' : '开启自动刷新' }}
        </button>
        <button @click="refreshQueues" class="btn btn-primary" :disabled="loading">
          <i class="flaticon-refresh me-2"></i>刷新
        </button>
        <button @click="showMonitorModal" class="btn btn-outline-info">
          <i class="flaticon-chart me-2"></i>监控大屏
        </button>
      </div>
    </div>

    <!-- 监控统计卡片 -->
    <div class="row g-3 mb-4" v-if="monitorStats">
      <div class="col-md-2">
        <div class="card bg-primary text-white">
          <div class="card-body text-center">
            <h4>{{ monitorStats.totalQueues }}</h4>
            <p class="mb-0">总队列数</p>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="card bg-warning text-white">
          <div class="card-body text-center">
            <h4>{{ monitorStats.pendingQueues }}</h4>
            <p class="mb-0">待制作</p>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="card bg-info text-white">
          <div class="card-body text-center">
            <h4>{{ monitorStats.inProgressQueues }}</h4>
            <p class="mb-0">制作中</p>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="card bg-success text-white">
          <div class="card-body text-center">
            <h4>{{ monitorStats.completedToday }}</h4>
            <p class="mb-0">今日完成</p>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="card bg-danger text-white">
          <div class="card-body text-center">
            <h4>{{ monitorStats.failedToday }}</h4>
            <p class="mb-0">今日失败</p>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="card bg-secondary text-white">
          <div class="card-body text-center">
            <h4>{{ monitorStats.averageTime }}分</h4>
            <p class="mb-0">平均耗时</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 状态标签页 -->
    <div class="card mb-4">
      <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs">
          <li class="nav-item">
            <button 
              :class="currentTab === 'all' ? 'nav-link active' : 'nav-link'"
              @click="switchTab('all')"
            >
              全部队列
              <span class="badge bg-primary ms-1">{{ queueList.length }}</span>
            </button>
          </li>
          <li class="nav-item">
            <button
              :class="currentTab === '0' ? 'nav-link active' : 'nav-link'"
              @click="switchTab('0')"
            >
              待制作
              <span class="badge bg-warning ms-1">{{ getQueueCountByStatus('0') }}</span>
            </button>
          </li>
          <li class="nav-item">
            <button
              :class="currentTab === '1' ? 'nav-link active' : 'nav-link'"
              @click="switchTab('1')"
            >
              制作中
              <span class="badge bg-info ms-1">{{ getQueueCountByStatus('1') }}</span>
            </button>
          </li>
          <li class="nav-item">
            <button
              :class="currentTab === '2' ? 'nav-link active' : 'nav-link'"
              @click="switchTab('2')"
            >
              已完成
              <span class="badge bg-success ms-1">{{ getQueueCountByStatus('2') }}</span>
            </button>
          </li>
          <li class="nav-item">
            <button
              :class="currentTab === '3' ? 'nav-link active' : 'nav-link'"
              @click="switchTab('3')"
            >
              失败
              <span class="badge bg-danger ms-1">{{ getQueueCountByStatus('3') }}</span>
            </button>
          </li>
        </ul>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="card mb-4">
      <div class="card-body">
        <div class="row g-3">
          <!-- 门店筛选 -->
          <div class="col-md-2">
            <label class="form-label">门店</label>
            <select v-model="filterForm.storeId" class="form-select" @change="handleFilter">
              <option value="">全部门店</option>
              <option v-for="store in storeList" :key="store.storeId" :value="store.storeId">
                {{ store.storeName }}
              </option>
            </select>
          </div>
          
          <!-- 设备筛选 -->
          <div class="col-md-2">
            <label class="form-label">设备</label>
            <select v-model="filterForm.deviceId" class="form-select" @change="handleFilter">
              <option value="">全部设备</option>
              <option v-for="device in deviceList" :key="device.deviceId" :value="device.deviceId">
                {{ device.deviceName }}
              </option>
            </select>
          </div>
          
          <!-- 优先级筛选 -->
          <div class="col-md-2">
            <label class="form-label">优先级</label>
            <select v-model="filterForm.priority" class="form-select" @change="handleFilter">
              <option value="">全部优先级</option>
              <option value="urgent">紧急</option>
              <option value="high">高</option>
              <option value="normal">普通</option>
              <option value="low">低</option>
            </select>
          </div>
          
          <!-- 搜索框 -->
          <div class="col-md-4">
            <label class="form-label">搜索</label>
            <div class="input-group">
              <input
                type="text"
                class="form-control"
                placeholder="搜索订单号、商品名称..."
                v-model="filterForm.searchValue"
                @keyup.enter="handleFilter"
              />
              <button class="btn btn-outline-secondary" type="button" @click="handleFilter">
                <i class="flaticon-search-interface-symbol"></i>
              </button>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="col-md-3">
            <label class="form-label">&nbsp;</label>
            <div class="d-flex gap-2 flex-wrap">
              <button @click="applyFilter" class="btn btn-primary" :disabled="loading">
                <i class="flaticon-search me-1"></i>筛选
              </button>
              <button @click="resetFilter" class="btn btn-outline-secondary">
                <i class="flaticon-refresh me-1"></i>重置
              </button>
              <button
                v-if="filterForm.deviceId"
                @click="clearDeviceRunningStatus(filterForm.deviceId)"
                class="btn btn-outline-warning"
                :disabled="deviceOperating"
                title="清理当前筛选设备的运行状态"
              >
                <i class="flaticon-settings me-1"></i>清理设备
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedQueues.length > 0" class="card mb-4">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
          <span>已选择 <strong>{{ selectedQueues.length }}</strong> 个队列</span>
          <div class="d-flex gap-2">
            <button @click="batchReset" class="btn btn-outline-warning btn-sm" :disabled="batchOperating">
              <i class="flaticon-refresh me-1"></i>批量重置
            </button>
            <button @click="batchMarkFailedAction" class="btn btn-outline-danger btn-sm" :disabled="batchOperating">
              <i class="flaticon-close me-1"></i>批量标记失败
            </button>
            <button @click="clearSelection" class="btn btn-outline-secondary btn-sm">
              <i class="flaticon-close me-1"></i>取消选择
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 队列列表 -->
    <div class="card">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">
            制作队列
            <span class="badge bg-primary ms-2">{{ filteredQueues.length }}</span>
          </h5>
          <div class="form-check">
            <input 
              class="form-check-input" 
              type="checkbox" 
              id="selectAll"
              :checked="isAllSelected"
              @change="toggleSelectAll"
            >
            <label class="form-check-label" for="selectAll">
              全选
            </label>
          </div>
        </div>
      </div>
      <div class="card-body">
        <!-- 加载状态 -->
        <div v-if="loading" class="text-center py-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
          <p class="mt-2 text-muted">正在加载队列数据...</p>
        </div>

        <!-- 队列表格 -->
        <div v-else-if="filteredQueues.length > 0" class="table-responsive">
          <table class="table table-hover">
            <thead class="table-light">
              <tr>
                <th width="50">
                  <input 
                    type="checkbox" 
                    class="form-check-input"
                    :checked="isAllSelected"
                    @change="toggleSelectAll"
                  >
                </th>
                <th>订单信息</th>
                <th>商品信息</th>
                <th>设备/门店</th>
                <th>状态</th>
                <th>优先级</th>
                <th>时间信息</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="queue in filteredQueues"
                :key="queue.queueId"
                @click="viewQueueDetail(queue)"
                style="cursor: pointer;"
                title="点击查看详情"
              >
                <td @click.stop>
                  <input
                    type="checkbox"
                    class="form-check-input"
                    :value="queue.queueId"
                    v-model="selectedQueues"
                  >
                </td>
                <td>
                  <div>
                    <strong>{{ queue.orderReqSeq }}</strong>
                    <br>
                    <small class="text-muted">订单ID: {{ queue.orderId }}</small>
                  </div>
                </td>
                <td>
                  <div>
                    <strong>{{ queue.drinkName }}</strong>
                    <br>
                    <small class="text-muted">{{ formatOptions(queue.drinkOptions) }}</small>
                    <br>
                    <span class="badge bg-light text-dark">数量: {{ queue.quantity }}</span>
                  </div>
                </td>
                <td>
                  <div>
                    <strong>{{ queue.targetKioskId }}</strong>
                    <br>
                    <small class="text-muted">门店{{ queue.storeId }}</small>
                  </div>
                </td>
                <td>
                  <span :class="getQueueStatusClass(queue.queueStatus)">
                    {{ getQueueStatusName(queue.queueStatus) }}
                  </span>
                  <div v-if="queue.failureReason" class="mt-1">
                    <small class="text-danger">{{ queue.failureReason }}</small>
                  </div>
                </td>
                <td>
                  <span class="badge bg-info">{{ queue.priority }}</span>
                </td>
                <td>
                  <div>
                    <small class="text-muted">创建: {{ formatDateTime(queue.createTime) }}</small>
                    <br>
                    <small v-if="queue.startTime" class="text-muted">开始: {{ formatDateTime(queue.startTime) }}</small>
                    <br>
                    <small v-if="queue.finishTime" class="text-muted">完成: {{ formatDateTime(queue.finishTime) }}</small>
                    <br>
                    <small v-if="queue.estimatedDuration" class="text-info">预计: {{ Math.round(queue.estimatedDuration / 60) }}分钟</small>
                  </div>
                </td>
                <td @click.stop>
                  <div class="btn-group-vertical btn-group-sm">
                    <button
                      @click="viewQueueDetail(queue)"
                      class="btn btn-outline-info btn-sm"
                      title="查看详情"
                    >
                      详情
                    </button>

                    <!-- 待制作状态的操作 -->
                    <button
                      v-if="queue.queueStatus === '0'"
                      @click="startQueue(queue)"
                      class="btn btn-outline-success btn-sm"
                      title="手动启动"
                      :disabled="operating"
                    >
                      启动
                    </button>

                    <!-- 制作中状态的操作 -->
                    <button
                      v-if="queue.queueStatus === '1'"
                      @click="completeQueue(queue)"
                      class="btn btn-outline-primary btn-sm"
                      title="手动完成"
                      :disabled="operating"
                    >
                      完成
                    </button>

                    <!-- 失败状态的操作 -->
                    <button
                      v-if="queue.queueStatus === '3'"
                      @click="resetQueue(queue)"
                      class="btn btn-outline-warning btn-sm"
                      title="重置队列"
                      :disabled="operating"
                    >
                      重置
                    </button>

                    <!-- 通用操作 -->
                    <button
                      v-if="['0', '1'].includes(queue.queueStatus)"
                      @click="markQueueFailed(queue)"
                      class="btn btn-outline-danger btn-sm"
                      title="标记失败"
                      :disabled="operating"
                    >
                      标记失败
                    </button>

                    <!-- 运维操作分隔线 -->
                    <hr class="my-1">

                    <!-- 运维操作 -->
                    <button
                      @click="manualStartQueueItem(queue)"
                      class="btn btn-outline-secondary btn-sm"
                      title="运维操作：手动启动队列"
                      :disabled="operating"
                    >
                      <i class="flaticon-play me-1"></i>手动启动
                    </button>

                    <button
                      @click="clearDeviceRunningStatus(queue.targetKioskId)"
                      class="btn btn-outline-warning btn-sm"
                      title="运维操作：清理设备运行状态"
                      :disabled="deviceOperating"
                    >
                      <i class="flaticon-settings me-1"></i>清理设备
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页组件 -->
        <div v-if="filteredQueues.length > 0" class="d-flex justify-content-between align-items-center mt-4">
          <div class="text-muted">
            共 <strong>{{ pagination.total }}</strong> 条记录，第 <strong>{{ pagination.currentPage }}</strong> 页 / 共 <strong>{{ pagination.totalPages }}</strong> 页
          </div>

          <div class="d-flex align-items-center gap-2">
            <!-- 上一页按钮 -->
            <button
              @click="goToPrevPage"
              :disabled="pagination.currentPage <= 1 || loading"
              class="btn btn-outline-primary btn-sm"
            >
              <i class="flaticon-arrow-left me-1"></i>上一页
            </button>

            <!-- 页码输入 -->
            <div class="d-flex align-items-center gap-1">
              <span class="text-muted">跳转到</span>
              <input
                v-model="pageInput"
                @keyup="handlePageInputKeyup"
                type="number"
                :min="1"
                :max="pagination.totalPages"
                class="form-control form-control-sm"
                style="width: 80px;"
                placeholder="页码"
                :disabled="loading"
              >
              <button
                @click="jumpToPage"
                :disabled="!pageInput || loading"
                class="btn btn-outline-secondary btn-sm"
              >
                跳转
              </button>
            </div>

            <!-- 下一页按钮 -->
            <button
              @click="goToNextPage"
              :disabled="pagination.currentPage >= pagination.totalPages || loading"
              class="btn btn-outline-primary btn-sm"
            >
              下一页<i class="flaticon-arrow-right ms-1"></i>
            </button>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else-if="!loading" class="text-center py-5">
          <i class="flaticon-list text-muted" style="font-size: 3rem;"></i>
          <h5 class="mt-3">暂无队列数据</h5>
          <p class="text-muted">请检查筛选条件或刷新页面</p>
        </div>
      </div>
    </div>

    <!-- 队列详情弹窗 -->
    <QueueDetailModal
      v-if="showDetailModal && selectedQueue"
      :queue-id="selectedQueue.queueId"
      @close="showDetailModal = false"
      @refresh="refreshQueues"
    />

    <!-- 监控大屏弹窗 -->
    <QueueMonitorModal
      v-if="showMonitorModalFlag"
      @close="showMonitorModalFlag = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { format } from 'date-fns'
import {
  getProductionQueueList,
  getQueueMonitorData,
  startQueue as startQueueAPI,
  completeQueue as completeQueueAPI,
  resetQueue as resetQueueAPI,
  markQueueFailed as markQueueFailedAPI,
  batchResetQueues,
  batchMarkFailed,
  clearDeviceStatus,
  manualStartQueue,
  getQueueStatusName,
  getQueueStatusClass,
  QueueStatus,
  type ProductionQueue,
  type QueueQueryParams
} from '@/utils/api/queue'
import { getOnlineDevices } from '@/utils/api/device'
import { getStoreList } from '@/utils/api/store'
import type { OnlineDevice } from '@/types/device'
import type { storeDetail } from '@/types'
import QueueDetailModal from './QueueDetailModal.vue'
// import QueueMonitorModal from './QueueMonitorModal.vue'

// 数据状态
const loading = ref(false)
const operating = ref(false)
const batchOperating = ref(false)
const deviceOperating = ref(false)
const queueList = ref<ProductionQueue[]>([])
const deviceList = ref<OnlineDevice[]>([])
const storeList = ref<storeDetail[]>([])
const monitorStats = ref<{
  totalQueues: number;
  pendingQueues: number;
  inProgressQueues: number;
  completedToday: number;
  failedToday: number;
  averageTime: number;
} | null>(null)

// 分页状态
const pagination = ref({
  currentPage: 1,
  pageSize: 20,
  total: 0,
  totalPages: 0
})
const pageInput = ref('')

// UI状态
const currentTab = ref<'all' | QueueStatus>('all')
const autoRefresh = ref(false)
const refreshInterval = ref<number | null>(null)
const showDetailModal = ref(false)
const showMonitorModalFlag = ref(false)
const selectedQueue = ref<ProductionQueue | null>(null)
const selectedQueues = ref<number[]>([])

// 筛选表单
const filterForm = ref({
  storeId: '',
  deviceId: '',
  priority: '',
  searchValue: ''
})

// 计算属性
const autoRefreshClass = computed(() => {
  return autoRefresh.value ? 'btn btn-success' : 'btn btn-outline-success'
})

// 由于改为服务端分页，大部分筛选通过API参数实现
// 只保留客户端搜索功能（因为API可能不支持搜索参数）
const filteredQueues = computed(() => {
  let queues = queueList.value

  // 只保留客户端搜索功能
  if (filterForm.value.searchValue) {
    const searchValue = filterForm.value.searchValue.toLowerCase()
    queues = queues.filter(queue =>
      queue.orderReqSeq.toLowerCase().includes(searchValue) ||
      queue.orderId.toString().includes(searchValue) ||
      queue.drinkName.toLowerCase().includes(searchValue)
    )
  }

  return queues
})

const isAllSelected = computed(() => {
  return filteredQueues.value.length > 0 && selectedQueues.value.length === filteredQueues.value.length
})

// 获取队列列表
const fetchQueues = async (page: number = pagination.value.currentPage) => {
  loading.value = true
  try {
    console.log(`🔄 [队列管理] 开始加载队列列表第${page}页...`)

    const params: QueueQueryParams = {
      pageNum: page,
      pageSize: pagination.value.pageSize
    }

    // 添加筛选条件
    if (filterForm.value.storeId) {
      params.storeId = Number(filterForm.value.storeId)
    }
    if (filterForm.value.deviceId) {
      params.deviceId = filterForm.value.deviceId
    }
    if (currentTab.value !== 'all') {
      params.queueStatus = currentTab.value
    }

    const response = await getProductionQueueList(params)

    if (response.code === 200) {
      queueList.value = response.rows || []
      pagination.value.total = response.total || 0
      pagination.value.currentPage = page
      pagination.value.totalPages = Math.ceil(pagination.value.total / pagination.value.pageSize)

      console.log(`✅ [队列管理] 队列列表加载成功: 第${page}页，${queueList.value.length}个队列，共${pagination.value.total}条记录`)
    } else {
      console.error('❌ [队列管理] API返回错误:', response.msg)
      queueList.value = []
      pagination.value.total = 0
      pagination.value.totalPages = 0
    }
  } catch (error) {
    console.error('❌ [队列管理] 获取队列列表失败:', error)
    queueList.value = []
    pagination.value.total = 0
    pagination.value.totalPages = 0
  } finally {
    loading.value = false
  }
}

// 获取监控统计
const fetchMonitorStats = async () => {
  try {
    const response = await getQueueMonitorData()
    if (response.code === 200) {
      monitorStats.value = response.data
      console.log('✅ [队列管理] 监控统计加载成功:', monitorStats.value)
    }
  } catch (error) {
    console.error('❌ [队列管理] 获取监控统计失败:', error)
  }
}

// 获取设备列表
const fetchDeviceList = async () => {
  try {
    const response = await getOnlineDevices()
    if (response.code === 200) {
      deviceList.value = response.data || []
      console.log(`✅ [队列管理] 设备列表加载成功: ${deviceList.value.length}个设备`)
    }
  } catch (error) {
    console.error('❌ [队列管理] 获取设备列表失败:', error)
  }
}

// 获取门店列表
const fetchStoreList = async () => {
  try {
    const response = await getStoreList({ pageNum: 1, pageSize: 1000 })
    storeList.value = (response.rows || []).filter((store: storeDetail) => store.status === '0')
    console.log(`✅ [队列管理] 门店列表加载成功: ${storeList.value.length}个门店`)
  } catch (error) {
    console.error('❌ [队列管理] 获取门店列表失败:', error)
  }
}

// 刷新队列数据
const refreshQueues = async () => {
  await Promise.all([
    fetchQueues(),
    fetchMonitorStats()
  ])
}

// 切换自动刷新
const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  
  if (autoRefresh.value) {
    refreshInterval.value = window.setInterval(() => {
      refreshQueues()
    }, 10000) // 每10秒刷新一次
    console.log('🔄 [队列管理] 开启自动刷新 (10秒间隔)')
  } else {
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
      refreshInterval.value = null
    }
    console.log('⏹️ [队列管理] 停止自动刷新')
  }
}

// 切换标签页
const switchTab = async (tab: 'all' | string) => {
  currentTab.value = tab as 'all' | QueueStatus
  selectedQueues.value = []
  resetPagination()

  // 重新加载第一页数据
  await fetchQueues(1)
}

// 筛选处理
const handleFilter = () => {
  console.log('🔍 [队列管理] 应用筛选条件:', filterForm.value)
  selectedQueues.value = []
}

// 重置筛选
const resetFilter = async () => {
  filterForm.value = {
    storeId: '',
    deviceId: '',
    priority: '',
    searchValue: ''
  }
  selectedQueues.value = []
  resetPagination()
  await fetchQueues(1)
}

// 全选/取消全选
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedQueues.value = []
  } else {
    selectedQueues.value = filteredQueues.value.map(queue => queue.queueId)
  }
}

// 清除选择
const clearSelection = () => {
  selectedQueues.value = []
}

// 获取状态队列数量（注意：这里只能统计当前页的数量，实际应该从API获取）
const getQueueCountByStatus = (status: QueueStatus): number => {
  return queueList.value.filter(queue => queue.queueStatus === status).length
}

// 分页相关函数
const goToPage = async (page: number) => {
  if (page < 1 || page > pagination.value.totalPages) {
    return
  }
  await fetchQueues(page)
  clearSelection()
}

const goToPrevPage = async () => {
  if (pagination.value.currentPage > 1) {
    await goToPage(pagination.value.currentPage - 1)
  }
}

const goToNextPage = async () => {
  if (pagination.value.currentPage < pagination.value.totalPages) {
    await goToPage(pagination.value.currentPage + 1)
  }
}

const jumpToPage = async () => {
  const page = parseInt(pageInput.value)
  if (isNaN(page) || page < 1 || page > pagination.value.totalPages) {
    pageInput.value = ''
    return
  }
  await goToPage(page)
  pageInput.value = ''
}

const handlePageInputKeyup = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    jumpToPage()
  }
}

// 重置分页到第一页
const resetPagination = () => {
  pagination.value.currentPage = 1
  pageInput.value = ''
}

// 应用筛选条件（重置分页并重新加载）
const applyFilter = async () => {
  resetPagination()
  await fetchQueues(1)
}

// 查看队列详情
const viewQueueDetail = (queue: ProductionQueue) => {
  selectedQueue.value = queue
  showDetailModal.value = true
}

// 显示监控大屏
const showMonitorModal = () => {
  showMonitorModalFlag.value = true
}

// 队列操作
const startQueue = async (queue: ProductionQueue) => {
  operating.value = true
  try {
    await startQueueAPI(queue.queueId)
    console.log(`✅ [队列管理] 队列${queue.queueId}已启动`)
    refreshQueues()
  } catch (error) {
    console.error('❌ [队列管理] 启动队列失败:', error)
  } finally {
    operating.value = false
  }
}

const completeQueue = async (queue: ProductionQueue) => {
  operating.value = true
  try {
    await completeQueueAPI(queue.queueId)
    console.log(`✅ [队列管理] 队列${queue.queueId}已完成`)
    refreshQueues()
  } catch (error) {
    console.error('❌ [队列管理] 完成队列失败:', error)
  } finally {
    operating.value = false
  }
}

const resetQueue = async (queue: ProductionQueue) => {
  operating.value = true
  try {
    await resetQueueAPI(queue.queueId)
    console.log(`✅ [队列管理] 队列${queue.queueId}已重置`)
    refreshQueues()
  } catch (error) {
    console.error('❌ [队列管理] 重置队列失败:', error)
  } finally {
    operating.value = false
  }
}

const markQueueFailed = async (queue: ProductionQueue) => {
  if (!confirm(`确定要标记队列 ${queue.orderReqSeq} 为失败吗？`)) {
    return
  }
  
  operating.value = true
  try {
    await markQueueFailedAPI(queue.queueId)
    console.log(`✅ [队列管理] 队列${queue.queueId}已标记为失败`)
    refreshQueues()
  } catch (error) {
    console.error('❌ [队列管理] 标记队列失败失败:', error)
  } finally {
    operating.value = false
  }
}

// 批量操作
const batchReset = async () => {
  if (!confirm(`确定要重置选中的 ${selectedQueues.value.length} 个队列吗？`)) {
    return
  }
  
  batchOperating.value = true
  try {
    await batchResetQueues(selectedQueues.value)
    console.log(`✅ [队列管理] 批量重置${selectedQueues.value.length}个队列成功`)
    selectedQueues.value = []
    refreshQueues()
  } catch (error) {
    console.error('❌ [队列管理] 批量重置失败:', error)
  } finally {
    batchOperating.value = false
  }
}

const batchMarkFailedAction = async () => {
  if (!confirm(`确定要标记选中的 ${selectedQueues.value.length} 个队列为失败吗？`)) {
    return
  }

  batchOperating.value = true
  try {
    await batchMarkFailed(selectedQueues.value)
    console.log(`✅ [队列管理] 批量标记失败${selectedQueues.value.length}个队列成功`)
    selectedQueues.value = []
    refreshQueues()
  } catch (error) {
    console.error('❌ [队列管理] 批量标记失败失败:', error)
  } finally {
    batchOperating.value = false
  }
}

// 运维操作函数
const clearDeviceRunningStatus = async (kioskId: string) => {
  if (!confirm(`确定要清理设备 ${kioskId} 的运行状态吗？\n\n此操作将清除设备的当前运行状态，可能会影响正在进行的制作任务。`)) {
    return
  }

  deviceOperating.value = true
  try {
    console.log(`🔄 [运维操作] 开始清理设备${kioskId}的运行状态...`)

    const response = await clearDeviceStatus(kioskId)

    if (response.code === 200) {
      console.log(`✅ [运维操作] 设备${kioskId}运行状态清理成功`)
      alert(`✅ 设备 ${kioskId} 运行状态清理成功`)
      await refreshQueues()
    } else {
      console.error(`❌ [运维操作] 设备${kioskId}运行状态清理失败:`, response.msg)
      alert(`❌ 清理失败: ${response.msg}`)
    }
  } catch (error) {
    console.error(`❌ [运维操作] 设备${kioskId}运行状态清理异常:`, error)
    alert('❌ 清理设备运行状态失败，请重试')
  } finally {
    deviceOperating.value = false
  }
}

const manualStartQueueItem = async (queue: ProductionQueue) => {
  if (!confirm(`确定要手动启动队列 ${queue.queueId} 吗？\n\n订单: ${queue.orderReqSeq}\n饮品: ${queue.drinkName}`)) {
    return
  }

  operating.value = true
  try {
    console.log(`🔄 [运维操作] 开始手动启动队列${queue.queueId}...`)

    const response = await manualStartQueue(queue.queueId.toString())

    if (response.code === 200) {
      console.log(`✅ [运维操作] 队列${queue.queueId}手动启动成功`)
      alert(`✅ 队列 ${queue.queueId} 启动成功`)
      await refreshQueues()
    } else {
      console.error(`❌ [运维操作] 队列${queue.queueId}手动启动失败:`, response.msg)
      alert(`❌ 启动失败: ${response.msg}`)
    }
  } catch (error) {
    console.error(`❌ [运维操作] 队列${queue.queueId}手动启动异常:`, error)
    alert('❌ 手动启动队列失败，请重试')
  } finally {
    operating.value = false
  }
}

// 工具函数

const formatDateTime = (dateTime: string): string => {
  if (!dateTime) return '-'
  return format(new Date(dateTime), 'MM-dd HH:mm')
}

const formatOptions = (options: string): string => {
  if (!options) return '-'
  
  // 如果是简单的点分隔格式（如"正常冰.正常糖"），直接显示
  if (options.includes('.') && !options.includes('{')) {
    return options.replace(/\./g, ' | ')
  }
  
  // 尝试解析JSON格式
  try {
    const opts = JSON.parse(options)
    if (typeof opts === 'object') {
      return Object.entries(opts).map(([key, value]) => `${key}: ${value}`).join(' | ')
    }
    return options
  } catch {
    return options
  }
}

// 生命周期
onMounted(async () => {
  console.log('🚀 [队列管理] 开始初始化...')
  await Promise.all([
    fetchStoreList(),
    fetchDeviceList()
  ])
  await refreshQueues()
  console.log('✅ [队列管理] 初始化完成')
})

onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
})
</script>

<style scoped>
.production-queue-management {
  padding: 1rem;
}

.nav-tabs .nav-link {
  border: none;
  color: #6c757d;
}

.nav-tabs .nav-link.active {
  background-color: #fff;
  border-bottom: 2px solid #0d6efd;
  color: #0d6efd;
}

.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.table th {
  font-weight: 600;
  border-bottom: 2px solid #dee2e6;
  background-color: #f8f9fa;
}

.badge {
  font-size: 0.75em;
}

.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.btn-group-vertical .btn {
  margin-bottom: 2px;
}

.btn-group-vertical .btn:last-child {
  margin-bottom: 0;
}

/* 表格行悬停效果 */
.table tbody tr {
  transition: background-color 0.15s ease-in-out;
}

.table tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.08) !important;
}

.table tbody tr[style*="cursor: pointer"]:hover {
  background-color: rgba(0, 123, 255, 0.12) !important;
}
</style>
