// 饮品相关
export interface drinkProduct{
    createBy: string | null,
    createTime: string | null,
    updateBy: string | null,
    updateTime: string | null,
    status: string | null,

    drinkId: number | null,
    drinkPrototype: number | null,

    // API返回的是options，但项目中使用option，保持兼容
    option?: string,
    options?: string,

    productPrice: string | null,
    remark: string | null,

    storeId: number | null,
    targetId: string,
}
export type drinkProductList = Array<drinkProduct>;

export interface drinkGroup{
    createBy: string | null,
    createTime: string | null,
    updateBy: string | null,
    updateTime: string | null,
    status: string,

    groupDescription: string | null,
    groupId: number,
    groupImage: string | null,
    groupName: string,

    orderNum: number,
    remark: string | null,
}
// 饮品原型相关
export interface drinkPrototype {
    prototypeId: number; // 原型ID
    drinkGroupId: number; // 原型所属分类
    prototypeName: string; // 原型名称
    prototypeDescription: string; // 原型描述
    prototypeImage: string; // 原型图片
    prototypeLabel: string; // 标签，逗号分割
    orderNum: number; // 显示顺序
    status: string; // 状态（0停用 1正常）
    defaultOptions: string; // 默认选项（JSON格式）
    standardPrice?: string; // 标准价格（可选字段）
    createBy: string; // 创建者
    createTime: string; // 创建时间
    updateBy: string; // 更新者
    updateTime: string; // 更新时间
    remark: string; // 备注
}

export type drinkPrototypeList = Array<drinkPrototype>;

