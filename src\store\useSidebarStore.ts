import {defineStore} from "pinia";
// import {useStoreStore} from "./useStoreStore";
import {ref} from "vue";

export const useSidebarStore = defineStore("sidebar", () => {
        // data
        const sidebarState = ref<boolean>(false);
        const navMode = ref<string>('initial')

        function toggleSidebar() {
            sidebarState.value = !sidebarState.value;
        }

        function switchDetailMode() {
            navMode.value = 'detail';
            console.log(navMode.value);
        }

        function switchInitialMode() {
            navMode.value = 'initial';
            console.log(navMode.value);
        }

        return {
            sidebarState,
            navMode,
            toggleSidebar,
            switchInitialMode,
            switchDetailMode,
        }

    }, {
        persist: {
            key: 'sidebar-data',
            storage: localStorage,
        }
    },// pinia-plugin-persistedstate 为当前store实现持久化
)