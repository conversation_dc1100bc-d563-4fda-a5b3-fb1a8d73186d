// Welcome LMS Courses
.welcome-lms-courses-box {
    background: linear-gradient(92.21deg, #3E39D9 1.38%, #6560F0 100%);

    .card-body {
        padding: {
            top: 0;
            bottom: 0;
        };
    }
    h2 {
        font-size: 30px;
    }
    span {
        color: #CCCCE1;
    }
    p {
        color: #CFCFD8;
    }
    .list {
        margin-top: 20px;

        h4 {
            font-size: 24px;
        }
        &.row {
            --bs-gutter-x: 10px;
        }
    }
}

// Today's Course
.todays-course-box {
    .content {
        padding-left: 15px;

        a {
            font-size: 14px;
        }
    }
    .swiper-pagination {
        bottom: 0;
        top: auto;

        .swiper-pagination-bullet {
            width: 6px;
            opacity: 1;
            height: 6px;
            background: #E7E6EE;
            margin: {
                left: 4px;
                right: 4px;
            };
            &:first-child {
                margin-left: 0;
            }
            &:last-child {
                margin-right: 0;
            }
            &.swiper-pagination-bullet-active {
                background: var(--splash-primary-color);
            }
        }
    }
}

// Today's Event
.todays-event-box {
    background: rgba(111, 211, 247, 0.3) !important;

    .content {
        padding-left: 15px;

        a {
            font-size: 14px;
        }
    }
    .card-body {
        bottom: 0;
        top: auto;

        .swiper-pagination-bullet {
            width: 6px;
            opacity: 1;
            height: 6px;
            background: rgba(255, 255, 255, 0.6);
            margin: {
                left: 4px;
                right: 4px;
            };
            &:first-child {
                margin-left: 0;
            }
            &:last-child {
                margin-right: 0;
            }
            &.swiper-pagination-bullet-active {
                background: var(--splash-primary-color);
            }
        }
    }
}

// Categories
.categories-box {
    .list {
        li {
            border-bottom: 1px dashed #d9e9ef;
            padding: {
                top: 17px;
                bottom: 17px;
            };
            &:first-child {
                border-top: 1px dashed #d9e9ef;
            }
            .title {
                a {
                    margin-bottom: 6px;
                }
            }
            .link-btn {
                width: 30px;
                height: 30px;
                font-size: 16px;
                border-radius: 2px;
                background: #F7F7F9;
                color: var(--splash-primary-color);
        
                i {
                    left: 0;
                    right: 0;
                    top: 50%;
                    line-height: 1;
                    position: absolute;
                    transform: translateY(-50%);
                }
                &:hover {
                    background-color: var(--splash-primary-color);
                    color: var(--splash-white-color);
                }
            }
        }
    }
}

// Courses List
.courses-list-box {
    background: rgba(101, 96, 240, 0.15);

    .card-body {
        padding: 30px;

        .title {
            .icon {
                width: 65px;
                height: 65px;
    
                img {
                    top: 50%;
                    transform: translateY(-50%);
                }
            }
            h6 {
                left: 0;
                top: 50%;
                right: 0;
                padding-left: 80px;
                position: absolute;
                transform: translateY(-50%);
            }
        }
        .list {
            li {
                border-radius: 30px;
                margin-right: 8px;
                padding: 6px 12px;

                &:last-child {
                    margin-right: 0;
                }
            }
        }
    }
    .link-btn {
        background: rgba(255, 255, 255, 0.45);
        padding: {
            top: 12px;
            left: 30px;
            right: 30px;
            bottom: 12px;
        };
        span {
            line-height: 1.3;

            &::before {
                left: 0;
                right: 0;
                bottom: 0;
                height: 1px;
                content: '';
                position: absolute;
                transition: var(--transition);
                background: var(--splash-black-color);
            }
        }
        i {
            top: 50%;
            right: 30px;
            line-height: 1;
            font-size: 20px;
            position: absolute;
            transform: translateY(-50%);
        }
        &.text-primary {
            span {
                &::before {
                    background: var(--splash-primary-color);
                }
            }
        }
        &.text-success {
            span {
                &::before {
                    background: var(--splash-success-color);
                }
            }
        }
    }
    &.bg2 {
        background: rgba(6, 180, 138, 0.15);

        .link-btn {
            background: rgba(255, 255, 255, 0.45);
        }
    }
}

// Student’s Progress
.students-progress-box {
    .list {
        li {
            border-bottom: 1px dashed #d9e9ef;
            padding: {
                top: 20.6px;
                bottom: 20.6px;
            };
            &:first-child {
                border-top: 1px dashed #d9e9ef;
            }
        }
    }
}

// Performance
.performance-box {
    .list {
        margin-top: 25px;

        li {
            padding-left: 8px;
            margin: {
                left: 8px;
                right: 8px;
            };
            &::before {
                top: 0;
                left: 0;
                bottom: 0;
                width: 2px;
                content: '';
                position: absolute;
                background: #f9f9f9;
            }
            &.text-primary {
                &::before {
                    background: var(--splash-primary-color);
                }
            }
            &.text-info {
                &::before {
                    background: var(--splash-info-color);
                }
            }
            &.text-success {
                &::before {
                    background: var(--splash-success-color);
                }
            }
            &:first-child {
                margin-left: 0;
            }
            &:last-child {
                margin-right: 0;
            }
        }
    }
}

// Dark Mode
.dark {

    .todays-course-box {
        .todays-course-swiper-pagination {
            .swiper-pagination-bullet {
                background: var(--splash-black-color);
                
                &.swiper-pagination-bullet-active {
                    background: var(--splash-primary-color);
                }
            }
        }
    }

    .todays-event-box {
        .todays-event-swiper-pagination {
            .swiper-pagination-bullet {
                background: var(--splash-black-color);

                &.swiper-pagination-bullet-active {
                    background: var(--splash-primary-color);
                }
            }
        }
    }

    .categories-box {
        .list {
            li {
                border-bottom-color: #45445e;
                
                &:first-child {
                    border-top-color: #45445e;
                }
                .link-btn {
                    background: var(--splash-black-color);
            
                    &:hover {
                        background-color: var(--splash-primary-color);
                    }
                }
            }
        }
    }

    .students-progress-box {
        .list {
            li {
                border-bottom-color: #45445e;
                
                svg {
                    circle {
                        &:first-child {
                            stroke: var(--splash-black-color);
                        }
                    }
                }
                &:first-child {
                    border-top-color: #45445e;
                }
            }
        }
    }

    .courses-list-box {
        background: rgba(101, 96, 240, 0.05);
    
        .link-btn {
            background: rgba(255, 255, 255, 0.04);
        }
        &.bg2 {
            background: rgba(6, 180, 138, 0.05);
    
            .link-btn {
                background: rgba(255, 255, 255, 0.04);
            }
        }
    }

}

@media only screen and (max-width : 767px) {

    // Welcome LMS Courses
    .welcome-lms-courses-box {
        padding: {
            top: 15px;
            bottom: 15px;
        };
        h2 {
            font-size: 20px;
        }
        .list {
            margin-top: -15px;

            h4 {
                font-size: 18px;
            }
        }
    }

    // Courses List
    .courses-list-box {
        .card-body {
            padding: 15px;
    
            .title {
                .icon {
                    width: 65px;
                    height: 65px;
                    margin-bottom: 15px;
                }
                h6 {
                    top: 0;
                    padding-left: 0;
                    position: relative;
                    transform: translateY(0);
                }
            }
            .list {
                li {
                    padding: 3px 9px;
                    margin-right: 5px;
                }
            }
        }
        .link-btn {
            padding: {
                top: 10px;
                left: 15px;
                right: 15px;
                bottom: 10px;
            };
            i {
                right: 15px;
            }
        }
    }

    // Performance
    .performance-box {
        .list {
            margin-top: 0;
    
            li {
                margin-top: 15px;
            }
        }
    }

}

@media only screen and (min-width : 768px) and (max-width : 991px) {

    // Welcome LMS Courses
    .welcome-lms-courses-box {
        padding-top: 25px;
    }

    // Courses List
    .courses-list-box {
        .card-body {
            padding: 25px;
        }
        .link-btn {
            padding: {
                left: 25px;
                right: 25px;
            };
            i {
                right: 25px;
            }
        }
    }

}

@media only screen and (min-width : 992px) and (max-width : 1199px) {

    // Welcome LMS Courses
    .welcome-lms-courses-box {
        padding-top: 30px;
    }

}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {

    // Welcome LMS Courses
    .welcome-lms-courses-box {
        padding-top: 30px;
    }

}

@media only screen and (min-width: 1600px) {

    // Welcome LMS Courses
    .welcome-lms-courses-box {
        .content {
            padding-right: 30px;
        }
        .main-image {
            margin-left: auto;
            max-width: 570px;
        }
    }

    // Today's Course
    .todays-course-box {
        .content {
            padding-left: 15px;
    
            a {
                font-size: 16px;
            }
        }
    }
    
    // Today's Event
    .todays-event-box {
        .content {
            padding-left: 15px;
    
            a {
                font-size: 16px;
            }
        }
    }

    // Performance
    .performance-box {
        .list {
            li {
                padding-left: 8px;
                margin: {
                    left: 12px;
                    right: 12px;
                };
            }
        }
    }

    // Student’s Progress
    .students-progress-box {
        .list {
            li {
                padding: {
                    top: 19.2px;
                    bottom: 19.2px;
                };
            }
        }
    }

}