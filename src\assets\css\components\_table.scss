.table {
    >:not(caption)>*>* {
        border-bottom: 0.5px dashed #d9e9ef;
        padding: 15px;

        .users-list {
            div {
                width: 33px;
                height: 33px;
                margin-right: -10px;
                border: 2px solid var(--splash-white-color);
                filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));
    
                &:last-child {
                    margin-right: 0;
                }
            }
        }
        .progress, .progress-stacked {
            height: 5px;
        }
        .dropdown-toggle {
            color: #A09FB0;
            font-size: 20px;
            
            &::after {
                display: none;
            }
            &:hover {
                color: var(--splash-primary-color);
            }
        }
        .form-select {
            line-height: 1.5;
            border-color: var(--bs-border-color);
            padding: 0.375rem 2.25rem 0.375rem 0.75rem;
            background: {
                position: right 0.75rem center;
                size: 16px 12px;
            };
            &:focus {
                border-color: var(--splash-primary-color);
            }
        }
        &.product-title {
            padding-right: 60px;
        }
        .rating {
            margin-bottom: 7px;

            i {
                line-height: 1;
                color: #F3C44C;
                margin-right: 4px;
            }
        }
    }
}
.table-responsive {
    &::-webkit-scrollbar {
        -webkit-appearance: none;

        &:vertical {
            width: 8px;
        }
        &:horizontal {
            height: 8px;
        }
    }
    &::-webkit-scrollbar-thumb {
        border-radius: 50px;
        background-color: rgba(0, 0, 0, .2);
    }
    &::-webkit-scrollbar-track {
        background-color: var(--splash-white-color);
    }
}
.table-borderless>:not(caption)>*>* {
    border-bottom-width: 0 !important;
}

// Dark Mode
.dark {
    
    .table {
        --bs-table-color: var(--splash-white-color);

        >:not(caption)>*>* {
            border-color: #45445e;
    
            .users-list {
                div {
                    border-color: #45445e;
                }
            }
            .dropdown-toggle {
                color: #BCBBC7;

                &:hover {
                    color: var(--splash-primary-color);
                }
            }
            .form-select {
                border-color: #45445e;
                
                &:focus {
                    border-color: var(--splash-primary-color);
                }
            }
            .form-check-input {
                border-color: #45445e;
            }
        }
        >:not(caption)>* {
            border-color: #45445e;
        }
    }
    .table-responsive {
        &::-webkit-scrollbar-thumb {
            background: #6D6C7D;
        }
        &::-webkit-scrollbar-track {
            background: #0D0C1D;
        }
    }

}

@media only screen and (max-width : 767px) {

    .table {
        >:not(caption)>*>* {
            padding: 10px;
            
            &.title {
                padding-right: 40px;
            }
        }
    }

}

@media only screen and (min-width : 768px) and (max-width : 991px) {

    .table {
        >:not(caption)>*>* {
            &.title {
                padding-right: 40px;
            }
        }
    }

}

@media only screen and (min-width : 992px) and (max-width : 1199px) {

    .table {
        >:not(caption)>*>* {
            &.title {
                padding-right: 40px;
            }
        }
    }

}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {

    .table {
        >:not(caption)>*>* {
            &.title {
                padding-right: 40px;
            }
        }
    }

}