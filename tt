GET
​/manager​/store​/sales-history
查询门店历史营业数据

从指定日期往前查询指定天数的营业数据，返回每天的销售额、订单数、制作杯数

Parameters
Try it out
Name	Description
storeId *
integer($int64)
(query)
门店ID

storeId - 门店ID
startDate *
string
(query)
起始日期(yyyy-MM-dd)

startDate - 起始日期(yyyy-MM-dd)
days *
integer($int32)
(query)
查询天数

days - 查询天数
Responses
Code	Description	Links
200	
OK

Media type

*/*
Controls Accept header.
Example Value
Schema
{
  "additionalProp1": {},
  "additionalProp2": {},
  "additionalProp3": {}
}
No links
401	
Unauthorized

No links
403	
Forbidden

No links
404	
Not Found

No links
GET
​/manager​/store​/sales-stat
统计门店某一天的销售额、订单数、制作杯数

参数为storeId和date(yyyy-MM-dd)，返回销售额、订单数、制作杯数

Parameters
Try it out
Name	Description
storeId *
integer($int64)
(query)
门店ID

storeId - 门店ID
date *
string
(query)
日期(yyyy-MM-dd)

date - 日期(yyyy-MM-dd)
Responses
Code	Description	Links
200	
OK

Media type

*/*
Controls Accept header.
Example Value
Schema
{
  "additionalProp1": {},
  "additionalProp2": {},
  "additionalProp3": {}
}
No links
401	
Unauthorized

No links
403	
Forbidden

No links
404	
Not Found。GET
​/manager​/order​/{orderId}
获取饮品订单详细信息

根据订单ID查询单个订单的详细信息，包括订单商品、支付信息、状态等

Parameters
Try it out
Name	Description
orderId *
integer($int64)
(path)
订单ID

348
Responses
Curl
curl -X GET "http://*************:9100/manager/order/348" -H "accept: */*"
Request URL
http://*************:9100/manager/order/348
Server response
Code	Details
200	
Response body
Download
{
  "msg": "操作成功",
  "code": 200,
  "data": {
    "createBy": "",
    "createTime": null,
    "updateBy": "",
    "updateTime": null,
    "remark": "所有制作任务都失败",
    "orderId": 348,
    "orderReqSeq": "TEST_20250717151817_239044D5",
    "storeId": 100,
    "orderSource": "pad",
    "userPhone": "",
    "orderStatus": "5",
    "orderItems": "[{\"unitPrice\":\"7.70\",\"quantity\":1,\"productId\":198,\"targetId\":\"191\",\"subtotal\":\"7.70\",\"options\":\"正常冰.少糖\",\"productName\":\"商品ID=198\"}]",
    "totalAmount": "7.70",
    "orderTime": "2025-07-17T15:18:18",
    "paymentTime": "2025-07-17T15:18:22",
    "paymentOrderNo": "PAY_TEST_20250717151817_239044D5_1752736697710",
    "pickupNo": null,
    "orderItemsList": [
      {
        "createBy": null,
        "createTime": null,
        "updateBy": null,
        "updateTime": null,
        "remark": null,
        "drinkId": null,
        "drinkPrototype": null,
        "storeId": null,
        "options": "正常冰.少糖",
        "targetId": "191",
        "productPrice": null,
        "status": null
      }
    ]
  }
}
Response headers
 connection: keep-alive 
 content-type: application/json 
 date: Sat02 Aug 2025 11:10:06 GMT 
 keep-alive: timeout=60 
 transfer-encoding: chunked 
 vary: OriginAccess-Control-Request-MethodAccess-Control-Request-Headers 
 x-content-type-options: nosniff 
 x-frame-options: SAMEORIGIN 
 x-xss-protection: 1; mode=block 
Responses
Code	Description	Links
200	
OK

Media type

*/*
Controls Accept header.
Example Value
Schema
{
  "additionalProp1": {},
  "additionalProp2": {},
  "additionalProp3": {}
}
No links
401	
Unauthorized

No links
403	
Forbidden

No links
404	
Not Found。GET
​/manager​/order​/list
查询饮品订单列表

分页查询所有饮品订单信息，支持按订单号、门店、状态、时间等条件筛选。时间查询支持：查询日期(queryDate，查询指定日期当天的数据，优先级最高)、下单时间范围(orderTimeBegin/orderTimeEnd)、支付时间范围(paymentTimeBegin/paymentTimeEnd)。当指定queryDate时，时间范围查询将被忽略。

Parameters
Try it out
Name	Description
orderId
integer($int64)
(query)
订单ID

orderId - 订单ID
orderReqSeq
string
(query)
订单流水号

orderReqSeq - 订单流水号
orderSource
string
(query)
下单途径（wx微信小程序 pad线下点单屏）

Available values : pad, wx


--
orderStatus
string
(query)
订单状态（0待支付 1已支付 2制作中 3待取餐 4已完成 5已取消）

Available values : 0, 1, 2, 3, 4, 5


--
orderTimeBegin
string
(query)
下单时间开始（格式：yyyy-MM-dd HH:mm:ss）

orderTimeBegin - 下单时间开始（格式：yyyy-MM-dd HH:mm:ss）
orderTimeEnd
string
(query)
下单时间结束（格式：yyyy-MM-dd HH:mm:ss）

orderTimeEnd - 下单时间结束（格式：yyyy-MM-dd HH:mm:ss）
orderTimeSort
string
(query)
下单时间排序：0或空-不排序，1-时间正序，2-时间倒序

Available values : 0, 1, 2


--
paymentTimeBegin
string
(query)
支付时间开始（格式：yyyy-MM-dd HH:mm:ss）

paymentTimeBegin - 支付时间开始（格式：yyyy-MM-dd HH:mm:ss）
paymentTimeEnd
string
(query)
支付时间结束（格式：yyyy-MM-dd HH:mm:ss）

paymentTimeEnd - 支付时间结束（格式：yyyy-MM-dd HH:mm:ss）
queryDate
string
(query)
查询日期（格式：yyyy-MM-dd，查询指定日期当天的数据，优先级最高）

queryDate - 查询日期（格式：yyyy-MM-dd，查询指定日期当天的数据，优先级最高）
storeId
integer($int64)
(query)
门店ID

storeId - 门店ID
userPhone
string
(query)
用户手机号

userPhone - 用户手机号
Responses
Curl
curl -X GET "http://*************:9100/manager/order/list" -H "accept: */*"
Request URL
http://*************:9100/manager/order/list
Server response
Code	Details
200	
Response body
Download
{
  "total": 696,
  "rows": [
    {
      "createBy": "",
      "createTime": null,
      "updateBy": "",
      "updateTime": null,
      "remark": "所有制作任务已完成",
      "orderId": 287,
      "orderReqSeq": "TEST_20250715182345_EE4A68D1",
      "storeId": 100,
      "orderSource": "pad",
      "userPhone": "",
      "orderStatus": "3",
      "orderItems": "[{\"unitPrice\":\"7.70\",\"quantity\":1,\"productId\":203,\"targetId\":\"202\",\"subtotal\":\"7.70\",\"options\":\"正常冰.正常糖\",\"productName\":\"商品ID=203\"}]",
      "totalAmount": "7.70",
      "orderTime": "2025-07-15T18:23:46",
      "paymentTime": "2025-07-15T18:23:49",
      "paymentOrderNo": "PAY_TEST_20250715182345_EE4A68D1_1752575025555",
      "pickupNo": null,
      "orderItemsList": [
        {
          "createBy": null,
          "createTime": null,
          "updateBy": null,
          "updateTime": null,
          "remark": null,
          "drinkId": null,
          "drinkPrototype": null,
          "storeId": null,
          "options": "正常冰.正常糖",
          "targetId": "202",
          "productPrice": null,
          "status": null
        }
      ]
    },
    {
      "createBy": "",
      "createTime": null,
      "updateBy": "",
      "updateTime": null,
      "remark": "所有制作任务已完成",
      "orderId": 288,
      "orderReqSeq": "TEST_20250715182948_516732C5",
      "storeId": 100,
      "orderSource": "pad",
      "userPhone": "",
      "orderStatus": "3",
      "orderItems": "[{\"unitPrice\":\"1.0\",\"quantity\":1,\"productId\":212,\"targetId\":\"231\",\"subtotal\":\"1.0\",\"options\":\"正常冰.不加糖\",\"productName\":\"商品ID=212\"}]",
      "totalAmount": "1.00",
      "orderTime": "2025-07-15T18:29:49",
      "paymentTime": "2025-07-15T18:29:53",
      "paymentOrderNo": "PAY_TEST_20250715182948_516732C5_1752575388819",
      "pickupNo": null,
      "orderItemsList": [
        {
          "createBy": null,
          "createTime": null,
          "updateBy": null,
          "updateTime": null,
          "remark": null,
          "drinkId": null,
          "drinkPrototype": null,
          "storeId": null,
          "options": "正常冰.不加糖",
          "targetId": "231",
          "productPrice": null,
          "status": null
        }
      ]
    },
    {
      "createBy": "",
      "createTime": null,
      "updateBy": "",
      "updateTime": null,
      "remark": "所有制作任务已完成",
      "orderId": 289,
      "orderReqSeq": "TEST_20250715184027_B3099A11",
      "storeId": 100,
      "orderSource": "pad",
      "userPhone": "",
      "orderStatus": "3",
      "orderItems": "[{\"unitPrice\":\"1.0\",\"quantity\":1,\"productId\":212,\"targetId\":\"231\",\"subtotal\":\"1.0\",\"options\":\"正常冰.不加糖\",\"productName\":\"商品ID=212\"},{\"unitPrice\":\"6.16\",\"quantity\":1,\"productId\":194,\"targetId\":\"181\",\"subtotal\":\"6.16\",\"options\":\"正常冰.少糖\",\"productName\":\"商品ID=194\"}]",
      "totalAmount": "7.16",
      "orderTime": "2025-07-15T18:40:27",
      "paymentTime": "2025-07-15T18:40:31",
      "paymentOrderNo": "PAY_TEST_20250715184027_B3099A11_1752576027279",
      "pickupNo": null,
      "orderItemsList": [
        {
          "createBy": null,
          "createTime": null,
          "updateBy": null,
          "updateTime": null,
          "remark": null,
          "drinkId": null,
          "drinkPrototype": null,
          "storeId": null,
          "options": "正常冰.不加糖",
          "targetId": "231",
          "productPrice": null,
          "status": null
        },
        {
          "createBy": null,
          "createTime": null,
          "updateBy": null,
          "updateTime": null,
          "remark": null,
          "drinkId": null,
          "drinkPrototype": null,
          "storeId": null,
          "options": "正常冰.少糖",
          "targetId": "181",
          "productPrice": null,
          "status": null
        }
      ]
    },
    {
      "createBy": "",
      "createTime": null,
      "updateBy": "",
      "updateTime": null,
      "remark": "所有制作任务已完成",
      "orderId": 290,
      "orderReqSeq": "TEST_20250715184348_3C0EEC6B",
      "storeId": 100,
      "orderSource": "pad",
      "userPhone": "",
      "orderStatus": "3",
      "orderItems": "[{\"unitPrice\":\"6.16\",\"quantity\":1,\"productId\":194,\"targetId\":\"181\",\"subtotal\":\"6.16\",\"options\":\"正常冰.少糖\",\"productName\":\"商品ID=194\"}]",
      "totalAmount": "6.16",
      "orderTime": "2025-07-15T18:43:48",
      "paymentTime": "2025-07-15T18:43:52",
      "paymentOrderNo": "PAY_TEST_20250715184348_3C0EEC6B_1752576228265",
      "pickupNo": null,
      "orderItemsList": [
        {
          "createBy": null,
          "createTime": null,
          "updateBy": null,
          "updateTime": null,
          "remark": null,
          "drinkId": null,
          "drinkPrototype": null,
          "storeId": null,
          "options": "正常冰.少糖",
          "targetId": "181",
          "productPrice": null,
          "status": null
        }
      ]
    },
    {
      "createBy": "",
      "createTime": null,
      "updateBy": "",
      "updateTime": null,
      "remark": "所有制作任务已完成",
      "orderId": 291,
      "orderReqSeq": "TEST_20250715184954_10D7900A",
      "storeId": 100,
      "orderSource": "pad",
      "userPhone": "",
      "orderStatus": "3",
      "orderItems": "[{\"unitPrice\":\"6.16\",\"quantity\":1,\"productId\":194,\"targetId\":\"181\",\"subtotal\":\"6.16\",\"options\":\"正常冰.少糖\",\"productName\":\"商品ID=194\"}]",
      "totalAmount": "6.16",
      "orderTime": "2025-07-15T18:49:54",
      "paymentTime": "2025-07-15T18:49:58",
      "paymentOrderNo": "PAY_TEST_20250715184954_10D7900A_1752576594470",
      "pickupNo": null,
      "orderItemsList": [
        {
          "createBy": null,
          "createTime": null,
          "updateBy": null,
          "updateTime": null,
          "remark": null,
          "drinkId": null,
          "drinkPrototype": null,
          "storeId": null,
          "options": "正常冰.少糖",
          "targetId": "181",
          "productPrice": null,
          "status": null
        }
      ]
    },
    {
      "createBy": "",
      "createTime": null,
      "updateBy": "",
      "updateTime": null,
      "remark": "所有制作任务已完成",
      "orderId": 292,
      "orderReqSeq": "TEST_20250715185328_47E1D7AF",
      "storeId": 100,
      "orderSource": "pad",
      "userPhone": "",
      "orderStatus": "3",
      "orderItems": "[{\"unitPrice\":\"1.0\",\"quantity\":1,\"productId\":212,\"targetId\":\"231\",\"subtotal\":\"1.0\",\"options\":\"正常冰.不加糖\",\"productName\":\"商品ID=212\"}]",
      "totalAmount": "1.00",
      "orderTime": "2025-07-15T18:53:29",
      "paymentTime": "2025-07-15T18:53:33",
      "paymentOrderNo": "PAY_TEST_20250715185328_47E1D7AF_1752576808759",
      "pickupNo": null,
      "orderItemsList": [
        {
          "createBy": null,
          "createTime": null,
          "updateBy": null,
          "updateTime": null,
          "remark": null,
          "drinkId": null,
          "drinkPrototype": null,
          "storeId": null,
          "options": "正常冰.不加糖",
          "targetId": "231",
          "productPrice": null,
          "status": null
        }
      ]
    },
    {
      "createBy": "",
      "createTime": null,
      "updateBy": "",
      "updateTime": null,
      "remark": "所有制作任务已完成",
      "orderId": 293,
      "orderReqSeq": "TEST_20250715185530_246A4951",
      "storeId": 100,
      "orderSource": "pad",
      "userPhone": "",
      "orderStatus": "3",
      "orderItems": "[{\"unitPrice\":\"1.0\",\"quantity\":1,\"productId\":212,\"targetId\":\"231\",\"subtotal\":\"1.0\",\"options\":\"正常冰.不加糖\",\"productName\":\"商品ID=212\"}]",
      "totalAmount": "1.00",
      "orderTime": "2025-07-15T18:55:30",
      "paymentTime": "2025-07-15T18:55:34",
      "paymentOrderNo": "PAY_TEST_20250715185530_246A4951_1752576930024",
      "pickupNo": null,
      "orderItemsList": [
        {
          "createBy": null,
          "createTime": null,
          "updateBy": null,
          "updateTime": null,
          "remark": null,
          "drinkId": null,
          "drinkPrototype": null,
          "storeId": null,
          "options": "正常冰.不加糖",
          "targetId": "231",
          "productPrice": null,
          "status": null
        }
      ]
    },
    {
      "createBy": "",
      "createTime": null,
      "updateBy": "",
      "updateTime": null,
      "remark": "所有制作任务已完成",
      "orderId": 294,
      "orderReqSeq": "TEST_20250715191131_870B3593",
      "storeId": 100,
      "orderSource": "pad",
      "userPhone": "",
      "orderStatus": "3",
      "orderItems": "[{\"unitPrice\":\"7.70\",\"quantity\":1,\"productId\":156,\"targetId\":\"111\",\"subtotal\":\"7.70\",\"options\":\"冰.不加糖.咖啡豆:奶油风味\",\"productName\":\"商品ID=156\"}]",
      "totalAmount": "7.70",
      "orderTime": "2025-07-15T19:11:31",
      "paymentTime": "2025-07-15T19:11:35",
      "paymentOrderNo": "PAY_TEST_20250715191131_870B3593_1752577891400",
      "pickupNo": null,
      "orderItemsList": [
        {
          "createBy": null,
          "createTime": null,
          "updateBy": null,
          "updateTime": null,
          "remark": null,
          "drinkId": null,
          "drinkPrototype": null,
          "storeId": null,
          "options": "冰.不加糖.咖啡豆:奶油风味",
          "targetId": "111",
          "productPrice": null,
          "status": null
        }
      ]
    },
    {
      "createBy": "",
      "createTime": null,
      "updateBy": "",
      "updateTime": null,
      "remark": "所有制作任务已完成",
      "orderId": 295,
      "orderReqSeq": "TEST_20250715193351_FD9406F2",
      "storeId": 100,
      "orderSource": "pad",
      "userPhone": "",
      "orderStatus": "3",
      "orderItems": "[{\"unitPrice\":\"6.16\",\"quantity\":1,\"productId\":194,\"targetId\":\"181\",\"subtotal\":\"6.16\",\"options\":\"正常冰.少糖\",\"productName\":\"商品ID=194\"},{\"unitPrice\":\"7.70\",\"quantity\":1,\"productId\":198,\"targetId\":\"191\",\"subtotal\":\"7.70\",\"options\":\"正常冰.少糖\",\"productName\":\"商品ID=198\"}]",
      "totalAmount": "13.86",
      "orderTime": "2025-07-15T19:33:51",
      "paymentTime": "2025-07-15T19:33:55",
      "paymentOrderNo": "PAY_TEST_20250715193351_FD9406F2_1752579231342",
      "pickupNo": null,
      "orderItemsList": [
        {
          "createBy": null,
          "createTime": null,
          "updateBy": null,
          "updateTime": null,
          "remark": null,
          "drinkId": null,
          "drinkPrototype": null,
          "storeId": null,
          "options": "正常冰.少糖",
          "targetId": "181",
          "productPrice": null,
          "status": null
        },
        {
          "createBy": null,
          "createTime": null,
          "updateBy": null,
          "updateTime": null,
          "remark": null,
          "drinkId": null,
          "drinkPrototype": null,
          "storeId": null,
          "options": "正常冰.少糖",
          "targetId": "191",
          "productPrice": null,
          "status": null
        }
      ]
    },
    {
      "createBy": "",
      "createTime": null,
      "updateBy": "",
      "updateTime": null,
      "remark": "所有制作任务已完成",
      "orderId": 296,
      "orderReqSeq": "TEST_20250715200657_936213D6",
      "storeId": 100,
      "orderSource": "pad",
      "userPhone": "",
      "orderStatus": "3",
      "orderItems": "[{\"unitPrice\":\"7.70\",\"quantity\":1,\"productId\":198,\"targetId\":\"191\",\"subtotal\":\"7.70\",\"options\":\"正常冰.少糖\",\"productName\":\"商品ID=198\"},{\"unitPrice\":\"7.70\",\"quantity\":1,\"productId\":200,\"targetId\":\"193\",\"subtotal\":\"7.70\",\"options\":\"少冰.少糖\",\"productName\":\"商品ID=200\"}]",
      "totalAmount": "15.40",
      "orderTime": "2025-07-15T20:06:57",
      "paymentTime": "2025-07-15T20:07:01",
      "paymentOrderNo": "PAY_TEST_20250715200657_936213D6_1752581217040",
      "pickupNo": null,
      "orderItemsList": [
        {
          "createBy": null,
          "createTime": null,
          "updateBy": null,
          "updateTime": null,
          "remark": null,
          "drinkId": null,
          "drinkPrototype": null,
          "storeId": null,
          "options": "正常冰.少糖",
          "targetId": "191",
          "productPrice": null,
          "status": null
        },
        {
          "createBy": null,
          "createTime": null,
          "updateBy": null,
          "updateTime": null,
          "remark": null,
          "drinkId": null,
          "drinkPrototype": null,
          "storeId": null,
          "options": "少冰.少糖",
          "targetId": "193",
          "productPrice": null,
          "status": null
        }
      ]
    }
  ],
  "code": 200,
  "msg": "查询成功"
}
Response headers
 connection: close 
 content-type: application/json 
 date: Fri01 Aug 2025 12:39:36 GMT 
 transfer-encoding: chunked 
 vary: OriginAccess-Control-Request-MethodAccess-Control-Request-Headers 
 x-content-type-options: nosniff 
 x-frame-options: SAMEORIGIN 
 x-xss-protection: 1; mode=block 
Responses
Code	Description	Links
200	
OK

Media type

*/*
Controls Accept header.
Example Value
Schema
{
  "code": 0,
  "msg": "string",
  "rows": [
    {}
  ],
  "total": 0
}
No links
401	
Unauthorized

No links
403	
Forbidden

No links
404	
Not Found。GET
​/manager​/order​/store​/{storeId}
根据门店查询订单列表

查询指定门店下的所有订单，可选择按订单状态筛选，用于门店订单管理和统计

Parameters
Try it out
Name	Description
storeId *
integer($int64)
(path)
门店ID

storeId - 门店ID
orderStatus
string
(query)
订单状态筛选：0待支付 1已支付 2制作中 3待取餐 4已完成 5已取消

Available values : 0, 1, 2, 3, 4, 5


--
Responses
Code	Description	Links
200	
OK

Media type

*/*
Controls Accept header.
Example Value
Schema
{
  "code": 0,
  "msg": "string",
  "rows": [
    {}
  ],
  "total": 0
}
No links
401	
Unauthorized

No links
403	
Forbidden

No links
404	
Not Found。目前现提供这几个api路径的信息，先根据这个完成相关统计工作。