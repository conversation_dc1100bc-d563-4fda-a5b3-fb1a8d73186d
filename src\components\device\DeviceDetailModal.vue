<template>
  <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="flaticon-gear text-primary me-2"></i>
            设备详情 - {{ device?.deviceName }}
          </h5>
          <button type="button" class="btn-close" @click="$emit('close')"></button>
        </div>
        
        <div class="modal-body">
          <!-- 加载状态 -->
          <div v-if="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在加载设备详细状态...</p>
          </div>

          <!-- 设备详情内容 -->
          <div v-else-if="device">
            <!-- 基本信息 -->
            <div class="row mb-4">
              <div class="col-md-6">
                <div class="card">
                  <div class="card-header">
                    <h6 class="mb-0">基本信息</h6>
                  </div>
                  <div class="card-body">
                    <table class="table table-sm table-borderless">
                      <tr>
                        <td class="text-muted" width="100">设备ID:</td>
                        <td>{{ device.deviceId }}</td>
                      </tr>
                      <tr>
                        <td class="text-muted">设备名称:</td>
                        <td>{{ device.deviceName }}</td>
                      </tr>
                      <tr>
                        <td class="text-muted">设备类型:</td>
                        <td>
                          <span class="badge bg-info">{{ getDeviceTypeName(device.deviceType) }}</span>
                        </td>
                      </tr>
                      <tr>
                        <td class="text-muted">所属门店:</td>
                        <td>{{ device.storeName || `门店${device.storeId}` }}</td>
                      </tr>
                      <tr>
                        <td class="text-muted">设备状态:</td>
                        <td>
                          <span :class="getDeviceStatusClass(device.status)">
                            {{ getDeviceStatusName(device.status) }}
                          </span>
                        </td>
                      </tr>
                      <tr v-if="device.location">
                        <td class="text-muted">设备位置:</td>
                        <td>{{ device.location }}</td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="card">
                  <div class="card-header">
                    <h6 class="mb-0">技术信息</h6>
                  </div>
                  <div class="card-body">
                    <table class="table table-sm table-borderless">
                      <tr v-if="device.ipAddress">
                        <td class="text-muted" width="100">IP地址:</td>
                        <td>{{ device.ipAddress }}</td>
                      </tr>
                      <tr v-if="device.version">
                        <td class="text-muted">固件版本:</td>
                        <td>{{ device.version }}</td>
                      </tr>
                      <tr>
                        <td class="text-muted">最后在线:</td>
                        <td>{{ formatDateTime(device.lastOnlineTime) }}</td>
                      </tr>
                      <tr>
                        <td class="text-muted">创建时间:</td>
                        <td>{{ formatDateTime(device.createTime) }}</td>
                      </tr>
                      <tr>
                        <td class="text-muted">更新时间:</td>
                        <td>{{ formatDateTime(device.updateTime) }}</td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
            </div>

            <!-- 详细状态信息 -->
            <div v-if="deviceDetailStatus" class="row mb-4">
              <div class="col-12">
                <div class="card">
                  <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">实时状态</h6>
                    <button @click="refreshDetailStatus" class="btn btn-sm btn-outline-primary" :disabled="refreshing">
                      <i class="flaticon-refresh me-1"></i>刷新
                    </button>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <div class="col-md-6">
                        <table class="table table-sm table-borderless">
                          <tr v-if="deviceDetailStatus.currentStep">
                            <td class="text-muted" width="120">当前步骤:</td>
                            <td>{{ deviceDetailStatus.currentStep }}</td>
                          </tr>
                          <tr v-if="deviceDetailStatus.progress !== undefined">
                            <td class="text-muted">执行进度:</td>
                            <td>
                              <div class="progress" style="height: 20px;">
                                <div 
                                  class="progress-bar" 
                                  :style="{ width: deviceDetailStatus.progress + '%' }"
                                >
                                  {{ deviceDetailStatus.progress }}%
                                </div>
                              </div>
                            </td>
                          </tr>
                          <tr v-if="deviceDetailStatus.temperature !== undefined">
                            <td class="text-muted">温度:</td>
                            <td>{{ deviceDetailStatus.temperature }}°C</td>
                          </tr>
                          <tr v-if="deviceDetailStatus.pressure !== undefined">
                            <td class="text-muted">压力:</td>
                            <td>{{ deviceDetailStatus.pressure }} bar</td>
                          </tr>
                        </table>
                      </div>
                      <div class="col-md-6">
                        <table class="table table-sm table-borderless">
                          <tr v-if="deviceDetailStatus.errorCode">
                            <td class="text-muted" width="120">错误代码:</td>
                            <td>
                              <span class="badge bg-danger">{{ deviceDetailStatus.errorCode }}</span>
                            </td>
                          </tr>
                          <tr v-if="deviceDetailStatus.errorMessage">
                            <td class="text-muted">错误信息:</td>
                            <td class="text-danger">{{ deviceDetailStatus.errorMessage }}</td>
                          </tr>
                          <tr>
                            <td class="text-muted">最后更新:</td>
                            <td>{{ formatDateTime(deviceDetailStatus.lastUpdate) }}</td>
                          </tr>
                        </table>
                      </div>
                    </div>

                    <!-- 传感器数据 -->
                    <div v-if="deviceDetailStatus.sensors && Object.keys(deviceDetailStatus.sensors).length > 0" class="mt-3">
                      <h6 class="text-muted mb-3">传感器数据</h6>
                      <div class="row g-3">
                        <div 
                          v-for="(value, key) in deviceDetailStatus.sensors" 
                          :key="key"
                          class="col-md-3"
                        >
                          <div class="card bg-light">
                            <div class="card-body text-center">
                              <h6 class="card-title">{{ key }}</h6>
                              <p class="card-text">{{ value }}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="row">
              <div class="col-12">
                <div class="card">
                  <div class="card-header">
                    <h6 class="mb-0">设备操作</h6>
                  </div>
                  <div class="card-body">
                    <div class="d-flex gap-2 flex-wrap">
                      <button 
                        @click="refreshDetailStatus"
                        class="btn btn-outline-primary"
                        :disabled="refreshing"
                      >
                        <i class="flaticon-refresh me-2"></i>刷新状态
                      </button>
                      
                      <button 
                        v-if="device.status === 'error'"
                        @click="clearError"
                        class="btn btn-outline-warning"
                        :disabled="operating"
                      >
                        <i class="flaticon-warning me-2"></i>清除错误
                      </button>
                      
                      <button 
                        @click="toggleMaintenance"
                        class="btn btn-outline-info"
                        :disabled="operating"
                      >
                        <i class="flaticon-gear me-2"></i>
                        {{ device.status === 'maintenance' ? '退出维护' : '进入维护' }}
                      </button>
                      
                      <button 
                        @click="restartDevice"
                        class="btn btn-outline-secondary"
                        :disabled="operating"
                      >
                        <i class="flaticon-refresh me-2"></i>重启设备
                      </button>
                      
                      <button 
                        @click="showMaintenanceRecord"
                        class="btn btn-outline-success"
                      >
                        <i class="flaticon-note me-2"></i>维修记录
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('close')">
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { format } from 'date-fns'
import {
  getDeviceDetailStatus,
  getDeviceTypeName,
  getDeviceStatusName,
  getDeviceStatusClass,
  clearDeviceError,
  setDeviceMaintenanceMode,
  restartDevice as restartDeviceAPI
} from '@/utils/api/device'
import type { Device } from '@/types/device'

// 设备详细状态类型
interface DeviceDetailStatus {
  deviceId: string;
  deviceName: string;
  deviceType: string;
  status: string;
  currentStep?: string;
  progress?: number;
  temperature?: number;
  pressure?: number;
  errorCode?: string;
  errorMessage?: string;
  sensors?: {
    [key: string]: string | number | boolean;
  };
  lastUpdate: string;
}

// Props
const props = defineProps<{
  device: Device | null
}>()

// Emits
const emit = defineEmits<{
  close: []
  refresh: []
}>()

// 数据状态
const loading = ref(false)
const refreshing = ref(false)
const operating = ref(false)
const deviceDetailStatus = ref<DeviceDetailStatus | null>(null)

// 获取设备详细状态
const fetchDetailStatus = async () => {
  if (!props.device) return
  
  loading.value = true
  try {
    console.log('🔍 [设备详情] 获取设备详细状态:', props.device.deviceId)
    const response = await getDeviceDetailStatus(props.device.deviceId)
    
    if (response.code === 200) {
      deviceDetailStatus.value = response.data
      console.log('✅ [设备详情] 设备详细状态获取成功:', deviceDetailStatus.value)
    } else {
      console.error('❌ [设备详情] API返回错误:', response.msg)
    }
  } catch (error) {
    console.error('❌ [设备详情] 获取设备详细状态失败:', error)
    deviceDetailStatus.value = null
  } finally {
    loading.value = false
  }
}

// 刷新详细状态
const refreshDetailStatus = async () => {
  refreshing.value = true
  try {
    await fetchDetailStatus()
  } finally {
    refreshing.value = false
  }
}

// 清除错误
const clearError = async () => {
  if (!props.device) return
  
  operating.value = true
  try {
    await clearDeviceError(props.device.deviceId)
    console.log(`✅ [设备详情] 设备${props.device.deviceName}错误已清除`)
    emit('refresh')
    await refreshDetailStatus()
  } catch (error) {
    console.error('❌ [设备详情] 清除设备错误失败:', error)
  } finally {
    operating.value = false
  }
}

// 切换维护模式
const toggleMaintenance = async () => {
  if (!props.device) return
  
  operating.value = true
  try {
    const maintenance = props.device.status !== 'maintenance'
    await setDeviceMaintenanceMode(props.device.deviceId, maintenance)
    console.log(`✅ [设备详情] 设备${props.device.deviceName}${maintenance ? '进入' : '退出'}维护模式`)
    emit('refresh')
    await refreshDetailStatus()
  } catch (error) {
    console.error('❌ [设备详情] 切换维护模式失败:', error)
  } finally {
    operating.value = false
  }
}

// 重启设备
const restartDevice = async () => {
  if (!props.device) return
  
  if (!confirm(`确定要重启设备 ${props.device.deviceName} 吗？`)) {
    return
  }
  
  operating.value = true
  try {
    await restartDeviceAPI(props.device.deviceId)
    console.log(`✅ [设备详情] 设备${props.device.deviceName}重启命令已发送`)
    emit('refresh')
    await refreshDetailStatus()
  } catch (error) {
    console.error('❌ [设备详情] 重启设备失败:', error)
  } finally {
    operating.value = false
  }
}

// 显示维修记录
const showMaintenanceRecord = () => {
  console.log('🔧 [设备详情] 显示维修记录 - 功能待实现')
  // TODO: 实现维修记录功能
}

// 工具函数
const formatDateTime = (dateTime?: string): string => {
  if (!dateTime) return '-'
  return format(new Date(dateTime), 'yyyy-MM-dd HH:mm:ss')
}

// 生命周期
onMounted(() => {
  if (props.device) {
    fetchDetailStatus()
  }
})
</script>

<style scoped>
.modal {
  z-index: 1055;
}

.card {
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.table-borderless td {
  border: none;
  padding: 0.25rem 0.5rem;
}

.badge {
  font-size: 0.75em;
}

.progress {
  background-color: #e9ecef;
}

.progress-bar {
  background-color: #0d6efd;
  color: white;
  text-align: center;
  line-height: 20px;
}
</style>
