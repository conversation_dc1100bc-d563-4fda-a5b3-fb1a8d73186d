<template>
  <div class="modal fade" id="orderDetailModal" tabindex="-1" aria-labelledby="orderDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="orderDetailModalLabel">订单详情</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div v-if="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
          </div>
          
          <div v-else-if="orderDetail">
            <!-- 订单基础信息 -->
            <div class="row mb-4">
              <div class="col-md-6">
                <h6 class="text-muted mb-3">订单信息</h6>
                <table class="table table-sm table-borderless">
                  <tr>
                    <td class="text-muted" width="100">订单号:</td>
                    <td class="fw-bold">#{{ orderDetail.orderId }}</td>
                  </tr>
                  <tr>
                    <td class="text-muted">流水号:</td>
                    <td>{{ orderDetail.orderReqSeq }}</td>
                  </tr>
                  <tr>
                    <td class="text-muted">订单状态:</td>
                    <td>
                      <span :class="getOrderStatusClass(orderDetail.orderStatus)">
                        {{ getOrderStatusText(orderDetail.orderStatus) }}
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td class="text-muted">订单来源:</td>
                    <td>
                      <span :class="getOrderSourceClass(orderDetail.orderSource)">
                        {{ getOrderSourceText(orderDetail.orderSource) }}
                      </span>
                    </td>
                  </tr>
                </table>
              </div>
              
              <div class="col-md-6">
                <h6 class="text-muted mb-3">客户信息</h6>
                <table class="table table-sm table-borderless">
                  <tr>
                    <td class="text-muted" width="100">手机号:</td>
                    <td>{{ orderDetail.userPhone }}</td>
                  </tr>
                  <tr>
                    <td class="text-muted">门店:</td>
                    <td>{{ getStoreName(orderDetail.storeId) }}</td>
                  </tr>
                  <tr>
                    <td class="text-muted">取餐号:</td>
                    <td>{{ orderDetail.pickupNo || '-' }}</td>
                  </tr>
                </table>
              </div>
            </div>

            <!-- 时间信息 -->
            <div class="mb-4">
              <h6 class="text-muted mb-3">时间信息</h6>
              <div class="row">
                <div class="col-md-4">
                  <small class="text-muted">下单时间</small>
                  <div>{{ formatDate(orderDetail.orderTime) }}</div>
                </div>
                <div class="col-md-4">
                  <small class="text-muted">支付时间</small>
                  <div>{{ formatDate(orderDetail.paymentTime) }}</div>
                </div>
                <div class="col-md-4">
                  <small class="text-muted">更新时间</small>
                  <div>{{ formatDate(orderDetail.updateTime) }}</div>
                </div>
              </div>
            </div>

            <!-- 商品明细 -->
            <div class="mb-4">
              <h6 class="text-muted mb-3">商品明细</h6>
              <div class="table-responsive">
                <table class="table table-sm">
                  <thead class="table-light">
                    <tr>
                      <th>商品名称</th>
                      <th>自定义选项</th>
                      <th>数量</th>
                      <th>单价</th>
                      <th>小计</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(item, index) in orderItems" :key="index">
                      <td>
                        <div>{{ item.productName }}</div>
                        <small class="text-muted">ID: {{ item.productId }}</small>
                      </td>
                      <td>
                        <small class="text-muted">{{ formatOptions(item.options) }}</small>
                      </td>
                      <td class="text-center">{{ item.quantity }}</td>
                      <td class="text-end">¥{{ item.unitPrice }}</td>
                      <td class="text-end fw-bold">¥{{ item.subtotal }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- 金额信息 -->
            <div class="mb-4">
              <h6 class="text-muted mb-3">金额信息</h6>
              <div class="row">
                <div class="col-md-6 offset-md-6">
                  <table class="table table-sm table-borderless">
                    <tr>
                      <td class="text-muted">商品总价:</td>
                      <td class="text-end">¥{{ calculateSubtotal() }}</td>
                    </tr>
                    <tr>
                      <td class="text-muted">优惠金额:</td>
                      <td class="text-end text-success">-¥0.00</td>
                    </tr>
                    <tr class="border-top">
                      <td class="fw-bold">实付金额:</td>
                      <td class="text-end fw-bold text-primary">¥{{ orderDetail.totalAmount }}</td>
                    </tr>
                  </table>
                </div>
              </div>
            </div>

            <!-- 备注信息 -->
            <div v-if="orderDetail.remark" class="mb-4">
              <h6 class="text-muted mb-3">备注信息</h6>
              <div class="alert alert-light">
                {{ orderDetail.remark }}
              </div>
            </div>

            <!-- 支付信息 -->
            <div v-if="orderDetail.paymentOrderNo" class="mb-4">
              <h6 class="text-muted mb-3">支付信息</h6>
              <table class="table table-sm table-borderless">
                <tr>
                  <td class="text-muted" width="120">支付单号:</td>
                  <td>{{ orderDetail.paymentOrderNo }}</td>
                </tr>
                <tr v-if="orderDetail.paymentTime">
                  <td class="text-muted">支付时间:</td>
                  <td>{{ formatDate(orderDetail.paymentTime) }}</td>
                </tr>
                <tr>
                  <td class="text-muted">支付金额:</td>
                  <td class="fw-bold text-success">¥{{ orderDetail.totalAmount }}</td>
                </tr>
              </table>
            </div>

            <!-- 订单项详情（来自orderItemsList） -->
            <div v-if="orderDetail.orderItemsList && orderDetail.orderItemsList.length > 0" class="mb-4">
              <h6 class="text-muted mb-3">订单项详情</h6>
              <div class="table-responsive">
                <table class="table table-sm">
                  <thead class="table-light">
                    <tr>
                      <th>目标ID</th>
                      <th>选项配置</th>
                      <th>状态</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(item, index) in orderDetail.orderItemsList" :key="index">
                      <td>{{ item.targetId || '-' }}</td>
                      <td>
                        <small class="text-muted">{{ formatOptions(item.options || '') }}</small>
                      </td>
                      <td>
                        <span v-if="item.status" :class="getItemStatusClass(item.status)">
                          {{ getItemStatusText(item.status) }}
                        </span>
                        <span v-else class="text-muted">-</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
          <button v-if="canChangeStatus(orderDetail?.orderStatus)" 
                  type="button" 
                  class="btn btn-primary" 
                  @click="showStatusModal">
            修改状态
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { format } from 'date-fns'
import { getOrderDetail, type OrderDetailResponse } from '@/utils/api/order'
import type { drinkOrder } from '@/types/order'

// Props
interface Props {
  storeList: Array<{ storeId: number; storeName: string }>
}

const props = defineProps<Props>()

// 数据状态
const loading = ref(false)
const orderDetail = ref<drinkOrder | null>(null)

// 解析订单商品
const orderItems = computed(() => {
  if (!orderDetail.value?.orderItems) return []

  try {
    // 解析orderItems JSON字符串
    const items = JSON.parse(orderDetail.value.orderItems)
    const parsedItems = Array.isArray(items) ? items : []

    console.log('📦 [订单详情] 解析的商品数据:', parsedItems)

    // 处理字段映射，确保兼容API返回的数据结构
    return parsedItems.map(item => ({
      productId: item.productId,
      productName: item.productName || `商品ID=${item.productId}`,
      options: item.options || '',
      quantity: item.quantity || 1,
      unitPrice: item.unitPrice || item.price || '0.00',
      subtotal: item.subtotal || ((item.quantity || 1) * parseFloat(item.unitPrice || item.price || '0')).toFixed(2),
      targetId: item.targetId
    }))
  } catch (error) {
    console.error('❌ [订单详情] 解析订单商品失败:', error)
    return []
  }
})

// 加载订单详情
const loadOrderDetail = async (orderId: number) => {
  loading.value = true
  try {
    console.log('🔍 [订单详情] 加载订单详情:', orderId)
    const response: OrderDetailResponse = await getOrderDetail(orderId)

    if (response.code === 200 && response.data) {
      orderDetail.value = response.data
      console.log('✅ [订单详情] 订单详情加载成功:', orderDetail.value)
    } else {
      console.error('❌ [订单详情] API返回错误:', response.msg)
      orderDetail.value = null
    }
  } catch (error) {
    console.error('❌ [订单详情] 加载订单详情失败:', error)
    orderDetail.value = null
  } finally {
    loading.value = false
  }
}

// 获取门店名称
const getStoreName = (storeId: number) => {
  const store = props.storeList.find(s => s.storeId === storeId)
  return store?.storeName || '未知门店'
}

// 获取订单状态文本
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '0': '待支付',
    '1': '已支付',
    '2': '制作中',
    '3': '待取餐',
    '4': '已完成',
    '5': '已取消'
  }
  return statusMap[status] || '未知状态'
}

// 获取订单状态样式
const getOrderStatusClass = (status: string) => {
  const classMap: Record<string, string> = {
    '0': 'badge bg-warning',
    '1': 'badge bg-info',
    '2': 'badge bg-primary',
    '3': 'badge bg-success',
    '4': 'badge bg-secondary',
    '5': 'badge bg-danger'
  }
  return classMap[status] || 'badge bg-light'
}

// 获取订单来源文本
const getOrderSourceText = (source: string) => {
  const sourceMap: Record<string, string> = {
    'wx': '微信小程序',
    'pad': '线下点单'
  }
  return sourceMap[source] || '未知来源'
}

// 获取订单来源样式
const getOrderSourceClass = (source: string) => {
  const classMap: Record<string, string> = {
    'wx': 'badge bg-success',
    'pad': 'badge bg-info'
  }
  return classMap[source] || 'badge bg-light'
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return format(new Date(dateString), 'yyyy-MM-dd HH:mm:ss')
}

// 格式化选项
const formatOptions = (options: string) => {
  if (!options) return '-'

  // 如果是简单的点分隔格式（如"正常冰.正常糖"），直接显示
  if (options.includes('.') && !options.includes('{')) {
    return options.replace(/\./g, ' | ')
  }

  // 尝试解析JSON格式
  try {
    const opts = JSON.parse(options)
    if (typeof opts === 'object') {
      return Object.entries(opts).map(([key, value]) => `${key}: ${value}`).join(' | ')
    }
    return options
  } catch {
    return options
  }
}

// 计算小计
const calculateSubtotal = () => {
  return orderItems.value.reduce((sum, item) => {
    return sum + parseFloat(item.subtotal || '0')
  }, 0).toFixed(2)
}

// 判断是否可以修改状态
const canChangeStatus = (status?: string) => {
  if (!status) return false
  return !['4', '5'].includes(status)
}

// 获取订单项状态文本
const getItemStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '0': '待制作',
    '1': '制作中',
    '2': '已完成',
    '3': '已取消'
  }
  return statusMap[status] || status
}

// 获取订单项状态样式
const getItemStatusClass = (status: string) => {
  const classMap: Record<string, string> = {
    '0': 'badge bg-warning',
    '1': 'badge bg-primary',
    '2': 'badge bg-success',
    '3': 'badge bg-danger'
  }
  return classMap[status] || 'badge bg-light'
}

// 显示状态修改弹窗
const showStatusModal = () => {
  console.log('显示状态修改弹窗')
  // TODO: 实现状态修改功能
}

// 暴露方法给父组件
defineExpose({
  loadOrderDetail
})
</script>

<style scoped>
.table td {
  padding: 0.25rem 0.5rem;
}

.table-borderless td {
  border: none;
}

.alert {
  margin-bottom: 0;
}
</style>
