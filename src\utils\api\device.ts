import serviceAxios from "@/utils/serviceAxios";
import type {
  Device,
  MaintenanceRecord,
  DeviceStatistics,
  DeviceApiListResponse,
  OnlineDeviceListResponse,
  DeviceDetailResponse
} from "@/types/device";
import { DeviceType, DeviceStatus } from "@/types/device";

// ==================== 工具函数 ====================

/**
 * 映射API返回的设备状态到枚举类型
 */
function mapDeviceStatus(status: string): DeviceStatus {
  const statusMap: Record<string, DeviceStatus> = {
    'ONLINE': DeviceStatus.ONLINE,
    'online': DeviceStatus.ONLINE,
    'OFFLINE': DeviceStatus.OFFLINE,
    'offline': DeviceStatus.OFFLINE,
    'BUSY': DeviceStatus.BUSY,
    'busy': DeviceStatus.BUSY,
    'ERROR': DeviceStatus.ERROR,
    'error': DeviceStatus.ERROR,
    'MAINTENANCE': DeviceStatus.MAINTENANCE,
    'maintenance': DeviceStatus.MAINTENANCE
  }
  return statusMap[status] || DeviceStatus.ONLINE
}

// ==================== 设备状态监控 API ====================

/**
 * 获取所有类型的在线设备列表
 * 用于全局设备概览
 */
export function getOnlineDevices(): Promise<OnlineDeviceListResponse> {
  return serviceAxios.get("/iot/device/online");
}

/**
 * 获取Kiosk（咖啡机等）在线设备列表
 * 如果只想关注咖啡机设备
 */
export function getOnlineKiosks(): Promise<OnlineDeviceListResponse> {
  return serviceAxios.get("/iot/device/kiosk/online");
}

/**
 * 按设备类型获取在线设备列表
 * 用于实现按设备类型筛选功能
 * @param deviceType 设备类型 (kiosk, robot_arm, ice_maker, grinder, dispenser)
 */
export function getOnlineDevicesByType(deviceType: DeviceType): Promise<OnlineDeviceListResponse> {
  return serviceAxios.get(`/iot/device/online/${deviceType}`);
}

/**
 * 获取指定设备的详细状态信息
 * 用于判断设备是否故障的重要依据
 * @param kioskId 设备ID
 */
export function getDeviceDetailStatus(kioskId: string): Promise<DeviceDetailResponse> {
  return serviceAxios.get(`/manager/production-queue/device-status/${kioskId}`);
}

// ==================== 设备管理 API ====================

/**
 * 获取所有设备列表（基于在线设备API）
 * 由于没有专门的设备列表API，我们基于在线设备API来构建设备列表
 * @param params 查询参数
 */
export async function getAllDevices(params?: {
  pageNum?: number;
  pageSize?: number;
  deviceType?: DeviceType;
  storeId?: number;
  status?: string;
}): Promise<DeviceApiListResponse> {
  try {
    console.log('🔄 [设备API] 基于在线设备API构建设备列表...')

    // 获取在线设备
    const onlineResponse = await getOnlineDevices()
    if (onlineResponse.code !== 200) {
      throw new Error(`在线设备API错误: ${onlineResponse.msg}`)
    }

    // 根据实际API格式提取设备数组：data.allDevices
    const onlineDevices = onlineResponse.data?.allDevices || []
    console.log('🔍 [设备API] 提取的设备数组:', onlineDevices)

    // 转换为设备列表格式，根据实际API字段映射
    const devices: Device[] = onlineDevices.map(device => ({
      deviceId: device.deviceId,
      deviceName: device.deviceId, // API中没有deviceName，使用deviceId
      deviceType: device.deviceType as DeviceType, // 类型断言
      storeId: 0, // API中没有storeId，使用默认值
      status: mapDeviceStatus(device.status), // 安全的状态映射
      lastOnlineTime: device.lastHeartbeatTime ? new Date(device.lastHeartbeatTime).toISOString() : new Date().toISOString(),
      createTime: device.connectTime ? new Date(device.connectTime).toISOString() : new Date().toISOString(),
      updateTime: device.lastHeartbeatTime ? new Date(device.lastHeartbeatTime).toISOString() : new Date().toISOString(),
      version: device.deviceVersion,
      ipAddress: undefined,
      location: undefined
    }))

    console.log('🔍 [设备API] 转换后的设备列表:', devices)

    // 应用筛选条件
    let filteredDevices = devices

    if (params?.deviceType) {
      filteredDevices = filteredDevices.filter(d => d.deviceType === params.deviceType)
    }

    if (params?.storeId) {
      filteredDevices = filteredDevices.filter(d => d.storeId === params.storeId)
    }

    if (params?.status) {
      filteredDevices = filteredDevices.filter(d => d.status === params.status)
    }

    console.log(`✅ [设备API] 设备列表构建成功: ${filteredDevices.length}个设备`)

    return {
      code: 200,
      msg: '操作成功',
      data: filteredDevices
    }
  } catch (error) {
    console.error('❌ [设备API] 构建设备列表失败:', error)
    throw error
  }
}

/**
 * 获取设备统计信息
 * 基于在线设备API计算统计信息
 */
export async function getDeviceStatistics(): Promise<{
  code: number;
  msg: string;
  data: DeviceStatistics;
}> {
  try {
    console.log('🔄 [设备API] 基于在线设备计算统计信息...')

    // 获取在线设备
    const onlineResponse = await getOnlineDevices()
    if (onlineResponse.code !== 200) {
      throw new Error(`在线设备API错误: ${onlineResponse.msg}`)
    }

    // 根据实际API格式提取设备数组和统计信息
    const onlineDevices = onlineResponse.data?.allDevices || []
    const deviceStats = onlineResponse.data?.deviceStatistics || {}

    console.log('🔍 [设备统计] 设备统计信息:', deviceStats)
    console.log('🔍 [设备统计] 设备数组:', onlineDevices)

    // 使用API返回的统计信息，如果没有则计算
    const totalDevices = deviceStats.kiosk_total || onlineDevices.length
    const onlineDevicesCount = deviceStats.kiosk_online || onlineDevices.filter(d => d.status?.toLowerCase() === 'online').length
    const busyDevices = onlineDevices.filter(d => d.status?.toLowerCase() === 'busy').length
    const errorDevices = onlineDevices.filter(d => d.status?.toLowerCase() === 'error').length

    // 按类型统计
    const deviceTypeStats: Record<DeviceType, { total: number; online: number; offline: number }> = {
      [DeviceType.KIOSK]: { total: 0, online: 0, offline: 0 },
      [DeviceType.ROBOT_ARM]: { total: 0, online: 0, offline: 0 },
      [DeviceType.ICE_MAKER]: { total: 0, online: 0, offline: 0 },
      [DeviceType.GRINDER]: { total: 0, online: 0, offline: 0 },
      [DeviceType.DISPENSER]: { total: 0, online: 0, offline: 0 }
    }

    // 使用API返回的统计信息填充kiosk类型，其他类型从设备数组计算
    deviceTypeStats[DeviceType.KIOSK] = {
      total: deviceStats.kiosk_total || 0,
      online: deviceStats.kiosk_online || 0,
      offline: (deviceStats.kiosk_total || 0) - (deviceStats.kiosk_online || 0)
    }

    // 其他设备类型从设备数组计算
    Object.values(DeviceType).forEach(type => {
      if (type !== DeviceType.KIOSK) {
        const typeDevices = onlineDevices.filter(d => d.deviceType === type)
        deviceTypeStats[type] = {
          total: typeDevices.length,
          online: typeDevices.filter(d => d.status?.toLowerCase() === 'online').length,
          offline: 0
        }
      }
    })

    const statistics: DeviceStatistics = {
      totalDevices,
      onlineDevices: onlineDevicesCount,
      offlineDevices: totalDevices - onlineDevicesCount, // 计算离线设备数量
      errorDevices,
      busyDevices,
      deviceTypeStats
    }

    console.log('✅ [设备API] 设备统计计算成功:', statistics)

    return {
      code: 200,
      msg: '操作成功',
      data: statistics
    }
  } catch (error) {
    console.error('❌ [设备API] 计算设备统计失败:', error)
    throw error
  }
}

/**
 * 更新设备信息
 * @param deviceId 设备ID
 * @param data 设备信息
 */
export function updateDevice(deviceId: string, data: Partial<Device>): Promise<{
  code: number;
  msg: string;
}> {
  return serviceAxios.put(`/manager/device/${deviceId}`, data);
}

/**
 * 删除设备
 * @param deviceId 设备ID
 */
export function deleteDevice(deviceId: string): Promise<{
  code: number;
  msg: string;
}> {
  return serviceAxios.delete(`/manager/device/${deviceId}`);
}

// ==================== 维修记录管理 API ====================

/**
 * 获取维修记录列表
 * @param params 查询参数
 */
export function getMaintenanceRecords(params?: {
  pageNum?: number;
  pageSize?: number;
  deviceId?: string;
  status?: string;
  startTime?: string;
  endTime?: string;
}): Promise<{
  code: number;
  msg: string;
  data: {
    total: number;
    rows: MaintenanceRecord[];
  };
}> {
  return serviceAxios.get("/manager/maintenance/list", { params });
}

/**
 * 创建维修记录
 * @param data 维修记录数据
 */
export function createMaintenanceRecord(data: Omit<MaintenanceRecord, 'recordId' | 'createTime' | 'updateTime'>): Promise<{
  code: number;
  msg: string;
  data: MaintenanceRecord;
}> {
  return serviceAxios.post("/manager/maintenance", data);
}

/**
 * 更新维修记录
 * @param recordId 记录ID
 * @param data 更新数据
 */
export function updateMaintenanceRecord(recordId: string, data: Partial<MaintenanceRecord>): Promise<{
  code: number;
  msg: string;
}> {
  return serviceAxios.put(`/manager/maintenance/${recordId}`, data);
}

/**
 * 删除维修记录
 * @param recordId 记录ID
 */
export function deleteMaintenanceRecord(recordId: string): Promise<{
  code: number;
  msg: string;
}> {
  return serviceAxios.delete(`/manager/maintenance/${recordId}`);
}

// ==================== 设备控制 API ====================

/**
 * 重启设备
 * @param deviceId 设备ID
 */
export function restartDevice(deviceId: string): Promise<{
  code: number;
  msg: string;
}> {
  return serviceAxios.post(`/manager/device/${deviceId}/restart`);
}

/**
 * 设置设备维护模式
 * @param deviceId 设备ID
 * @param maintenance 是否进入维护模式
 */
export function setDeviceMaintenanceMode(deviceId: string, maintenance: boolean): Promise<{
  code: number;
  msg: string;
}> {
  return serviceAxios.post(`/manager/device/${deviceId}/maintenance`, { maintenance });
}

/**
 * 清除设备错误状态
 * @param deviceId 设备ID
 */
export function clearDeviceError(deviceId: string): Promise<{
  code: number;
  msg: string;
}> {
  return serviceAxios.post(`/manager/device/${deviceId}/clear-error`);
}

// ==================== 实用工具函数 ====================

/**
 * 获取设备类型显示名称
 */
export function getDeviceTypeName(type: DeviceType): string {
  const typeNames = {
    [DeviceType.KIOSK]: '咖啡机',
    [DeviceType.ROBOT_ARM]: '机械臂',
    [DeviceType.ICE_MAKER]: '制冰机',
    [DeviceType.GRINDER]: '研磨机',
    [DeviceType.DISPENSER]: '分配器'
  };
  return typeNames[type] || type;
}

/**
 * 获取设备状态显示名称
 */
export function getDeviceStatusName(status: string): string {
  const statusNames: Record<string, string> = {
    'online': '在线',
    'offline': '离线',
    'busy': '忙碌中',
    'error': '故障',
    'maintenance': '维护中'
  };
  return statusNames[status] || status;
}

/**
 * 获取设备状态样式类
 */
export function getDeviceStatusClass(status: string): string {
  const statusClasses: Record<string, string> = {
    'online': 'badge bg-success',
    'offline': 'badge bg-secondary',
    'busy': 'badge bg-warning',
    'error': 'badge bg-danger',
    'maintenance': 'badge bg-info'
  };
  return statusClasses[status] || 'badge bg-light';
}
