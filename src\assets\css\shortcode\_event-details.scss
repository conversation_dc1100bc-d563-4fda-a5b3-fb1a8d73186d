.event-details-card {
    .card-body {
        .date {
            width: 80px;
            border: 1px solid var(--splash-primary-color);

            span {
                &:first-child {
                    padding: {
                        top: 5px;
                        bottom: 5px;
                    };
                }
                &:last-child {
                    font-size: 28px;
                }
            }
        }
        .buttons-list {
            margin-bottom: -10px;

            button {
                padding: 12px 46px 12px 25px;
                background: #F2F1F9;
                margin: {
                    right: 10px;
                    bottom: 10px;
                };
                i {
                    top: 50%;
                    right: 25px;
                    line-height: 1;
                    margin-top: 1px;
                    position: absolute;
                    transform: translateY(-50%);
                    transition: var(--transition);
                }
                &:hover {
                    background-color: var(--splash-primary-color);
                    color: var(--splash-white-color) !important;

                    i {
                        color: var(--splash-white-color) !important;
                    }
                }
                &:last-child {
                    margin-right: 0;
                }
                &:last-child {
                    padding: 0;
                    width: 45px;
                    height: 45px;
                    line-height: 45px;
                    text-align: center;

                    i {
                        left: 0;
                        right: 0;
                    }
                }
            }
        }
        p {
            margin-bottom: 15px;

            &:last-child {
                margin-bottom: 0;
            }
        }
        .info {
            padding: {
                top: 30px;
                left: 30px;
                right: 30px;
                bottom: 5px;
            };
            .info-card {
                margin-bottom: 25px;
                padding: {
                    top: 8px;
                    left: 78px;
                    bottom: 8px;
                };
                .icon {
                    top: 50%;
                    width: 65px;
                    height: 65px;
                    font-size: 28px;
                    transform: translateY(-50%);
    
                    i {
                        left: 0;
                        top: 50%;
                        right: 0;
                        line-height: 1;
                        margin-top: 1px;
                        position: absolute;
                        transform: translateY(-50%);
                    }
                }
            }
        }
    }
}
.event-speakers-card {
    .card-body {
        ul {
            li {
                border-bottom: 1px dashed #d9e9ef;
                padding: {
                    top: 15px;
                    bottom: 15px;
                };
                img {
                    border: 2px solid var(--splash-white-color);
                    filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1))
                }
                &:last-child {
                    border-bottom: none;
                    padding-bottom: 0;
                }
                &:first-child {
                    padding-top: 0;
                }
            }
        }
    }
}
.event-topic-card {
    .card-body {
        ul {
            li {
                margin-bottom: 14px;
                padding-left: 28px;

                i {
                    left: 0;
                    top: 50%;
                    line-height: 1;
                    font-size: 18px;
                    position: absolute;
                    transform: translateY(-50%);
                    color: var(--splash-primary-color);
                }
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
}

// Dark Mode
.dark {
    .event-details-card {
        .card-body {
            .buttons-list {
                button {
                    background: var(--splash-black-color);
                    
                    &:hover {
                        background-color: var(--splash-primary-color);
                    }
                }
            }
        }
    }
    .event-speakers-card {
        .card-body {
            ul {
                li {
                    border-bottom-color: #45445e;
                    
                    img {
                        border-color: #45445e;
                    }
                }
            }
        }
    }
}

@media only screen and (max-width : 767px) {

    .event-details-card {
        .card-body {
            .date {
                width: 65px;
    
                span {
                    &:last-child {
                        font-size: 25px;
                    }
                }
            }
            .buttons-list {
                margin-bottom: -15px;
                
                button {
                    padding: 10px 40px 10px 20px;
                    margin-right: 5px;

                    i {
                        right: 20px;
                    }
                    &:last-child {
                        top: -4px;
                        width: 41px;
                        height: 41px;
                        line-height: 41px;
                    }
                }
            }
            .info {
                padding: {
                    top: 20px;
                    left: 15px;
                    right: 15px;
                    bottom: 5px;
                };
                .info-card {
                    margin-bottom: 15px;
                    padding: {
                        top: 8px;
                        left: 78px;
                        bottom: 8px;
                    };
                }
            }
        }
    }
    .event-speakers-card {
        .card-body {
            ul {
                li {
                    padding: {
                        top: 10px;
                        bottom: 10px;
                    };
                }
            }
        }
    }
    .event-topic-card {
        .card-body {
            ul {
                li {
                    margin-bottom: 12px;
                    padding-left: 24px;
    
                    i {
                        font-size: 16px;
                    }
                }
            }
        }
    }

}

@media only screen and (min-width : 576px) and (max-width : 767px) {

    .event-details-card {
        .card-body {
            .buttons-list {
                button {
                    &:last-child {
                        top: 0;
                    }
                }
            }
        }
    }

}

@media only screen and (min-width : 768px) and (max-width : 991px) {

    .event-details-card {
        .card-body {
            .info {
                padding: {
                    top: 25px;
                    left: 25px;
                    right: 25px;
                    bottom: 5px;
                };
                .info-card {
                    margin-bottom: 20px;
                }
            }
        }
    }
    .event-speakers-card {
        .card-body {
            ul {
                li {
                    padding: {
                        top: 12px;
                        bottom: 12px;
                    };
                }
            }
        }
    }

}