<template>
  <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <!-- 弹窗头部 -->
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="flaticon-list me-2"></i>队列详情
          </h5>
          <button type="button" class="btn-close" @click="$emit('close')"></button>
        </div>

        <!-- 弹窗内容 -->
        <div class="modal-body">
          <!-- 加载状态 -->
          <div v-if="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在加载队列详情...</p>
          </div>

          <!-- 详情内容 -->
          <div v-else-if="queueDetail" class="row">
            <!-- 基本信息 -->
            <div class="col-md-6">
              <div class="card h-100">
                <div class="card-header">
                  <h6 class="mb-0">
                    <i class="flaticon-info me-2"></i>基本信息
                  </h6>
                </div>
                <div class="card-body">
                  <div class="row mb-3">
                    <div class="col-sm-4 text-muted">队列ID:</div>
                    <div class="col-sm-8">
                      <strong>{{ queueDetail.queueId }}</strong>
                    </div>
                  </div>
                  <div class="row mb-3">
                    <div class="col-sm-4 text-muted">订单ID:</div>
                    <div class="col-sm-8">{{ queueDetail.orderId }}</div>
                  </div>
                  <div class="row mb-3">
                    <div class="col-sm-4 text-muted">订单流水号:</div>
                    <div class="col-sm-8">
                      <code>{{ queueDetail.orderReqSeq }}</code>
                    </div>
                  </div>
                  <div class="row mb-3">
                    <div class="col-sm-4 text-muted">门店ID:</div>
                    <div class="col-sm-8">{{ queueDetail.storeId }}</div>
                  </div>
                  <div class="row mb-3">
                    <div class="col-sm-4 text-muted">目标设备:</div>
                    <div class="col-sm-8">
                      <span class="badge bg-info">{{ queueDetail.targetKioskId }}</span>
                    </div>
                  </div>
                  <div class="row mb-3">
                    <div class="col-sm-4 text-muted">状态:</div>
                    <div class="col-sm-8">
                      <span :class="getQueueStatusClass(queueDetail.queueStatus)">
                        {{ getQueueStatusName(queueDetail.queueStatus) }}
                      </span>
                    </div>
                  </div>
                  <div class="row mb-3">
                    <div class="col-sm-4 text-muted">优先级:</div>
                    <div class="col-sm-8">
                      <span class="badge bg-secondary">{{ queueDetail.priority }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 商品信息 -->
            <div class="col-md-6">
              <div class="card h-100">
                <div class="card-header">
                  <h6 class="mb-0">
                    <i class="flaticon-coffee me-2"></i>商品信息
                  </h6>
                </div>
                <div class="card-body">
                  <div class="row mb-3">
                    <div class="col-sm-4 text-muted">饮品ID:</div>
                    <div class="col-sm-8">{{ queueDetail.targetDrinkId }}</div>
                  </div>
                  <div class="row mb-3">
                    <div class="col-sm-4 text-muted">饮品名称:</div>
                    <div class="col-sm-8">
                      <strong>{{ queueDetail.drinkName }}</strong>
                    </div>
                  </div>
                  <div class="row mb-3">
                    <div class="col-sm-4 text-muted">饮品选项:</div>
                    <div class="col-sm-8">
                      <span class="text-info">{{ formatOptions(queueDetail.drinkOptions) }}</span>
                    </div>
                  </div>
                  <div class="row mb-3">
                    <div class="col-sm-4 text-muted">数量:</div>
                    <div class="col-sm-8">
                      <span class="badge bg-primary">{{ queueDetail.quantity }} 杯</span>
                    </div>
                  </div>
                  <div class="row mb-3">
                    <div class="col-sm-4 text-muted">预计时长:</div>
                    <div class="col-sm-8">
                      {{ Math.round(queueDetail.estimatedDuration / 60) }} 分钟
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 时间信息 -->
            <div class="col-12 mt-3">
              <div class="card">
                <div class="card-header">
                  <h6 class="mb-0">
                    <i class="flaticon-time me-2"></i>时间信息
                  </h6>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-3">
                      <div class="text-muted mb-1">创建时间</div>
                      <div>{{ formatDateTime(queueDetail.createTime) }}</div>
                    </div>
                    <div class="col-md-3">
                      <div class="text-muted mb-1">开始时间</div>
                      <div>
                        {{ queueDetail.startTime ? formatDateTime(queueDetail.startTime) : '-' }}
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="text-muted mb-1">完成时间</div>
                      <div>
                        {{ queueDetail.finishTime ? formatDateTime(queueDetail.finishTime) : '-' }}
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="text-muted mb-1">实际耗时</div>
                      <div>
                        <span v-if="queueDetail.startTime && queueDetail.finishTime" class="text-success">
                          {{ calculateDuration(queueDetail.startTime, queueDetail.finishTime) }}
                        </span>
                        <span v-else class="text-muted">-</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 失败信息 -->
            <div v-if="queueDetail.failureReason" class="col-12 mt-3">
              <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                  <h6 class="mb-0">
                    <i class="flaticon-warning me-2"></i>失败信息
                  </h6>
                </div>
                <div class="card-body">
                  <div class="alert alert-danger mb-0">
                    <strong>失败原因：</strong>
                    <br>
                    {{ queueDetail.failureReason }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 其他信息 -->
            <div class="col-12 mt-3">
              <div class="card">
                <div class="card-header">
                  <h6 class="mb-0">
                    <i class="flaticon-settings me-2"></i>其他信息
                  </h6>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-4">
                      <div class="text-muted mb-1">命令ID</div>
                      <div>{{ queueDetail.commandId || '-' }}</div>
                    </div>
                    <div class="col-md-4">
                      <div class="text-muted mb-1">创建人</div>
                      <div>{{ queueDetail.createBy || '系统' }}</div>
                    </div>
                    <div class="col-md-4">
                      <div class="text-muted mb-1">更新时间</div>
                      <div>{{ formatDateTime(queueDetail.updateTime) }}</div>
                    </div>
                  </div>
                  <div v-if="queueDetail.remark" class="row mt-3">
                    <div class="col-12">
                      <div class="text-muted mb-1">备注</div>
                      <div class="alert alert-info mb-0">{{ queueDetail.remark }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 错误状态 -->
          <div v-else class="text-center py-4">
            <i class="flaticon-warning text-danger" style="font-size: 3rem;"></i>
            <h5 class="mt-3 text-danger">加载失败</h5>
            <p class="text-muted">无法获取队列详情信息</p>
            <button @click="loadQueueDetail" class="btn btn-outline-primary">
              <i class="flaticon-refresh me-2"></i>重新加载
            </button>
          </div>
        </div>

        <!-- 弹窗底部 -->
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('close')">
            关闭
          </button>
          <button 
            v-if="queueDetail" 
            @click="$emit('refresh')" 
            class="btn btn-outline-primary"
          >
            <i class="flaticon-refresh me-2"></i>刷新列表
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { format, differenceInMinutes } from 'date-fns'
import { getQueueDetail, getQueueStatusName, getQueueStatusClass } from '@/utils/api/queue'
import type { ProductionQueue } from '@/utils/api/queue'

// Props
interface Props {
  queueId: number | string
}

const props = defineProps<Props>()

// Emits
defineEmits<{
  close: []
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const queueDetail = ref<ProductionQueue | null>(null)

// 加载队列详情
const loadQueueDetail = async () => {
  loading.value = true
  try {
    console.log(`🔄 [队列详情] 开始加载队列${props.queueId}的详情...`)
    
    const response = await getQueueDetail(props.queueId.toString())
    
    if (response.code === 200) {
      queueDetail.value = response.data
      console.log('✅ [队列详情] 队列详情加载成功:', queueDetail.value)
    } else {
      console.error('❌ [队列详情] API返回错误:', response.msg)
      queueDetail.value = null
    }
  } catch (error) {
    console.error('❌ [队列详情] 加载队列详情失败:', error)
    queueDetail.value = null
  } finally {
    loading.value = false
  }
}

// 格式化时间
const formatDateTime = (dateTime: string): string => {
  if (!dateTime) return '-'
  try {
    return format(new Date(dateTime), 'yyyy-MM-dd HH:mm:ss')
  } catch {
    return dateTime
  }
}

// 格式化选项
const formatOptions = (options: string): string => {
  if (!options) return '-'
  return options.replace(/\./g, ' · ')
}

// 计算耗时
const calculateDuration = (startTime: string, endTime: string): string => {
  if (!startTime || !endTime) return '-'
  try {
    const start = new Date(startTime)
    const end = new Date(endTime)
    const minutes = differenceInMinutes(end, start)
    
    if (minutes < 1) {
      return '< 1分钟'
    } else if (minutes < 60) {
      return `${minutes}分钟`
    } else {
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60
      return `${hours}小时${remainingMinutes}分钟`
    }
  } catch {
    return '-'
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadQueueDetail()
})
</script>

<style scoped>
.modal {
  z-index: 1055;
}

.card {
  border: 1px solid #e3e6f0;
}

.card-header {
  background-color: #f8f9fc;
  border-bottom: 1px solid #e3e6f0;
}

.badge {
  font-size: 0.875em;
}

code {
  font-size: 0.875em;
  color: #6c757d;
  background-color: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}
</style>
