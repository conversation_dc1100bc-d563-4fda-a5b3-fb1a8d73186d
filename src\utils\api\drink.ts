import serviceAxios from "@/utils/serviceAxios";
import type {queryPageParams} from "@/types/queryPageParams";
import type {TableDataInfo} from "@/types/tableDataInfo";
import {drinkPrototype, drinkGroup} from "@/types";

// ==================== 饮品原型管理 API ====================

// 原型查询参数
export interface PrototypeQueryParams extends queryPageParams {
  prototypeName?: string
  drinkGroupId?: number
  status?: string
}

/**
 * 获取饮品原型列表
 */
export function getDrinkPrototypeList(params: PrototypeQueryParams): Promise<TableDataInfo<drinkPrototype>> {
    return serviceAxios.get("/manager/prototype/list", {params});
}

/**
 * 获取饮品原型详细信息
 */
export function getPrototypeDetail(prototypeId: number): Promise<drinkPrototype> {
    return serviceAxios.get(`/manager/prototype/${prototypeId}`);
}

/**
 * 新增饮品原型
 */
export function createPrototype(data: Partial<drinkPrototype>): Promise<void> {
    return serviceAxios.post('/manager/prototype', data);
}

/**
 * 修改饮品原型
 */
export function updatePrototype(data: drinkPrototype): Promise<void> {
    return serviceAxios.put('/manager/prototype', data);
}

/**
 * 删除饮品原型
 */
export function deletePrototype(prototypeIds: string): Promise<void> {
    return serviceAxios.delete(`/manager/prototype/${prototypeIds}`);
}

/**
 * 根据分类查询饮品原型列表
 */
export function getPrototypesByGroup(drinkGroupId: number, status?: string): Promise<TableDataInfo<drinkPrototype>> {
    const params = status ? { status } : {};
    return serviceAxios.get(`/manager/prototype/group/${drinkGroupId}`, { params });
}

/**
 * 获取热门饮品原型
 */
export function getPopularPrototypes(limit?: number): Promise<drinkPrototype[]> {
    const params = limit ? { limit } : {};
    return serviceAxios.get('/manager/prototype/popular', { params });
}

/**
 * 导出饮品原型列表
 */
export function exportPrototypeList(params: PrototypeQueryParams): Promise<Blob> {
    return serviceAxios.post('/manager/prototype/export', params, {
        responseType: 'blob'
    });
}

// ==================== 饮品分类管理 API ====================

// 分类查询参数
export interface GroupQueryParams extends queryPageParams {
  groupName?: string
  status?: string
}

/**
 * 获取饮品分类列表
 */
export function getDrinkGroupList(params: GroupQueryParams): Promise<TableDataInfo<drinkGroup>> {
    return serviceAxios.get("/manager/group/list", {params});
}

/**
 * 获取启用的饮品分类列表
 */
export function getEnabledDrinkGroups(): Promise<TableDataInfo<drinkGroup>> {
    return serviceAxios.get("/manager/group/enabled");
}

/**
 * 获取饮品分类详细信息
 */
export function getGroupDetail(groupId: number): Promise<drinkGroup> {
    return serviceAxios.get(`/manager/group/${groupId}`);
}

/**
 * 新增饮品分类
 */
export function createGroup(data: Partial<drinkGroup>): Promise<void> {
    return serviceAxios.post('/manager/group', data);
}

/**
 * 修改饮品分类
 */
export function updateGroup(data: drinkGroup): Promise<void> {
    return serviceAxios.put('/manager/group', data);
}

/**
 * 删除饮品分类
 */
export function deleteGroup(groupIds: string): Promise<void> {
    return serviceAxios.delete(`/manager/group/${groupIds}`);
}

/**
 * 导出饮品分类列表
 */
export function exportGroupList(params: GroupQueryParams): Promise<Blob> {
    return serviceAxios.post('/manager/group/export', params, {
        responseType: 'blob'
    });
}