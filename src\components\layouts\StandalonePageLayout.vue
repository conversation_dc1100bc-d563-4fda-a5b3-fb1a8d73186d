<template>
  <div class="standalone-page-layout">
    <!-- 统一顶部导航栏 -->
    <header class="standalone-header bg-white shadow-sm">
      <div class="container-fluid">
        <div class="row align-items-center py-3">
          <!-- 左侧：Logo和页面标题 -->
          <div class="col-md-6">
            <div class="d-flex align-items-center">
              <!-- Logo -->
              <router-link to="/" class="d-flex align-items-center text-decoration-none me-4">
                <img src="../../assets/images/favicon.png" alt="logo" width="32" height="32" />
                <span class="fw-bold ms-2 text-dark">创界智联</span>
              </router-link>
              
              <!-- 页面标题 -->
              <div class="page-title-section">
                <h1 class="page-title mb-0">{{ pageTitle }}</h1>
                <nav v-if="breadcrumbs.length > 0" class="breadcrumb-nav">
                  <ol class="breadcrumb mb-0 small">
                    <li class="breadcrumb-item">
                      <router-link to="/" class="text-decoration-none">首页</router-link>
                    </li>
                    <li 
                      v-for="(crumb, index) in breadcrumbs" 
                      :key="index"
                      :class="['breadcrumb-item', { active: index === breadcrumbs.length - 1 }]"
                    >
                      <router-link 
                        v-if="crumb.url && index !== breadcrumbs.length - 1" 
                        :to="crumb.url" 
                        class="text-decoration-none"
                      >
                        {{ crumb.label }}
                      </router-link>
                      <span v-else>{{ crumb.label }}</span>
                    </li>
                  </ol>
                </nav>
              </div>
            </div>
          </div>
          
          <!-- 右侧：快捷导航和用户信息 -->
          <div class="col-md-6">
            <div class="d-flex align-items-center justify-content-end">
              <!-- 快捷导航菜单 -->
              <div class="dropdown me-3" ref="quickNavDropdown">
                <button
                  class="btn btn-outline-primary btn-sm dropdown-toggle"
                  type="button"
                  :disabled="isNavigating"
                  @click="toggleDropdown"
                >
                  <i v-if="!isNavigating" class="flaticon-menu me-1"></i>
                  <div v-else class="spinner-border spinner-border-sm me-1" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                  {{ isNavigating ? '跳转中...' : '快捷导航' }}
                </button>
                <ul class="dropdown-menu quick-nav-menu" :class="{ show: dropdownVisible }">
                  <li>
                    <a
                      href="#"
                      @click.prevent="navigateTo('/')"
                      :class="['dropdown-item', { active: isCurrentRoute('/') }]"
                    >
                      <i class="flaticon-home me-2"></i>
                      <span>系统总览</span>
                      <i v-if="isCurrentRoute('/')" class="flaticon-check ms-auto text-success"></i>
                    </a>
                  </li>
                  <li><hr class="dropdown-divider"></li>
                  <li v-for="navItem in navigationItems" :key="navItem.path">
                    <a
                      href="#"
                      @click.prevent="navigateTo(navItem.path)"
                      :class="['dropdown-item', { active: isCurrentRoute(navItem.path) }]"
                    >
                      <i :class="[navItem.icon, 'me-2']"></i>
                      <span>{{ navItem.label }}</span>
                      <i v-if="isCurrentRoute(navItem.path)" class="flaticon-check ms-auto text-success"></i>
                    </a>
                  </li>
                </ul>
              </div>
              
              <!-- 用户信息 -->
              <div class="dropdown">
                <button
                  class="btn btn-link text-decoration-none d-flex align-items-center p-0"
                  type="button"
                  data-bs-toggle="dropdown"
                >
                  <img
                    src="../../assets/images/admin.jpg"
                    class="rounded"
                    width="36"
                    height="36"
                    alt="admin"
                  />
                  <span class="ms-2 d-none d-lg-block">
                    <span class="d-block fw-bold small">Victor James</span>
                    <span class="text-muted small">Admin</span>
                  </span>
                  <i class="flaticon-down-arrow ms-2"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                  <li>
                    <router-link to="/profile" class="dropdown-item">
                      <i class="flaticon-user me-2"></i>个人资料
                    </router-link>
                  </li>
                  <li>
                    <router-link to="/settings" class="dropdown-item">
                      <i class="flaticon-settings me-2"></i>设置
                    </router-link>
                  </li>
                  <li><hr class="dropdown-divider"></li>
                  <li>
                    <a href="#" class="dropdown-item" @click="logout">
                      <i class="flaticon-logout me-2"></i>退出登录
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
    
    <!-- 主要内容区域 -->
    <main class="standalone-main">
      <div class="container-fluid">
        <slot></slot>
      </div>
    </main>
    
    <!-- 底部 -->
    <footer class="standalone-footer bg-light text-center py-3">
      <div class="container-fluid">
        <p class="mb-0 text-muted small">
          © 2025 创界智联. All rights reserved.
        </p>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/store/useAuthStore'

// Props
interface Props {
  pageTitle?: string
  breadcrumbs?: Array<{ label: string; url?: string }>
}

const props = withDefaults(defineProps<Props>(), {
  pageTitle: '系统页面',
  breadcrumbs: () => []
})

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 导航状态
const isNavigating = ref(false)

// 下拉菜单状态
const dropdownVisible = ref(false)
const quickNavDropdown = ref<HTMLElement>()

// 导航菜单项配置
const navigationItems = ref([
  {
    path: '/dashboard',
    label: '数据总览',
    icon: 'flaticon-chart'
  },
  {
    path: '/orders',
    label: '订单管理',
    icon: 'flaticon-shopping-cart'
  },
  {
    path: '/stores',
    label: '门店管理',
    icon: 'flaticon-store'
  },
  {
    path: '/merchant-management',
    label: '商户管理',
    icon: 'flaticon-user-1'
  },
  {
    path: '/product-management',
    label: '商品管理',
    icon: 'flaticon-coffee-cup'
  },
  {
    path: '/data-center',
    label: '数据中心',
    icon: 'flaticon-analytics'
  },
  {
    path: '/device-management',
    label: '设备运维',
    icon: 'flaticon-robot'
  },
  {
    path: '/system-management',
    label: '系统配置',
    icon: 'flaticon-settings'
  },
  {
    path: '/all-stores-fullscreen',
    label: '大屏看板',
    icon: 'flaticon-monitor'
  },
  {
    path: '/test/product-api',
    label: 'API测试',
    icon: 'flaticon-code'
  }
])

// 根据路由自动生成页面标题
const pageTitle = computed(() => {
  if (props.pageTitle !== '系统页面') {
    return props.pageTitle
  }

  const titleMap: Record<string, string> = {
    '/': '系统总览',
    '/overview-workspace': '系统总览',
    '/dashboard': '数据总览',
    '/orders': '订单管理',
    '/stores': '门店管理',
    '/product-management': '商品管理',
    '/data-center': '数据中心',
    '/device-management': '设备运维',
    '/system-management': '系统配置',
    '/all-stores-fullscreen': '大屏看板'
  }

  return titleMap[route.path] || '系统页面'
})

// 检查是否为当前路由
const isCurrentRoute = (path: string) => {
  if (path === '/') {
    return route.path === '/' || route.path === '/overview-workspace'
  }
  return route.path === path
}

// 切换下拉菜单显示状态
const toggleDropdown = () => {
  dropdownVisible.value = !dropdownVisible.value
}

// 关闭下拉菜单
const closeDropdown = () => {
  dropdownVisible.value = false
}

// 点击外部关闭下拉菜单
const handleClickOutside = (event: Event) => {
  if (quickNavDropdown.value && !quickNavDropdown.value.contains(event.target as Node)) {
    closeDropdown()
  }
}

// 导航到指定页面
const navigateTo = async (path: string) => {
  if (isCurrentRoute(path)) {
    closeDropdown()
    return // 如果已经在当前页面，不需要跳转
  }

  try {
    isNavigating.value = true
    closeDropdown() // 关闭下拉菜单

    // 添加一个小延迟以显示加载状态
    await new Promise(resolve => setTimeout(resolve, 300))

    await router.push(path)
  } catch (error) {
    console.error('导航失败:', error)
  } finally {
    isNavigating.value = false
  }
}

// 退出登录
const logout = async () => {
  try {
    await authStore.logout()
    router.push('/login')
  } catch (error) {
    console.error('退出登录失败:', error)
    // 即使退出失败，也跳转到登录页
    router.push('/login')
  }
}

// 生命周期钩子
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.standalone-page-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.standalone-header {
  position: sticky;
  top: 0;
  z-index: 1020;
  border-bottom: 1px solid #e9ecef;
}

.standalone-main {
  flex: 1;
  padding: 20px 0;
  background-color: #f8f9fa;
}

.standalone-footer {
  border-top: 1px solid #e9ecef;
  margin-top: auto;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
}

.breadcrumb {
  background: none;
  padding: 0;
  margin: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
  content: ">";
  color: #6c757d;
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-outline-primary:hover {
  background-color: #007bff;
  border-color: #007bff;
}

/* 快捷导航下拉菜单样式 */
.quick-nav-menu {
  border: 1px solid #e9ecef;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  padding: 8px 0;
  min-width: 220px;
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  background-color: #fff;
  margin-top: 2px;
}

.quick-nav-menu.show {
  display: block;
}

.quick-nav-menu .dropdown-item {
  padding: 12px 16px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  border-radius: 0;
  position: relative;
  font-weight: 500;
}

.quick-nav-menu .dropdown-item:hover {
  background-color: #f8f9fa;
  color: #007bff;
  transform: translateX(2px);
}

.quick-nav-menu .dropdown-item.active {
  background-color: #e3f2fd;
  color: #007bff;
  font-weight: 600;
  border-left: 3px solid #007bff;
  padding-left: 13px;
}

.quick-nav-menu .dropdown-item.active:hover {
  background-color: #e3f2fd;
  transform: none;
}

.quick-nav-menu .dropdown-item i {
  width: 18px;
  text-align: center;
  font-size: 14px;
}

.quick-nav-menu .dropdown-item span {
  flex: 1;
}

.quick-nav-menu .dropdown-item .ms-auto {
  margin-left: auto;
  font-size: 12px;
}

.quick-nav-menu .dropdown-divider {
  margin: 8px 0;
  border-color: #e9ecef;
}

/* 导航按钮加载状态 */
.btn:disabled {
  opacity: 0.8;
  cursor: not-allowed;
}

.spinner-border-sm {
  width: 0.875rem;
  height: 0.875rem;
}

/* 通用下拉菜单样式 */
.dropdown-menu {
  border: 1px solid #e9ecef;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dropdown-item {
  padding: 0.5rem 1rem;
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item i {
  width: 16px;
  text-align: center;
}

@media (max-width: 768px) {
  .page-title {
    font-size: 1.25rem;
  }
  
  .standalone-main {
    padding: 15px 0;
  }
  
  .breadcrumb {
    font-size: 0.875rem;
  }
}
</style>
