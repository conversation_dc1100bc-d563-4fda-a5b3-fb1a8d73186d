// 模拟订单详情数据，用于测试
export const mockOrderDetailResponse = {
  "msg": "操作成功",
  "code": 200,
  "data": {
    "createBy": "",
    "createTime": null,
    "updateBy": "",
    "updateTime": null,
    "remark": "所有制作任务已完成",
    "orderId": 287,
    "orderReqSeq": "TEST_20250715182345_EE4A68D1",
    "storeId": 100,
    "orderSource": "pad",
    "userPhone": "13800138001",
    "orderStatus": "3",
    "orderItems": "[{\"unitPrice\":\"7.70\",\"quantity\":1,\"productId\":203,\"targetId\":\"202\",\"subtotal\":\"7.70\",\"options\":\"正常冰.正常糖\",\"productName\":\"商品ID=203\"}]",
    "totalAmount": "7.70",
    "orderTime": "2025-07-15T18:23:46",
    "paymentTime": "2025-07-15T18:23:49",
    "paymentOrderNo": "PAY_TEST_20250715182345_EE4A68D1_1752575025555",
    "pickupNo": null,
    "orderItemsList": [
      {
        "createBy": null,
        "createTime": null,
        "updateBy": null,
        "updateTime": null,
        "remark": null,
        "drinkId": null,
        "drinkPrototype": null,
        "storeId": null,
        "options": "正常冰.正常糖",
        "targetId": "202",
        "productPrice": null,
        "status": null
      }
    ]
  }
}

// 解析后的订单商品数据示例
export const parsedOrderItems = [
  {
    productId: 203,
    productName: "商品ID=203",
    options: "正常冰.正常糖",
    quantity: 1,
    unitPrice: "7.70",
    subtotal: "7.70",
    targetId: "202"
  }
]

// 订单状态映射
export const orderStatusMap = {
  '0': '待支付',
  '1': '已支付',
  '2': '制作中',
  '3': '待取餐',
  '4': '已完成',
  '5': '已取消'
}

// 订单来源映射
export const orderSourceMap = {
  'wx': '微信小程序',
  'pad': '线下点单'
}
