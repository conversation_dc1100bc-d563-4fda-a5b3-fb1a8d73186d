import serviceAxios from '@/utils/serviceAxios'
import type { TableDataInfo } from '@/types/tableDataInfo'

// 用户信息接口
export interface SystemUser {
  userId?: number
  userName: string
  nickName: string
  password?: string
  deptId?: number
  phonenumber?: string
  email?: string
  sex?: string
  status: string
  roleIds?: number[]
  postIds?: number[]
  remark?: string
  createTime?: string
  updateTime?: string
}

// 用户查询参数
export interface UserQueryParams {
  pageNum: number
  pageSize: number
  userName?: string
  phonenumber?: string
  status?: string
  deptId?: number
}

// 角色信息接口
export interface SystemRole {
  roleId?: number
  roleName: string
  roleKey: string
  roleSort?: number
  dataScope?: string
  menuCheckStrictly?: boolean
  deptCheckStrictly?: boolean
  status: string
  delFlag?: string
  createTime?: string
  updateTime?: string
  remark?: string
}

// 角色查询参数
export interface RoleQueryParams {
  pageNum: number
  pageSize: number
  roleName?: string
  roleKey?: string
  status?: string
}

/**
 * 获取用户列表
 */
export function getUserList(params: UserQueryParams): Promise<TableDataInfo<SystemUser>> {
  return serviceAxios.get('/system/user/list', { params })
}

/**
 * 获取用户详细信息
 */
export function getUserDetail(userId: number): Promise<SystemUser> {
  return serviceAxios.get(`/system/user/${userId}`)
}

/**
 * 新增用户
 */
export function createUser(data: SystemUser): Promise<void> {
  return serviceAxios.post('/system/user', data)
}

/**
 * 修改用户
 */
export function updateUser(data: SystemUser): Promise<void> {
  return serviceAxios.put('/system/user', data)
}

/**
 * 删除用户
 */
export function deleteUser(userId: number): Promise<void> {
  return serviceAxios.delete(`/system/user/${userId}`)
}

/**
 * 批量删除用户
 */
export function deleteUsers(userIds: number[]): Promise<void> {
  return serviceAxios.delete(`/system/user/${userIds.join(',')}`)
}

/**
 * 重置用户密码
 */
export function resetUserPassword(userId: number, password: string): Promise<void> {
  return serviceAxios.put('/system/user/resetPwd', { userId, password })
}

/**
 * 修改用户状态
 */
export function changeUserStatus(userId: number, status: string): Promise<void> {
  return serviceAxios.put('/system/user/changeStatus', { userId, status })
}

/**
 * 获取角色列表
 */
export function getRoleList(params: RoleQueryParams): Promise<TableDataInfo<SystemRole>> {
  return serviceAxios.get('/system/role/list', { params })
}

/**
 * 获取角色详细信息
 */
export function getRoleDetail(roleId: number): Promise<SystemRole> {
  return serviceAxios.get(`/system/role/${roleId}`)
}

/**
 * 新增角色
 */
export function createRole(data: SystemRole): Promise<void> {
  return serviceAxios.post('/system/role', data)
}

/**
 * 修改角色
 */
export function updateRole(data: SystemRole): Promise<void> {
  return serviceAxios.put('/system/role', data)
}

/**
 * 删除角色
 */
export function deleteRole(roleId: number): Promise<void> {
  return serviceAxios.delete(`/system/role/${roleId}`)
}

/**
 * 批量删除角色
 */
export function deleteRoles(roleIds: number[]): Promise<void> {
  return serviceAxios.delete(`/system/role/${roleIds.join(',')}`)
}

/**
 * 修改角色状态
 */
export function changeRoleStatus(roleId: number, status: string): Promise<void> {
  return serviceAxios.put('/system/role/changeStatus', { roleId, status })
}

/**
 * 获取角色选择框列表
 */
export function getRoleOptions(): Promise<SystemRole[]> {
  return serviceAxios.get('/system/role/optionselect')
}

/**
 * 查询已分配给角色的用户列表
 */
export function getAllocatedUserList(roleId: number, params: UserQueryParams): Promise<TableDataInfo<SystemUser>> {
  return serviceAxios.get(`/system/role/authUser/allocatedList`, { 
    params: { ...params, roleId } 
  })
}

/**
 * 查询未分配给角色的用户列表
 */
export function getUnallocatedUserList(roleId: number, params: UserQueryParams): Promise<TableDataInfo<SystemUser>> {
  return serviceAxios.get(`/system/role/authUser/unallocatedList`, { 
    params: { ...params, roleId } 
  })
}

/**
 * 批量为用户分配角色
 */
export function selectAuthUserAll(roleId: number, userIds: number[]): Promise<void> {
  return serviceAxios.put('/system/role/authUser/selectAll', { 
    roleId, 
    userIds: userIds.join(',') 
  })
}

/**
 * 取消单个用户的角色授权
 */
export function cancelAuthUser(roleId: number, userId: number): Promise<void> {
  return serviceAxios.put('/system/role/authUser/cancel', { roleId, userId })
}

/**
 * 批量取消用户的角色授权
 */
export function cancelAuthUserAll(roleId: number, userIds: number[]): Promise<void> {
  return serviceAxios.put('/system/role/authUser/cancelAll', {
    roleId,
    userIds: userIds.join(',')
  })
}

/**
 * ==================== 权限管理 API ====================
 */

// 权限信息接口
export interface SystemPermission {
  permissionId?: number
  permissionName: string
  permissionKey: string
  permissionType: string // menu, button, api
  parentId?: number
  orderNum?: number
  path?: string
  component?: string
  icon?: string
  status: string
  createTime?: string
  updateTime?: string
  remark?: string
}

// 菜单权限接口
export interface MenuPermission {
  menuId?: number
  menuName: string
  parentId?: number
  orderNum?: number
  path?: string
  component?: string
  menuType: string // M目录 C菜单 F按钮
  visible?: string
  status: string
  perms?: string
  icon?: string
  createTime?: string
  updateTime?: string
  remark?: string
  children?: MenuPermission[]
}

/**
 * 获取角色权限列表
 */
export function getRolePermissions(roleId: number): Promise<number[]> {
  return serviceAxios.get(`/system/role/authMenu/${roleId}`)
}

/**
 * 修改角色权限
 */
export function updateRolePermissions(roleId: number, menuIds: number[]): Promise<void> {
  return serviceAxios.put('/system/role/authMenu', {
    roleId,
    menuIds: menuIds.join(',')
  })
}

/**
 * 获取菜单权限树
 */
export function getMenuTree(): Promise<MenuPermission[]> {
  return serviceAxios.get('/system/menu/treeselect')
}

/**
 * 获取角色菜单权限树
 */
export function getRoleMenuTree(roleId: number): Promise<{
  checkedKeys: number[]
  menus: MenuPermission[]
}> {
  return serviceAxios.get(`/system/menu/roleMenuTreeselect/${roleId}`)
}

/**
 * 获取权限列表
 */
export function getPermissionList(): Promise<SystemPermission[]> {
  return serviceAxios.get('/system/permission/list')
}

/**
 * 获取用户权限列表
 */
export function getUserPermissions(userId: number): Promise<string[]> {
  return serviceAxios.get(`/system/user/permissions/${userId}`)
}

/**
 * 获取角色权限代码列表
 */
export function getRolePermissionCodes(roleId: number): Promise<string[]> {
  return serviceAxios.get(`/system/role/permissions/${roleId}`)
}
