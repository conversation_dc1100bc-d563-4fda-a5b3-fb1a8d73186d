// 设备类型枚举
export enum DeviceType {
  KIOSK = 'kiosk',           // 咖啡机/自助终端
  ROBOT_ARM = 'robot_arm',   // 机械臂
  ICE_MAKER = 'ice_maker',   // 制冰机
  GRINDER = 'grinder',       // 研磨机
  DISPENSER = 'dispenser'    // 分配器
}

// 设备状态枚举
export enum DeviceStatus {
  ONLINE = 'online',         // 在线
  OFFLINE = 'offline',       // 离线
  BUSY = 'busy',            // 忙碌中
  ERROR = 'error',          // 故障
  MAINTENANCE = 'maintenance' // 维护中
}

// 设备基本信息
export interface Device {
  deviceId: string;          // 设备ID
  deviceName: string;        // 设备名称
  deviceType: DeviceType;    // 设备类型
  storeId: number;          // 所属门店ID
  storeName?: string;       // 门店名称
  status: DeviceStatus;     // 设备状态
  lastOnlineTime: string;   // 最后在线时间
  ipAddress?: string;       // IP地址
  version?: string;         // 固件版本
  location?: string;        // 设备位置
  createTime: string;       // 创建时间
  updateTime: string;       // 更新时间
}

// 在线设备信息（匹配实际API返回格式）
export interface OnlineDevice {
  deviceId: string;
  deviceType: string;
  connectTime: number;
  lastHeartbeatTime: number;
  status: string;
  deviceVersion?: string;
  deviceSpecificData: string;
  runningWorkflows: Record<string, unknown>;
  capabilities: Record<string, unknown>;
  runningWorkflowCount: number;
  availableWorkflowCount: number;
  availableWorkflows: Record<string, unknown>;
  statusDetails: Record<string, unknown>;
  deviceStatusSummary: {
    deviceId: string;
    deviceType: string;
    lastHeartbeatTime: number;
    runningWorkflowCount: number;
    availableWorkflowCount: number;
    capabilities: Record<string, unknown>;
    statusDetails: Record<string, unknown>;
  };
  deviceSpecificDataAsJson: Record<string, unknown>;
  deviceSpecificInfo: string;
}

// 设备详细状态
export interface DeviceDetailStatus {
  deviceId: string;
  deviceName: string;
  deviceType: DeviceType;
  status: DeviceStatus;
  currentStep?: string;     // 当前执行步骤
  progress?: number;        // 执行进度 (0-100)
  temperature?: number;     // 温度
  pressure?: number;        // 压力
  errorCode?: string;       // 错误代码
  errorMessage?: string;    // 错误信息
  sensors?: {               // 传感器数据
    [key: string]: string | number | boolean;
  };
  lastUpdate: string;       // 最后更新时间
}

// 维修记录
export interface MaintenanceRecord {
  recordId: string;
  deviceId: string;
  deviceName: string;
  faultDescription: string;  // 故障描述
  maintenanceTime: string;   // 维修时间
  technician: string;        // 负责人
  solution: string;          // 处理措施
  status: 'pending' | 'in_progress' | 'completed'; // 维修状态
  cost?: number;            // 维修费用
  parts?: string[];         // 更换部件
  createTime: string;
  updateTime: string;
}

// 设备统计信息
export interface DeviceStatistics {
  totalDevices: number;     // 总设备数
  onlineDevices: number;    // 在线设备数
  offlineDevices: number;   // 离线设备数
  errorDevices: number;     // 故障设备数
  busyDevices: number;      // 忙碌设备数
  deviceTypeStats: {        // 按类型统计
    [key in DeviceType]?: {
      total: number;
      online: number;
      offline: number;
    };
  };
}

// API响应类型
export interface DeviceApiListResponse {
  code: number;
  msg: string;
  data: Device[];
}

export interface OnlineDeviceListResponse {
  code: number;
  msg: string;
  data: {
    deviceStatistics: {
      kiosk_online: number;
      kiosk_total: number;
    };
    allDevices: OnlineDevice[];
    supportedDeviceTypes: string[];
  };
}

export interface DeviceDetailResponse {
  code: number;
  msg: string;
  data: DeviceDetailStatus;
}
