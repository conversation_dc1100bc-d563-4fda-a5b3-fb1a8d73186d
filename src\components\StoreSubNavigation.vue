<template>
  <div class="store-sub-navigation">
    <!-- 顶部标签栏导航 (默认样式) -->
    <div v-if="navStyle === 'tabs'" class="nav-tabs-container">
      <div class="card border-0 rounded-0 bg-white box-shadow">
        <div class="card-body p-0">
          <ul class="nav nav-tabs border-0" role="tablist">
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                :class="{ active: currentRoute === 'details' }"
                @click="handleNavClick('details')"
                type="button"
              >
                <i class="flaticon-info me-2"></i>
                店铺总览
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                :class="{ active: currentRoute === 'business' }"
                @click="handleNavClick('business')"
                type="button"
              >
                <i class="flaticon-chart me-2"></i>
                业务看板
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                :class="{ active: currentRoute === 'dashboard' }"
                @click="handleNavClick('dashboard')"
                type="button"
              >
                <i class="flaticon-settings me-2"></i>
                属性看板
              </button>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 侧边栏导航 -->
    <div v-else-if="navStyle === 'sidebar'" class="nav-sidebar-container">
      <div class="row">
        <div class="col-lg-3 col-md-4">
          <div class="card border-0 rounded-0 bg-white box-shadow">
            <div class="card-body p-20">
              <h6 class="fw-bold mb-15 text-black">导航菜单</h6>
              <ul class="nav nav-pills flex-column">
                <li class="nav-item mb-2">
                  <button
                    class="nav-link w-100 text-start"
                    :class="{ active: currentRoute === 'details' }"
                    @click="handleNavClick('details')"
                  >
                    <i class="flaticon-info me-2"></i>
                    店铺总览
                  </button>
                </li>
                <li class="nav-item mb-2">
                  <button
                    class="nav-link w-100 text-start"
                    :class="{ active: currentRoute === 'business' }"
                    @click="handleNavClick('business')"
                  >
                    <i class="flaticon-chart me-2"></i>
                    业务看板
                  </button>
                </li>
                <li class="nav-item mb-2">
                  <button
                    class="nav-link w-100 text-start"
                    :class="{ active: currentRoute === 'dashboard' }"
                    @click="handleNavClick('dashboard')"
                  >
                    <i class="flaticon-settings me-2"></i>
                    属性看板
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="col-lg-9 col-md-8">
          <slot name="content"></slot>
        </div>
      </div>
    </div>

    <!-- 卡片式导航 -->
    <div v-else-if="navStyle === 'cards'" class="nav-cards-container">
      <div class="row mb-20">
        <div class="col-lg-4 col-md-6 mb-15">
          <div
            class="nav-card card border-0 rounded-0 bg-white box-shadow cursor-pointer transition"
            :class="{ active: currentRoute === 'details' }"
            @click="handleNavClick('details')"
          >
            <div class="card-body p-20 text-center">
              <i class="flaticon-info text-success mb-10" style="font-size: 2rem;"></i>
              <h6 class="fw-bold mb-5">店铺总览</h6>
              <p class="text-muted small mb-0">查看门店基本信息和详情</p>
            </div>
          </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-15">
          <div
            class="nav-card card border-0 rounded-0 bg-white box-shadow cursor-pointer transition"
            :class="{ active: currentRoute === 'business' }"
            @click="handleNavClick('business')"
          >
            <div class="card-body p-20 text-center">
              <i class="flaticon-chart text-primary mb-10" style="font-size: 2rem;"></i>
              <h6 class="fw-bold mb-5">业务看板</h6>
              <p class="text-muted small mb-0">查看营业数据和销售分析</p>
            </div>
          </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-15">
          <div
            class="nav-card card border-0 rounded-0 bg-white box-shadow cursor-pointer transition"
            :class="{ active: currentRoute === 'dashboard' }"
            @click="handleNavClick('dashboard')"
          >
            <div class="card-body p-20 text-center">
              <i class="flaticon-settings text-info mb-10" style="font-size: 2rem;"></i>
              <h6 class="fw-bold mb-5">属性看板</h6>
              <p class="text-muted small mb-0">查看设备状态和运营信息</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

// 定义属性
interface Props {
  currentRoute: string
  storeId: number
  navStyle?: 'tabs' | 'sidebar' | 'cards'
}

const props = withDefaults(defineProps<Props>(), {
  navStyle: 'tabs'
})

// 定义事件
const emit = defineEmits<{
  navigate: [route: string]
}>()

// 处理导航点击
function handleNavClick(route: string) {
  if (route !== props.currentRoute) {
    emit('navigate', route)
  }
}
</script>

<style scoped>
.store-sub-navigation {
  margin-bottom: 20px;
}

/* 标签栏样式 */
.nav-tabs-container .nav-tabs {
  padding: 0 20px;
}

.nav-tabs-container .nav-link {
  border: none;
  border-bottom: 3px solid transparent;
  background: none;
  color: #6c757d;
  font-weight: 500;
  padding: 15px 20px;
  transition: all 0.3s ease;
}

.nav-tabs-container .nav-link:hover {
  color: #495057;
  border-bottom-color: #dee2e6;
}

.nav-tabs-container .nav-link.active {
  color: #007bff;
  border-bottom-color: #007bff;
  background: none;
}

/* 侧边栏样式 */
.nav-sidebar-container .nav-pills .nav-link {
  border: none;
  background: none;
  color: #6c757d;
  font-weight: 500;
  padding: 12px 15px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.nav-sidebar-container .nav-pills .nav-link:hover {
  background-color: #f8f9fa;
  color: #495057;
}

.nav-sidebar-container .nav-pills .nav-link.active {
  background-color: #007bff;
  color: white;
}

/* 卡片式导航样式 */
.nav-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.nav-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.nav-card.active {
  border: 2px solid #007bff;
  transform: translateY(-2px);
}

.nav-card.active .card-body {
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
}

/* 通用样式 */
.box-shadow {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cursor-pointer {
  cursor: pointer;
}

.transition {
  transition: all 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-tabs-container .nav-tabs {
    padding: 0 10px;
    flex-wrap: nowrap;
    overflow-x: auto;
  }
  
  .nav-tabs-container .nav-link {
    padding: 12px 15px;
    white-space: nowrap;
  }
  
  .nav-cards-container .col-lg-4 {
    margin-bottom: 10px;
  }
}
</style>
