// src/utils/api/product.ts
import serviceAxios from "@/utils/serviceAxios";
import type { TableDataInfo } from '@/types/tableDataInfo'
import type { queryPageParams } from '@/types/queryPageParams'
import type { drinkProduct } from '@/types/drink'

// 定义从API返回的单个产品数据的类型
export interface ProductDetail {
    drinkId: number;
    drinkPrototype: number;
    storeId: number;
    options: string;
    targetId: string;
    productPrice: string;
    status: string;
}

// 定义后端返回的完整JSON负载的类型
interface ApiFullResponse<T> {
    code: number;
    msg: string;
    data: T;
}

// 创建一个简单的内存缓存
const productCache = new Map<number, ProductDetail>();

/**
 * 根据产品ID获取产品详情。
 * @param id - 产品ID
 * @returns 返回产品详情数据 (ProductDetail) 或在找不到时返回 null
 */
export async function getProductDetailById(id: number): Promise<ProductDetail | null> {
    // 1. 检查缓存
    if (productCache.has(id)) {
        console.log(`[Cache] Hit for productId: ${id}`);
        return Promise.resolve(productCache.get(id)!);
    }

    // 2. 发起API请求
    console.log(`[API] Fetching for productId: ${id}`);

    // 【关键修复】
    // serviceAxios 拦截器已经处理了外层，直接返回了后端 JSON。
    // 所以我们用 ApiFullResponse<ProductDetail> 类型来接收 await 的结果。
    const backendPayload: ApiFullResponse<ProductDetail> = await serviceAxios({
        url: `/manager/product/${id}`,
        method: 'get',
    });

    // 现在，backendPayload 的值就是 {"msg":"...", "code":200, "data":{...}}

    // 我们直接从 backendPayload 中提取核心数据 .data
    if (backendPayload && backendPayload.data) {
        // 3. 将核心数据存入缓存
        productCache.set(id, backendPayload.data);

        // 4. 返回核心数据
        return backendPayload.data;
    } else {
        // 如果后端返回了 code: 200 但 data 字段确实是 null 或不存在
        console.warn(`产品(id: ${id})查询成功，但数据为空，将忽略此商品。`);
        return null;
    }
}

export interface PrototypeDetail {
    prototypeId: number;
    prototypeName: string;
    prototypeDescription: string;
    prototypeImage: string;
    // 其他您可能需要的字段
}

// 【新】为原型详情也创建一个缓存，避免重复请求
const prototypeCache = new Map<number, PrototypeDetail>();

/**
 * 【新】根据原型ID获取原型详情
 * @param id - 原型ID
 * @returns 返回原型详情数据 (PrototypeDetail) 或在找不到时返回 null
 */
export async function getPrototypeDetailById(id: number): Promise<PrototypeDetail | null> {
    // 1. 检查缓存
    if (prototypeCache.has(id)) {
        return Promise.resolve(prototypeCache.get(id)!);
    }

    // 2. 发起API请求
    const backendPayload: ApiFullResponse<PrototypeDetail> = await serviceAxios({
        url: `/manager/prototype/${id}`,
        method: 'get',
    });

    // 3. 处理返回结果
    if (backendPayload && backendPayload.data) {
        prototypeCache.set(id, backendPayload.data);
        return backendPayload.data;
    } else {
        console.warn(`原型(id: ${id})查询成功，但数据为空。`);
        return null;
    }
}

// ==================== 扩展的商品管理 API ====================

// 商品查询参数
export interface ProductQueryParams extends queryPageParams {
  storeId?: number
  drinkPrototype?: number
  status?: string
  productName?: string
}

/**
 * 获取商品列表
 */
export function getProductList(params: ProductQueryParams): Promise<TableDataInfo<drinkProduct>> {
  return serviceAxios.get('/manager/product/list', { params })
}

/**
 * 根据门店查询商品列表 (获取门店菜单)
 */
export function getStoreMenu(storeId: number, params?: Partial<ProductQueryParams>): Promise<TableDataInfo<drinkProduct>> {
  return serviceAxios.get(`/manager/product/store/${storeId}`, { params })
}

/**
 * 新增商品 (上架商品)
 */
export function createProduct(data: {
  drinkPrototype: number
  storeId: number
  productPrice: string
  targetId: string
  status: string
  options?: string
  remark?: string
}): Promise<void> {
  return serviceAxios.post('/manager/product', data)
}

/**
 * 修改商品信息
 */
export function updateProduct(data: drinkProduct): Promise<void> {
  return serviceAxios.put('/manager/product', data)
}

/**
 * 删除商品 (下架商品)
 */
export function deleteProduct(drinkId: number): Promise<void> {
  return serviceAxios.delete(`/manager/product/${drinkId}`)
}

/**
 * 修改商品状态 (上架/下架)
 */
export function changeProductStatus(drinkId: number, status: string): Promise<void> {
  return serviceAxios.put('/manager/product/changeStatus', { drinkId, status })
}