<template>
  <div class="product-options-editor">
    <!-- 选项配置区域 -->
    <div class="options-config mb-4">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h6 class="mb-0">
          <i class="flaticon-settings me-2"></i>
          商品自定义选项配置
        </h6>
        <button @click="addOption" type="button" class="btn btn-sm btn-primary">
          <i class="flaticon-plus me-1"></i>
          添加选项
        </button>
      </div>

      <!-- 选项列表 -->
      <div v-if="optionsList.length === 0" class="text-center py-4 text-muted">
        <i class="flaticon-info-circle fs-1 mb-2"></i>
        <p>暂无自定义选项，点击"添加选项"开始配置</p>
      </div>

      <div v-else class="options-list">
        <div
          v-for="(option, index) in optionsList"
          :key="index"
          class="option-item card mb-3"
        >
          <div class="card-body">
            <div class="d-flex align-items-center">
              <input
                v-model="option.value"
                type="text"
                class="form-control me-2"
                placeholder="输入选项内容，如：正常冰、少糖、大杯等"
              >
              <button
                @click="removeOption(index)"
                type="button"
                class="btn btn-sm btn-outline-danger"
              >
                <i class="flaticon-delete"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 预览区域 -->
      <div v-if="optionsList.length > 0" class="mt-3">
        <label class="form-label">选项预览：</label>
        <div class="alert alert-info">
          <strong>最终格式：</strong> {{ previewText }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'

// Props
interface Props {
  modelValue?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: ''
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

// 简化的选项数据结构
interface SimpleOption {
  value: string
}

// 内部状态
const optionsList = ref<SimpleOption[]>([])

// 初始化数据 - 从点分隔字符串解析
const initializeFromString = (optionsString: string) => {
  if (!optionsString || optionsString.trim() === '') {
    optionsList.value = []
    return
  }

  // 按点号分割字符串
  const parts = optionsString.split('.').filter(part => part.trim() !== '')
  optionsList.value = parts.map(part => ({
    value: part.trim()
  }))
}

// 生成点分隔字符串
const generateString = (): string => {
  return optionsList.value
    .map(option => option.value.trim())
    .filter(value => value !== '')
    .join('.')
}

// 预览文本
const previewText = computed(() => {
  return generateString() || '(空)'
})

// 监听props变化，但不立即emit
watch(() => props.modelValue, (newValue) => {
  initializeFromString(newValue)
}, { immediate: true })

// 只在组件销毁前或父组件需要时才更新数据
const updateParentData = () => {
  const optionsString = generateString()
  emit('update:modelValue', optionsString)
}

// 添加选项 - 不立即更新父组件
const addOption = () => {
  optionsList.value.push({
    value: ''
  })
  // 注意：这里不调用updateParentData()
}

// 删除选项 - 不立即更新父组件
const removeOption = (index: number) => {
  optionsList.value.splice(index, 1)
  // 注意：这里不调用updateParentData()
}

// 暴露给父组件的方法，用于在保存时获取数据
defineExpose({
  updateParentData
})
</script>

<style scoped>
.product-options-editor {
  background: #fff;
}

.option-item {
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.option-item .card-body {
  padding: 0.75rem;
}

.form-label {
  font-weight: 500;
  color: #495057;
}

.text-danger {
  color: #dc3545 !important;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.alert-info {
  background-color: #e7f3ff;
  border-color: #b8daff;
  color: #004085;
}
</style>
