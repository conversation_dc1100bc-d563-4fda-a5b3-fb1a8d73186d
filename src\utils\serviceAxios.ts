import axios, { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import type { ApiResponse } from '@/types/api';
/* eslint-disable */

// 获取token的函数（避免循环依赖）
const getToken = (): string => {
  try {
    const authData = localStorage.getItem('auth-data')
    if (authData) {
      const parsed = JSON.parse(authData)
      return parsed.token || ''
    }
  } catch (error) {
    console.error('获取token失败:', error)
  }
  return ''
}

// 清除认证信息的函数
const clearAuth = (): void => {
  try {
    localStorage.removeItem('auth-data')
    // 跳转到登录页面
    if (window.location.pathname !== '/login') {
      window.location.href = '/login'
    }
  } catch (error) {
    console.error('清除认证信息失败:', error)
  }
}

// 使用统一的API响应类型
type BackendResponse<T = unknown> = ApiResponse<T>;

// Axios实例
const serviceAxios: AxiosInstance = axios.create({
    baseURL: '/api', // 基础请求地址
    timeout: 30000, // 超时设置改为30秒
    withCredentials: false, // 跨域请求是否需要携带cookie
});

// 请求拦截器 - 添加认证token
serviceAxios.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
        // 获取token
        const token = getToken()

        // 如果有token且不是登录相关接口，则添加Authorization头
        if (token && !isAuthEndpoint(config.url || '')) {
            config.headers.Authorization = `Bearer ${token}`
        }

        // 设置Content-Type
        if (!config.headers['Content-Type']) {
            config.headers['Content-Type'] = 'application/json'
        }

        return config;
    },
    (error: any) => {
        // 处理请求错误
        console.error("Request Error: ", error);
        return Promise.reject(error);
    }
);

// 判断是否为认证相关接口
const isAuthEndpoint = (url: string): boolean => {
    const authEndpoints = ['/login', '/register', '/captchaImage', '/refresh']
    return authEndpoints.some(endpoint => url.includes(endpoint))
}

// 响应拦截器
serviceAxios.interceptors.response.use(
    /**
     * 如果你想获取 http 信息，如响应头或状态码
     * 请 return response => response
     */
    (response: AxiosResponse<BackendResponse>) => {
        // response.data 就是后端返回的JSON
        const res = response.data;

        // 检查业务状态码
        // code 不为 200 或 201 都被视为错误
        if (res.code !== 200 && res.code !== 201) {
            // 这里可以根据不同的code做不同的提示，例如 Message 组件
            // Message.error(res.msg || 'Error');
            console.error('API Error:', res.msg);

            // 返回一个被拒绝的 Promise，会触发 catch 块
            return Promise.reject(new Error(res.msg || 'Error'));
        } else {
            // 业务状态码正确，直接返回后端JSON数据本身。
            // 我们在此处改变了 Axios 默认的返回行为，使得调用者直接获取到 data 部分。
            // 使用 `as any` 是为了告诉 TypeScript 我们有意改变返回类型，以符合项目中的封装策略。
            return res as any;
        }
    },
    /**
     * 处理网络层面的错误 (非2xx状态码)
     */
    (error: any) => {
        let message = error.message;
        if (error.response) {
            const { status, data } = error.response;
            // 如果后端在错误时也返回了消息体，则使用后端的消息
            message = data?.msg || data?.message || `网络错误，状态码: ${status}`;

            switch (status) {
                case 401: // Unauthorized
                    message = '认证失败，请重新登录';
                    // 清除认证信息并跳转到登录页
                    clearAuth();
                    break;
                case 403: // Forbidden
                    message = '您没有权限执行此操作';
                    break;
                case 404: // Not Found
                    message = '请求的资源未找到';
                    break;
                case 500:
                    message = '服务器内部错误';
                    break;
                default:
                // 其他HTTP错误
            }
        } else if (error.request) {
            // 请求已发出，但没有收到响应
            message = '请求超时，请检查您的网络连接';
        }
        // TODO:使用UI组件提示错误
        // Message.error(message);

        console.error('Network Error:', message);
        return Promise.reject(error);
    }
);

export default serviceAxios;
