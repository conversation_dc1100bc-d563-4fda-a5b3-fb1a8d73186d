<template>
  <div class="fullscreen-dashboard">
    <!-- 顶部标题栏 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="dashboard-title">全门店营业情况数据看板</h1>
          <div class="date-time-section">
            <div class="current-date">{{ currentDate }}</div>
            <div class="current-time">{{ currentTime }}</div>
          </div>
        </div>
        <div class="controls-section">
          <div class="date-controls">
            <div class="quick-date-buttons">
              <button class="quick-btn" :class="{ active: quickDateActive === 'today' }" @click="selectQuickDate('today')">今日</button>
              <button class="quick-btn" :class="{ active: quickDateActive === 'yesterday' }" @click="selectQuickDate('yesterday')">昨日</button>
              <button class="quick-btn" :class="{ active: quickDateActive === 'week' }" @click="selectQuickDate('week')">本周</button>
              <button class="quick-btn" :class="{ active: quickDateActive === 'lastWeek' }" @click="selectQuickDate('lastWeek')">上周</button>
              <button class="quick-btn" :class="{ active: quickDateActive === 'month' }" @click="selectQuickDate('month')">本月</button>
              <button class="quick-btn" :class="{ active: quickDateActive === 'lastMonth' }" @click="selectQuickDate('lastMonth')">上月</button>
            </div>
            <div class="time-selector">
              <flat-pickr
                v-model="selectedDate"
                :config="datePickerConfig"
                class="form-control date-picker"
                placeholder="选择日期范围"
                @on-change="onDateChange"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-content">
      <!-- 第一行 - 核心KPI指标区 -->
      <div class="kpi-section">
        <div class="kpi-card">
          <div class="kpi-icon revenue-icon">
            <i class="flaticon-money"></i>
          </div>
          <div class="kpi-content">
            <div class="kpi-label">当日营业额</div>
            <div class="kpi-value">
              <span class="value revenue">{{ totalRevenue.toLocaleString() }}</span>
              <span class="unit currency">¥</span>
            </div>
            <div class="kpi-comparison" v-if="revenueComparison.text">
              <i class="comparison-icon" :class="{
                'flaticon-up-arrow': revenueComparison.trend === 'up',
                'flaticon-down-arrow': revenueComparison.trend === 'down',
                'flaticon-minus': revenueComparison.trend === 'neutral'
              }"></i>
              <span class="comparison-text" :class="`trend-${revenueComparison.trend}`">
                {{ revenueComparison.text }}
              </span>
            </div>
          </div>
        </div>
        <div class="kpi-card">
          <div class="kpi-icon orders-icon">
            <i class="flaticon-shopping-cart"></i>
          </div>
          <div class="kpi-content">
            <div class="kpi-label">订单量</div>
            <div class="kpi-value">
              <span class="value orders">{{ totalOrders.toLocaleString() }}</span>
              <span class="unit order-unit">单</span>
            </div>
            <div class="kpi-comparison" v-if="ordersComparison.text">
              <i class="comparison-icon" :class="{
                'flaticon-up-arrow': ordersComparison.trend === 'up',
                'flaticon-down-arrow': ordersComparison.trend === 'down',
                'flaticon-minus': ordersComparison.trend === 'neutral'
              }"></i>
              <span class="comparison-text" :class="`trend-${ordersComparison.trend}`">
                {{ ordersComparison.text }}
              </span>
            </div>
          </div>
        </div>
        <div class="kpi-card">
          <div class="kpi-icon cups-icon">
            <i class="flaticon-drink"></i>
          </div>
          <div class="kpi-content">
            <div class="kpi-label">杯量</div>
            <div class="kpi-value">
              <span class="value cups">{{ totalCups.toLocaleString() }}</span>
              <span class="unit cup-unit">杯</span>
            </div>
            <div class="kpi-comparison" v-if="cupsComparison.text">
              <i class="comparison-icon" :class="{
                'flaticon-up-arrow': cupsComparison.trend === 'up',
                'flaticon-down-arrow': cupsComparison.trend === 'down',
                'flaticon-minus': cupsComparison.trend === 'neutral'
              }"></i>
              <span class="comparison-text" :class="`trend-${cupsComparison.trend}`">
                {{ cupsComparison.text }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 第二行 - 产品分析与时段分析 -->
      <div class="analysis-section">
        <!-- 左侧：全门店明星单品 -->
        <div class="analysis-card products-card">
          <div class="card-header">
            <i class="flaticon-star"></i>
            <h3>全门店明星单品</h3>
          </div>
          <div class="products-grid">
            <!-- 明星产品加载中 -->
            <template v-if="isLoading && starProducts.length === 0">
              <div v-for="i in 8" :key="i" class="product-item skeleton">
                <div class="product-rank skeleton-circle"></div>
                <div class="product-name skeleton-text"></div>
                <div class="product-sales skeleton-text-small"></div>
              </div>
            </template>
            <!-- 空状态 -->
            <div v-else-if="!isLoading && starProducts.length === 0" class="empty-state">
              <i class="flaticon-warning me-2"></i>
              暂无明星单品数据
            </div>
            <!-- 实际数据 -->
            <div v-else v-for="product in starProducts" :key="product.id" class="product-item">
              <div class="product-rank">{{ product.rank }}</div>
              <div class="product-name">{{ product.name }}</div>
              <div class="product-sales">
                <span class="sales-value">{{ product.sales }}</span>
                <span v-if="typeof product.sales === 'number'" class="sales-unit cup-unit">杯</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：数据分时段分析 -->
        <div class="analysis-card chart-card">
          <div class="card-header">
            <i class="flaticon-chart"></i>
            <h3>{{ chartTitle }}分时段分析</h3>
            <div class="chart-controls">
              <div class="data-type-selector">
                <button
                  v-for="option in chartDataOptions"
                  :key="option.value"
                  class="data-type-btn"
                  :class="{ active: selectedChartDataType === option.value }"
                  @click="selectChartDataType(option.value)"
                >
                  {{ option.label }}
                </button>
              </div>
            </div>
          </div>
          <div class="chart-container">
            <apexchart
              type="line"
              height="300"
              :options="dynamicChartOptions"
              :series="dynamicChartSeries"
              class="chart"
              id="dynamicChart"
            />
          </div>
        </div>
      </div>

      <!-- 第三行 - 门店排行与趋势分析 -->
      <div class="ranking-section">
        <!-- 左侧：门店营业额排行 -->
        <div class="ranking-card stores-ranking">
          <div class="card-header">
            <i class="flaticon-trophy"></i>
            <h3>门店营业额排行</h3>
          </div>
          <div class="ranking-list">
            <!-- 门店排行加载中 -->
            <template v-if="isLoading && storeRanking.length === 0">
              <div v-for="i in 6" :key="i" class="ranking-item skeleton">
                <div class="rank-number skeleton-circle"></div>
                <div class="store-info">
                  <div class="store-name skeleton-text"></div>
                  <div class="store-revenue skeleton-text-small"></div>
                </div>
              </div>
            </template>
            <!-- 空状态 -->
            <div v-else-if="!isLoading && storeRanking.length === 0" class="empty-state">
              <i class="flaticon-warning me-2"></i>
              暂无门店排行数据
            </div>
            <!-- 实际数据 -->
            <div v-else v-for="store in storeRanking" :key="store.id" class="ranking-item">
              <div class="rank-number">{{ store.rank }}</div>
              <div class="store-info">
                <div class="store-name">{{ store.name }}</div>
                <div class="store-revenue">
                  <span class="revenue-value">{{ store.revenue.toLocaleString() }}</span>
                  <span class="revenue-unit currency">¥</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 中间：单品杯数排行 -->
        <div class="ranking-card products-ranking">
          <div class="card-header">
            <i class="flaticon-drink"></i>
            <h3>单品杯数排行</h3>
          </div>
          <div class="ranking-list">
            <!-- 异步加载状态：单品排行 -->
            <template v-if="isLoadingDetails && productRanking.length === 0">
              <div v-for="i in 5" :key="i" class="ranking-item skeleton">
                <div class="rank-number skeleton-circle"></div>
                <div class="product-info">
                  <div class="product-name skeleton-text"></div>
                  <div class="product-cups skeleton-text-small"></div>
                </div>
              </div>
            </template>
            <!-- 空状态 -->
            <div v-else-if="!isLoadingDetails && productRanking.length === 0" class="empty-state">
              <i class="flaticon-warning me-2"></i>
              暂无单品排行数据
            </div>
            <!-- 实际数据 -->
            <div v-else v-for="product in productRanking" :key="product.id" class="ranking-item">
              <div class="rank-number">{{ product.rank }}</div>
              <div class="product-info">
                <div class="product-name">{{ product.name }}</div>
                <div class="product-cups">
                  <span class="cups-value">{{ product.cups }}</span>
                  <span class="cups-unit cup-unit">杯</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：销量变化曲线 -->
        <div class="ranking-card trend-chart">
          <div class="card-header">
            <i class="flaticon-trending"></i>
            <h3>销量变化曲线（本周）</h3>
          </div>
          <div class="chart-container">
            <apexchart
              type="line"
              height="250"
              :options="weeklyTrendChartOptions"
              :series="weeklyTrendChartSeries"
              class="chart"
              id="weeklyTrendChart"
            />
          </div>
        </div>
      </div>

      <!-- 第四行 - 周期性汇总 -->
      <div class="summary-section">
        <div class="summary-card">
          <div class="card-header">
            <i class="flaticon-calendar"></i>
            <h3>本周营业额汇总</h3>
          </div>
          <div class="summary-stats">
            <div class="stat-item">
              <div class="stat-label">本周总营业额</div>
              <div class="stat-value">
                <span class="value">{{ weeklyRevenue.toLocaleString() }}</span>
                <span class="unit currency">¥</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">日均营业额</div>
              <div class="stat-value">
                <span class="value">{{ Math.round(weeklyRevenue / 7).toLocaleString() }}</span>
                <span class="unit currency">¥</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">环比增长</div>
              <div class="stat-value growth">
                <span class="value growth-positive">+12.5</span>
                <span class="unit">%</span>
              </div>
            </div>
          </div>
        </div>

        <div class="summary-card">
          <div class="card-header">
            <i class="flaticon-calendar"></i>
            <h3>本月营业额汇总</h3>
          </div>
          <div class="summary-stats">
            <div class="stat-item">
              <div class="stat-label">本月总营业额</div>
              <div class="stat-value">
                <span class="value">{{ monthlyRevenue.toLocaleString() }}</span>
                <span class="unit currency">¥</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">日均营业额</div>
              <div class="stat-value">
                <span class="value">{{ Math.round(monthlyRevenue / 30).toLocaleString() }}</span>
                <span class="unit currency">¥</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">环比增长</div>
              <div class="stat-value growth">
                <span class="value growth-positive">+8.3</span>
                <span class="unit">%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive, watch, computed } from 'vue'
import { format } from 'date-fns'
import { getOrderListByParams } from '@/utils/api/order'
import { getProductDetailById, getPrototypeDetailById } from '@/utils/api/product'
import { getStoreList } from '@/utils/api/store'
import serviceAxios from '@/utils/serviceAxios'
import type { drinkOrder } from '@/types/order'
import type { drinkProductionQueueItem, storeDetail } from '@/types'
import { extractListData } from '@/types/api'
import flatPickr from "vue-flatpickr-component"
import 'flatpickr/dist/flatpickr.css'
import 'flatpickr/dist/l10n/zh.js'
import '@/assets/css/dashboard-fullscreen.css'

// 加载状态
const isLoading = ref<boolean>(true) // 主要数据加载状态
const isLoadingDetails = ref<boolean>(false) // 详细数据加载状态
const isFullyLoaded = ref<boolean>(false) // 所有数据加载完成状态
const error = ref<string | null>(null)

// 当前查询日期
const currentQueryDate = ref<Date>(new Date())
const currentQueryEndDate = ref<Date | null>(null) // 查询结束日期（用于范围查询）

// 统一的数据存储
const fullscreenData = reactive<{
  stores: storeDetail[]
  allOrders: drinkOrder[]
  allQueueItems: drinkProductionQueueItem[]
}>({
  stores: [],
  allOrders: [],
  allQueueItems: [],
})

// 当前时间和日期显示
const currentTime = ref('')
const currentDate = ref('')
let timeInterval: NodeJS.Timeout

const updateTime = () => {
  const now = new Date()

  // 格式化日期为 "YYYY年MM月DD日"
  currentDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  // 格式化时间
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})

// 时间选择器配置
const selectedDate = ref('')
const quickDateActive = ref('today')
const timeRange = ref('today') // 'today', 'week', 'month', 'manual'

// 时间选择选项（已集成到快速选择按钮中，此变量已移除）

// 图表数据类型选择
const selectedChartDataType = ref('revenue')
const chartDataOptions = [
  { value: 'revenue', label: '营业额', unit: '¥', color: '#6560F0' },
  { value: 'orders', label: '订单量', unit: '单', color: '#17a2b8' },
  { value: 'cups', label: '杯数', unit: '杯', color: '#ffc107' }
]

// 动态图表标题
const chartTitle = computed(() => {
  try {
    const option = chartDataOptions.find(opt => opt.value === selectedChartDataType.value)
    return option?.label || '数据'
  } catch (error) {
    console.error('图表标题计算错误:', error)
    return '数据'
  }
})

// 分时段数据存储
const hourlyData = ref({
  revenue: { current: new Array(24).fill(0), previous: new Array(24).fill(0) },
  orders: { current: new Array(24).fill(0), previous: new Array(24).fill(0) },
  cups: { current: new Array(24).fill(0), previous: new Array(24).fill(0) }
})
const datePickerConfig = ref({
  mode: 'range',
  dateFormat: 'Y-m-d',
  locale: 'zh',
  defaultDate: 'today',
  maxDate: new Date(), // 不能选择未来日期
  allowInput: true, // 允许手动输入
  clickOpens: true, // 点击打开日历
  enableTime: false, // 不启用时间选择
  weekNumbers: true, // 显示周数
  showMonths: 1, // 显示月数
  rangeSeparator: ' 至 ', // 范围分隔符
  plugins: []
})

// 快速日期选择功能
const selectQuickDate = (type: string) => {
  quickDateActive.value = type
  timeRange.value = type // 更新时间范围，用于环比计算
  const today = new Date()
  let targetDate: Date

  let endDate: Date | null = null

  switch (type) {
    case 'today': {
      targetDate = new Date(today)
      break
    }
    case 'yesterday': {
      targetDate = new Date(today)
      targetDate.setDate(today.getDate() - 1)
      break
    }
    case 'week': {
      // 获取本周一
      const dayOfWeek = today.getDay()
      const diff = today.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1)
      targetDate = new Date(today.setDate(diff))
      endDate = new Date(today) // 本周结束日期为今天
      break
    }
    case 'lastWeek': {
      // 上周的开始日期（上周一）
      const lastWeekStart = new Date(today)
      const currentDayOfWeek = today.getDay()
      const daysToLastMonday = currentDayOfWeek === 0 ? 13 : currentDayOfWeek + 6
      lastWeekStart.setDate(today.getDate() - daysToLastMonday)
      targetDate = lastWeekStart
      endDate = new Date(lastWeekStart)
      endDate.setDate(lastWeekStart.getDate() + 6) // 上周日
      break
    }
    case 'month': {
      targetDate = new Date(today.getFullYear(), today.getMonth(), 1)
      endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0) // 本月最后一天
      break
    }
    case 'lastMonth': {
      targetDate = new Date(today.getFullYear(), today.getMonth() - 1, 1)
      endDate = new Date(today.getFullYear(), today.getMonth(), 0) // 上月最后一天
      break
    }
    default: {
      targetDate = new Date(today)
    }
  }

  const formatDate = (date: Date) => {
    return date.toISOString().split('T')[0]
  }

  // 更新选中日期和查询日期
  if (endDate) {
    // 对于范围选择，使用范围格式
    selectedDate.value = `${formatDate(targetDate)} 至 ${formatDate(endDate)}`
    currentQueryDate.value = targetDate
    currentQueryEndDate.value = endDate
  } else {
    // 对于单日选择
    selectedDate.value = formatDate(targetDate)
    currentQueryDate.value = targetDate
    currentQueryEndDate.value = null
  }

  console.log(`📅 [时间选择] 选择了${type}: ${selectedDate.value}`)
}

// 获取历史数据用于环比计算
async function fetchPreviousPeriodData(currentDate: Date) {
  try {
    let previousDate: Date

    switch (timeRange.value) {
      case 'today':
        // 获取昨日数据
        previousDate = new Date(currentDate)
        previousDate.setDate(previousDate.getDate() - 1)
        break
      case 'yesterday':
        // 获取前日数据
        previousDate = new Date(currentDate)
        previousDate.setDate(previousDate.getDate() - 1)
        break
      case 'week':
        // 获取上周同期数据
        previousDate = new Date(currentDate)
        previousDate.setDate(previousDate.getDate() - 7)
        break
      case 'lastWeek':
        // 获取前一周数据
        previousDate = new Date(currentDate)
        previousDate.setDate(previousDate.getDate() - 7)
        break
      case 'month':
        // 获取上月同期数据
        previousDate = new Date(currentDate)
        previousDate.setMonth(previousDate.getMonth() - 1)
        break
      case 'lastMonth':
        // 获取前一月数据
        previousDate = new Date(currentDate)
        previousDate.setMonth(previousDate.getMonth() - 1)
        break
      default:
        previousDate = new Date(currentDate)
        previousDate.setDate(previousDate.getDate() - 1)
    }

    const formattedPreviousDate = format(previousDate, "yyyy-MM-dd")
    console.log(`📊 [环比] 获取历史数据，日期: ${formattedPreviousDate}`)

    // 获取历史销售统计数据
    const previousSalesPromises = fullscreenData.stores.map(store =>
      serviceAxios.get('/manager/store/sales-stat', {
        params: {
          storeId: store.storeId,
          date: formattedPreviousDate
        }
      }).then(response => response).catch(err => {
        console.error(`获取门店${store.storeId}历史销售统计失败:`, err)
        return { data: { totalAmount: '0', orderCount: 0, totalCups: 0 } }
      })
    )

    const previousSalesResponses = await Promise.all(previousSalesPromises)

    // 汇总历史数据
    let previousRevenue = 0
    let previousOrders = 0
    let previousCups = 0

    previousSalesResponses.forEach((response) => {
      const data = response.data || response
      previousRevenue += parseFloat(data.totalAmount || '0')
      previousOrders += parseInt(data.orderCount || '0')
      previousCups += parseInt(data.totalCups || '0')
    })

    previousPeriodData.value = {
      revenue: previousRevenue,
      orders: previousOrders,
      cups: previousCups
    }

    console.log(`✅ [环比] 历史数据获取完成 - 营业额:¥${previousRevenue}, 订单:${previousOrders}单, 杯数:${previousCups}杯`)

    // 获取历史分时段数据
    await fetchPreviousHourlyData(previousDate)

  } catch (err) {
    console.error('❌ [环比] 获取历史数据失败:', err)
    previousPeriodData.value = { revenue: 0, orders: 0, cups: 0 }
  }
}

// 获取历史分时段数据
async function fetchPreviousHourlyData(previousDate: Date) {
  try {
    const formattedPreviousDate = format(previousDate, "yyyy-MM-dd")
    console.log(`📊 [环比] 获取历史分时段数据，日期: ${formattedPreviousDate}`)

    // 获取历史订单数据
    const previousOrderPromises = fullscreenData.stores.map(store =>
      getOrderListByParams({
        pageNum: 1,
        pageSize: 9999,
        queryDate: formattedPreviousDate,
        storeId: store.storeId,
        orderTimeSort: "1",
      }).catch(err => {
        console.error(`获取门店${store.storeId}历史订单失败:`, err)
        return { rows: [], total: 0 }
      })
    )

    const previousOrderResponses = await Promise.all(previousOrderPromises)
    const previousOrders = previousOrderResponses.flatMap(response => response.rows ?? [])

    if (previousOrders.length === 0) {
      console.log(`⚠️ [环比] 无历史订单数据`)
      return
    }

    // 计算历史分时段数据
    const previousHourlyRevenue = new Array(24).fill(0)
    const previousHourlyOrders = new Array(24).fill(0)
    const previousHourlyCups = new Array(24).fill(0)

    for (const order of previousOrders) {
      try {
        const orderTime = new Date(order.orderTime)
        const hour = orderTime.getHours()
        const revenue = parseFloat(order.totalAmount || '0')

        previousHourlyRevenue[hour] += revenue
        previousHourlyOrders[hour] += 1

        // 简化杯数计算（假设每个订单平均1.5杯）
        previousHourlyCups[hour] += Math.round(1.5)

      } catch (err) {
        console.error(`处理历史订单${order.orderId}时间失败:`, err)
      }
    }

    // 更新历史分时段数据
    hourlyData.value.revenue.previous = previousHourlyRevenue.map(val => Math.round(val))
    hourlyData.value.orders.previous = previousHourlyOrders
    hourlyData.value.cups.previous = previousHourlyCups

    console.log(`✅ [环比] 历史分时段数据计算完成`)

  } catch (err) {
    console.error('❌ [环比] 获取历史分时段数据失败:', err)
  }
}

// 更新环比数据
function updateComparisonData() {
  revenueComparison.value = calculateComparison(totalRevenue.value, previousPeriodData.value.revenue)
  ordersComparison.value = calculateComparison(totalOrders.value, previousPeriodData.value.orders)
  cupsComparison.value = calculateComparison(totalCups.value, previousPeriodData.value.cups)

  console.log(`📈 [环比] 环比数据更新完成`)
  console.log(`- 营业额: ${revenueComparison.value.text}`)
  console.log(`- 订单量: ${ordersComparison.value.text}`)
  console.log(`- 杯数: ${cupsComparison.value.text}`)
}

// 数据获取函数 - 分层加载优化，支持时间范围查询
async function fetchFullscreenData() {
  isLoading.value = true
  error.value = null

  try {
    const startDate = format(currentQueryDate.value, "yyyy-MM-dd")
    const endDate = currentQueryEndDate.value ? format(currentQueryEndDate.value, "yyyy-MM-dd") : startDate
    const isRangeQuery = currentQueryEndDate.value !== null

    console.log(`🚀 [大屏] 开始分层加载数据`)
    console.log(`📅 [大屏] 查询时间: ${isRangeQuery ? `${startDate} 至 ${endDate}` : startDate}`)

    // 第一层：核心数据并行加载（最重要，优先显示）
    console.log(`📊 [大屏] 第一层：加载核心数据`)
    const [storesResponse, starProductsResponse] = await Promise.all([
      // 门店列表
      getStoreList({ pageNum: 1, pageSize: 9999 }).catch(err => {
        console.error('获取门店列表失败:', err)
        return { rows: [] }
      }),
      // 明星产品（独立API，可并行）
      serviceAxios.get('/manager/prototype/popular', {
        params: {
          limit: 8  // 限制返回8个热门饮品原型
        }
      }).then(response => {
        console.log('✅ [大屏] 热门饮品原型API调用成功')
        return response
      }).catch(err => {
        console.error('❌ [大屏] 获取热门饮品失败:', err)
        return { rows: [] }  // 修正返回格式，直接返回包含rows的对象
      })
    ])

    fullscreenData.stores = storesResponse.rows ?? []
    console.log(`🔍 [调试] 加载门店列表: ${fullscreenData.stores.length}个门店`)
    fullscreenData.stores.forEach((store, index) => {
      console.log(`🔍 [调试] 门店${index + 1}: ID=${store.storeId}, 名称=${store.storeName}`)
    })

    // 立即处理明星产品数据

    // 使用类型安全的辅助函数提取列表数据
    const hotPrototypes = extractListData(starProductsResponse)

    if (hotPrototypes.length > 0) {
      starProducts.value = hotPrototypes.slice(0, 8).map((item, index) => ({
        id: item.prototypeId || item.id || index + 1,
        name: item.prototypeName || item.name || `产品${index + 1}`,
        sales: item.sales || item.popularity || item.count || '热门', // API没有销量数据，显示"热门"
        rank: index + 1
      }))
      console.log(`✅ [大屏] 明星产品加载完成: ${starProducts.value.length}个`)
    } else {
      console.log(`⚠️ [大屏] 热门饮品原型API返回数据为空`)
      starProducts.value = []
    }

    // 第二层：销售统计数据（核心KPI）- 支持时间范围查询
    console.log(`📈 [大屏] 第二层：加载销售统计数据`)
    let salesStatResponses: Array<{ data: { totalAmount: string; orderCount: number; totalCups: number } }> = []

    if (isRangeQuery) {
      // 时间范围查询：使用历史营业数据API
      const daysDiff = Math.ceil((new Date(endDate).getTime() - new Date(startDate).getTime()) / (1000 * 60 * 60 * 24)) + 1
      console.log(`📊 [大屏] 时间范围查询，天数: ${daysDiff}`)

      const salesHistoryPromises = fullscreenData.stores.map(store => {
        console.log(`🔍 [调试] 门店${store.storeId}(${store.storeName}) - 调用历史数据API，参数: storeId=${store.storeId}, startDate=${startDate}, days=${daysDiff}`)
        return serviceAxios.get('/manager/store/sales-history', {
          params: {
            storeId: store.storeId,
            startDate: startDate,
            days: daysDiff
          }
        }).then(response => {
          console.log(`✅ [大屏] 门店${store.storeId}历史销售数据完成`)
          // 汇总历史数据
          const historyData = response.data || response.rows || []
          const totalAmount = historyData.reduce((sum: number, item: { totalAmount?: string }) => sum + parseFloat(item.totalAmount || '0'), 0)
          const orderCount = historyData.reduce((sum: number, item: { orderCount?: number }) => sum + parseInt(String(item.orderCount || '0')), 0)
          const totalCups = historyData.reduce((sum: number, item: { totalCups?: number }) => sum + parseInt(String(item.totalCups || '0')), 0)

          return {
            data: {
              totalAmount: totalAmount.toString(),
              orderCount: orderCount,
              totalCups: totalCups
            }
          }
        }).catch(err => {
          console.error(`❌ [大屏] 门店${store.storeId}历史销售数据失败:`, err)
          return { data: { totalAmount: '0', orderCount: 0, totalCups: 0 } }
        })
      })

      salesStatResponses = await Promise.all(salesHistoryPromises)
    } else {
      // 单日查询：使用销售统计API
      const salesStatPromises = fullscreenData.stores.map(store => {
        console.log(`🔍 [调试] 门店${store.storeId}(${store.storeName}) - 调用销售统计API，参数: storeId=${store.storeId}, date=${startDate}`)
        return serviceAxios.get('/manager/store/sales-stat', {
          params: {
            storeId: store.storeId,
            date: startDate
          }
        }).then(response => {
          console.log(`✅ [大屏] 门店${store.storeId}销售统计完成`)
          return response
        }).catch(err => {
          console.error(`❌ [大屏] 门店${store.storeId}销售统计失败:`, err)
          return { data: { totalAmount: '0', orderCount: 0, totalCups: 0 } }
        })
      })

      salesStatResponses = await Promise.all(salesStatPromises)
    }

    // 立即处理销售统计数据并更新核心KPI
    console.log(`📊 [大屏] 处理销售统计数据`)
    let totalSalesAmount = 0
    let totalOrderCount = 0
    let totalCupCount = 0

    salesStatResponses.forEach((response) => {
      // 修正：数据在response.data中，需要正确访问
      const data = response.data || response
      const salesAmount = parseFloat(data.totalAmount || '0')
      const orderCount = parseInt(data.orderCount || '0')
      const cupCount = parseInt(data.totalCups || '0')

      totalSalesAmount += salesAmount
      totalOrderCount += orderCount
      totalCupCount += cupCount
    })

    // 立即更新核心KPI（让用户先看到主要数据）
    totalRevenue.value = totalSalesAmount
    totalOrders.value = totalOrderCount
    totalCups.value = totalCupCount
    totalStores.value = fullscreenData.stores.length
    onlineStores.value = fullscreenData.stores.filter(store =>
      store.status === 'OPEN' || store.status === 'open' || store.status === '营业中'
    ).length

    // 立即计算门店排行（基于已有的销售统计数据）
    calculateStoreRankingFromSalesData(salesStatResponses)

    console.log(`✅ [大屏] 核心数据加载完成 - 营业额:¥${totalSalesAmount}, 订单:${totalOrderCount}单, 杯数:${totalCupCount}杯`)

    // 第三层：异步获取历史数据并计算环比
    console.log(`📊 [大屏] 第三层：异步获取环比数据`)
    fetchPreviousPeriodData(currentQueryDate.value).then(() => {
      updateComparisonData()
    })

    // 第四层：详细数据异步加载（不阻塞主要显示）
    console.log(`🔄 [大屏] 第四层：异步加载详细数据`)
    // 不等待异步加载完成，让主界面先显示
    loadDetailedDataAsync()

  } catch (err) {
    console.error("❌ [大屏] 获取核心数据失败:", err)
    error.value = "数据加载失败，请稍后重试。"
    fullscreenData.allOrders = []
    fullscreenData.stores = []
    totalRevenue.value = 0
    totalOrders.value = 0
    totalCups.value = 0
    totalStores.value = 0
    onlineStores.value = 0
    starProducts.value = []
    storeRanking.value = []
    productRanking.value = []
  } finally {
    // 核心数据加载完成，取消主加载状态
    isLoading.value = false
    // 如果核心数据加载失败，也标记为完全加载完成
    if (error.value) {
      isFullyLoaded.value = true
    }
  }
}

// 基于销售统计数据计算门店排行（无需额外API调用）
function calculateStoreRankingFromSalesData(salesStatResponses) {
  try {
    const storeRankingData = salesStatResponses.map((response, index) => {
      // 修正：数据在response.data中，需要正确访问
      const storeInfo = fullscreenData.stores[index]
      const data = response.data || response
      const revenue = parseFloat(data.totalAmount || '0')

      return {
        id: storeInfo.storeId,
        name: storeInfo.storeName,
        revenue: revenue,
        rank: 0
      }
    })

    // 按营业额排序并设置排名
    storeRankingData.sort((a, b) => b.revenue - a.revenue)
    storeRanking.value = storeRankingData.slice(0, 6).map((item, index) => ({
      ...item,
      rank: index + 1
    }))

    console.log(`✅ [大屏] 门店排行计算完成: ${storeRanking.value.length}个门店`)
  } catch (err) {
    console.error("❌ [大屏] 计算门店排行失败:", err)
  }
}

// 异步加载详细数据（不阻塞主界面）- 支持时间范围查询
async function loadDetailedDataAsync() {
  isLoadingDetails.value = true

  try {
    const startDate = format(currentQueryDate.value, "yyyy-MM-dd")
    const endDate = currentQueryEndDate.value ? format(currentQueryEndDate.value, "yyyy-MM-dd") : startDate
    const isRangeQuery = currentQueryEndDate.value !== null

    console.log(`🔄 [大屏] 开始异步加载订单详情数据`)
    console.log(`📅 [大屏] 订单查询时间: ${isRangeQuery ? `${startDate} 至 ${endDate}` : startDate}`)

    // 获取详细订单数据用于单品排行
    let allOrdersData: Array<Record<string, unknown>> = []

    if (isRangeQuery) {
      // 时间范围查询：需要逐日查询订单数据（因为订单API只支持单日查询）
      const dateRange = getDateRangeArray(new Date(startDate), new Date(endDate))
      console.log(`📊 [大屏] 时间范围查询，需要查询 ${dateRange.length} 天的订单数据`)

      for (const queryDate of dateRange) {
        const formattedDate = format(queryDate, "yyyy-MM-dd")
        const orderPromises = fullscreenData.stores.map(store =>
          getOrderListByParams({
            pageNum: 1,
            pageSize: 9999,
            queryDate: formattedDate,
            storeId: store.storeId,
            orderTimeSort: "1",
          }).catch(err => {
            console.error(`获取门店${store.storeId}订单失败(${formattedDate}):`, err)
            return { rows: [], total: 0 }
          })
        )

        const orderResponses = await Promise.all(orderPromises)
        const dayOrders = orderResponses.flatMap(response => response.rows ?? [])
        allOrdersData.push(...dayOrders)
      }
    } else {
      // 单日查询
      const orderPromises = fullscreenData.stores.map(store =>
        getOrderListByParams({
          pageNum: 1,
          pageSize: 9999,
          queryDate: startDate,
          storeId: store.storeId,
          orderTimeSort: "1",
        }).catch(err => {
          console.error(`获取门店${store.storeId}订单失败:`, err)
          return { rows: [], total: 0 }
        })
      )

      const orderResponses = await Promise.all(orderPromises)
      allOrdersData = orderResponses.flatMap(response => response.rows ?? [])
    }

    // 合并订单数据
    fullscreenData.allOrders = allOrdersData
    console.log(`✅ [大屏] 订单数据加载完成: ${fullscreenData.allOrders.length}条订单`)

    // 计算单品杯数排行
    await calculateProductRanking()

    // 计算分时段数据（营业额、订单量、杯数）
    calculateHourlyData()

    console.log(`🎉 [大屏] 所有数据加载完成`)
  } catch (err) {
    console.error("❌ [大屏] 异步加载详细数据失败:", err)
  } finally {
    isLoadingDetails.value = false
    isFullyLoaded.value = true
    console.log(`✨ [大屏] 全部加载流程完成，可以隐藏加载动画`)
  }
}

// 获取日期范围数组的辅助函数
function getDateRangeArray(startDate: Date, endDate: Date): Date[] {
  const dates: Date[] = []
  const currentDate = new Date(startDate)

  while (currentDate <= endDate) {
    dates.push(new Date(currentDate))
    currentDate.setDate(currentDate.getDate() + 1)
  }

  return dates
}

// 数据处理函数已优化为分层加载，不再需要统一处理函数

// 核心KPI数据（100%基于API数据）
const totalRevenue = ref(0)
const totalOrders = ref(0)
const totalCups = ref(0)
const onlineStores = ref(0)
const totalStores = ref(0)

// 全门店明星单品数据（100%基于API数据）
const starProducts = ref([])

// 明星产品数据已在第一层加载中处理，此函数已废弃

// 门店营业额排行数据（100%基于API数据）
const storeRanking = ref([])

// 原门店排行函数已优化为 calculateStoreRankingFromSalesData，基于销售统计数据计算

// 计算单品杯数排行
async function calculateProductRanking() {
  if (!fullscreenData.allOrders || fullscreenData.allOrders.length === 0) {
    productRanking.value = []
    return
  }

  try {
    const productCups = new Map<number, number>()

    // 统计每个产品的杯数
    for (const order of fullscreenData.allOrders) {
      let parsedItems
      try {
        parsedItems = JSON.parse(order.orderItems)
      } catch (e) {
        continue
      }
      if (!Array.isArray(parsedItems)) continue

      for (const item of parsedItems) {
        if (item && item.productId) {
          const productId = Number(item.productId)
          const quantity = Number(item.quantity) || 1
          productCups.set(productId, (productCups.get(productId) || 0) + quantity)
        }
      }
    }

    // 获取产品详情并按杯数排序
    const productDetails = []
    for (const [productId, cups] of productCups.entries()) {
      try {
        const detail = await getProductDetailById(productId)
        if (detail && detail.drinkPrototype) {
          const prototypeDetail = await getPrototypeDetailById(detail.drinkPrototype)
          productDetails.push({
            id: productId,
            name: prototypeDetail?.prototypeName || `产品${productId}`,
            cups: cups,
            rank: 0
          })
        }
      } catch (err) {
        console.error(`获取产品${productId}详情失败:`, err)
      }
    }

    // 按杯数排序并设置排名（显示前5个）
    productDetails.sort((a, b) => b.cups - a.cups)
    productRanking.value = productDetails.slice(0, 5).map((item, index) => ({
      ...item,
      rank: index + 1
    }))
  } catch (err) {
    console.error("计算单品杯数排行失败:", err)
  }
}

// 计算分时段数据（营业额、订单量、杯数）
function calculateHourlyData() {
  try {
    console.log(`📊 [大屏] 开始计算分时段数据，订单数: ${fullscreenData.allOrders.length}`)

    if (!fullscreenData.allOrders || fullscreenData.allOrders.length === 0) {
      console.log(`⚠️ [大屏] 无订单数据，保持默认分时段数据`)
      return
    }

    // 初始化24小时数据
    const hourlyRevenue = new Array(24).fill(0)
    const hourlyOrders = new Array(24).fill(0)
    const hourlyCups = new Array(24).fill(0)

    // 按小时统计各类数据
    for (const order of fullscreenData.allOrders) {
      try {
        const orderTime = new Date(order.orderTime)
        const hour = orderTime.getHours()
        const revenue = parseFloat(order.totalAmount || '0')

        hourlyRevenue[hour] += revenue
        hourlyOrders[hour] += 1

        // 计算杯数（从订单的队列项目中统计）
        const orderCups = fullscreenData.allQueueItems.filter(item =>
          item.orderId === order.orderId
        ).length
        hourlyCups[hour] += orderCups

      } catch (err) {
        console.error(`处理订单${order.orderId}时间失败:`, err)
      }
    }

    // 更新分时段数据
    hourlyData.value.revenue.current = hourlyRevenue.map(val => Math.round(val))
    hourlyData.value.orders.current = hourlyOrders
    hourlyData.value.cups.current = hourlyCups

    // 更新旧的图表数据（保持兼容性）
    hourlyRevenueChartSeries.value = [
      {
        name: '营业额',
        data: hourlyData.value.revenue.current
      }
    ]

    console.log(`✅ [大屏] 分时段数据计算完成`)
    console.log(`- 营业额峰值: ¥${Math.max(...hourlyData.value.revenue.current)}`)
    console.log(`- 订单峰值: ${Math.max(...hourlyData.value.orders.current)}单`)
    console.log(`- 杯数峰值: ${Math.max(...hourlyData.value.cups.current)}杯`)
  } catch (err) {
    console.error("❌ [大屏] 计算分时段数据失败:", err)
  }
}

// 单品杯数排行数据（100%基于API数据）
const productRanking = ref([])

// 周期性汇总数据（100%基于API数据）
const weeklyRevenue = ref(0)
const monthlyRevenue = ref(0)

// 环比数据
const revenueComparison = ref({ value: 0, trend: 'up', text: '' })
const ordersComparison = ref({ value: 0, trend: 'up', text: '' })
const cupsComparison = ref({ value: 0, trend: 'up', text: '' })

// 历史数据存储
const previousPeriodData = ref({
  revenue: 0,
  orders: 0,
  cups: 0
})

// 环比计算函数
const calculateComparison = (currentValue: number, previousValue: number) => {
  if (previousValue === 0) {
    return {
      value: currentValue > 0 ? 100 : 0,
      trend: currentValue > 0 ? 'up' : 'neutral',
      text: currentValue > 0 ? '新增数据' : '暂无对比'
    }
  }

  const change = ((currentValue - previousValue) / previousValue) * 100
  const absChange = Math.abs(change)

  return {
    value: absChange,
    trend: change > 0 ? 'up' : change < 0 ? 'down' : 'neutral',
    text: change > 0
      ? `较${getComparisonPeriodText()}上升${absChange.toFixed(1)}%`
      : change < 0
      ? `较${getComparisonPeriodText()}下降${absChange.toFixed(1)}%`
      : `与${getComparisonPeriodText()}持平`
  }
}

// 获取对比周期文本
const getComparisonPeriodText = () => {
  switch (timeRange.value) {
    case 'today':
      return '昨日'
    case 'yesterday':
      return '前日'
    case 'week':
      return '上周'
    case 'lastWeek':
      return '前一周'
    case 'month':
      return '上月'
    case 'lastMonth':
      return '前一月'
    default:
      return '前一期'
  }
}

// 图表数据类型选择函数
const selectChartDataType = (dataType: string) => {
  selectedChartDataType.value = dataType
  updateDynamicChart()
  console.log(`📊 [图表] 切换数据类型: ${dataType}`)
}

// 动态图表系列数据
const dynamicChartSeries = computed(() => {
  try {
    const currentOption = chartDataOptions.find(opt => opt.value === selectedChartDataType.value)
    if (!currentOption) {
      return [{
        name: '数据',
        data: new Array(24).fill(0),
        color: '#6560F0'
      }]
    }

    const currentData = hourlyData.value?.[selectedChartDataType.value]?.current || new Array(24).fill(0)
    const previousData = hourlyData.value?.[selectedChartDataType.value]?.previous || new Array(24).fill(0)

    return [
      {
        name: `当前${currentOption.label}`,
        data: currentData,
        color: currentOption.color
      },
      {
        name: `${getComparisonPeriodText()}${currentOption.label}`,
        data: previousData,
        color: `${currentOption.color}80` // 50% 透明度
      }
    ]
  } catch (error) {
    console.error('动态图表系列数据计算错误:', error)
    return [{
      name: '数据',
      data: new Array(24).fill(0),
      color: '#6560F0'
    }]
  }
})

// 动态图表配置
const dynamicChartOptions = computed(() => {
  try {
    const currentOption = chartDataOptions.find(opt => opt.value === selectedChartDataType.value) || chartDataOptions[0]

    return {
      chart: {
        type: 'line',
        height: 300,
        toolbar: { show: false },
        fontFamily: 'Source Han Serif, serif',
        background: 'transparent'
      },
      colors: [currentOption.color, `${currentOption.color}80`],
      stroke: {
        width: [3, 2],
        curve: 'smooth'
      },
      dataLabels: { enabled: false },
      xaxis: {
        categories: ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00', '07:00', '08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'],
        labels: {
          style: {
            colors: '#6c757d',
            fontSize: '11px',
            fontFamily: 'Source Han Serif, serif'
          }
        },
        axisBorder: { show: false },
        axisTicks: { show: false }
      },
      yaxis: {
        labels: {
          style: {
            colors: '#6c757d',
            fontSize: '11px',
            fontFamily: 'Source Han Serif, serif'
          },
          formatter: function (value: number) {
            return currentOption.unit + value
          }
        }
      },
      grid: {
        borderColor: '#f0f1f3',
        strokeDashArray: 3
      },
      tooltip: {
        theme: 'light',
        style: {
          fontSize: '12px',
          fontFamily: 'Source Han Serif, serif'
        },
        y: {
          formatter: function (value: number) {
            return currentOption.unit + value
          }
        }
      },
      legend: {
        position: 'top',
        horizontalAlign: 'right',
        fontFamily: 'Source Han Serif, serif',
        fontSize: '12px'
      }
    }
  } catch (error) {
    console.error('动态图表配置计算错误:', error)
    return {
      chart: {
        type: 'line',
        height: 300,
        toolbar: { show: false },
        fontFamily: 'Source Han Serif, serif',
        background: 'transparent'
      },
      colors: ['#6560F0', '#6560F080'],
      stroke: {
        width: [3, 2],
        curve: 'smooth'
      },
      dataLabels: { enabled: false }
    }
  }
})

// 更新动态图表数据
function updateDynamicChart() {
  // 图表数据会通过 computed 属性自动更新
  console.log(`📊 [图表] 动态图表数据已更新: ${selectedChartDataType.value}`)
}

// 营业额分时段分析图表配置（保留作为备用）
const hourlyRevenueChartSeries = ref([
  {
    name: '营业额',
    data: [1200, 2800, 4500, 6800, 8900, 12400, 15600, 18200, 21500, 19800, 16400, 13200, 9800, 7200, 5400, 3600, 2100, 1800, 2400, 3200, 2800, 2200, 1600, 1000]
  }
])

// 销量变化曲线图配置（旧的hourlyRevenueChartOptions已被dynamicChartOptions替代）
const weeklyTrendChartSeries = ref([
  {
    name: '销量',
    data: [1240, 1580, 1320, 1680, 1890, 2100, 1950]
  }
])

const weeklyTrendChartOptions = ref({
  chart: {
    type: 'line',
    height: 250,
    toolbar: {
      show: false
    },
    fontFamily: 'Source Han Serif, serif',
    background: 'transparent'
  },
  colors: ['#28a745'],
  stroke: {
    curve: 'smooth',
    width: 3
  },
  markers: {
    size: 6,
    colors: ['#28a745'],
    strokeColors: '#fff',
    strokeWidth: 2,
    hover: {
      size: 8
    }
  },
  xaxis: {
    categories: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    labels: {
      style: {
        colors: '#6c757d',
        fontSize: '11px',
        fontFamily: 'Source Han Serif, serif'
      }
    },
    axisBorder: {
      show: false
    },
    axisTicks: {
      show: false
    }
  },
  yaxis: {
    labels: {
      style: {
        colors: '#6c757d',
        fontSize: '11px',
        fontFamily: 'Source Han Serif, serif'
      },
      formatter: function (value: number) {
        return value + '杯'
      }
    }
  },
  grid: {
    borderColor: '#f0f1f3',
    strokeDashArray: 3
  },
  tooltip: {
    theme: 'light',
    style: {
      fontSize: '12px',
      fontFamily: 'Source Han Serif, serif'
    },
    y: {
      formatter: function (value: number) {
        return value + '杯'
      }
    }
  }
})

// 监听日期变化
watch(() => currentQueryDate.value, () => {
  // 重置加载状态
  isFullyLoaded.value = false
  isLoadingDetails.value = false
  fetchFullscreenData()
})

// 事件处理函数
const onDateChange = (selectedDates: Date[]) => {
  console.log('选择的日期范围:', selectedDates)
  // 当手动选择日期时，清除快速选择的激活状态
  quickDateActive.value = 'manual'
  timeRange.value = 'manual'

  if (selectedDates && selectedDates.length > 0) {
    // 更新查询日期为选中的第一个日期
    currentQueryDate.value = selectedDates[0]

    if (selectedDates.length > 1) {
      // 如果选择了日期范围
      currentQueryEndDate.value = selectedDates[1]
      console.log(`📅 [手动选择] 日期范围: ${selectedDates[0].toLocaleDateString()} 至 ${selectedDates[1].toLocaleDateString()}`)
    } else {
      // 如果只选择了单个日期
      currentQueryEndDate.value = null
      console.log(`📅 [手动选择] 单个日期: ${selectedDates[0].toLocaleDateString()}`)
    }
  }
}

// 初始化时选择今日
onMounted(async () => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
  selectQuickDate('today')
  // selectQuickDate 会触发 currentQueryDate 变化，进而触发 watch 调用 fetchFullscreenData
  // 所以这里不需要再次调用 fetchFullscreenData()
})
</script>

<style scoped>
/* 全屏布局 */
.fullscreen-dashboard {
  min-height: 100vh;
  width: 100%;
  background: var(--splash-body-bg);
  color: var(--bs-body-color);
  font-family: 'Source Han Serif', serif;
  overflow-x: hidden;
  padding: 20px;
}

/* 顶部标题栏 */
.dashboard-header {
  padding: 20px 0;
  background: var(--splash-white-color);
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1920px;
  margin: 0 auto;
  padding: 0 30px;
}

.title-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.dashboard-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  color: var(--splash-primary-color);
  font-family: 'Source Han Serif', serif;
}

.date-time-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.current-date {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--splash-secondary-color);
  font-family: 'Source Han Serif', serif;
}

.current-time {
  font-size: 1.1rem;
  color: var(--splash-secondary-color);
  font-weight: 500;
  font-family: 'Source Han Serif', serif;
}

.controls-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.date-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-end;
}

.quick-date-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.quick-btn {
  padding: 6px 12px;
  border: 2px solid var(--splash-primary-color);
  background: var(--splash-white-color);
  color: var(--splash-primary-color);
  border-radius: 6px;
  font-weight: 500;
  font-family: 'Source Han Serif', serif;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 13px;
  white-space: nowrap;
  min-width: 50px;
}

.quick-btn:hover {
  background: var(--splash-primary-color);
  color: var(--splash-white-color);
  transform: translateY(-1px);
}

.quick-btn.active {
  background: var(--splash-primary-color);
  color: var(--splash-white-color);
  box-shadow: 0 2px 8px rgba(101, 96, 240, 0.3);
}

.time-selector {
  min-width: 280px;
}

.date-picker {
  border: 2px solid #d1d5db;
  border-radius: 8px;
  padding: 12px 16px;
  font-family: 'Source Han Serif', serif;
  font-size: 16px;
  background: var(--splash-white-color);
  color: var(--bs-body-color);
  width: 100%;
}

.date-picker::placeholder {
  color: var(--splash-secondary-color);
}

.date-picker:focus {
  border-color: var(--splash-primary-color);
  box-shadow: 0 0 0 3px rgba(101, 96, 240, 0.1);
  outline: none;
}



/* 主要内容区域 */
.dashboard-content {
  max-width: 1920px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* KPI指标区 */
.kpi-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.kpi-card {
  background: var(--splash-white-color);
  border: 0;
  border-radius: 8px;
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.kpi-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.kpi-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  flex-shrink: 0;
}

.revenue-icon {
  background: var(--splash-success-color);
  color: var(--splash-white-color);
}

.orders-icon {
  background: var(--splash-info-color);
  color: var(--splash-white-color);
}

.cups-icon {
  background: var(--splash-warning-color);
  color: var(--splash-white-color);
}

.kpi-content {
  flex: 1;
}

.kpi-label {
  font-size: 1.1rem;
  color: var(--splash-secondary-color);
  margin-bottom: 8px;
  font-weight: 500;
  font-family: 'Source Han Serif', serif;
}

.kpi-value {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.kpi-value .value {
  font-size: 2.5rem;
  font-weight: 700;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.kpi-value .unit {
  font-size: 1.2rem;
  font-weight: 600;
  font-family: 'Source Han Serif', serif;
}

/* 统一单位颜色 */
.currency {
  color: var(--splash-success-color);
}

.order-unit {
  color: var(--splash-info-color);
}

.cup-unit {
  color: var(--splash-warning-color);
}

/* 环比数据显示 */
.kpi-comparison {
  margin-top: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  font-weight: 500;
}

.comparison-icon {
  font-size: 0.8rem;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.comparison-text {
  font-family: 'Source Han Serif', serif;
}

.trend-up {
  color: var(--splash-success-color);
}

.trend-up .comparison-icon {
  color: var(--splash-success-color);
}

.trend-down {
  color: var(--splash-danger-color);
}

.trend-down .comparison-icon {
  color: var(--splash-danger-color);
}

.trend-neutral {
  color: var(--splash-secondary-color);
}

.trend-neutral .comparison-icon {
  color: var(--splash-secondary-color);
}

/* 分析区域 */
.analysis-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.analysis-card {
  background: var(--splash-white-color);
  border: 0;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
}

.card-header i {
  font-size: 1.5rem;
  color: var(--splash-warning-color);
}

/* 图表控制器样式 */
.chart-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.data-type-selector {
  display: flex;
  gap: 8px;
}

.data-type-btn {
  padding: 6px 12px;
  border: 1px solid #e9ecef;
  background: var(--splash-white-color);
  color: var(--splash-secondary-color);
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Source Han Serif', serif;
}

.data-type-btn:hover {
  border-color: var(--splash-primary-color);
  color: var(--splash-primary-color);
}

.data-type-btn.active {
  background: var(--splash-primary-color);
  border-color: var(--splash-primary-color);
  color: var(--splash-white-color);
}

.card-header h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
  color: var(--bs-body-color);
  font-family: 'Source Han Serif', serif;
}

/* 产品网格 */
.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.product-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.product-item:last-child {
  border-bottom: none;
}

.product-item:hover {
  background: #f8f9fa;
}

.product-rank {
  width: 30px;
  height: 30px;
  background: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: #000000;
  flex-shrink: 0;
  font-family: 'Source Han Serif', serif;
}

.product-name {
  flex: 1;
  font-weight: 500;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.product-sales {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.sales-value {
  font-weight: 600;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.sales-unit {
  font-size: 0.9rem;
  font-weight: 500;
  font-family: 'Source Han Serif', serif;
}

/* 图表容器 */
.chart-container {
  margin-top: 10px;
}

/* 排行区域 */
.ranking-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.ranking-card {
  background: var(--splash-white-color);
  border: 0;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 0;
  border-bottom: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-item:hover {
  background: #f8f9fa;
}

.rank-number {
  width: 35px;
  height: 35px;
  background: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: #000000;
  flex-shrink: 0;
  font-family: 'Source Han Serif', serif;
}

.store-info,
.product-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.store-name,
.product-name {
  font-weight: 500;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.store-revenue,
.product-cups {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.revenue-value,
.cups-value {
  font-weight: 600;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.revenue-unit,
.cups-unit {
  font-size: 0.9rem;
  font-weight: 500;
  font-family: 'Source Han Serif', serif;
}

/* 汇总区域 */
.summary-section {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}

.summary-card {
  background: var(--splash-white-color);
  border: 0;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.summary-stats {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f1f3;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 1rem;
  color: var(--splash-secondary-color);
  font-weight: 500;
  font-family: 'Source Han Serif', serif;
}

.stat-value {
  display: flex;
  align-items: baseline;
  gap: 6px;
}

.stat-value .value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.stat-value .unit {
  font-size: 1rem;
  font-weight: 500;
  font-family: 'Source Han Serif', serif;
}

.growth-positive {
  color: var(--splash-success-color) !important;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .dashboard-title {
    font-size: 2rem;
  }

  .kpi-section {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }

  .ranking-section {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }
}

@media (max-width: 1200px) {
  .dashboard-content {
    padding: 20px;
    gap: 20px;
  }

  .analysis-section {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .ranking-section {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: 15px 20px;
  }

  .header-content {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .date-time-section {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .current-date {
    font-size: 1rem;
  }

  .current-time {
    font-size: 0.9rem;
  }

  .controls-section {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .date-controls {
    width: 100%;
  }

  .quick-date-buttons {
    justify-content: center;
  }

  .time-selector {
    min-width: auto;
    width: 100%;
  }

  .dashboard-title {
    font-size: 1.5rem;
  }

  .kpi-section {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .kpi-card {
    padding: 20px;
  }

  .kpi-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .kpi-value .value {
    font-size: 2rem;
  }

  .ranking-section {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .summary-section {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .dashboard-content {
    padding: 15px;
  }

  .current-date {
    font-size: 0.9rem;
  }

  .current-time {
    font-size: 0.8rem;
  }

  .quick-btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .date-picker {
    padding: 10px 12px;
    font-size: 14px;
  }

  .kpi-card {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .analysis-card,
  .ranking-card,
  .summary-card {
    padding: 20px;
  }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #6c757d;
  font-size: 14px;
  text-align: center;
  min-height: 120px;
}

/* 骨架屏样式 */
.skeleton {
  pointer-events: none;
}

.skeleton-circle {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-text {
  height: 16px;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  width: 80%;
}

.skeleton-text-small {
  height: 12px;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  width: 60%;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
