import {createWebHistory, createRouter} from "vue-router";
import LoginPage from "@/pages/LoginPage.vue";
import ErrorPage from "@/pages/ErrorPage.vue";
import { useAuthStore } from "@/store/useAuthStore";

// 主要模块页面
import OverviewWorkspace from "@/pages/OverviewWorkspace.vue";
// import ProductManagement from "@/pages/ProductManagement.vue";
// import SystemManagement from "@/pages/SystemManagement.vue";
// import DataCenter from "@/pages/DataCenter.vue";
// import DeviceManagement from "@/pages/DeviceManagement.vue";

// 独立页面组件
// import DashboardStandalone from "@/pages/standalone/DashboardStandalone.vue"; // 已注释，数据总览集成到主页
import OrdersStandalone from "@/pages/standalone/OrdersStandalone.vue";
import StoresStandalone from "@/pages/standalone/StoresStandalone.vue";
import ProductManagementStandalone from "@/pages/standalone/ProductManagementStandalone.vue";
import DataCenterStandalone from "@/pages/standalone/DataCenterStandalone.vue";
import DeviceManagementStandalone from "@/pages/standalone/DeviceManagementStandalone.vue";
import SystemManagementStandalone from "@/pages/standalone/SystemManagementStandalone.vue";
import MerchantManagementStandalone from "@/pages/standalone/MerchantManagementStandalone.vue";



// 保留现有的详细页面组件
import DashBoard from "@/pages/DashBoard.vue";
import CustomerStats from "@/pages/customers/CustomerStats.vue"
import StoreStats from "@/pages/stores/StoreStats.vue"
import OrderStats from "@/pages/orders/OrderStats.vue"
import StoreMachines from "@/pages/stores/_id/StoreMachines.vue";
import StoreOrders from "@/pages/stores/_id/StoreOrders.vue";
import DataList from "@/components/DataList.vue";
import StoreDetailReborn from "@/pages/stores/StoreDetailReborn.vue";
import StoreDashboard from "@/pages/stores/StoreDashboard.vue";
import StoreBusinessDashboard from "@/pages/stores/StoreBusinessDashboard.vue";
import AllStoresDashboard from "@/pages/stores/AllStoresDashboard.vue";
import AllStoresFullscreenDashboard from "@/pages/stores/AllStoresFullscreenDashboard.vue";
import StoreManagement from "@/pages/stores/StoreManagement.vue";
import SystemConfig from "@/pages/system/SystemConfig.vue";


// 新的门店详情页面组件
import StoreDetail from "@/pages/stores/StoreDetail.vue";

// 测试页面组件
import ProductApiTest from "@/pages/test/ProductApiTest.vue";
import StoreApiTest from "@/pages/test/StoreApiTest.vue";

const routes = [
    {
        path: "/login",
        component: LoginPage,
        meta: { layout: "auth" }
    },
    // 默认路由重定向到总览和工作台
    {
        path: "/",
        redirect: "/overview-workspace"
    },
    // 三个主要模块
    {
        path: "/overview-workspace",
        name: "OverviewWorkspace",
        component: OverviewWorkspace,
    },
    {
        path: "/product-management",
        name: "ProductManagement",
        component: ProductManagementStandalone,
        meta: {
            layout: "standalone"
        }
    },
    {
        path: "/system-management",
        name: "SystemManagement",
        component: SystemManagementStandalone,
        meta: {
            layout: "standalone"
        }
    },
    {
        path: "/data-center",
        name: "DataCenter",
        component: DataCenterStandalone,
        meta: {
            layout: "standalone"
        }
    },
    {
        path: "/device-management",
        name: "DeviceManagement",
        component: DeviceManagementStandalone,
        meta: {
            layout: "standalone"
        }
    },
    {
        path: "/merchant-management",
        name: "MerchantManagement",
        component: MerchantManagementStandalone,
        meta: {
            layout: "standalone"
        }
    },

    // 独立页面路由
    // 注释掉独立的dashboard路由，因为数据总览现在集成在主页中
    // {
    //     path: "/dashboard",
    //     name: "DashboardStandalone",
    //     component: DashboardStandalone,
    //     meta: {
    //         layout: "standalone"
    //     }
    // },
    {
        path: "/orders",
        name: "OrdersStandalone",
        component: OrdersStandalone,
        meta: {
            layout: "standalone"
        }
    },
    {
        path: "/stores",
        name: "StoresStandalone",
        component: StoresStandalone,
        meta: {
            layout: "standalone"
        }
    },
    // 保留现有的详细页面路由（用于内部组件）
    {
        path: "/dashboard-internal",
        component: DashBoard,
    },
    {
        path: "/customers",
        component: CustomerStats,
    },
    {
        path: "/orders-internal",
        component: OrderStats,
    },
    {
        path: "/stores-internal",
        component: StoreStats,
    },
    {
        path: "/store-management",
        name: "StoreManagement",
        component: StoreManagement,
    },
    {
        path: "/system-config",
        name: "SystemConfig",
        component: SystemConfig,
    },

    {
        path: "/all-stores-dashboard",
        name: "AllStoresDashboard",
        component: AllStoresDashboard,
    },
    {
        path: "/all-stores-fullscreen",
        name: "AllStoresFullscreenDashboard",
        component: AllStoresFullscreenDashboard,
        meta: {
            layout: 'fullscreen',
        },
    },
    // 新的门店详情页面路由结构
    {
        path: "/stores/:id",
        name: "StoreDetailPage",
        component: StoreDetail,
        redirect: { name: "StoreDetailReborn" },
        children: [
            {
                path: "details",
                name: "StoreDetailReborn",
                component: StoreDetailReborn,
                props: true
            },
            {
                path: "business",
                name: "StoreDetailBusiness",
                component: StoreBusinessDashboard,
                props: true
            },
            {
                path: "dashboard",
                name: "StoreDetailDashboard",
                component: StoreDashboard,
                props: true
            },

        ],
        props: true
    },
    // 店铺详情页面路由（直接跳转到StoreDetailReborn.vue）
    {
        path: "/stores/:id/info",
        name: "StoreDetailInfo",
        component: StoreDetailReborn,
        children: [
            {
                path: "machines",
                component: StoreMachines,
            },
            {
                path: "orders",
                component: StoreOrders,
            }
        ],
        props: true
    },
    // 保留原有的大屏模式路由（重命名以避免冲突）
    {
        path: "/stores/:id/fullscreen",
        name: "StoreDetailFullscreen",
        component: StoreDetailReborn,
        meta:{
            layout:'fullscreen',
        },
        children: [
            {
                path: "machines",
                component: StoreMachines,
            },
            {
                path: "orders",
                component: StoreOrders,
            }
        ],
        props: true
    },
    {
        path: "/:pathMatch(.*)*",
        name: "ErrorPage",
        component: ErrorPage,
    },
    {
        path: "/datalist",
        name: "DataListPage",
        component: DataList,
    },
    // API测试页面
    {
        path: "/test/product-api",
        name: "ProductApiTest",
        component: ProductApiTest,
        meta: {
            layout: "default",
            title: "商品API测试"
        }
    },
    {
        path: "/test/store-api",
        name: "StoreApiTest",
        component: StoreApiTest,
        meta: {
            layout: "default",
            title: "门店API测试"
        }
    }
]
export const router = createRouter({
        history: createWebHistory(),
        routes: routes,
        linkExactActiveClass: "active", // 这部分与sidebar的选中显示效果有关
        scrollBehavior() {
            return {
                top: 0,// 导航到新标签时默认平滑滚动到页面顶部
                behavior: "smooth"// 平滑地滚动
            }
        },
    }
)

// 路由守卫
router.beforeEach(async (to, from, next) => {
    // 获取认证store
    const authStore = useAuthStore()

    // 公开路由（不需要认证）
    const publicRoutes = ['/login', '/register']
    const isPublicRoute = publicRoutes.includes(to.path)

    // 如果是公开路由
    if (isPublicRoute) {
        // 如果已经登录，重定向到首页
        if (authStore.isLoggedIn) {
            next('/')
        } else {
            next()
        }
        return
    }

    // 检查是否已登录
    if (!authStore.token) {
        // 未登录，重定向到登录页
        next('/login')
        return
    }

    // 如果有token但没有用户信息，尝试获取用户信息
    if (authStore.token && !authStore.userInfo) {
        try {
            await authStore.getUserInfo()
            next()
        } catch (error) {
            // 获取用户信息失败，清除token并重定向到登录页
            console.error('获取用户信息失败:', error)
            authStore.logout()
            next('/login')
        }
        return
    }

    // 已登录且有用户信息，允许访问
    next()
})