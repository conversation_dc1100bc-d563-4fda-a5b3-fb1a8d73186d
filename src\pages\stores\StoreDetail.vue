<template>
  <div class="store-detail-page">
    <!-- 门店基本信息头部 -->
    <div class="card mb-25 border-0 rounded-0 bg-white box-shadow">
      <div class="card-body p-20">
        <div class="row align-items-center">
          <div class="col-lg-8">
            <div class="store-header-info">
              <div class="d-flex align-items-center mb-2">
                <router-link to="/stores" class="btn btn-link p-0 me-3 text-decoration-none">
                  <i class="flaticon-left-arrow fs-4 text-muted"></i>
                </router-link>
                <h2 class="fw-bold mb-0 text-black">{{ currentStore?.storeName || '门店名称' }}</h2>
              </div>
              <div class="store-meta d-flex flex-wrap gap-3">
                <span class="text-muted">
                  <i class="flaticon-location me-1"></i>
                  {{ currentStore?.storeAddress || '门店地址' }}
                </span>
                <span class="text-muted">
                  <i class="flaticon-phone me-1"></i>
                  {{ currentStore?.contactPhone || '联系电话' }}
                </span>
                <span class="badge" :class="getStatusBadgeClass(currentStore?.status)">
                  {{ getStatusText(currentStore?.status) }}
                </span>
              </div>
            </div>
          </div>
          <div class="col-lg-4 text-end">
            <div class="store-actions">
              <span class="text-muted small">
                更新时间: {{ formatDate(currentStore?.updateTime) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 二级导航组件 -->
    <StoreSubNavigation 
      :current-route="currentSubRoute"
      :store-id="storeId"
      @navigate="handleSubNavigation"
    />

    <!-- 子页面内容区域 -->
    <div class="store-detail-content">
      <router-view :store-id="storeId" :store="currentStore" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStoreStore } from '@/store/useStoreStore'
import { storeToRefs } from 'pinia'
import StoreSubNavigation from '@/components/StoreSubNavigation.vue'
import type { storeDetail } from '@/types/store'

// 路由和状态管理
const route = useRoute()
const router = useRouter()
const storeStore = useStoreStore()
const { stores } = storeToRefs(storeStore)

// 响应式数据
const currentStore = ref<storeDetail | null>(null)
const isLoading = ref(true)

// 计算属性
const storeId = computed(() => Number(route.params.id))
const currentSubRoute = computed(() => {
  const routeName = route.name as string
  if (routeName?.includes('Business')) return 'business'
  if (routeName?.includes('Dashboard')) return 'dashboard'
  if (routeName?.includes('Reborn')) return 'details'
  return 'details' // 默认显示店铺总览
})

// 获取门店信息
async function fetchStoreInfo() {
  try {
    isLoading.value = true
    
    // 如果stores为空，先获取门店列表
    if (stores.value.length === 0) {
      await storeStore.fetchStore()
    }
    
    // 根据ID找到对应门店
    const store = stores.value.find(s => s.storeId === storeId.value)
    if (store) {
      currentStore.value = store
      // 设置为选中门店（用于其他组件）
      storeStore.selectStore(store)
    } else {
      console.error('未找到对应门店信息')
    }
  } catch (error) {
    console.error('获取门店信息失败:', error)
  } finally {
    isLoading.value = false
  }
}

// 处理子导航切换
function handleSubNavigation(subRoute: string) {
  const routeMap = {
    details: 'StoreDetailReborn',
    business: 'StoreDetailBusiness',
    dashboard: 'StoreDetailDashboard'
  }

  const targetRoute = routeMap[subRoute as keyof typeof routeMap]
  if (targetRoute) {
    router.push({
      name: targetRoute,
      params: { id: storeId.value }
    })
  }
}

// 获取状态徽章样式
function getStatusBadgeClass(status?: string) {
  switch (status) {
    case '营业中':
    case 'OPEN':
      return 'bg-success text-white'
    case '暂停营业':
    case 'CLOSED':
      return 'bg-danger text-white'
    case '维护中':
    case 'MAINTENANCE':
      return 'bg-warning text-dark'
    default:
      return 'bg-secondary text-white'
  }
}

// 获取状态文本
function getStatusText(status?: string) {
  switch (status) {
    case 'OPEN':
      return '营业中'
    case 'CLOSED':
      return '暂停营业'
    case 'MAINTENANCE':
      return '维护中'
    default:
      return status || '未知状态'
  }
}

// 格式化日期
function formatDate(dateString?: string) {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  fetchStoreInfo()
})

// 监听路由参数变化
watch(() => route.params.id, (newId) => {
  if (newId && Number(newId) !== storeId.value) {
    fetchStoreInfo()
  }
})
</script>

<style scoped>
.store-detail-page {
  min-height: 100vh;
}

.store-header-info h2 {
  color: #2c3e50;
  font-size: 1.8rem;
}

.store-meta {
  font-size: 0.9rem;
}

.store-meta i {
  color: #6c757d;
}

.store-detail-content {
  margin-top: 20px;
}

.box-shadow {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 返回按钮样式 */
.btn-link {
  transition: all 0.2s ease;
}

.btn-link:hover {
  transform: translateX(-2px);
}

.btn-link:hover i {
  color: #007bff !important;
}

@media (max-width: 768px) {
  .store-header-info h2 {
    font-size: 1.5rem;
  }

  .store-meta {
    flex-direction: column;
    gap: 0.5rem !important;
  }

  /* 移动端返回按钮调整 */
  .btn-link {
    margin-right: 0.5rem !important;
  }

  .btn-link i {
    font-size: 1.2rem !important;
  }
}
</style>
