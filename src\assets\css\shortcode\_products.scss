.products-sidebar-filter {
    .title {
        border-bottom: 1px dashed #d9e9ef;
        margin-bottom: 25px;
        padding: 25px 30px;
    }
    .sidebar-item {
        padding: {
            left: 30px;
            right: 30px;
            bottom: 50px;
        };
        h6 {
            margin-bottom: 20px;
        }
        .search-box {
            .form-control {
                background: #F5F4FA;
                padding: {
                    top: 14px;
                    bottom: 14px;
                };
            }
            button {
                top: 50%;
                right: 15px;
                line-height: 1;
                margin-top: 1px;
                font-size: 17px;
                position: absolute;
                transform: translateY(-50%);
            }
        }
        .previous-searches-list {
            margin-bottom: -10px;
            
            .item {
                padding: 4px 22px 4px 8px;
                background: #F7F7F9;
                border-radius: 2px;
                margin: {
                    right: 10px;
                    bottom: 10px;
                };
                button {
                    top: 50%;
                    right: 8px;
                    line-height: 1;
                    color: #8E8DA2;
                    font-size: 10px;
                    margin-top: 1px;
                    position: absolute;
                    transform: translateY(-50%);

                    &:hover {
                        color: red;
                    }
                }
            }
        }
        .clear-btn {
            line-height: 1.2;
        
            &::before {
                left: 0;
                right: 0;
                bottom: 0;
                height: 1px;
                content: '';
                position: absolute;
                transition: var(--transition);
                background: var(--splash-black-color);
            }
            &:hover {
                &::before {
                    transform: scaleX(0);
                }
            }
        }
        .categories-list {
            li {
                border-bottom: 1px solid #EEEDF2;
                padding: {
                    top: 10px;
                    bottom: 10px;
                };
                &:first-child {
                    padding-top: 0;
                }
            }
        }
        .brands-list {
            li {
                border-bottom: 1px solid #EEEDF2;
                padding: {
                    top: 10px;
                    bottom: 10px;
                };
                &:first-child {
                    padding-top: 0;
                }
            }
        }
        .see-more-btn {
            line-height: 1.25;
        
            &::before {
                left: 0;
                right: 0;
                bottom: 0;
                height: 1px;
                content: '';
                position: absolute;
                transition: var(--transition);
                background: var(--splash-primary-color);
            }
            &:hover {
                &::before {
                    transform: scaleX(0);
                }
            }
        }
        .ratings-list {
            li {
                border-bottom: 1px solid #EEEDF2;
                padding: {
                    top: 10px;
                    bottom: 10px;
                };
                span {
                    i {
                        top: 1px;
                        line-height: 1;
                        color: #F3C44C;
                        margin-right: 4px;
                        position: relative;
                    }
                }
                &:first-child {
                    padding-top: 0;
                }
            }
        }
        .pricing-filter {
            .range-slider {
                position: relative;
                height: 5px;
                
                input {
                    &[type=range] {
                        -webkit-appearance: none;
                        background: transparent;
                        border-radius: 5px;
                        position: absolute;
                        width: 100%;
                        left: 0;
                        top: 0;

                        &::-webkit-slider-thumb {
                            background: var(--splash-primary-color);
                            -webkit-appearance: none;
                            position: relative;
                            border-radius: 50%;
                            margin-top: -5px;
                            cursor: pointer;
                            height: 14px;
                            width: 14px;
                            z-index: 1;
                        }
                        &::-webkit-slider-runnable-track {
                            width: 100%;
                            height: 5px;
                            border: none;
                            border-radius: 5px;
                            background: #f4f4f4;
                        }
                    }
                }
            }
            .price-content {
                margin-top: 12px;
            }
        }
    }
}
.single-product-box {
    .card-body {
        .image {
            .fav {
                color: #EF2929;
                font-size: 18px;
                height: 38px;
                width: 38px;
                right: 15px;
                top: 15px;

                i {
                    left: 0;
                    right: 0;
                    top: 50%;
                    line-height: 1;
                    margin-top: 1px;
                    position: absolute;
                    transform: translateY(-50%);
                }
            }
        }
        .content {
            .reviews {
                .rating {
                    color: #F3C44C;
                    i {
                        margin-right: 4px;
                    }
                }
            }
            .add-to-cart-btn {
                background-color: transparent;
                padding: 11px 35px 11px 20px;
                border: 1px solid #EDEBF3;

                i {
                    top: 50%;
                    line-height: 1;
                    font-size: 16px;
                    position: absolute;
                    transform: translateY(-50%);
                    color: var(--splash-primary-color);
                    margin: {
                        left: 7px;
                    };
                }
                &:hover {
                    background-color: var(--splash-primary-color);
                    border-color: var(--splash-primary-color);
                    color: var(--splash-white-color);

                    i {
                        color: var(--splash-white-color);
                    }
                }
            }
        }
    }
}

// Dark Mode
.dark {
    .products-sidebar-filter {
        .title {
            border-bottom-color: #45445e;
        }
        .sidebar-item {
            .search-box {
                .form-control {
                    background: var(--splash-black-color);
                }
            }
            .previous-searches-list {
                .item {
                    background: var(--splash-black-color);

                    button {
                        color: #BCBBC7;
    
                        &:hover {
                            color: red;
                        }
                    }
                }
            }
            .clear-btn {
                &::before {
                    background: var(--splash-white-color);
                }
            }
            .categories-list {
                li {
                    border-bottom-color: #45445e;
                }
            }
            .brands-list {
                li {
                    border-bottom-color: #45445e;
                }
            }
            .ratings-list {
                li {
                    border-bottom-color: #45445e;
                }
            }
            .pricing-filter {
                .range-slider {
                    input {
                        &[type=range] {
                            &::-webkit-slider-thumb {
                                background: var(--splash-primary-color);
                            }
                            &::-webkit-slider-runnable-track {
                                background: var(--splash-black-color);
                            }
                        }
                    }
                }
            }
        }
    }
    .single-product-box {
        .card-body {
            .content {
                .add-to-cart-btn {
                    border-color: #45445e;
    
                    &:hover {
                        border-color: var(--splash-primary-color);
                    }
                }
            }
        }
    }
}

@media only screen and (max-width : 767px) {

    .products-sidebar-filter {
        .title {
            margin-bottom: 20px;
            padding: 15px;
        }
        .sidebar-item {
            padding: {
                left: 15px;
                right: 15px;
                bottom: 20px;
            };
            h6 {
                margin-bottom: 15px;
            }
            .search-box {
                .form-control {
                    padding: {
                        top: 13px;
                        bottom: 13px;
                    };
                }
                button {
                    font-size: 15px;
                }
            }
            .previous-searches-list {
                margin-bottom: -5px;
                
                .item {
                    margin: {
                        right: 5px;
                        bottom: 5px;
                    };
                }
            }
            .categories-list {
                li {
                    padding: {
                        top: 9px;
                        bottom: 9px;
                    };
                }
            }
            .brands-list {
                li {
                    padding: {
                        top: 9px;
                        bottom: 9px;
                    };
                }
            }
            .ratings-list {
                li {
                    padding: {
                        top: 9px;
                        bottom: 9px;
                    };
                }
            }
        }
    }
    .single-product-box {
        .card-body {
            .image {
                .fav {
                    font-size: 16px;
                    height: 35px;
                    width: 35px;
                }
            }
        }
    }

}

@media only screen and (min-width : 768px) and (max-width : 991px) {

    .products-sidebar-filter {
        .title {
            padding: 20px 25px;
        }
        .sidebar-item {
            padding: {
                left: 25px;
                right: 25px;
                bottom: 25px;
            };
            h6 {
                margin-bottom: 15px;
            }
        }
    }

}

@media only screen and (min-width : 992px) and (max-width : 1199px) {

    .products-sidebar-filter {
        .title {
            padding: 25px;
        }
        .sidebar-item {
            padding: {
                left: 25px;
                right: 25px;
                bottom: 25px;
            };
            h6 {
                margin-bottom: 15px;
            }
        }
    }

}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {

    .products-sidebar-filter {
        .title {
            padding: 25px;
        }
        .sidebar-item {
            padding: {
                left: 25px;
                right: 25px;
                bottom: 25px;
            };
            h6 {
                margin-bottom: 15px;
            }
        }
    }

}