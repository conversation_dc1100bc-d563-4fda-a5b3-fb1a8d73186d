# 饮品商品管理 API 测试界面

## 概述

这是一个专门用于测试饮品商品管理相关API接口的测试界面，可以帮助开发者验证API接口的功能和数据结构。

## 访问方式

1. **直接访问**: `http://localhost:8080/test/product-api`
2. **通过导航**: 在任意页面的导航菜单中选择"API测试"

## 功能特性

### 🔧 测试控制面板
- **门店ID**: 设置要测试的门店ID（默认：100）
- **商品ID**: 设置要测试的商品ID（默认：148）
- **原型ID**: 设置要测试的原型ID（默认：100）
- **分页参数**: 设置页码和页大小

### 🚀 支持的API接口测试

1. **获取商品列表** - `GET /manager/product/list`
   - 支持分页查询
   - 支持按门店筛选

2. **获取商品详情** - `GET /manager/product/{drinkId}`
   - 根据商品ID获取详细信息

3. **获取门店菜单** - `GET /manager/product/store/{storeId}`
   - 获取指定门店的所有商品

4. **根据原型查询商品** - `GET /manager/product/prototype/{prototypeId}`
   - 查询使用指定原型的所有商品

5. **创建商品** - `POST /manager/product`
   - 测试商品创建功能

6. **更新商品** - `PUT /manager/product`
   - 测试商品更新功能

7. **删除商品** - `DELETE /manager/product/{drinkId}`
   - 测试商品删除功能

8. **导出商品** - `POST /manager/product/export`
   - 测试商品数据导出功能

9. **批量操作测试**
   - 批量测试多个门店的商品数据

### 📊 测试结果展示

- **实时状态**: 显示成功/失败统计
- **详细信息**: 包含请求URL、参数、响应状态、耗时等
- **数据展示**: 格式化显示API返回的JSON数据
- **复制功能**: 一键复制响应数据到剪贴板
- **时间戳**: 记录每次测试的执行时间

## 使用说明

### 基本测试流程

1. **设置参数**: 在控制面板中输入相应的测试参数
2. **选择接口**: 点击对应的测试按钮
3. **查看结果**: 在下方查看详细的测试结果
4. **分析数据**: 复制响应数据进行进一步分析

### 测试建议

1. **先测试基础接口**: 建议先测试"获取商品列表"等基础查询接口
2. **验证数据结构**: 重点关注返回数据的字段名称和数据类型
3. **测试边界情况**: 使用不存在的ID测试错误处理
4. **批量测试**: 使用批量操作测试系统性能

### 常见问题

**Q: 测试失败怎么办？**
A: 检查控制台错误信息，确认：
- 后端服务是否启动
- 网络连接是否正常
- 参数是否正确

**Q: 如何查看详细的错误信息？**
A: 点击失败的测试结果，查看响应数据部分的错误详情

**Q: 可以测试自定义参数吗？**
A: 可以修改控制面板中的参数，或者直接修改测试代码中的请求参数

## 开发说明

### 文件位置
- 测试页面: `src/pages/test/ProductApiTest.vue`
- 路由配置: `src/router/index.ts`
- 导航配置: `src/components/layouts/StandalonePageLayout.vue`

### 扩展测试

如需添加新的API测试，可以：

1. 在测试页面中添加新的测试按钮
2. 实现对应的测试方法
3. 调用 `executeApiTest` 函数执行测试

```javascript
const testNewApi = () => {
  executeApiTest(
    '测试标题',
    'HTTP方法',
    'API路径',
    请求参数,
    请求体数据
  )
}
```

## 注意事项

- 测试数据可能会影响实际业务数据，请在测试环境中使用
- 删除操作请谨慎使用，避免误删重要数据
- 建议在测试前备份重要数据
- 测试结果仅供开发调试使用

## 更新日志

- v1.0.0: 初始版本，支持基础的商品管理API测试
- 支持9种主要API接口的测试
- 提供详细的测试结果展示和数据分析功能
