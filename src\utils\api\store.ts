import serviceAxios from "@/utils/serviceAxios";
import type {storeDetail} from "@/types";
import type {queryPageParams} from "@/types/queryPageParams";
import type {TableDataInfo} from "@/types/tableDataInfo";

// 门店详情API响应类型
export interface StoreDetailResponse {
    code: number;
    msg: string;
    data: storeDetail;
}

export function getStoreList(params: queryPageParams): Promise<TableDataInfo<storeDetail>> {
    return serviceAxios.get("/manager/store/list", {params});
}

export function getStoreByStoreId(storeId: number): Promise<StoreDetailResponse> {
    return serviceAxios.get(`/manager/store/${storeId}`);
}