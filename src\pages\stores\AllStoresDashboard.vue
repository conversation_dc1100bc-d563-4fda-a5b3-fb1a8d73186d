<template>
  <div class="main-content">
    <!-- 面包屑导航 -->
    <BreadCrumb title="全门店营业情况" :breadcrumb="breadcrumb" />

    <!-- 页面标题与时间选择器 -->
    <div class="d-flex justify-content-between align-items-center mb-30">
      <h1 class="main-title">全门店营业情况</h1>
      <div class="time-selector">
        <flat-pickr
          v-model="selectedDate"
          :config="datePickerConfig"
          class="form-control"
          placeholder="选择日期范围"
          @on-change="onDateChange"
        />
      </div>
    </div>

    <!-- 第一行 - 核心KPI指标区 -->
    <div class="row mb-30">
      <div class="col-lg-4 col-md-6 mb-20">
        <div class="card border-0 rounded-0 bg-white h-100 box-shadow">
          <div class="card-body p-20 text-center">
            <div class="metric-item">
              <div class="metric-content">
                <span class="metric-label">总营业额</span>
                <div class="metric-value-container">
                  <span class="metric-value revenue">{{ totalRevenue.toLocaleString() }}</span>
                  <span class="metric-unit currency">¥</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-4 col-md-6 mb-20">
        <div class="card border-0 rounded-0 bg-white h-100 box-shadow">
          <div class="card-body p-20 text-center">
            <div class="metric-item">
              <div class="metric-content">
                <span class="metric-label">订单量</span>
                <div class="metric-value-container">
                  <span class="metric-value orders">{{ totalOrders.toLocaleString() }}</span>
                  <span class="metric-unit order-unit">单</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-4 col-md-6 mb-20">
        <div class="card border-0 rounded-0 bg-white h-100 box-shadow">
          <div class="card-body p-20 text-center">
            <div class="metric-item">
              <div class="metric-content">
                <span class="metric-label">杯量</span>
                <div class="metric-value-container">
                  <span class="metric-value cups">{{ totalCups.toLocaleString() }}</span>
                  <span class="metric-unit cup-unit">杯</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第二行 - 产品分析区 -->
    <div class="row mb-30">
      <div class="col-12">
        <div class="card border-0 rounded-0 bg-white box-shadow">
          <div class="card-body p-20">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-star me-10 text-warning" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">全门店明星单品</h5>
            </div>
            <div class="star-products-grid">
              <div v-for="product in starProducts" :key="product.id" class="product-item">
                <div class="product-info">
                  <div class="product-name-container">
                    <div class="product-rank">{{ product.rank }}</div>
                    <span class="product-name">{{ product.name }}</span>
                  </div>
                  <div class="product-sales-container">
                    <span class="product-sales">{{ product.sales }}</span>
                    <span class="product-unit cup-unit">杯</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第三行 - 时段与门店对比区 -->
    <div class="row mb-30">
      <div class="col-lg-6 mb-25">
        <div class="card border-0 rounded-0 bg-white box-shadow">
          <div class="card-body p-20">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-chart me-10 text-primary" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">营业额分时段分析</h5>
            </div>
            <div class="chart-container">
              <apexchart
                type="bar"
                height="300"
                :options="hourlyRevenueChartOptions"
                :series="hourlyRevenueChartSeries"
                class="chart"
                id="hourlyRevenueChart"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-6 mb-25">
        <div class="card border-0 rounded-0 bg-white box-shadow">
          <div class="card-body p-20">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-trophy me-10 text-success" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">门店营业额排行</h5>
            </div>
            <div class="store-ranking">
              <div v-for="store in storeRanking" :key="store.id" class="store-item">
                <div class="store-info">
                  <div class="store-name-container">
                    <div class="store-rank">{{ store.rank }}</div>
                    <span class="store-name">{{ store.name }}</span>
                  </div>
                  <div class="store-revenue-container">
                    <span class="store-revenue">{{ store.revenue.toLocaleString() }}</span>
                    <span class="store-unit currency">¥</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第四行 - 产品与趋势分析区 -->
    <div class="row mb-30">
      <div class="col-lg-6 mb-25">
        <div class="card border-0 rounded-0 bg-white box-shadow">
          <div class="card-body p-20">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-drink me-10 text-info" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">单品杯数排行</h5>
            </div>
            <div class="product-ranking">
              <div v-for="product in productRanking" :key="product.id" class="product-rank-item">
                <div class="product-rank-info">
                  <div class="product-rank-name-container">
                    <div class="product-rank-number">{{ product.rank }}</div>
                    <span class="product-rank-name">{{ product.name }}</span>
                  </div>
                  <div class="product-rank-cups-container">
                    <span class="product-rank-cups">{{ product.cups }}</span>
                    <span class="product-rank-unit cup-unit">杯</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-6 mb-25">
        <div class="card border-0 rounded-0 bg-white box-shadow">
          <div class="card-body p-20">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-trending me-10 text-warning" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">销量变化曲线图</h5>
            </div>
            <div class="chart-container">
              <apexchart
                type="line"
                height="300"
                :options="weeklyTrendChartOptions"
                :series="weeklyTrendChartSeries"
                class="chart"
                id="weeklyTrendChart"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第五行 - 周期性汇总区 -->
    <div class="row mb-30">
      <div class="col-lg-6 mb-25">
        <div class="card border-0 rounded-0 bg-white box-shadow">
          <div class="card-body p-20">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-calendar me-10 text-primary" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">本周营业额汇总</h5>
            </div>
            <div class="summary-stats">
              <div class="summary-item">
                <span class="summary-label">本周总营业额</span>
                <div class="summary-value-container">
                  <span class="summary-value">{{ weeklyRevenue.toLocaleString() }}</span>
                  <span class="summary-unit currency">¥</span>
                </div>
              </div>
              <div class="summary-item">
                <span class="summary-label">日均营业额</span>
                <div class="summary-value-container">
                  <span class="summary-value">{{ (weeklyRevenue / 7).toFixed(0) }}</span>
                  <span class="summary-unit currency">¥</span>
                </div>
              </div>
              <div class="summary-item">
                <span class="summary-label">环比增长</span>
                <div class="summary-value-container">
                  <span class="summary-value growth-positive">+12.5</span>
                  <span class="summary-unit">%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-6 mb-25">
        <div class="card border-0 rounded-0 bg-white box-shadow">
          <div class="card-body p-20">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-calendar me-10 text-success" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">本月营业额汇总</h5>
            </div>
            <div class="summary-stats">
              <div class="summary-item">
                <span class="summary-label">本月总营业额</span>
                <div class="summary-value-container">
                  <span class="summary-value">{{ monthlyRevenue.toLocaleString() }}</span>
                  <span class="summary-unit currency">¥</span>
                </div>
              </div>
              <div class="summary-item">
                <span class="summary-label">日均营业额</span>
                <div class="summary-value-container">
                  <span class="summary-value">{{ (monthlyRevenue / 30).toFixed(0) }}</span>
                  <span class="summary-unit currency">¥</span>
                </div>
              </div>
              <div class="summary-item">
                <span class="summary-label">环比增长</span>
                <div class="summary-value-container">
                  <span class="summary-value growth-positive">+8.3</span>
                  <span class="summary-unit">%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部刷新按钮 -->
    <div class="d-flex justify-content-end mb-25">
      <button class="btn btn-primary" @click="refreshData">
        <i class="flaticon-refresh me-2"></i>刷新数据
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BreadCrumb from "@/components/layouts/BreadCrumb.vue"
import flatPickr from "vue-flatpickr-component"

// 面包屑导航
const breadcrumb = ref([
  { label: '工作台', url: '/dashboard' },
  { label: '全门店营业情况', url: '' }
])

// 时间选择器配置
const selectedDate = ref('')
const datePickerConfig = ref({
  mode: 'range',
  dateFormat: 'Y-m-d',
  locale: 'zh'
})

// 核心KPI数据
const totalRevenue = ref(156800)
const totalOrders = ref(2340)
const totalCups = ref(4680)

// 全门店明星单品数据
const starProducts = ref([
  { id: 1, name: '美式咖啡', sales: 890, rank: 1 },
  { id: 2, name: '拿铁咖啡', sales: 760, rank: 2 },
  { id: 3, name: '卡布奇诺', sales: 540, rank: 3 },
  { id: 4, name: '摩卡咖啡', sales: 430, rank: 4 },
  { id: 5, name: '焦糖玛奇朵', sales: 380, rank: 5 },
  { id: 6, name: '冰美式', sales: 320, rank: 6 },
  { id: 7, name: '抹茶拿铁', sales: 280, rank: 7 },
  { id: 8, name: '香草拿铁', sales: 240, rank: 8 }
])

// 门店营业额排行数据
const storeRanking = ref([
  { id: 1, name: '中关村店', revenue: 28500, rank: 1 },
  { id: 2, name: '三里屯店', revenue: 25800, rank: 2 },
  { id: 3, name: '国贸店', revenue: 23400, rank: 3 },
  { id: 4, name: '望京店', revenue: 21200, rank: 4 },
  { id: 5, name: '西单店', revenue: 19800, rank: 5 },
  { id: 6, name: '朝阳门店', revenue: 18600, rank: 6 }
])

// 单品杯数排行数据
const productRanking = ref([
  { id: 1, name: '美式咖啡', cups: 1240, rank: 1 },
  { id: 2, name: '拿铁咖啡', cups: 980, rank: 2 },
  { id: 3, name: '卡布奇诺', cups: 760, rank: 3 },
  { id: 4, name: '冰美式', cups: 650, rank: 4 },
  { id: 5, name: '摩卡咖啡', cups: 540, rank: 5 }
])

// 周期性汇总数据
const weeklyRevenue = ref(1098000)
const monthlyRevenue = ref(4680000)

// 营业额分时段分析图表配置
const hourlyRevenueChartSeries = ref([
  {
    name: '营业额',
    data: [1200, 2800, 4500, 6800, 8900, 12400, 15600, 18200, 21500, 19800, 16400, 13200, 9800, 7200, 5400, 3600, 2100, 1800, 2400, 3200, 2800, 2200, 1600, 1000]
  }
])

const hourlyRevenueChartOptions = ref({
  chart: {
    type: 'bar',
    height: 300,
    toolbar: {
      show: false
    },
    fontFamily: 'Source Han Serif, serif'
  },
  colors: ['#6560F0'],
  plotOptions: {
    bar: {
      borderRadius: 4,
      columnWidth: '60%'
    }
  },
  dataLabels: {
    enabled: false
  },
  xaxis: {
    categories: ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00', '07:00', '08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'],
    labels: {
      style: {
        colors: '#6c757d',
        fontSize: '11px',
        fontFamily: 'Source Han Serif, serif'
      }
    },
    axisBorder: {
      show: false
    },
    axisTicks: {
      show: false
    }
  },
  yaxis: {
    labels: {
      style: {
        colors: '#6c757d',
        fontSize: '11px',
        fontFamily: 'Source Han Serif, serif'
      },
      formatter: function (value: number) {
        return '¥' + value
      }
    }
  },
  grid: {
    borderColor: '#f0f1f3',
    strokeDashArray: 3
  },
  tooltip: {
    theme: 'light',
    style: {
      fontSize: '12px',
      fontFamily: 'Source Han Serif, serif'
    },
    y: {
      formatter: function (value: number) {
        return '¥' + value
      }
    }
  }
})

// 销量变化曲线图配置
const weeklyTrendChartSeries = ref([
  {
    name: '销量',
    data: [1240, 1580, 1320, 1680, 1890, 2100, 1950]
  }
])

const weeklyTrendChartOptions = ref({
  chart: {
    type: 'line',
    height: 300,
    toolbar: {
      show: false
    },
    fontFamily: 'Source Han Serif, serif'
  },
  colors: ['#28a745'],
  stroke: {
    curve: 'smooth',
    width: 3
  },
  markers: {
    size: 6,
    colors: ['#28a745'],
    strokeColors: '#fff',
    strokeWidth: 2,
    hover: {
      size: 8
    }
  },
  xaxis: {
    categories: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    labels: {
      style: {
        colors: '#6c757d',
        fontSize: '11px',
        fontFamily: 'Source Han Serif, serif'
      }
    },
    axisBorder: {
      show: false
    },
    axisTicks: {
      show: false
    }
  },
  yaxis: {
    labels: {
      style: {
        colors: '#6c757d',
        fontSize: '11px',
        fontFamily: 'Source Han Serif, serif'
      },
      formatter: function (value: number) {
        return value + '杯'
      }
    }
  },
  grid: {
    borderColor: '#f0f1f3',
    strokeDashArray: 3
  },
  tooltip: {
    theme: 'light',
    style: {
      fontSize: '12px',
      fontFamily: 'Source Han Serif, serif'
    },
    y: {
      formatter: function (value: number) {
        return value + '杯'
      }
    }
  }
})

// 事件处理函数
const onDateChange = (selectedDates: Date[]) => {
  console.log('选择的日期范围:', selectedDates)
  // 这里可以根据选择的日期范围重新加载数据
  refreshData()
}

const refreshData = () => {
  console.log('刷新数据...')
  // 这里可以调用API重新获取数据
}
</script>

<style scoped>
/* 页面标题样式 */
.main-title {
  font-size: 2.5rem;
  color: var(--splash-primary-color);
  margin-bottom: 0;
  font-family: 'Source Han Serif', serif;
}

/* 时间选择器样式 */
.time-selector {
  min-width: 250px;
}

.time-selector .form-control {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 8px 12px;
  font-family: 'Source Han Serif', serif;
}

/* 核心指标样式 */
.metric-item {
  padding: 10px 0;
}

.metric-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.metric-label {
  font-size: 14px;
  color: var(--splash-secondary-color);
  font-weight: 500;
  margin: 0;
  font-family: 'Source Han Serif', serif;
}

.metric-value-container {
  display: flex;
  align-items: baseline;
  gap: 4px;
  justify-content: center;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.metric-value.revenue {
  color: var(--splash-success-color);
}

.metric-value.orders {
  color: var(--splash-info-color);
}

.metric-value.cups {
  color: var(--splash-warning-color);
}

/* 统一单位颜色 */
.metric-unit {
  font-size: 16px;
  font-weight: 500;
  font-family: 'Source Han Serif', serif;
}

.currency {
  color: var(--splash-success-color);
}

.order-unit {
  color: var(--splash-info-color);
}

.cup-unit {
  color: var(--splash-warning-color);
}

/* 明星产品网格样式 */
.star-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.product-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #d1d5db;
}

.product-item:last-child {
  border-bottom: none;
}

.product-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  width: 100%;
}

.product-name-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.product-rank {
  background: transparent;
  color: #000000;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 700;
  flex-shrink: 0;
  font-family: 'Source Han Serif', serif;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.product-sales-container {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.product-sales {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.product-unit {
  font-size: 12px;
  font-weight: 500;
  font-family: 'Source Han Serif', serif;
}

/* 门店排行样式 */
.store-ranking {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.store-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #d1d5db;
}

.store-item:last-child {
  border-bottom: none;
}

.store-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  width: 100%;
}

.store-name-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.store-rank {
  background: transparent;
  color: #000000;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 700;
  flex-shrink: 0;
  font-family: 'Source Han Serif', serif;
}

.store-name {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.store-revenue-container {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.store-revenue {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.store-unit {
  font-size: 12px;
  font-weight: 500;
  font-family: 'Source Han Serif', serif;
}

/* 产品排行样式 */
.product-ranking {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.product-rank-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #d1d5db;
}

.product-rank-item:last-child {
  border-bottom: none;
}

.product-rank-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  width: 100%;
}

.product-rank-name-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.product-rank-number {
  background: transparent;
  color: #000000;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 700;
  flex-shrink: 0;
  font-family: 'Source Han Serif', serif;
}

.product-rank-name {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.product-rank-cups-container {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.product-rank-cups {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.product-rank-unit {
  font-size: 12px;
  font-weight: 500;
  font-family: 'Source Han Serif', serif;
}

/* 汇总统计样式 */
.summary-stats {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f1f3;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  font-size: 14px;
  color: var(--splash-secondary-color);
  font-weight: 500;
  font-family: 'Source Han Serif', serif;
}

.summary-value-container {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.summary-value {
  font-size: 20px;
  font-weight: 700;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.summary-value.growth-positive {
  color: var(--splash-success-color);
}

.summary-unit {
  font-size: 14px;
  font-weight: 500;
  font-family: 'Source Han Serif', serif;
}

/* 图表容器样式 */
.chart-container {
  margin-top: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .star-products-grid {
    grid-template-columns: 1fr;
  }

  .main-title {
    font-size: 2rem;
  }

  .time-selector {
    min-width: 200px;
    margin-top: 15px;
  }
}
</style>
