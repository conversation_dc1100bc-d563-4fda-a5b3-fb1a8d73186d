<template>
  <div class="store-dashboard">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="text-center py-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-3 text-muted">正在加载设备数据...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="alert alert-danger" role="alert">
      <div class="text-center py-4">
        <i class="flaticon-warning fs-1 text-danger mb-3"></i>
        <h5 class="mb-3">设备数据获取失败</h5>
        <p class="mb-3">{{ error }}</p>
        <button @click="fetchDeviceData" class="btn btn-primary">
          <i class="flaticon-refresh me-2"></i>
          重新加载
        </button>
      </div>
    </div>

    <!-- 主要内容 -->
    <div v-else>
      <!-- 顶部标题与状态区 -->
      <div class="card mb-25 border-0 rounded-0 bg-white box-shadow">
        <div class="card-body p-15 p-sm-20 p-md-25 p-lg-30 letter-spacing">
          <div class="d-flex align-items-center justify-content-between mb-20">
            <div class="d-flex align-items-center">
              <i class="flaticon-dashboard me-10 text-primary" style="font-size: 24px;"></i>
              <h4 class="fw-bold mb-0 text-black">{{ selectedStore?.storeName || '门店' }}运营状态</h4>
            </div>
            <div class="d-flex align-items-center">
              <i class="flaticon-settings me-8 text-black-emphasis"></i>
              <span class="me-10 fw-medium text-black-emphasis">机器状态：</span>
              <span class="badge text-white" :class="machineStatus.statusClass">
                <i class="flaticon-check me-5" v-if="machineStatus.isOnline"></i>
                <i class="flaticon-warning me-5" v-else></i>
                {{ machineStatus.statusText }}
              </span>
            </div>
          </div>
          <div class="date-selector">
            <div class="d-flex align-items-center">
              <label class="form-label fw-medium text-black-emphasis mb-0 me-3">查询日期：</label>
              <flat-pickr
                v-model="selectedDate"
                :config="flatpickrConfig"
                class="form-control date-picker"
                placeholder="选择日期"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 核心操作时间记录区 -->
      <div class="card mb-25 border-0 rounded-0 bg-white box-shadow">
        <div class="card-body p-15 p-sm-20 p-md-25 p-lg-30 letter-spacing">
          <div class="d-flex align-items-center mb-20">
            <i class="flaticon-clock me-10 text-primary" style="font-size: 20px;"></i>
            <h5 class="fw-bold mb-0 text-black">核心操作时间记录</h5>
          </div>
          <div class="row">
            <div class="col-6">
              <div class="mb-15">
                <div class="data-item">
                  <label class="data-label">今日填充时间</label>
                  <div class="data-content">
                    <span class="data-value data-time">{{ operationTimes?.fillTime || '未知' }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-6">
              <div class="mb-15">
                <div class="data-item">
                  <label class="data-label">今日清洗时间</label>
                  <div class="data-content">
                    <span class="data-value data-time">{{ operationTimes?.cleanTime || '未知' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    <!-- 设备分项检查区 -->
    <div class="row">
      <!-- 咖啡机部分 -->
      <div class="col-lg-4 col-md-6 mb-25">
        <div class="card border-0 rounded-0 bg-white h-100 box-shadow">
          <div class="card-body p-15 p-sm-20 p-md-25 p-lg-30 letter-spacing coffee-machine-area">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-coffee-cup me-10 text-primary" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">咖啡机</h5>
            </div>
            
            <!-- A项（咖啡豆） -->
            <div class="coffee-item">
              <div class="d-flex align-items-center justify-content-between mb-15">
                <span class="fw-medium text-black-emphasis">A项（咖啡豆）</span>
              </div>
              <div class="row">
                <div class="col-6">
                  <div class="data-item">
                    <label class="data-label">填充量</label>
                    <div class="data-content">
                      <span class="data-value coffee-data-number">{{ coffeeData.bean?.filled || 0 }}</span>
                      <span class="data-unit-text">g</span>
                    </div>
                  </div>
                </div>
                <div class="col-6">
                  <div class="data-item">
                    <label class="data-label">剩余量</label>
                    <div class="data-content">
                      <span class="data-value coffee-data-number">{{ coffeeData.bean?.remaining || 0 }}</span>
                      <span class="data-unit-text">g</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- B项（牛奶） -->
            <div class="coffee-item">
              <div class="d-flex align-items-center justify-content-between mb-15">
                <span class="fw-medium text-black-emphasis">B项（牛奶）</span>
              </div>
              <div class="row">
                <div class="col-6">
                  <div class="data-item">
                    <label class="data-label">填充量</label>
                    <div class="data-content">
                      <span class="data-value coffee-data-number">{{ coffeeData.milk?.filled || 0 }}</span>
                      <span class="data-unit-text">ml</span>
                    </div>
                  </div>
                </div>
                <div class="col-6">
                  <div class="data-item">
                    <label class="data-label">剩余量</label>
                    <div class="data-content">
                      <span class="data-value coffee-data-number">{{ coffeeData.milk?.remaining || 0 }}</span>
                      <span class="data-unit-text">ml</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 饮料机部分 -->
      <div class="col-lg-5 col-md-6 mb-25">
        <div class="card border-0 rounded-0 bg-white h-100 box-shadow">
          <div class="card-body p-15 p-sm-20 p-md-25 p-lg-30 letter-spacing beverage-machine-area">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-drink me-10 text-primary" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">饮料机</h5>
            </div>
            
            <!-- 货道数据 -->
            <div class="beverage-channels">
              <div v-if="beverageChannels.length === 0" class="text-center text-muted py-3">
                <i class="flaticon-warning me-2"></i>
                暂无货道数据
              </div>
              <div v-else v-for="channel in beverageChannels" :key="channel.id" class="channel-row">
                <div class="channel-item">
                  <div class="channel-number">
                    <span class="data-value channel-id-number">{{ channel.id }}</span>
                  </div>
                  <div class="channel-name">
                    <span class="data-text">{{ channel.name }}</span>
                  </div>
                  <div class="channel-quantity">
                    <span class="data-value beverage-data-number">{{ channel.quantity }}</span>
                    <span class="data-unit-text">瓶</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 物料库存部分 -->
      <div class="col-lg-3 col-md-12 mb-25">
        <div class="card border-0 rounded-0 bg-white h-100 box-shadow">
          <div class="card-body p-15 p-sm-20 p-md-25 p-lg-30 letter-spacing materials-area">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-box me-10 text-primary" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">物料库存</h5>
            </div>
            
            <!-- 物料列表 -->
            <div class="materials-list">
              <div v-if="materials.length === 0" class="text-center text-muted py-3">
                <i class="flaticon-warning me-2"></i>
                暂无物料数据
              </div>
              <div v-else v-for="material in materials" :key="material.name" class="material-item">
                <div class="material-name">{{ material.name }}</div>
                <div class="material-quantity">
                  <span class="data-value material-data-number">{{ material.quantity }}</span>
                  <span class="data-unit-text">{{ material.unit }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

      <!-- 底部信息和刷新按钮 -->
      <div class="d-flex justify-content-between align-items-center mb-25">
        <div class="text-muted">
          <small>
            <i class="flaticon-clock me-2"></i>
            数据更新时间：<span class="data-value">{{ lastUpdateTime }}</span>
          </small>
        </div>
        <button @click="fetchDeviceData" class="btn btn-primary btn-sm">
          <i class="flaticon-refresh me-2"></i>
          刷新数据
        </button>
      </div>
    </div> <!-- 结束主要内容的条件渲染 -->
  </div> <!-- 结束 store-dashboard -->
</template>

<script setup lang="ts">
import { ref, onMounted, computed, reactive, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useStoreStore } from '@/store/useStoreStore'
import { storeToRefs } from 'pinia'
import { format } from 'date-fns'
import type { storeDetail } from '@/types/store'
import serviceAxios from '@/utils/serviceAxios'
import flatPickr from 'vue-flatpickr-component'
import 'flatpickr/dist/flatpickr.css'

// 定义属性
interface Props {
  storeId?: number
  store?: storeDetail | null
}

const props = defineProps<Props>()

// 设备信息类型定义
interface DeviceInfo {
  deviceInfoId: number
  deviceId: string
  deviceType: string
  storeId: number
  status: string
  lastUpdateTime: string
  // 其他设备属性
}

// 实际API返回的设备状态结构
interface DeviceStatus {
  isBusy: boolean                    // 设备是否忙碌（正在制作）
  runningRecipeCount: number         // 正在运行的配方数量
  databaseBusyQueues: number         // 数据库忙碌队列数
  isOnline: boolean                  // 设备是否在线
  runningRecipes: Record<string, unknown> // 正在运行的配方详情
  availableRecipeCount: number       // 可用配方数量
  databaseBusyQueueIds: number[]     // 忙碌队列ID列表
  deviceStatus: string               // 设备状态字符串
  timestamp: number                  // 时间戳

  // 可选的扩展字段（可能在其他API中返回）
  coffeeBean?: { filled: number; remaining: number }
  milk?: { filled: number; remaining: number }
  channels?: Array<{ id: number; name: string; quantity: number }>
  materials?: Array<{ name: string; quantity: number; unit: string }>
  lastFillTime?: string
  lastCleanTime?: string
}

// 获取路由和store
const route = useRoute()
const storeStore = useStoreStore()
const { selectedStore, stores } = storeToRefs(storeStore)

// 加载状态和数据
const isLoading = ref<boolean>(true)
const error = ref<string | null>(null)

// 获取当前门店ID
const currentStoreId = computed(() => {
  return props.storeId || Number(route.params.id) || 0
})

// 设备数据状态
const deviceData = reactive<{
  devices: DeviceInfo[]
  deviceStatus: DeviceStatus | null
  isOnline: boolean
}>({
  devices: [],
  deviceStatus: null,
  isOnline: false
})

// 日期选择器状态
const selectedDate = ref<Date>(new Date())

// 日期选择器配置
const flatpickrConfig = {
  dateFormat: "Y-m-d",
  locale: "zh",
  allowInput: true,
  enableTime: false,
  maxDate: new Date(), // 不能选择未来日期
}

// 数据获取函数
async function fetchDeviceData() {
  if (!currentStoreId.value) {
    console.warn("门店ID无效，无法获取设备数据。")
    return
  }

  isLoading.value = true
  error.value = null

  try {
    console.log(`🔍 [属性看板] 开始获取门店${currentStoreId.value}的设备数据，查询日期: ${format(selectedDate.value, 'yyyy-MM-dd')}`)

    // 获取门店设备列表
    const devicesResponse = await serviceAxios.get(`/iot/deviceInfo/store/${currentStoreId.value}`)
    // 修正：根据设备API的实际响应格式调整
    deviceData.devices = devicesResponse.data || devicesResponse.rows || devicesResponse || []

    console.log(`✅ [属性看板] 设备API返回: ${deviceData.devices.length}个设备`)
    deviceData.devices.forEach(device => {
      console.log(`   - 设备${device.deviceId}: ${device.deviceType} (${device.deviceName})`)
    })

    // 查找Kiosk设备
    const kioskDevice = deviceData.devices.find(device =>
      device.deviceType === 'KIOSK' || device.deviceType === 'kiosk'
    )

    console.log(`🔍 [属性看板] Kiosk设备查找结果:`, kioskDevice ? `找到设备${kioskDevice.deviceId}` : '未找到Kiosk设备')

    if (kioskDevice) {
      // 获取设备详细状态
      const statusResponse = await serviceAxios.get(`/manager/production-queue/device-status/${kioskDevice.deviceId}`)
      // 修正：根据设备状态API的实际响应格式调整
      deviceData.deviceStatus = statusResponse.deviceStatus || statusResponse.data || statusResponse

      // 修正：使用设备详细状态API返回的真实在线状态
      if (deviceData.deviceStatus && 'isOnline' in deviceData.deviceStatus) {
        deviceData.isOnline = deviceData.deviceStatus.isOnline
      } else {
        // 回退到设备列表中的状态
        deviceData.isOnline = kioskDevice.status === 'ONLINE' || kioskDevice.status === 'online'
      }

      console.log(`🔍 [属性看板] 设备在线状态: ${deviceData.isOnline}`)
    } else {
      throw new Error("未找到Kiosk设备")
    }

  } catch (err) {
    console.error("获取设备数据失败:", err)
    error.value = "设备数据加载失败，请检查网络连接或稍后重试。"
    deviceData.devices = []
    deviceData.deviceStatus = null
    deviceData.isOnline = false
  } finally {
    isLoading.value = false
  }
}



// 从URL参数或props获取门店ID并设置选中门店
onMounted(async () => {
  let storeId: string | number | undefined

  // 优先使用props中的storeId，其次使用路由参数
  if (props.storeId) {
    storeId = props.storeId.toString()
  } else if (route.query.storeId) {
    storeId = route.query.storeId as string
  } else if (route.params.id) {
    storeId = route.params.id as string
  }

  if (storeId && !selectedStore.value) {
    // 如果stores为空，先获取门店列表
    if (stores.value.length === 0) {
      await storeStore.fetchStore()
    }
    // 根据ID找到对应门店并设置为选中状态
    const store = stores.value.find(s => s.storeId.toString() === storeId)
    if (store) {
      storeStore.selectStore(store)
    }
  }

  // 获取设备数据
  await fetchDeviceData()
})

// 监听门店ID变化
watch(() => currentStoreId.value, (newStoreId) => {
  if (newStoreId) {
    fetchDeviceData()
  }
})

// 监听日期变化
watch(() => selectedDate.value, () => {
  if (currentStoreId.value) {
    fetchDeviceData()
  }
})

// 计算属性：基于真实数据的展示
const beverageChannels = computed(() => {
  return deviceData.deviceStatus?.channels || []
})

const materials = computed(() => {
  return deviceData.deviceStatus?.materials || []
})

const coffeeData = computed(() => {
  const defaultData = { filled: 0, remaining: 0 }

  if (!deviceData.deviceStatus) {
    return {
      bean: defaultData,
      milk: defaultData
    }
  }

  // 确保每个属性都有正确的结构
  const beanData = deviceData.deviceStatus.coffeeBean
  const milkData = deviceData.deviceStatus.milk

  return {
    bean: (beanData && typeof beanData === 'object' && 'filled' in beanData && 'remaining' in beanData)
      ? beanData
      : defaultData,
    milk: (milkData && typeof milkData === 'object' && 'filled' in milkData && 'remaining' in milkData)
      ? milkData
      : defaultData
  }
})

const operationTimes = computed(() => {
  return {
    fillTime: deviceData.deviceStatus?.lastFillTime || '未知',
    cleanTime: deviceData.deviceStatus?.lastCleanTime || '未知'
  }
})

const machineStatus = computed(() => {
  if (!deviceData.isOnline) {
    return {
      isOnline: false,
      statusText: '离线',
      statusClass: 'bg-danger'
    }
  }

  // 设备在线时，进一步判断工作状态
  const isBusy = deviceData.deviceStatus?.isBusy || false
  const runningCount = deviceData.deviceStatus?.runningRecipeCount || 0

  if (isBusy || runningCount > 0) {
    return {
      isOnline: true,
      statusText: `正在制作 (${runningCount}个配方)`,
      statusClass: 'bg-warning'
    }
  } else {
    return {
      isOnline: true,
      statusText: '在线空闲',
      statusClass: 'bg-success'
    }
  }
})



const lastUpdateTime = computed(() => {
  const latestDevice = deviceData.devices.find(d => d.lastUpdateTime)
  return latestDevice ?
    format(new Date(latestDevice.lastUpdateTime), 'yyyy-MM-dd HH:mm:ss') :
    format(new Date(), 'yyyy-MM-dd HH:mm:ss')
})


</script>

<style scoped>
.store-dashboard {
  letter-spacing: 0.01em;
  font-family: 'Source Han Serif', serif;
}

.store-dashboard * {
  font-family: 'Source Han Serif', serif;
}

/* 数据展示样式 - 重新设计 */
.data-item {
  margin-bottom: 12px;
}

.data-label {
  font-family: 'Source Han Serif', serif;
  font-size: 13px;
  font-weight: 500;
  color: var(--splash-secondary-color);
  margin-bottom: 4px;
  display: block;
}

.data-content {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

/* 区域背景样式 - 移除蓝色底纹 */
.coffee-machine-area,
.beverage-machine-area,
.materials-area {
  background-color: transparent !important;
}

/* 字体样式 - 使用Source Han Serif */
.data-value {
  font-family: 'Source Han Serif', serif;
  font-weight: 600;
  color: #000000;
}

/* 咖啡机数字样式 - 最大最突出 */
.coffee-data-number {
  font-size: 32px;
  color: #000000;
  font-weight: 700;
}

/* 饮料机数字样式 - 调整为更协调的大小 */
.beverage-data-number {
  font-size: 20px;
  color: #000000;
  font-weight: 600;
}

/* 物料库存数字样式 - 中等大小 */
.material-data-number {
  font-size: 28px;
  color: #000000;
  font-weight: 700;
}

/* 饮料机货道编号 - 小一些 */
.channel-id-number {
  font-size: 16px;
  color: #000000;
  font-weight: 600;
}

.data-value.data-time {
  font-size: 20px;
  color: #000000;
}

.data-value.data-highlight {
  font-size: 16px;
  color: #000000;
  font-weight: 700;
}

/* 单位文本样式 - 弱化显示 */
.data-unit-text {
  font-family: 'Source Han Serif', serif;
  font-size: 12px;
  font-weight: 400;
  color: #6c757d;
  margin-left: 4px;
}

.data-text {
  font-family: 'Source Han Serif', serif;
  font-size: 14px;
  color: #000000;
  font-weight: 500;
}

/* 咖啡机区域分隔线 */
.coffee-item {
  padding: 16px 0;
  border-bottom: 2px solid #e3f2fd;
  margin-bottom: 16px;
}

.coffee-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

/* 饮料机货道样式 */
.beverage-channels {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.channel-row {
  border-bottom: 2px solid #e1e8ed;
  padding-bottom: 12px;
}

.channel-row:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.channel-item {
  display: grid;
  grid-template-columns: 60px 1fr 80px;
  gap: 16px;
  align-items: center;
  padding: 8px 0;
  transition: background-color 0.2s ease;
}

.channel-item:hover {
  background-color: rgba(101, 96, 240, 0.08);
  border-radius: 6px;
  padding: 8px 12px;
  margin: 0 -12px;
}

.channel-number {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.channel-label {
  font-family: 'Source Han Serif', serif;
  font-size: 12px;
  color: var(--splash-secondary-color);
  font-weight: 500;
}

.channel-name {
  display: flex;
  align-items: center;
}

.channel-quantity {
  display: flex;
  align-items: baseline;
  gap: 6px;
  justify-content: flex-end;
}

/* 物料库存样式 */
.materials-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.material-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 2px solid #e3f2fd;
}

.material-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.material-name {
  font-family: 'Source Han Serif', serif;
  font-size: 16px;
  font-weight: 600;
  color: #000000;
}

.material-quantity {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.badge {
  font-family: 'Source Han Serif', serif;
  font-size: 12px;
  padding: 8px 12px;
  font-weight: 600;
}

.badge.bg-success {
  background-color: var(--splash-success-color) !important;
}

.card.box-shadow {
  box-shadow: 0px 4px 34px rgba(101, 96, 240, 0.1);
  transition: box-shadow 0.3s ease;
}

.card.box-shadow:hover {
  box-shadow: 0px 6px 40px rgba(101, 96, 240, 0.15);
}

h4, h5 {
  font-family: 'Source Han Serif', serif;
  color: var(--splash-black-color);
}

.form-label {
  font-family: 'Source Han Serif', serif;
  font-size: 13px;
  margin-bottom: 6px;
}

/* 数据展示区域的悬停效果 */
.data-item:hover .data-value {
  color: var(--splash-primary-active-color);
  transform: scale(1.05);
  transition: all 0.2s ease;
}

/* 日期选择器样式 */
.date-picker {
  width: 160px;
  font-family: 'Source Han Serif', serif;
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  border: 1px solid #e3f2fd;
  border-radius: 6px;
  padding: 8px 12px;
  background-color: #fff;
  transition: all 0.2s ease;
}

.date-picker:focus {
  border-color: var(--splash-primary-color);
  box-shadow: 0 0 0 0.2rem rgba(101, 96, 240, 0.25);
  outline: none;
}

.date-picker:hover {
  border-color: var(--splash-primary-color);
}

/* 刷新按钮样式 */
.btn-primary {
  font-family: 'Source Han Serif', serif;
  background-color: var(--splash-primary-color);
  border-color: var(--splash-primary-color);
}

.btn-primary:hover {
  background-color: var(--splash-primary-active-color);
  border-color: var(--splash-primary-active-color);
  transform: translateY(-1px);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .store-dashboard h4 {
    font-size: 1.25rem;
  }

  .store-dashboard h5 {
    font-size: 1.1rem;
  }

  .coffee-data-number {
    font-size: 28px;
  }

  .beverage-data-number {
    font-size: 18px;
  }

  .material-data-number {
    font-size: 24px;
  }

  .channel-id-number {
    font-size: 14px;
  }

  .data-value.data-time {
    font-size: 18px;
  }

  .data-value.data-highlight {
    font-size: 14px;
  }

  .data-unit-text {
    font-size: 11px;
  }

  .badge {
    font-size: 11px;
    padding: 6px 10px;
  }

  .channel-item {
    grid-template-columns: 50px 1fr 70px;
    gap: 12px;
  }

  .material-item {
    padding: 10px 0;
  }
}

@media (max-width: 576px) {
  .coffee-data-number {
    font-size: 24px;
  }

  .beverage-data-number {
    font-size: 16px;
  }

  .material-data-number {
    font-size: 20px;
  }

  .channel-id-number {
    font-size: 12px;
  }

  .data-value.data-time {
    font-size: 16px;
  }

  .data-text {
    font-size: 12px;
  }

  .channel-item {
    grid-template-columns: 40px 1fr 60px;
    gap: 8px;
  }

  .material-name {
    font-size: 14px;
  }

  .data-unit-text {
    font-size: 10px;
  }

  .date-selector {
    flex-direction: column;
    align-items: flex-start !important;
  }

  .date-selector .d-flex {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 8px;
  }

  .date-picker {
    width: 100%;
    font-size: 13px;
  }

  .coffee-item,
  .material-item {
    padding: 12px 0;
  }
}
</style>
