.event-card {
    .card-body {
        .date {
            width: 80px;
            border: 1px solid var(--splash-primary-color);

            span {
                &:first-child {
                    padding: {
                        top: 5px;
                        bottom: 5px;
                    };
                }
                &:last-child {
                    font-size: 28px;
                }
            }
        }
        p {
            margin-bottom: 15px;

            &:last-child {
                margin-bottom: 0;
            }
        }
        .info-list {
            li {
                margin-bottom: 12px;
                padding-left: 100px;
    
                span {
                    top: 0;
                    left: 0;
                    position: absolute;
                }
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
        .users-list {
            div {
                width: 33px;
                height: 33px;
                margin-right: -10px;
                border: 2px solid var(--splash-white-color);
                filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));
    
                &:last-child {
                    margin-right: 0;
                }
            }
        }
        .link-btn {
            background: #F2F1F9;

            span {
                line-height: 1.3;

                &::before {
                    left: 0;
                    right: 0;
                    bottom: 0;
                    height: 1px;
                    content: '';
                    position: absolute;
                    transition: var(--transition);
                    background: var(--splash-primary-color);
                }
            }
            i {
                line-height: .01;
                font-size: 20px;
            }
            &:hover {
                span {
                    &::before {
                        transform: scaleX(0);
                    }
                }
            }
            &.closed {
                background: #F8F8FC;
                pointer-events: none;

                span {
                    &::before {
                        display: none;
                    }
                }
            }
        }
    }
}

// Dark Mode
.dark {
    .event-card {
        .card-body {
            .users-list {
                div {
                    border-color: #45445e;
                }
            }
            .link-btn {
                background: var(--splash-black-color);

                &.closed {
                    background: #3a3950;
                }
            }
        }
    }
}

@media only screen and (max-width : 767px) {

    .event-card {
        .card-body {
            .date {
                width: 65px;
    
                span {
                    &:last-child {
                        font-size: 25px;
                    }
                }
            }
            .info-list {
                li {
                    padding-left: 80px;
                }
            }
        }
    }

}

@media only screen and (min-width : 768px) and (max-width : 991px) {

    .event-card {
        .card-body {
            .info-list {
                li {
                    padding-left: 85px;
                }
            }
        }
    }

}