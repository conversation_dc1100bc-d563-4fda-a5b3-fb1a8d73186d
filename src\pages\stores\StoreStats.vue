<script lang="ts">
import {defineComponent} from "vue";
import BreadCrumb from "@/components/layouts/BreadCrumb.vue";

export default defineComponent({
  name: 'StoreStats',
  components: {
    BreadCrumb,
  }
})
</script>
<script setup lang="ts">
/* eslint-disable */
import {useStoreStore} from "@/store/useStoreStore";
import {storeToRefs} from "pinia";
import {onMounted} from "vue";
import {useRouter} from "vue-router";
import type {storeDetail} from "@/types/store";

const storeStore = useStoreStore();
const {stores} = storeToRefs(storeStore);
const router = useRouter();

onMounted(()=>{
  updateStoreList();
})

function updateStoreList() {
  console.log("Trying to update store list.");
  storeStore.fetchStore();
}



</script>

<template>
  <BreadCrumb pageTitle="店铺列表"/>
  <div class="refresh">
    <button
        class="transition bg-transparent p-0 border-0"
        @click="updateStoreList"
        aria-label="刷新">
      <i class="flaticon-refresh"></i>
    </button>
  </div>
  <div class="cards-list row">
    <div class="col-lg-4 " v-for="store in stores" :key="store.storeId">
      <div class="card mb-25 border-1 rounded-1 bg-white shadow-sm letter-spacing transition">
        <div class="card-body p-15 p-sm-20 p-md-25 p-lg-30">
          <h5 class="card-title fw-bold mb-10">{{ store.storeName }}</h5>
          <ul class="list-unstyled info fs-md-15 fs-lg-16">
            <li>
              店铺描述: {{ store.storeDescription }}
            </li>
            <li>
              店铺状态: {{ store.status }}
            </li>
            <li>
              地址: {{ store.storeAddress }}
            </li>
            <li>
              更新时间: {{ store.updateTime }}
            </li>
          </ul>

          <!-- 门店操作按钮 -->
          <div class="store-actions mt-20">
            <router-link
              :to="{
                name: 'StoreDetailPage',
                params:{
                  id:store.storeId
                }
              }"
              class="btn btn-outline-secondary btn-sm w-100 text-decoration-none"
            >
              查看详情
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.cards-list {
  justify-content: flex-start;
  align-content: flex-start;
}

.card-link:hover .card {

  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.card-link:active .card {
  transition: 0.1s !important;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.30) !important;
}

.info {
  line-height: 2.5
}

.refresh button {
  /* font-size 控制 flaticon 图标的大小 */
  font-size: 20px;

  /* --- 实现方形、图标居中和圆角 --- */
  display: inline-flex; /* 使按钮表现为行内元素，但内部使用 flex 布局 */
  align-items: center; /* 垂直居中内部的图标 */
  justify-content: center; /* 水平居中内部的图标 */
  width: 38px; /* 设置宽度，使其略大于图标 (例如图标22px + 两边各8px留白) */
  height: 38px; /* 设置高度等于宽度，形成正方形 */
  border-radius: 8px; /* 您期望的圆角大小，例如 6px, 8px, 0.25rem 等 */
  box-sizing: border-box; /* 确保 padding 和 border 不会增加元素的总宽高 */

  /* --- 间距控制 --- */
  margin: 5px 22px 5px 5px;
}

.refresh button:hover {
  color: var(--splash-primary-color);
}

.refresh button:active {
  transform: scale(0.85);
}
</style>