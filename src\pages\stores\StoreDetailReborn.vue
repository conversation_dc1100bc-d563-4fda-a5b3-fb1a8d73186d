<script setup lang="ts">
/* eslint-disable */
import {useStoreStore} from "@/store/useStoreStore";
// 【新】导入 useRouter 用于导航
import {useRoute, useRouter} from "vue-router";
import {useSidebarStore} from "@/store/useSidebarStore";
import {computed, onMounted, onUnmounted, ref, watch, reactive} from "vue";
import {format} from "date-fns";

// 【修改】从 bootstrap-vue-next 导入所需组件，移除了 BDropdown 和 BDropdownItem
import {BModal} from "bootstrap-vue-next";

// API 和类型导入 (保持不变)
import {drinkOrderParams, getOrderListByParams} from "@/utils/api/order";
import {getProductionQueueList, type QueueQueryParams} from "@/utils/api/queue";
import type {TableDataInfo} from "@/types/tableDataInfo";
import type {drinkOrder} from "@/types/order";
import type {drinkProductionQueueItem, storeDetail} from "@/types";

import {getProductDetailById, getPrototypeDetailById} from "@/utils/api/product";
import type {ProductDetail} from "@/utils/api/product"; // 【新】导入产品类型

// 子组件导入 (保持不变)
import BreadCrumb from "@/components/layouts/BreadCrumb.vue";
import StatsCard from "@/components/StatsCard.vue";
import flatPickr from "vue-flatpickr-component";
import {BPagination} from "bootstrap-vue-next";

// --- Pinia 仓库和路由 ---
const storeStore = useStoreStore();
const sidebarStore = useSidebarStore();
const route = useRoute();
const router = useRouter(); // 【新】获取 router 实例

// --- 核心状态管理 ---

// 加载状态
const isLoading = ref<boolean>(true);
const error = ref<string | null>(null);

// 【新】用于存储所有可选门店的列表
const availableStores = ref<storeDetail[]>([]);

// 核心筛选元数据：作为触发数据更新的唯一入口
const filterCriteria = reactive({
  storeId: Number(route.params.id), // 从当前路由初始化
  selectedDate: new Date(),
});

// 统一的数据存储
const dailyData = reactive<{
  orders: drinkOrder[];
  queueItems: drinkProductionQueueItem[];
}>({
  orders: [],
  queueItems: [],
});

// 【新】用于异步存储最热饮品计算结果
const popularDrinkInfo = ref<{ id: number | string; name: string }>({
  id: 'N/A',
  name: '暂无数据'
});

// --- 【修改】模态框状态管理 (仅保留日期部分) ---
const isDateModalVisible = ref(false);

// 用于在模态框中临时存储用户的选择，点击“确认”后才真正应用
const tempSelectedDate = ref<Date>(filterCriteria.selectedDate);

// 获取当前的店铺信息
const currentStoreInfo = computed(() => availableStores.value.find(s => s.storeId === filterCriteria.storeId));

// --- 分页和日期选择器配置 ---
const currentPage = ref(1); // 订单列表的当前页
const queueCurrentPage = ref(1); // 【新】制作队列的当前页
const pageSize = 10;
const flatpickrConfig = {
  dateFormat: "Y-m-d",
  locale: "zh",
  allowInput: true,
  enableTime: false,
}

// --- 【修改】模态框和选择器处理函数 ---

// 打开日期选择模态框
function showDateModal() {
  // 每次打开时，都将当前已选的日期同步到临时变量
  tempSelectedDate.value = filterCriteria.selectedDate;
  isDateModalVisible.value = true;
}

// 确认日期更改 (逻辑保持不变)
function confirmDateChange() {
  let rawDateValue = tempSelectedDate.value;
  if (Array.isArray(rawDateValue) && rawDateValue.length > 0) {
    rawDateValue = rawDateValue[0];
  }
  if (!rawDateValue) {
    isDateModalVisible.value = false;
    return;
  }
  const newSelectedDate = new Date(rawDateValue);
  if (isNaN(newSelectedDate.getTime())) {
    isDateModalVisible.value = false;
    return;
  }
  if (newSelectedDate.toDateString() !== filterCriteria.selectedDate.toDateString()) {
    filterCriteria.selectedDate = newSelectedDate;
  }
  isDateModalVisible.value = false;
}

// 【保留】获取所有门店列表的函数
async function fetchAvailableStores() {
  try {
    if (storeStore.stores.length === 0) {
      await storeStore.fetchStore();
    }
    availableStores.value = storeStore.stores;
  } catch (err) {
    console.error("获取门店列表失败:", err);
  }
}

// 【保留】处理门店选择变化的函数，现在由 <select> 元素调用
function handleStoreChange(event: Event) {
  const newStoreId = Number((event.target as HTMLSelectElement).value);
  if (newStoreId && newStoreId !== filterCriteria.storeId) {
    router.push({
      name: 'StoreDetailPage',
      params: {id: newStoreId}
    });
  }
}

// 数据获取与处理主函数 (保持不变)
async function fetchAndProcessDailyData() {
  if (!filterCriteria.storeId) {
    console.warn("门店ID无效，无法获取数据。");
    return;
  }
  isLoading.value = true;
  error.value = null;
  currentPage.value = 1;
  queueCurrentPage.value = 1;

  try {
    const formattedApiDate: string = format(filterCriteria.selectedDate, "yyyy-MM-dd");
    const queryParamsDrinkOrders: drinkOrderParams = {
      pageNum: 1,
      pageSize: 9999,
      queryDate: formattedApiDate,
      storeId: filterCriteria.storeId,
      orderTimeSort: "1",
    };
    const queryParamsProductionQueue: QueueQueryParams = {
      pageNum: 1,
      pageSize: 9999,
      storeId: filterCriteria.storeId,
    };
    let queryParams = {
      pageNum: 1,
      pageSize: 9999,
      queryDate: formattedApiDate,
      storeId: filterCriteria.storeId,
      orderTimeSort: "1",
    };
    const [orderResponse, queueItemResponse] = await Promise.all([
      getOrderListByParams(queryParamsDrinkOrders),
      getProductionQueueList(queryParamsProductionQueue)
    ]);
    dailyData.orders = orderResponse.rows ?? [];
    dailyData.queueItems = queueItemResponse.rows ?? [];

    calculatePopularDrink().then(() => {
    });

  } catch (err) {
    console.error("获取店铺日数据失败:", err);
    error.value = "数据加载失败，请稍后重试。";
    dailyData.orders = [];
    dailyData.queueItems = [];
  } finally {
    isLoading.value = false;
  }
}

// --- 计算属性和辅助函数 (保持不变) ---
const stats = computed(() => {
  const totalOrderCount = dailyData.orders.length;
  const totalQueueItemCount = dailyData.queueItems.length;
  const totalSales = dailyData.orders.reduce((sum, order) => {
    return sum + parseFloat(order.totalAmount || '0');
  }, 0);
  const completedOrders = dailyData.orders.filter(o => o.orderStatus === '4').length;
  const cancelledOrders = dailyData.orders.filter(o => o.orderStatus === '5').length;
  const pendingOrders = totalOrderCount - completedOrders - cancelledOrders;
  const finishedItems = dailyData.queueItems.filter(o => o.queueStatus === '2').length;
  const failedItems = dailyData.queueItems.filter(o => o.queueStatus === '3').length;
  const pendingItems = totalQueueItemCount - finishedItems - failedItems;

  return {
    orderStats: {
      totalSales,
      totalOrders: totalOrderCount,
      completedOrders,
      pendingOrders,
      cancelledOrders,
      popularDrink: popularDrinkInfo.value.name,
    },
    queueItemStats: {
      totalItems: totalQueueItemCount,
      finishedItems,
      pendingItems,
      failedItems,
    }
  };
});

const paginatedOrders = computed(() => {
  const start = (currentPage.value - 1) * pageSize;
  const end = start + pageSize;
  return dailyData.orders.slice(start, end);
});

const paginatedQueueItems = computed(() => {
  const start = (queueCurrentPage.value - 1) * pageSize;
  const end = start + pageSize;
  return dailyData.queueItems.slice(start, end);
});

// --- 辅助函数 (保持不变) ---
function getOrderStatusClass(status: string): string {
  let statusClass;
  switch (status) {
    case "0":
      statusClass = "待支付";
      break;
    case "1":
      statusClass = "已支付";
      break;
    case "2":
      statusClass = "制作中";
      break;
    case "3":
      statusClass = "待取餐";
      break;
    case "4":
      statusClass = "已完成";
      break;
    case "5":
      statusClass = "已取消";
      break;
    default:
      statusClass = "未定义";
  }
  return statusClass;
}

function getQueueItemStatusClass(status: string): string {
  let statusClass;
  switch (status) {
    case "0":
      statusClass = "待制作";
      break;
    case "1":
      statusClass = "制作中";
      break;
    case "2":
      statusClass = "制作完成";
      break;
    case "3":
      statusClass = "制作失败";
      break;
    default:
      statusClass = "未定义";
  }
  return statusClass;
}

// --- 生命周期钩子 ---
onMounted(async () => {
  sidebarStore.switchDetailMode();
  await fetchAvailableStores();
  await fetchAndProcessDailyData();
});

onUnmounted(() => {
  sidebarStore.switchInitialMode();
});

// --- 侦听器 (保持不变) ---
watch(() => filterCriteria.selectedDate, () => {
  fetchAndProcessDailyData();
});

watch(
    () => route.params.id,
    (newId) => {
      const newStoreId = Number(newId);
      if (newId && newStoreId !== filterCriteria.storeId) {
        filterCriteria.storeId = newStoreId;
        filterCriteria.selectedDate = new Date();
        fetchAndProcessDailyData();
      }
    }
);

// 计算最畅销饮品函数
async function calculatePopularDrink() {
  popularDrinkInfo.value = {id: 'N/A', name: '计算中...'};

  if (!dailyData.orders || dailyData.orders.length === 0) {
    popularDrinkInfo.value = {id: 'N/A', name: '暂无数据'};
    return;
  }

  try {
    const productQuantities = new Map<number, number>();

    for (const order of dailyData.orders) {
      // if (!order.orderItems || typeof order.orderItems !== 'string') {
      //   continue;
      // }
      let parsedItems;
      try {
        parsedItems = JSON.parse(order.orderItems);
      } catch (e) {
        continue;
      }
      if (!Array.isArray(parsedItems)) {
        continue;
      }
      for (const item of parsedItems) {
        if (item && item.productId) {
          const productId = Number(item.productId);
          const quantity = Number(item.quantity) || 1;
          productQuantities.set(productId, (productQuantities.get(productId) || 0) + quantity);
        }
      }
    }

    if (productQuantities.size === 0) {
      popularDrinkInfo.value = {id: 'N/A', name: '无有效商品'};
      return;
    }

    const uniqueProductIds = Array.from(productQuantities.keys());
    const detailPromises = uniqueProductIds.map(id =>
        getProductDetailById(id).catch(err => {
          console.error(`获取 productId ${id} 详情失败:`, err);
          return null;
        })
    );
    const productDetails = await Promise.all(detailPromises);

    const prototypeLookup = new Map<number, number>();
    productDetails.forEach((detail, index) => {
      if (detail && detail.drinkPrototype) {
        const productId = uniqueProductIds[index];
        prototypeLookup.set(productId, detail.drinkPrototype);
      }
    });

    const prototypeCounts = new Map<number, number>();
    productQuantities.forEach((totalQuantity, productId) => {
      const prototypeId = prototypeLookup.get(productId);
      if (prototypeId !== undefined) {
        prototypeCounts.set(prototypeId, (prototypeCounts.get(prototypeId) || 0) + totalQuantity);
      }
    });

    if (prototypeCounts.size > 0) {
      let maxCount = 0;
      let popularPrototypeId: number = -1;

      prototypeCounts.forEach((count, protoId) => {
        if (count > maxCount) {
          maxCount = count;
          popularPrototypeId = protoId;
        }
      });

      if (popularPrototypeId !== -1) {
        const prototypeDetail = await getPrototypeDetailById(popularPrototypeId);
        if (prototypeDetail && prototypeDetail.prototypeName) {
          popularDrinkInfo.value = {
            id: popularPrototypeId,
            name: prototypeDetail.prototypeName,
          };
        } else {
          popularDrinkInfo.value = {
            id: popularPrototypeId,
            name: `原型ID: ${popularPrototypeId}`,
          };
        }
      } else {
        popularDrinkInfo.value = {id: 'N/A', name: '无有效原型'};
      }
    } else {
      popularDrinkInfo.value = {id: 'N/A', name: '无有效原型'};
    }
  } catch (err) {
    console.error("计算最热饮品时发生未知错误:", err);
    popularDrinkInfo.value = {id: 'Error', name: '计算出错'};
  }
}
</script>

<template>
  <div v-if="isLoading">
    <div class="loader">Loading...</div>
  </div>
  <div v-else-if="currentStoreInfo">
    <div class="card mb-25 border-0 rounded-0 welcome-box">
      <div class="card-body pe-15 pe-sm-20 pe-md-0 pb-0 pt-15 pt-sm-20">
        <div class="row align-items-center">
          <div class="col-lg-6 col-md-6">
            <div class="title position-relative">
              <h3 class="fw-semibold mb-8 d-flex align-items-center">
                <span class="fw-bold me-2">{{ currentStoreInfo.storeName }}</span>
              </h3>
              <span class="d-block text-black-emphasis fs-md-15 fs-lg-16">
                这是
                <a id="date-change-module" href="#" @click.prevent="showDateModal" class="text-primary fw-bold ">
                  {{ format(filterCriteria.selectedDate, 'yyyy-MM-dd') }}
                </a>
                的门店数据概览。
              </span>
            </div>
            <ul class="ps-0 mb-0 list-unstyled">
              <li
                  class="d-inline-block text-uppercase fw-medium fs-13 text-black-emphasis position-relative"
              >
                今日杯数
                <span class="d-block fw-black lh-1 text-black mt-5 mt-md-10">
                {{ stats.orderStats.totalOrders }}杯
                </span>
              </li>
              <li
                  class="d-inline-block text-uppercase fw-medium fs-13 text-black-emphasis position-relative"
              >
                今日总销售额
                <span class="d-block fw-black lh-1 text-black mt-5 mt-md-10">
                ￥{{ stats.orderStats.totalSales.toFixed(2) }}
              </span>
              </li>
            </ul>
          </div>
          <div class="col-lg-6 col-md-6 text-center mt-15 mt-md-0">
            <img
                src="@/assets/images/welcome/welcome1.png"
                alt="welcome-image"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <!-- StatsCard 部分保持不变 -->
      <StatsCard
          title="今日销售额"
          :value="`¥${stats.orderStats.totalSales.toFixed(2)}`"
          icon="flaticon-idea"
          color="primary"
          sub-text=""
      />
      <StatsCard
          title="总杯数"
          :value="`${stats.queueItemStats.totalItems}杯`"
          icon="flaticon-sugar-cubes"
          color="danger"
          sub-text=""
      />
      <StatsCard
          title="总订单数"
          :value="`${stats.orderStats.totalOrders}单`"
          icon="flaticon-sterile-box"
          color="success"
          :progress="stats.orderStats.totalOrders > 0 ? Math.round((stats.orderStats.completedOrders / stats.orderStats.totalOrders) * 100) : 0"
          :progress-text="`${stats.orderStats.completedOrders} 已完成`"
      />
      <StatsCard
          title="最热饮品"
          :value="stats.orderStats.popularDrink"
          icon="flaticon-user-3"
          color="info"
          sub-text=""
      />
    </div>

    <!-- 订单列表和制作队列 -->
    <div class="row">
      <div class="col-sm-12 col-xxl-6">
        <div class="card mb-25 border-0 rounded-0 bg-white letter-spacing ">
          <div
              class="card-head box-shadow bg-white d-md-flex align-items-center justify-content-between p-15 p-sm-20 p-md-25"
          >
            <div class="d-sm-flex align-items-center mt-15 mt-md-3">
              <h3>订单信息</h3>
            </div>
          </div>
          <div class="card-body p-15 p-sm-20 p-md-25">
            <div v-if="isLoading" class="text-center">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
            <div v-else-if="error" class="alert alert-danger" role="alert">
              {{ error }}
            </div>
            <div v-else class="table-responsive">
              <table class="table text-nowrap align-middle mb-0">
                <thead>
                <tr>
                  <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0 ps-0">
                    #订单编号
                  </th>
                  <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">订单杯数
                  </th>
                  <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">付款金额
                  </th>
                  <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">订单状态
                  </th>
                  <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">下单时间
                  </th>
                </tr>
                </thead>
                <tbody>
                <tr v-if="paginatedOrders.length === 0">
                  <td colspan="9" class="text-center text-muted fst-italic">暂无订单数据。</td>
                </tr>
                <tr v-for="order in paginatedOrders" :key="order.orderTime">
                  <td class="shadow-none lh-1 fw-medium text-paragraph">#{{ order.orderId }}</td>
                  <td class="shadow-none lh-1 fw-medium text-paragraph">×{{ order.orderItemsList.length }}</td>
                  <td class="shadow-none lh-1 fw-medium text-paragraph">${{ order.totalAmount }}</td>
                  <td class="shadow-none lh-1 fw-medium text-paragraph">{{
                      getOrderStatusClass(order.orderStatus)
                    }}
                  </td>
                  <td class="shadow-none lh-1 fw-medium">{{
                      format(new Date(order.orderTime), 'yyyy-MM-dd HH:mm')
                    }}
                  </td>
                </tr>
                </tbody>
              </table>
            </div>
            <div class="pagination-area d-md-flex mt-15 mt-sm-20 mt-md-25 justify-content-between align-items-center">
              <p class="mb-0 text-paragraph">
                显示 <span class="fw-bold">{{ paginatedOrders.length }}</span> 条，共 <span
                  class="fw-bold">{{ dailyData.orders.length }}</span> 条结果
              </p>
              <b-pagination
                  v-model="currentPage"
                  :total-rows="dailyData.orders.length"
                  :per-page="pageSize"
                  aria-controls="order-table"
              />
            </div>
          </div>
        </div>
      </div>
      <!-- 制作队列部分保持不变 -->
      <div class="col-sm-12 col-xxl-6">
        <div class="card mb-25 border-0 rounded-0 bg-white letter-spacing ">
          <div
              class="card-head box-shadow bg-white d-md-flex align-items-center justify-content-between p-15 p-sm-20 p-md-25">
            <div class="d-sm-flex align-items-center mt-15 mt-md-3">
              <h3>制作队列</h3>
            </div>
          </div>
          <div class="card-body p-15 p-sm-20 p-md-25">
            <div v-if="isLoading" class="text-center">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
            <div v-else-if="error" class="alert alert-danger" role="alert">
              {{ error }}
            </div>
            <div v-else class="table-responsive">
              <table class="table text-nowrap align-middle mb-0">
                <thead>
                <tr>
                  <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0 ps-0">
                    #队列编号
                  </th>
                  <th scope="col"
                      class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0 drink-name-column">饮品名称
                  </th>
                  <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">
                    制作开始时间
                  </th>
                  <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">
                    制作结束时间
                  </th>
                  <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">状态</th>
                </tr>
                </thead>
                <tbody>
                <tr v-if="paginatedQueueItems.length === 0">
                  <td colspan="9" class="text-center text-muted fst-italic">暂无制作队列数据。</td>
                </tr>
                <tr v-for="item in paginatedQueueItems" :key="item.queueId">
                  <td class="shadow-none lh-1 fw-medium text-paragraph">#{{ item.queueId }}</td>
                  <td class="shadow-none lh-1 fw-medium text-paragraph drink-name-column">
                    <div class="truncate-text">{{ item.drinkName }}</div>
                  </td>
                  <td class="shadow-none lh-1 fw-medium text-paragraph">
                    {{ format(new Date(item.startTime), 'yyyy-MM-dd HH:mm') }}
                  </td>
                  <td class="shadow-none lh-1 fw-medium text-paragraph">
                    {{ format(new Date(item.finishTime), 'yyyy-MM-dd HH:mm') }}
                  </td>
                  <td class="shadow-none lh-1 fw-medium">{{ getQueueItemStatusClass(item.queueStatus) }}</td>
                </tr>
                </tbody>
              </table>
            </div>
            <div class="pagination-area d-md-flex mt-15 mt-sm-20 mt-md-25 justify-content-between align-items-center">
              <p class="mb-0 text-paragraph">
                显示 <span class="fw-bold">{{ paginatedQueueItems.length }}</span> 条，共 <span
                  class="fw-bold">{{ dailyData.queueItems.length }}</span> 条结果
              </p>
              <b-pagination
                  v-model="queueCurrentPage"
                  :total-rows="dailyData.queueItems.length"
                  :per-page="pageSize"
                  aria-controls="queue-table"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-else>
    <p>无法加载店铺信息，请检查店铺ID是否正确。</p>
  </div>

  <!-- 【修改】移除了门店选择模态框 -->

  <!-- 日期选择模态框 (保持不变) -->
  <b-modal
      v-model="isDateModalVisible"
      title="更换日期"
      centered
  >
    <div class="p-3">
      <p>请选择您要查看的日期：</p>
      <flat-pickr
          v-model="tempSelectedDate"
          :config="flatpickrConfig"
          class="form-control"
      />
    </div>
    <template #footer>
      <div class="d-flex justify-content-end w-100 gap-2">
        <button class="btn btn-secondary" @click="isDateModalVisible = false">取消</button>
        <button class="btn btn-primary" @click="confirmDateChange">确认</button>
      </div>
    </template>
  </b-modal>

  <!-- 【修改】移除了悬浮按钮 -->

</template>

<style scoped>
/* 【修改】移除了悬浮按钮的所有相关样式 */

/* 定义饮品名称列的固定宽度 */
.drink-name-column {
  max-width: 200px; /* 设置最大宽度，防止被其他列挤压 */
}

/* 实现文本溢出省略的核心样式 */
.truncate-text {
  white-space: nowrap; /* 1. 强制文本在同一行显示，不换行 */
  overflow: hidden; /* 2. 隐藏超出容器部分的内容 */
  text-overflow: ellipsis; /* 3. 当文本溢出时，用省略号(...)代替 */
  width: 100%; /* 确保 div 填满 <td> 的宽度 */
}
#date-change-module{
  font-family: Comfortaa,sans-serif;
}
#date-change-module:link{
  text-decoration: none;
}
#date-change-module:hover{
  text-decoration:underline;
}
</style>
