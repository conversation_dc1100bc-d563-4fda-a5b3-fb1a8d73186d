<template>
  <div class="order-management">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2 class="mb-0">订单管理</h2>
      <div class="d-flex gap-2">
        <button @click="toggleExceptionStats" class="btn btn-outline-info" :disabled="loading">
          <i class="flaticon-chart me-2"></i>{{ showExceptionStats ? '隐藏' : '显示' }}统计
        </button>
        <button @click="exportOrders" class="btn btn-outline-primary" :disabled="loading">
          <i class="flaticon-export me-2"></i>导出订单
        </button>
        <button @click="refreshData" class="btn btn-primary" :disabled="loading">
          <i class="flaticon-refresh me-2"></i>刷新
        </button>
      </div>
    </div>

    <!-- 异常订单统计卡片（可折叠） -->
    <div v-if="showExceptionStats" class="row g-3 mb-4">
      <div class="col-md-2">
        <div class="card bg-danger text-white">
          <div class="card-body text-center">
            <h4>{{ exceptionStats.totalExceptions }}</h4>
            <p class="mb-0">总异常订单</p>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="card bg-warning text-white">
          <div class="card-body text-center">
            <h4>{{ exceptionStats.unhandledCount }}</h4>
            <p class="mb-0">待处理</p>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="card bg-success text-white">
          <div class="card-body text-center">
            <h4>{{ exceptionStats.handledCount }}</h4>
            <p class="mb-0">已处理</p>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="card bg-info text-white">
          <div class="card-body text-center">
            <h4>{{ exceptionStats.timeoutProduction }}</h4>
            <p class="mb-0">制作超时</p>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="card bg-secondary text-white">
          <div class="card-body text-center">
            <h4>{{ exceptionStats.timeoutPickup }}</h4>
            <p class="mb-0">取餐超时</p>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="card bg-dark text-white">
          <div class="card-body text-center">
            <h4>{{ exceptionStats.paymentFailed }}</h4>
            <p class="mb-0">支付失败</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 订单类型切换标签页 -->
    <div class="card mb-4">
      <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs" role="tablist">
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              :class="{ active: currentTab === 'normal' }"
              @click="switchTab('normal')"
              type="button"
            >
              <i class="flaticon-list me-2"></i>全部订单
              <span v-if="normalOrderCount > 0" class="badge bg-primary ms-1">{{ normalOrderCount }}</span>
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              :class="{ active: currentTab === 'exception' }"
              @click="switchTab('exception')"
              type="button"
            >
              <i class="flaticon-warning me-2"></i>异常订单
              <span v-if="exceptionStats.unhandledCount > 0" class="badge bg-danger ms-1">{{ exceptionStats.unhandledCount }}</span>
            </button>
          </li>
        </ul>
      </div>

      <!-- 搜索和筛选区域 -->
      <div class="card-body">
        <div class="row g-3">
          <!-- 搜索框 -->
          <div class="col-md-4">
            <div class="input-group">
              <input
                type="text"
                class="form-control"
                placeholder="搜索订单号、流水号、手机号..."
                v-model="searchForm.searchValue"
                @keyup.enter="handleSearch"
              />
              <button class="btn btn-outline-secondary" type="button" @click="handleSearch">
                <i class="flaticon-search-interface-symbol"></i>
              </button>
            </div>
          </div>
          
          <!-- 门店筛选 -->
          <div class="col-md-2">
            <select v-model="searchForm.storeId" class="form-select" @change="handleSearch">
              <option value="">全部门店</option>
              <option v-for="store in storeList" :key="store.storeId" :value="store.storeId">
                {{ store.storeName }}
              </option>
            </select>
          </div>
          
          <!-- 订单状态筛选（普通订单） -->
          <div v-if="currentTab === 'normal'" class="col-md-2">
            <select v-model="searchForm.orderStatus" class="form-select" @change="handleSearch">
              <option value="">全部状态</option>
              <option value="0">待支付</option>
              <option value="1">已支付</option>
              <option value="2">制作中</option>
              <option value="3">待取餐</option>
              <option value="4">已完成</option>
              <option value="5">已取消</option>
            </select>
          </div>

          <!-- 异常类型筛选（异常订单） -->
          <div v-if="currentTab === 'exception'" class="col-md-2">
            <select v-model="searchForm.exceptionType" class="form-select" @change="handleSearch">
              <option value="">全部异常类型</option>
              <option value="timeout_production">制作超时</option>
              <option value="timeout_pickup">取餐超时</option>
              <option value="payment_failed">支付失败</option>
              <option value="production_failed">制作失败</option>
              <option value="system_error">系统错误</option>
            </select>
          </div>

          <!-- 处理状态筛选（异常订单） -->
          <div v-if="currentTab === 'exception'" class="col-md-2">
            <select v-model="searchForm.isHandled" class="form-select" @change="handleSearch">
              <option value="">全部处理状态</option>
              <option value="false">待处理</option>
              <option value="true">已处理</option>
            </select>
          </div>

          <!-- 订单来源筛选（普通订单） -->
          <div v-if="currentTab === 'normal'" class="col-md-2">
            <select v-model="searchForm.orderSource" class="form-select" @change="handleSearch">
              <option value="">全部来源</option>
              <option value="wx">微信小程序</option>
              <option value="pad">线下点单</option>
            </select>
          </div>
          
          <!-- 日期筛选 -->
          <div class="col-md-2">
            <input 
              type="date" 
              v-model="searchForm.queryDate" 
              class="form-control" 
              @change="handleSearch"
            />
          </div>
        </div>
        
        <div class="row g-3 mt-2">
          <!-- 时间排序 -->
          <div class="col-md-2">
            <select v-model="searchForm.orderTimeSort" class="form-select" @change="handleSearch">
              <option value="">默认排序</option>
              <option value="1">时间正序</option>
              <option value="2">时间倒序</option>
            </select>
          </div>

          <!-- 批量操作（仅异常订单显示） -->
          <div v-if="currentTab === 'exception'" class="col-md-3">
            <div class="input-group">
              <select v-model="batchAction" class="form-select">
                <option value="">选择批量操作</option>
                <option value="remake">批量重新制作</option>
                <option value="refund">批量退款</option>
                <option value="cancel">批量取消</option>
                <option value="contact">批量联系客户</option>
              </select>
              <button
                @click="handleBatchAction"
                class="btn btn-warning"
                :disabled="!batchAction || selectedOrders.length === 0"
              >
                执行 ({{ selectedOrders.length }})
              </button>
            </div>
          </div>

          <!-- 重置按钮 -->
          <div class="col-md-2">
            <button @click="resetSearch" class="btn btn-outline-secondary w-100">
              <i class="flaticon-refresh me-2"></i>重置
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 订单列表 -->
    <div class="card">
      <div class="card-body">
        <!-- 加载状态 -->
        <div v-if="loading" class="text-center py-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
          <p class="mt-2 text-muted">正在加载订单数据...</p>
        </div>

        <!-- 订单表格 -->
        <div v-else-if="currentOrderList.length > 0" class="table-responsive">
          <table class="table table-hover">
            <thead class="table-light">
              <tr>
                <!-- 异常订单显示复选框 -->
                <th v-if="currentTab === 'exception'" style="width: 40px;">
                  <input
                    type="checkbox"
                    @change="toggleSelectAll"
                    :checked="isAllSelected"
                    class="form-check-input"
                  />
                </th>
                <th>订单信息</th>
                <th>门店</th>
                <th>金额</th>
                <!-- 普通订单显示订单状态 -->
                <th v-if="currentTab === 'normal'">订单状态</th>
                <!-- 异常订单显示异常信息 -->
                <th v-if="currentTab === 'exception'">异常类型</th>
                <th v-if="currentTab === 'exception'">异常时间</th>
                <th v-if="currentTab === 'exception'">处理状态</th>
                <!-- 普通订单显示来源和时间 -->
                <th v-if="currentTab === 'normal'">订单来源</th>
                <th v-if="currentTab === 'normal'">下单时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="order in currentOrderList"
                :key="order.orderId"
                :class="{ 'table-warning': currentTab === 'exception' && !order.isHandled }"
              >
                <!-- 异常订单复选框 -->
                <td v-if="currentTab === 'exception'">
                  <input
                    type="checkbox"
                    :value="order.orderId"
                    v-model="selectedOrders"
                    class="form-check-input"
                  />
                </td>

                <!-- 订单信息 -->
                <td>
                  <div class="order-info">
                    <span class="order-id">#{{ order.orderId }}</span>
                    <span class="order-seq">{{ order.orderReqSeq }}</span>
                    <span class="customer-phone">{{ order.userPhone }}</span>
                  </div>
                </td>

                <!-- 门店 -->
                <td>
                  <span class="badge bg-info">{{ getStoreName(order.storeId) }}</span>
                </td>

                <!-- 金额 -->
                <td>
                  <span class="amount-display">¥{{ order.totalAmount }}</span>
                </td>

                <!-- 普通订单：订单状态 -->
                <td v-if="currentTab === 'normal'">
                  <span :class="getOrderStatusClass(order.orderStatus)">
                    {{ getOrderStatusText(order.orderStatus) }}
                  </span>
                </td>

                <!-- 异常订单：异常类型 -->
                <td v-if="currentTab === 'exception'">
                  <span :class="getExceptionTypeClass(order.exceptionType)">
                    {{ getExceptionTypeName(order.exceptionType) }}
                  </span>
                </td>

                <!-- 异常订单：异常时间 -->
                <td v-if="currentTab === 'exception'">
                  <div class="exception-time">
                    <small>{{ formatDateTime(order.exceptionTime) }}</small>
                    <span v-if="order.timeoutMinutes" class="badge bg-warning">
                      {{ order.timeoutMinutes }}分钟
                    </span>
                  </div>
                </td>

                <!-- 异常订单：处理状态 -->
                <td v-if="currentTab === 'exception'">
                  <div class="handle-status">
                    <span v-if="order.isHandled" class="badge bg-success">已处理</span>
                    <span v-else class="badge bg-warning">待处理</span>
                    <small v-if="order.isHandled" class="text-muted">{{ getActionName(order.handledAction) }}</small>
                  </div>
                </td>

                <!-- 普通订单：订单来源 -->
                <td v-if="currentTab === 'normal'">
                  <span :class="getOrderSourceClass(order.orderSource)">
                    {{ getOrderSourceText(order.orderSource) }}
                  </span>
                </td>

                <!-- 普通订单：下单时间 -->
                <td v-if="currentTab === 'normal'">{{ formatDate(order.orderTime) }}</td>

                <!-- 操作列 -->
                <td>
                  <!-- 普通订单操作 -->
                  <div v-if="currentTab === 'normal'" class="btn-group">
                    <!-- 直接使用按钮，避免下拉菜单问题 -->
                    <button
                      @click="viewOrderDetail(order)"
                      class="btn btn-sm btn-outline-info"
                      title="查看详情"
                      :disabled="loading"
                    >
                      <i class="flaticon-eye me-1"></i>详情
                    </button>
                    <button
                      v-if="canChangeStatus(order.orderStatus)"
                      @click="showStatusModal(order)"
                      class="btn btn-sm btn-outline-warning"
                      title="修改状态"
                      :disabled="loading"
                    >
                      <i class="flaticon-edit me-1"></i>状态
                    </button>
                    <button
                      @click="editOrderRemark(order)"
                      class="btn btn-sm btn-outline-secondary"
                      title="添加备注"
                      :disabled="loading"
                    >
                      <i class="flaticon-note me-1"></i>备注
                    </button>
                  </div>

                  <!-- 异常订单操作 -->
                  <div v-else class="btn-group-vertical btn-group-sm">
                    <button
                      @click="viewOrderDetail(order)"
                      class="btn btn-outline-info btn-sm"
                      title="查看详情"
                    >
                      详情
                    </button>
                    <button
                      v-if="!order.isHandled"
                      @click="showHandleModal(order)"
                      class="btn btn-outline-warning btn-sm"
                      title="处理异常"
                    >
                      处理
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 空状态 -->
        <div v-else class="text-center py-5">
          <i v-if="currentTab === 'normal'" class="flaticon-search-interface-symbol fs-1 text-muted mb-3"></i>
          <i v-else class="flaticon-check-mark fs-1 text-success mb-3"></i>
          <h5 class="text-muted">
            {{ currentTab === 'normal' ? '暂无订单数据' : '暂无异常订单' }}
          </h5>
          <p class="text-muted">
            {{ currentTab === 'normal' ? '请尝试调整筛选条件或刷新页面' : '当前没有需要处理的异常订单' }}
          </p>
        </div>

        <!-- 分页 -->
        <div v-if="pagination.total > 0" class="d-flex justify-content-between align-items-center mt-4">
          <div class="text-muted">
            共 {{ pagination.total }} 条记录，第 {{ pagination.pageNum }} / {{ Math.ceil(pagination.total / pagination.pageSize) }} 页
          </div>
          <nav>
            <ul class="pagination mb-0">
              <li class="page-item" :class="{ disabled: pagination.pageNum <= 1 }">
                <button class="page-link" @click="changePage(pagination.pageNum - 1)">上一页</button>
              </li>
              <li 
                v-for="page in getPageNumbers()" 
                :key="page" 
                class="page-item" 
                :class="{ active: page === pagination.pageNum }"
              >
                <button class="page-link" @click="changePage(page)">{{ page }}</button>
              </li>
              <li class="page-item" :class="{ disabled: pagination.pageNum >= Math.ceil(pagination.total / pagination.pageSize) }">
                <button class="page-link" @click="changePage(pagination.pageNum + 1)">下一页</button>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>

    <!-- 订单详情弹窗 -->
    <OrderDetailModal
      ref="orderDetailModalRef"
      :store-list="storeList"
    />

    <!-- 订单状态修改弹窗 -->
    <OrderStatusModal
      ref="orderStatusModalRef"
      @status-changed="handleStatusChanged"
    />

    <!-- 异常订单处理弹窗 -->
    <ExceptionHandleModal
      v-if="showHandleModalFlag"
      :order="selectedExceptionOrder"
      @close="showHandleModalFlag = false"
      @handled="onOrderHandled"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { format } from 'date-fns'
import { Modal } from 'bootstrap'
import {
  getOrderList,
  exportOrderList,
  type OrderQueryParams,
  type ExceptionOrder,
  type ExceptionAction
} from '@/utils/api/order'
import { getStoreList } from '@/utils/api/store'
import type { drinkOrder } from '@/types/order'
import type { storeDetail } from '@/types'
import OrderDetailModal from './OrderDetailModal.vue'
import OrderStatusModal from './OrderStatusModal.vue'
import ExceptionHandleModal from './ExceptionHandleModal.vue'

// 异常统计类型定义
interface ExceptionStatistics {
  totalExceptions: number
  unhandledCount: number
  handledCount: number
  timeoutProduction: number
  timeoutPickup: number
  paymentFailed: number
  productionFailed: number
  systemError: number
}

// 数据状态
const loading = ref(false)
const orderList = ref<drinkOrder[]>([])
const exceptionOrderList = ref<ExceptionOrder[]>([])
const storeList = ref<storeDetail[]>([])
const exceptionCount = ref(0)
const normalOrderCount = ref(0)

// UI状态
const currentTab = ref<'normal' | 'exception'>('normal')
const showExceptionStats = ref(false)
const exceptionStats = ref<ExceptionStatistics>({
  totalExceptions: 0,
  unhandledCount: 0,
  handledCount: 0,
  timeoutProduction: 0,
  timeoutPickup: 0,
  paymentFailed: 0,
  productionFailed: 0,
  systemError: 0
})

// 异常订单相关状态
const selectedOrders = ref<string[]>([])
const batchAction = ref('')
const showHandleModalFlag = ref(false)
const selectedExceptionOrder = ref<ExceptionOrder | null>(null)

// 组件引用
const orderDetailModalRef = ref<InstanceType<typeof OrderDetailModal>>()
const orderStatusModalRef = ref<InstanceType<typeof OrderStatusModal>>()

// 搜索表单
const searchForm = ref({
  searchValue: '',
  storeId: '',
  orderStatus: '',
  orderSource: '',
  queryDate: '',
  orderTimeSort: '',
  // 异常订单相关字段
  exceptionType: '',
  isHandled: ''
})

// 分页信息
const pagination = ref({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 计算属性
const currentOrderList = computed(() => {
  return currentTab.value === 'normal' ? orderList.value : exceptionOrderList.value
})

const isAllSelected = computed(() => {
  return currentTab.value === 'exception' &&
         exceptionOrderList.value.length > 0 &&
         selectedOrders.value.length === exceptionOrderList.value.length
})

// 获取订单列表
const fetchOrderList = async () => {
  loading.value = true
  try {
    const params: OrderQueryParams = {
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize,
      storeId: searchForm.value.storeId ? parseInt(searchForm.value.storeId) : undefined,
      orderStatus: searchForm.value.orderStatus || undefined,
      orderSource: searchForm.value.orderSource || undefined,
      queryDate: searchForm.value.queryDate || undefined,
      orderTimeSort: searchForm.value.orderTimeSort || undefined,
      // 根据搜索值判断是订单号、流水号还是手机号
      ...(searchForm.value.searchValue && {
        ...(searchForm.value.searchValue.match(/^\d+$/) && searchForm.value.searchValue.length <= 10
          ? { orderId: parseInt(searchForm.value.searchValue) }
          : searchForm.value.searchValue.match(/^1[3-9]\d{9}$/)
          ? { userPhone: searchForm.value.searchValue }
          : { orderReqSeq: searchForm.value.searchValue })
      })
    }

    console.log('🔍 [订单管理] 查询参数:', params)
    const response = await getOrderList(params)
    orderList.value = response.rows || []
    pagination.value.total = response.total || 0

    console.log(`✅ [订单管理] 订单列表加载成功: ${orderList.value.length}个订单`)
  } catch (error) {
    console.error('❌ [订单管理] 获取订单列表失败:', error)
    orderList.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

// 获取门店列表
const fetchStoreList = async () => {
  try {
    console.log('🔄 [订单管理] 开始加载门店列表...')
    const response = await getStoreList({ pageNum: 1, pageSize: 1000 })

    console.log('📦 [订单管理] 门店API原始响应:', response)

    const allStores = response.rows || []
    console.log(`📊 [订单管理] API返回门店数据: ${allStores.length}个门店`)

    if (allStores.length > 0) {
      console.log('🏪 [订单管理] 门店状态分布:', allStores.map(s => `${s.storeId}:${s.storeName}(状态:${s.status})`))
    }

    // 根据测试结果，门店的status都是'0'，我们需要调整筛选逻辑
    // 先尝试筛选营业中的门店（status === '1'）
    let activeStores = allStores.filter((store: storeDetail) => store.status === '1')

    // 如果没有营业中的门店，尝试其他状态值
    if (activeStores.length === 0) {
      console.log('⚠️ [订单管理] 没有找到status=1的门店，尝试status=0的门店...')
      // 根据测试结果，所有门店的status都是'0'，我们将其视为可用门店
      activeStores = allStores.filter((store: storeDetail) => store.status === '0')
      console.log(`📋 [订单管理] 找到${activeStores.length}个status=0的门店，将其视为可用门店`)

      if (activeStores.length === 0) {
        console.log('⚠️ [订单管理] 没有找到任何状态的门店，使用所有门店')
        activeStores = allStores
      }
    }

    storeList.value = activeStores
    console.log(`✅ [订单管理] 门店列表加载成功: ${storeList.value.length}个可用门店`)
    console.log('🏪 [订单管理] 可用门店列表:', storeList.value.map(s => `${s.storeId}:${s.storeName}`))

  } catch (error) {
    console.error('❌ [订单管理] 获取门店列表失败:', error)
    storeList.value = []
  }
}

// 搜索处理
const handleSearch = () => {
  console.log('🔍 [订单管理] 执行搜索，条件:', searchForm.value)
  pagination.value.pageNum = 1
  fetchOrderList()
}

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    searchValue: '',
    storeId: '',
    orderStatus: '',
    orderSource: '',
    queryDate: '',
    orderTimeSort: ''
  }
  pagination.value.pageNum = 1
  fetchOrderList()
}

// 分页处理
const changePage = (page: number) => {
  if (page < 1 || page > Math.ceil(pagination.value.total / pagination.value.pageSize)) return
  pagination.value.pageNum = page
  fetchOrderList()
}

// 获取页码数组
const getPageNumbers = () => {
  const totalPages = Math.ceil(pagination.value.total / pagination.value.pageSize)
  const current = pagination.value.pageNum
  const pages = []

  const start = Math.max(1, current - 2)
  const end = Math.min(totalPages, current + 2)

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
}

// 获取门店名称
const getStoreName = (storeId: number) => {
  const store = storeList.value.find(s => s.storeId === storeId)
  return store?.storeName || '未知门店'
}

// 获取订单状态文本
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '0': '待支付',
    '1': '已支付',
    '2': '制作中',
    '3': '待取餐',
    '4': '已完成',
    '5': '已取消'
  }
  return statusMap[status] || '未知状态'
}

// 获取订单状态样式
const getOrderStatusClass = (status: string) => {
  const classMap: Record<string, string> = {
    '0': 'badge bg-warning',
    '1': 'badge bg-info',
    '2': 'badge bg-primary',
    '3': 'badge bg-success',
    '4': 'badge bg-secondary',
    '5': 'badge bg-danger'
  }
  return classMap[status] || 'badge bg-light'
}

// 获取订单来源文本
const getOrderSourceText = (source: string) => {
  const sourceMap: Record<string, string> = {
    'wx': '微信小程序',
    'pad': '线下点单'
  }
  return sourceMap[source] || '未知来源'
}

// 获取订单来源样式
const getOrderSourceClass = (source: string) => {
  const classMap: Record<string, string> = {
    'wx': 'badge bg-success',
    'pad': 'badge bg-info'
  }
  return classMap[source] || 'badge bg-light'
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return format(new Date(dateString), 'yyyy-MM-dd HH:mm')
}

// 判断是否可以修改状态
const canChangeStatus = (status: string) => {
  // 已完成和已取消的订单不能修改状态
  return !['4', '5'].includes(status)
}

// 查看订单详情
const viewOrderDetail = async (order: drinkOrder | ExceptionOrder) => {
  console.log('🔍 [订单管理] 查看订单详情:', order)

  try {
    // 处理orderId可能是string或number的情况
    const orderId = typeof order.orderId === 'string' ? parseInt(order.orderId) : order.orderId
    console.log('📋 [订单管理] 加载订单ID:', orderId)

    if (orderDetailModalRef.value) {
      await orderDetailModalRef.value.loadOrderDetail(orderId)
    }

    // 使用Bootstrap的Modal API显示弹窗
    const modalElement = document.getElementById('orderDetailModal')
    if (modalElement) {
      const modal = new Modal(modalElement)
      modal.show()
    } else {
      console.error('❌ [订单管理] 找不到订单详情弹窗元素')
    }
  } catch (error) {
    console.error('❌ [订单管理] 显示订单详情失败:', error)
  }
}

// 显示状态修改弹窗
const showStatusModal = (order: drinkOrder) => {
  console.log('修改订单状态:', order)
  if (orderStatusModalRef.value) {
    orderStatusModalRef.value.showModal(order)
    const modal = new Modal(document.getElementById('orderStatusModal')!)
    modal.show()
  }
}

// 处理状态修改成功
const handleStatusChanged = (orderId: string, newStatus: string) => {
  console.log(`✅ [订单管理] 订单${orderId}状态已修改为${newStatus}`)
  // 刷新订单列表
  fetchOrderList()
}

// 编辑订单备注
const editOrderRemark = (order: drinkOrder) => {
  console.log('编辑订单备注:', order)
  // TODO: 实现备注编辑功能
}

// 导出订单
const exportOrders = async () => {
  try {
    const params: OrderQueryParams = {
      pageNum: 1,
      pageSize: 10000, // 导出所有数据
      storeId: searchForm.value.storeId ? parseInt(searchForm.value.storeId) : undefined,
      orderStatus: searchForm.value.orderStatus || undefined,
      orderSource: searchForm.value.orderSource || undefined,
      queryDate: searchForm.value.queryDate || undefined,
      orderTimeSort: searchForm.value.orderTimeSort || undefined
    }

    console.log('📤 [订单管理] 开始导出订单...')
    const blob = await exportOrderList(params)

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `订单列表_${format(new Date(), 'yyyy-MM-dd_HH-mm-ss')}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    console.log('✅ [订单管理] 订单导出成功')
  } catch (error) {
    console.error('❌ [订单管理] 订单导出失败:', error)
  }
}

// 标签页切换
const switchTab = (tab: 'normal' | 'exception') => {
  currentTab.value = tab
  selectedOrders.value = []
  pagination.value.pageNum = 1

  if (tab === 'normal') {
    fetchOrderList()
  } else {
    fetchExceptionOrders()
  }
}

// 切换异常统计显示
const toggleExceptionStats = () => {
  showExceptionStats.value = !showExceptionStats.value
  if (showExceptionStats.value) {
    fetchExceptionStatistics()
  }
}

// 刷新数据
const refreshData = () => {
  if (currentTab.value === 'normal') {
    fetchOrderList()
  } else {
    fetchExceptionOrders()
  }
  fetchExceptionStatistics()
}

// 获取异常订单列表
const fetchExceptionOrders = async () => {
  loading.value = true
  try {
    console.log('🔄 [订单管理] 开始加载异常订单列表...')

    // 模拟异常订单数据
    const mockExceptionOrders: ExceptionOrder[] = [
      {
        orderId: 'ORD001',
        orderReqSeq: 'REQ001',
        orderStatus: '2',
        orderTime: '2025-08-01 10:30:00',
        paymentOrderNo: 'PAY001',
        paymentTime: '2025-08-01 10:31:00',
        pickupNo: 'P001',
        remark: '',
        storeId: 100,
        totalAmount: '25.80',
        userPhone: '13800138001',
        createBy: 'system',
        createTime: '2025-08-01 10:30:00',
        updateBy: 'system',
        updateTime: '2025-08-01 11:45:00',
        orderItems: '',
        orderItemsList: [],
        exceptionType: 'timeout_production',
        exceptionReason: '制作时间超过30分钟',
        exceptionTime: '2025-08-01 11:45:00',
        timeoutMinutes: 45,
        isHandled: false
      }
    ]

    exceptionOrderList.value = mockExceptionOrders
    pagination.value.total = mockExceptionOrders.length

    console.log(`✅ [订单管理] 异常订单列表加载成功: ${exceptionOrderList.value.length}个异常订单`)
  } catch (error) {
    console.error('❌ [订单管理] 获取异常订单列表失败:', error)
    exceptionOrderList.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

// 获取异常统计
const fetchExceptionStatistics = async () => {
  try {
    // 模拟统计数据
    exceptionStats.value = {
      totalExceptions: 5,
      unhandledCount: 3,
      handledCount: 2,
      timeoutProduction: 2,
      timeoutPickup: 1,
      paymentFailed: 1,
      productionFailed: 1,
      systemError: 0
    }

    exceptionCount.value = exceptionStats.value.unhandledCount
    console.log('✅ [订单管理] 异常统计加载成功:', exceptionStats.value)
  } catch (error) {
    console.error('❌ [订单管理] 获取异常统计失败:', error)
    exceptionCount.value = 0
  }
}

// 全选/取消全选
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedOrders.value = []
  } else {
    selectedOrders.value = exceptionOrderList.value.map(order => order.orderId)
  }
}

// 批量操作
const handleBatchAction = async () => {
  if (!batchAction.value || selectedOrders.value.length === 0) return

  const action = batchAction.value as ExceptionAction

  try {
    console.log(`🔄 [订单管理] 开始批量${getActionName(action)}: ${selectedOrders.value.length}个订单`)

    // 这里应该调用实际的API
    console.log('批量操作参数:', {
      orderIds: selectedOrders.value,
      action,
      remark: `批量${getActionName(action)}`
    })

    console.log(`✅ [订单管理] 批量${getActionName(action)}成功`)

    // 刷新列表
    selectedOrders.value = []
    batchAction.value = ''
    fetchExceptionOrders()
    fetchExceptionStatistics()
  } catch (error) {
    console.error(`❌ [订单管理] 批量${getActionName(action)}失败:`, error)
  }
}

// 显示异常处理弹窗
const showHandleModal = (order: ExceptionOrder) => {
  selectedExceptionOrder.value = order
  showHandleModalFlag.value = true
}

// 异常订单处理完成回调
const onOrderHandled = () => {
  fetchExceptionOrders()
  fetchExceptionStatistics()
}

// 异常订单相关工具函数
const getExceptionTypeName = (type: string): string => {
  const names: Record<string, string> = {
    timeout_production: '制作超时',
    timeout_pickup: '取餐超时',
    payment_failed: '支付失败',
    production_failed: '制作失败',
    system_error: '系统错误'
  }
  return names[type] || type
}

const getExceptionTypeClass = (type: string): string => {
  const classes: Record<string, string> = {
    timeout_production: 'badge bg-warning',
    timeout_pickup: 'badge bg-info',
    payment_failed: 'badge bg-danger',
    production_failed: 'badge bg-secondary',
    system_error: 'badge bg-dark'
  }
  return classes[type] || 'badge bg-secondary'
}

const getActionName = (action?: string): string => {
  if (!action) return ''
  const names: Record<string, string> = {
    remake: '重新制作',
    refund: '退款',
    cancel: '取消订单',
    contact: '联系客户',
    complete: '手动完成'
  }
  return names[action] || action
}

const formatDateTime = (dateTime: string): string => {
  if (!dateTime) return ''
  return format(new Date(dateTime), 'MM-dd HH:mm')
}

// 初始化
onMounted(async () => {
  console.log('🚀 [订单管理] 开始初始化...')

  // 先加载门店列表
  await fetchStoreList()

  // 验证门店数据是否加载成功
  if (storeList.value.length === 0) {
    console.error('❌ [订单管理] 门店数据加载失败，门店筛选将不可用')
    console.log('🔧 [订单管理] 建议检查：')
    console.log('   1. 门店API是否正常: GET /manager/store/list')
    console.log('   2. 门店数据的status字段值')
    console.log('   3. 网络连接是否正常')
  }

  // 加载订单列表
  await fetchOrderList()

  // 获取异常订单统计
  await fetchExceptionStatistics()

  // 更新订单计数
  normalOrderCount.value = orderList.value.length

  console.log(`✅ [订单管理] 初始化完成: ${storeList.value.length}个门店, ${orderList.value.length}个订单, ${exceptionCount.value}个异常订单`)
})
</script>

<style scoped>
.order-management {
  padding: 20px;
}

/* 表格整体样式优化 */
.table-responsive {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table {
  margin-bottom: 0;
  border-collapse: separate;
  border-spacing: 0;
}

/* 表头样式优化 */
.table th {
  background-color: #f8f9fa;
  border-top: none;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
  padding: 16px 12px;
  vertical-align: middle;
  text-align: center;
  white-space: nowrap;
  position: sticky;
  top: 0;
  z-index: 10;
}

/* 表格单元格样式优化 */
.table td {
  padding: 14px 12px;
  vertical-align: middle;
  border-bottom: 1px solid #f1f3f4;
  line-height: 1.4;
}

/* 表格行样式 */
.table tbody tr {
  transition: all 0.2s ease;
}

.table tbody tr:hover {
  background-color: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 异常订单行特殊样式 */
.table-warning {
  background-color: #fff3cd !important;
  border-left: 4px solid #ffc107;
}

.table-warning:hover {
  background-color: #ffeaa7 !important;
}

/* 列宽优化 */
.table th:nth-child(1), /* 复选框 */
.table td:nth-child(1) {
  width: 50px;
  text-align: center;
}

.table th:nth-child(2), /* 订单信息 */
.table td:nth-child(2) {
  width: 200px;
  text-align: left;
}

.table th:nth-child(3), /* 门店 */
.table td:nth-child(3) {
  width: 120px;
  text-align: center;
}

.table th:nth-child(4), /* 金额 */
.table td:nth-child(4) {
  width: 100px;
  text-align: right;
  font-weight: 600;
  color: #28a745;
}

.table th:nth-child(5), /* 异常类型 */
.table td:nth-child(5) {
  width: 120px;
  text-align: center;
}

.table th:nth-child(6), /* 异常时间 */
.table td:nth-child(6) {
  width: 140px;
  text-align: center;
}

.table th:nth-child(7), /* 处理状态 */
.table td:nth-child(7) {
  width: 120px;
  text-align: center;
}

.table th:nth-child(8), /* 操作 */
.table td:nth-child(8) {
  width: 100px;
  text-align: center;
}

/* 徽章样式优化 */
.badge {
  font-size: 0.75rem;
  padding: 6px 10px;
  border-radius: 6px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 按钮组样式优化 */
.btn-group-vertical .btn {
  margin-bottom: 2px;
  border-radius: 4px !important;
  font-size: 0.8rem;
  padding: 4px 8px;
}

.btn-group-vertical .btn:last-child {
  margin-bottom: 0;
}

/* 复选框样式优化 */
.form-check-input {
  width: 18px;
  height: 18px;
  border-radius: 4px;
  border: 2px solid #dee2e6;
  transition: all 0.2s ease;
}

.form-check-input:checked {
  background-color: #007bff;
  border-color: #007bff;
}

.form-check-input:focus {
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

/* 订单信息样式 */
.order-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.order-id {
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
}

.order-seq {
  font-size: 0.8rem;
  color: #6c757d;
}

.customer-phone {
  font-size: 0.8rem;
  color: #28a745;
}

/* 金额显示样式 */
.amount-display {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #28a745;
}

/* 异常时间显示样式 */
.exception-time {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.exception-time small {
  color: #6c757d;
  font-size: 0.8rem;
}

/* 处理状态样式 */
.handle-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.handle-status .badge {
  min-width: 60px;
}

.handle-status small {
  color: #6c757d;
  font-size: 0.75rem;
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .table th:nth-child(2),
  .table td:nth-child(2) {
    width: 180px;
  }

  .table th:nth-child(6),
  .table td:nth-child(6) {
    width: 120px;
  }
}

@media (max-width: 992px) {
  .table-responsive {
    border-radius: 0;
    box-shadow: none;
  }

  .table th,
  .table td {
    padding: 10px 8px;
    font-size: 0.9rem;
  }

  .btn-group-vertical .btn {
    font-size: 0.75rem;
    padding: 3px 6px;
  }
}

@media (max-width: 768px) {
  .order-management {
    padding: 15px;
  }

  .table th,
  .table td {
    padding: 8px 6px;
    font-size: 0.85rem;
  }

  .badge {
    font-size: 0.7rem;
    padding: 4px 6px;
  }

  .btn-group-vertical .btn {
    font-size: 0.7rem;
    padding: 2px 4px;
  }
}

.dropdown-toggle::after {
  margin-left: 0.5rem;
}
</style>
