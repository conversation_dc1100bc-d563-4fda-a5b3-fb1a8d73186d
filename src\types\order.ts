import {type drinkProductList} from "@/types/drink";

export interface drinkOrder{
    createBy:string,
    createTime:string,
    updateBy:string,
    updateTime:string,

    orderId:string,
    orderItems:string,
    orderItemsList:drinkProductList,
    orderReqSeq:string,
    orderStatus:string,
    orderTime:string,

    paymentOrderNo:string,
    paymentTime:string,
    pickupNo:string,

    remark:string,
    storeId:number,
    totalAmount:string,
    userPhone:string,
}
export type drinkOrderList = Array<drinkOrder>;
export interface orderDrinkRel {
    relId: number; // 关联ID
    orderId: number; // 订单ID
    drinkId: number; // 饮品ID
    drinkOptions: string; // 饮品选项（JSON格式）
    drinkPrice: string; // 饮品价格
    drinkQuantity: number; // 饮品数量
    createTime: string; // 创建时间
}

export type orderDrinkRelList = Array<orderDrinkRel>;