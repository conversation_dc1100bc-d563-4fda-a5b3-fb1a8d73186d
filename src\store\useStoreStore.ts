import {defineStore} from 'pinia';
import {Ref, ref} from "vue";
import {getStoreList} from "@/utils/api/store"
import type {storeList} from "@/types/store";
import {storeDetail} from "@/types/store";

export const useStoreStore = defineStore('store', () => {
        const stores = ref<storeList>([]);
        const activeStore = ref<storeDetail | null>(null);
        const selectedStore = ref<storeDetail | null>(null);

        async function fetchStore() {
            try {
                // getStoreList 返回的 res 类型就是 TableDataInfo<storeDetail>
                const res = await getStoreList({
                    pageNum: 1,
                    pageSize: 999,
                });

                // 关键改动：直接访问 res.rows
                // TypeScript 现在不会报错了，因为 TableDataInfo 类型中应该包含了 rows 属性
                // 使用 '?? []' 是为了防止当 res.rows 为 null 或 undefined 时程序报错
                stores.value = res.rows ?? [];

            } catch (err) {
                console.error('店铺数据获取失败', err);
                // 请求失败时，务必清空数组
                stores.value = [];
            }
        }

        function findStoreById(targetId: number | undefined): Ref<storeDetail | undefined> {
            const result = ref<storeDetail | undefined>(undefined);
            if (targetId !== undefined) {
                console.log("正在查找ID:", targetId);
                result.value = stores.value.find(store => store.storeId == targetId);
            }
            return result;
        }

        function selectStore(store: storeDetail) {
            selectedStore.value = store;
            console.log('选中门店:', store.storeName);
        }

        function clearSelectedStore() {
            selectedStore.value = null;
        }

        function getSelectedStoreId(): number | null {
            return selectedStore.value?.storeId || null;
        }

        return {
            stores, // 加载的店铺列表
            fetchStore, // 获取店铺列表数据
            activeStore, // 当前查看的店铺数据
            selectedStore, // 选中的门店
            findStoreById,
            selectStore, // 选择门店
            clearSelectedStore, // 清除选中的门店
            getSelectedStoreId, // 获取选中门店ID
        }
    },
    {
        persist: {
            key: 'store-data',
            storage: localStorage,
        }, // pinia-plugin-persistedstate 为当前pinia store实现持久化
    }
)

