/*
** - Default CSS
*/
@import url("https://fonts.googleapis.com/css2?family=Red+Hat+Display:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
:root {
  --transition: 0.25s;
  --bs-body-bg: #ffffff;
  --bs-body-font-size: 14px;
  --bs-body-color: #2b2a3f;
  --splash-body-bg: #f2f1f9;
  --splash-info-color: #1FB1E6;
  --splash-gray-color: #F8F8FA;
  --splash-white-color: #ffffff;
  --splash-black-color: #2b2a3f;
  --splash-muted-color: #8e8da1;
  --splash-danger-color: #EF2929;
  --splash-orange-color: #F1421B;
  --splash-primary-color: #6560F0;
  --splash-success-color: #06b48a;
  --splash-warning-color: #F3C44C;
  --splash-emphasis-color: #a2a1bb;
  --splash-tertiary-color: #9998B4;
  --splash-secondary-color: #8e8da2;
  --splash-paragraph-color: #79788e;
  --splash-info-light-color: #6FD3F7;
  --splash-warning-light-color: #EF7C29;
  --splash-dark-emphasis-color: #9C9AB6;
  --splash-black-emphasis-color: #4c4a68;
  --splash-primary-active-color: #726eed;
  --splash-danger-emphasis-color: #d4623a;
  --splash-primary-emphasis-color: #59CBB7;
  --bs-font-sans-serif: "SimSun", sans-serif;
}

:focus {
  outline: 0 !important;
}

img {
  max-width: 100%;
  height: auto;
}

.transition {
  transition: var(--transition) !important;
}

ol li:first-child, ul li:first-child {
  margin-left: 0 !important;
}
ol li:last-child, ul li:last-child {
  margin-right: 0 !important;
}

.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
  color: var(--bs-body-color);
}

.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
  color: var(--splash-black-color);
}

.lh-base {
  line-height: 1.7 !important;
}

.bg-body-secondary {
  background-color: var(--splash-body-bg) !important;
}

.text-body-secondary {
  color: var(--splash-secondary-color) !important;
}

.text-primary {
  color: var(--splash-primary-color) !important;
}

.text-black-emphasis {
  color: var(--splash-black-emphasis-color) !important;
}

.text-body-emphasis {
  color: var(--splash-emphasis-color) !important;
}

.bg-black {
  background-color: var(--splash-black-color) !important;
}

.bg-success {
  background-color: var(--splash-success-color) !important;
}

.text-success {
  color: var(--splash-success-color) !important;
}

.text-black {
  color: var(--splash-black-color) !important;
}

.text-muted {
  color: var(--splash-muted-color) !important;
}

.text-dark-emphasis {
  color: var(--splash-dark-emphasis-color) !important;
}

a {
  color: var(--splash-primary-color);
}
a:hover {
  color: var(--splash-black-color);
}

.bg-primary {
  background-color: var(--splash-primary-color) !important;
}

.bg-gray {
  background-color: var(--splash-gray-color) !important;
}

.start-auto {
  left: auto !important;
}

.text-paragraph {
  color: var(--splash-paragraph-color) !important;
}

.text-info {
  color: var(--splash-info-color) !important;
}

.text-danger {
  color: var(--splash-danger-color) !important;
}

.bg-secondary {
  background-color: var(--splash-secondary-color) !important;
}

.bg-primary-emphasis {
  background-color: var(--splash-primary-emphasis-color) !important;
}

.text-body-tertiary {
  color: var(--splash-tertiary-color) !important;
}

.text-outline-success {
  background-color: #e9f1ef !important;
  color: var(--splash-success-color) !important;
}

.text-outline-danger {
  background-color: #f3ebeb !important;
  color: var(--splash-danger-color) !important;
}

.text-outline-danger-emphasis {
  background-color: #f1eae8 !important;
  color: var(--splash-danger-emphasis-color) !important;
}

.text-bg-primary {
  background-color: var(--splash-primary-color) !important;
}

.text-outline-primary {
  background-color: #F4F3F7 !important;
  color: var(--splash-primary-color) !important;
}

.text-outline-info {
  background-color: #EBF0F2 !important;
  color: var(--splash-info-color) !important;
}

.text-outline-muted {
  background-color: #EFEEF3 !important;
  color: var(--splash-muted-color) !important;
}

.text-outline-warning {
  background-color: #F8F8F8 !important;
  color: var(--splash-warning-color) !important;
}

.cursor-pointer {
  cursor: pointer !important;
}

.letter-spacing {
  letter-spacing: 0.01em !important;
}

.bg-info {
  background-color: var(--splash-info-color) !important;
}

.bg-muted {
  background-color: var(--splash-muted-color) !important;
}

.bg-info-light {
  background-color: var(--splash-info-light-color) !important;
}

.text-info-light {
  color: var(--splash-info-light-color) !important;
}

.text-warning-light {
  color: var(--splash-warning-light-color) !important;
}

.bg-warning {
  background-color: var(--splash-warning-color) !important;
}

.bg-orange {
  background-color: var(--splash-orange-color) !important;
}

.text-warning {
  color: var(--splash-warning-color) !important;
}

.bg-f2f1f9 {
  background-color: #F2F1F9 !important;
}

.bg-faf7f7 {
  background-color: #FAF7F7 !important;
}

.bg-ecf3f2 {
  background-color: #ECF3F2 !important;
}

.bg-f3f7f9 {
  background-color: #f3f7f9 !important;
}

.bg-image {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}

.text-opacity-75 {
  opacity: var(--bs-text-opacity);
}

.text-opacity-50 {
  opacity: var(--bs-text-opacity);
}

.text-opacity-25 {
  opacity: var(--bs-text-opacity);
}

.link-opacity-10 {
  opacity: var(--bs-link-opacity);
}

.link-opacity-25 {
  opacity: var(--bs-link-opacity);
}

.link-opacity-50 {
  opacity: var(--bs-link-opacity);
}

.link-opacity-75 {
  opacity: var(--bs-link-opacity);
}

.link-opacity-100 {
  opacity: var(--bs-link-opacity);
}

.link-opacity-10-hover:hover, .link-opacity-25-hover:hover, .link-opacity-50-hover:hover, .link-opacity-75-hover:hover, .link-opacity-100-hover:hover {
  opacity: var(--bs-link-opacity);
}

.w-25-percentage {
  width: 25% !important;
}

.w-50-percentage {
  width: 50% !important;
}

.w-75-percentage {
  width: 75% !important;
}

.h-25-percentage {
  height: 25% !important;
}

.h-50-percentage {
  height: 50% !important;
}

.h-75-percentage {
  height: 75% !important;
}

.dark.bg-body-secondary {
  background-color: var(--splash-black-color) !important;
}
.dark .bg-body-secondary {
  background-color: var(--splash-black-color) !important;
}
.dark .bg-white {
  background-color: #34334a !important;
}
.dark .bg-body-tertiary {
  background-color: var(--splash-black-color) !important;
}
.dark .text-black-emphasis {
  color: #BCBBC7 !important;
}
.dark .h1, .dark .h2, .dark .h3, .dark .h4, .dark .h5, .dark .h6, .dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
  color: var(--splash-white-color) !important;
}
.dark a:hover {
  color: var(--splash-white-color);
}
.dark .text-black {
  color: var(--splash-white-color) !important;
}
.dark .text-secondary {
  color: #BCBBC7 !important;
}
.dark .text-dark {
  color: #BCBBC7 !important;
}
.dark .text-muted {
  color: #BCBBC7 !important;
}
.dark .text-dark-emphasis {
  color: #BCBBC7 !important;
}
.dark .text-body-tertiary {
  color: #BCBBC7 !important;
}
.dark .text-body-secondary {
  color: #BCBBC7 !important;
}
.dark .text-outline-primary {
  background-color: var(--splash-black-color) !important;
}
.dark .text-outline-info {
  background-color: var(--splash-black-color) !important;
}
.dark .text-outline-muted {
  background-color: var(--splash-black-color) !important;
}
.dark .text-outline-warning {
  background-color: var(--splash-black-color) !important;
}
.dark .text-outline-success {
  background-color: var(--splash-black-color) !important;
}
.dark .text-outline-danger {
  background-color: var(--splash-black-color) !important;
}
.dark .text-outline-danger-emphasis {
  background-color: var(--splash-black-color) !important;
}
.dark .bg-f2f1f9 {
  background-color: var(--splash-black-color) !important;
}
.dark .bg-faf7f7 {
  background-color: var(--splash-black-color) !important;
}
.dark .bg-ecf3f2 {
  background-color: var(--splash-black-color) !important;
}
.dark .bg-f3f7f9 {
  background-color: var(--splash-black-color) !important;
}
.dark .text-paragraph {
  color: #BCBBC7 !important;
}
.dark .bg-gray {
  background-color: var(--splash-black-color) !important;
}

.main-content {
  min-height: 100vh;
  padding-left: 275px;
  padding-right: 25px;
  padding-top: 125px;
}

.sidebar-hide .main-content {
  padding-left: 85px;
}

pre[class*=language-] {
  max-height: 430px;
}
pre[class*=language-]::-webkit-scrollbar {
  -webkit-appearance: none;
}
pre[class*=language-]::-webkit-scrollbar:vertical {
  width: 5px;
}
pre[class*=language-]::-webkit-scrollbar:horizontal {
  height: 5px;
}
pre[class*=language-]::-webkit-scrollbar-thumb {
  border-radius: 10px;
  border: 2px solid var(--whiteColor);
  background-color: rgba(0, 0, 0, 0.2);
}
pre[class*=language-]::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: var(--whiteColor);
}

.token.punctuation {
  color: #999 !important;
}
.token.tag {
  color: #905 !important;
}
.token.attr-name {
  color: #690 !important;
}
.token.attr-value {
  color: #07a !important;
}

pre[class*=language-] {
  font-size: 1em;
}

code[class*=language-] {
  color: #000 !important;
}

.ql-toolbar.ql-snow {
  border-color: #dedee4;
  font-family: var(--bs-body-font-family);
}
.ql-toolbar.ql-snow button:hover, .ql-toolbar.ql-snow button:focus, .ql-toolbar.ql-snow button.ql-active, .ql-toolbar.ql-snow .ql-picker-label:hover, .ql-toolbar.ql-snow .ql-picker-label:focus, .ql-toolbar.ql-snow .ql-picker-label.ql-active {
  color: var(--splash-primary-color);
}
.ql-toolbar.ql-snow button:hover .ql-stroke, .ql-toolbar.ql-snow button:focus .ql-stroke, .ql-toolbar.ql-snow button.ql-active .ql-stroke, .ql-toolbar.ql-snow .ql-picker-label:hover .ql-stroke, .ql-toolbar.ql-snow .ql-picker-label:focus .ql-stroke, .ql-toolbar.ql-snow .ql-picker-label.ql-active .ql-stroke {
  stroke: var(--splash-primary-color);
}

.ql-editor {
  text-align: start;
}
.ql-editor.ql-blank::before {
  color: var(--splash-secondary-color);
  font-size: 14px;
  font-style: normal;
}

.ql-container {
  font-family: var(--bs-body-font-family);
  min-height: 120px;
  height: auto;
}
.ql-container.ql-snow {
  border-color: #dedee4;
}

.dark {
  color: var(--splash-white-color) !important;
}
.dark button {
  color: var(--splash-white-color);
}
.dark .table > :not(caption) > * > * {
  background-color: initial;
}

.table > :not(caption) > * > * {
  border-bottom: 0.5px dashed #d9e9ef;
  padding: 15px;
}
.table > :not(caption) > * > * .users-list div {
  width: 33px;
  height: 33px;
  margin-right: -10px;
  border: 2px solid var(--splash-white-color);
  filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));
}
.table > :not(caption) > * > * .users-list div:last-child {
  margin-right: 0;
}
.table > :not(caption) > * > * .progress, .table > :not(caption) > * > * .progress-stacked {
  height: 5px;
}
.table > :not(caption) > * > * .dropdown-toggle {
  color: #A09FB0;
  font-size: 20px;
}
.table > :not(caption) > * > * .dropdown-toggle::after {
  display: none;
}
.table > :not(caption) > * > * .dropdown-toggle:hover {
  color: var(--splash-primary-color);
}
.table > :not(caption) > * > * .form-select {
  line-height: 1.5;
  border-color: var(--bs-border-color);
  padding: 0.375rem 2.25rem 0.375rem 0.75rem;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
}
.table > :not(caption) > * > * .form-select:focus {
  border-color: var(--splash-primary-color);
}
.table > :not(caption) > * > *.product-title {
  padding-right: 60px;
}
.table > :not(caption) > * > * .rating {
  margin-bottom: 7px;
}
.table > :not(caption) > * > * .rating i {
  line-height: 1;
  color: #F3C44C;
  margin-right: 4px;
}

.table-responsive::-webkit-scrollbar {
  -webkit-appearance: none;
}
.table-responsive::-webkit-scrollbar:vertical {
  width: 8px;
}
.table-responsive::-webkit-scrollbar:horizontal {
  height: 8px;
}
.table-responsive::-webkit-scrollbar-thumb {
  border-radius: 50px;
  background-color: rgba(0, 0, 0, 0.2);
}
.table-responsive::-webkit-scrollbar-track {
  background-color: var(--splash-white-color);
}

.table-borderless > :not(caption) > * > * {
  border-bottom-width: 0 !important;
}

.dark .table {
  --bs-table-color: var(--splash-white-color);
}
.dark .table > :not(caption) > * > * {
  border-color: #45445e;
}
.dark .table > :not(caption) > * > * .users-list div {
  border-color: #45445e;
}
.dark .table > :not(caption) > * > * .dropdown-toggle {
  color: #BCBBC7;
}
.dark .table > :not(caption) > * > * .dropdown-toggle:hover {
  color: var(--splash-primary-color);
}
.dark .table > :not(caption) > * > * .form-select {
  border-color: #45445e;
}
.dark .table > :not(caption) > * > * .form-select:focus {
  border-color: var(--splash-primary-color);
}
.dark .table > :not(caption) > * > * .form-check-input {
  border-color: #45445e;
}
.dark .table > :not(caption) > * {
  border-color: #45445e;
}
.dark .table-responsive::-webkit-scrollbar-thumb {
  background: #6D6C7D;
}
.dark .table-responsive::-webkit-scrollbar-track {
  background: #0D0C1D;
}

@media only screen and (max-width: 767px) {
  .table > :not(caption) > * > * {
    padding: 10px;
  }
  .table > :not(caption) > * > *.title {
    padding-right: 40px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .table > :not(caption) > * > *.title {
    padding-right: 40px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .table > :not(caption) > * > *.title {
    padding-right: 40px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .table > :not(caption) > * > *.title {
    padding-right: 40px;
  }
}
.badge {
  --bs-badge-padding-x: 10px;
  --bs-badge-padding-y: 7px;
  --bs-badge-font-size: 12px;
  --bs-badge-font-weight: 600;
  --bs-badge-border-radius: 2px;
  letter-spacing: 0.01em;
}
.badge.bg-outline-primary {
  border: 1px solid var(--splash-primary-color);
  color: var(--splash-primary-color);
}
.badge.bg-outline-secondary {
  border: 1px solid var(--splash-secondary-color);
  color: var(--splash-secondary-color);
}
.badge.bg-outline-success {
  border: 1px solid var(--splash-success-color);
  color: var(--splash-success-color);
}
.badge.bg-outline-warning {
  border: 1px solid var(--splash-warning-color);
  color: var(--splash-warning-color);
}
.badge.bg-outline-info {
  border: 1px solid var(--splash-info-color);
  color: var(--splash-info-color);
}
.badge.bg-outline-dark {
  border: 1px solid var(--splash-black-color);
  color: var(--splash-black-color);
}

.text-badge {
  background: #E8F4F1;
  border-radius: 3px;
  padding: 2px 8px;
}
.text-badge.text-danger {
  background: #F3EBEB;
}

.text-bg-light {
  color: var(--splash-black-color) !important;
}

.dark .text-badge {
  background: var(--splash-black-color);
}
.dark .text-badge.text-danger {
  background: var(--splash-black-color);
}

.default-btn {
  background-color: var(--splash-primary-color);
}
.default-btn:hover {
  background-color: var(--splash-primary-active-color);
}

.default-outline-btn {
  border: 1px solid rgba(101, 96, 240, 0.3);
}

.card-btn {
  color: var(--splash-secondary-color);
  border: 1px solid #d6d4ec;
  border-radius: 4px;
}
.card-btn:hover {
  background-color: var(--splash-primary-color) !important;
  border-color: var(--splash-primary-color);
  color: var(--splash-white-color);
}
.card-btn.active {
  background-color: var(--splash-white-color) !important;
  border-color: var(--splash-primary-color);
  color: var(--splash-primary-color);
}

.card-link-btn {
  line-height: 1.3;
}
.card-link-btn::before {
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  content: "";
  position: absolute;
  transition: var(--transition);
  background: var(--splash-primary-color);
}
.card-link-btn:hover::before {
  transform: scaleX(0);
}

.card-dot-btn.dropdown-toggle {
  margin-right: -7px;
  color: #A09FB0;
  font-size: 20px;
}
.card-dot-btn.dropdown-toggle::after {
  display: none;
}
.card-dot-btn.dropdown-toggle:hover {
  color: var(--splash-primary-color);
}

.btn {
  --bs-btn-padding-x: 35px;
  --bs-btn-padding-y: 11px;
  --bs-btn-font-size: 16px;
  --bs-btn-font-weight: 500;
}

.btn-primary {
  --bs-btn-bg: var(--splash-primary-color);
  --bs-btn-border-color: var(--splash-primary-color);
  --bs-btn-hover-bg: var(--splash-primary-active-color);
  --bs-btn-hover-border-color: var(--splash-primary-active-color);
  --bs-btn-active-bg: var(--splash-primary-active-color);
  --bs-btn-active-border-color: var(--splash-primary-active-color);
  --bs-btn-disabled-border-color: var(--splash-primary-color);
}

.btn-link {
  --bs-btn-color: var(--splash-primary-color);
  --bs-btn-hover-color: var(--splash-primary-active-color);
  --bs-btn-active-color: var(--splash-primary-active-color);
}

.btn-outline-primary {
  --bs-btn-color: var(--splash-primary-color);
  --bs-btn-border-color: var(--splash-primary-color);
  --bs-btn-hover-bg: var(--splash-primary-color);
  --bs-btn-hover-border-color: var(--splash-primary-color);
  --bs-btn-active-bg: var(--splash-primary-color);
  --bs-btn-active-border-color: var(--splash-primary-color);
  --bs-btn-disabled-color: var(--splash-primary-color);
  --bs-btn-disabled-border-color: var(--splash-primary-color);
}
.btn-outline-primary.bg-white:hover {
  background-color: var(--splash-primary-color) !important;
}

.btn-sm {
  --bs-btn-padding-y: 6px;
  --bs-btn-padding-x: 18px;
  --bs-btn-font-size: 14px;
}

.btn-outline-light {
  --bs-btn-color: var(--splash-black-color);
}

.btn-group-lg > .btn, .btn-lg {
  --bs-btn-font-size: 1.25rem;
}

.btn-group-sm > .btn, .btn-sm {
  --bs-btn-font-size: 0.875rem;
}

.dark .card-btn {
  color: var(--splash-white-color);
  border-color: #45445e;
}
.dark .card-btn:hover {
  background-color: var(--splash-primary-color) !important;
  border-color: var(--splash-primary-color);
  color: var(--splash-white-color);
}
.dark .card-btn.active {
  background-color: var(--splash-black-color) !important;
  border-color: var(--splash-black-color);
  color: var(--splash-primary-color);
}
.dark .card-dot-btn.dropdown-toggle {
  color: #BCBBC7;
}
.dark .btn-light {
  color: var(--splash-black-color);
}
.dark .btn-close {
  filter: invert(1);
}

.form-control {
  line-height: 1;
  font-size: 14px;
  border-radius: 5px;
  padding: 13px 20px;
  border-color: #dedee4;
}
.form-control::placeholder {
  color: var(--splash-secondary-color);
  transition: var(--transition);
}
.form-control:focus {
  border-color: var(--splash-primary-color);
}
.form-control:focus::placeholder {
  color: transparent;
}

textarea.form-control {
  padding-top: 20px;
}

.form-control-lg {
  padding-top: 15px;
  padding-bottom: 15px;
}

.form-control-sm {
  padding-top: 10px;
  padding-bottom: 10px;
}

.form-check-input {
  width: 20px;
  height: 20px;
  margin-top: 0.5px;
}
.form-check-input[type=checkbox] {
  border-radius: 1px;
}
.form-check-input[type=checkbox]:indeterminate {
  background-color: var(--splash-primary-color);
  border-color: var(--splash-primary-color);
}
.form-check-input:checked {
  background-color: var(--splash-primary-color);
  border-color: var(--splash-primary-color);
}
.form-check-input:focus {
  border-color: var(--splash-primary-color);
}
.form-check-input[type=radio] {
  border-radius: 0;
  border-color: var(--splash-primary-color);
}

.form-check-label {
  cursor: pointer;
}

.form-check {
  padding-left: 2em;
  min-height: 20px;
}
.form-check .form-check-input {
  margin-left: -2em;
}

.form-check-primary .form-check-input {
  border-color: var(--splash-primary-color);
}
.form-check-primary .form-check-input:checked {
  background-color: var(--splash-primary-color);
  border-color: var(--splash-primary-color);
}
.form-check-primary .form-check-input:focus {
  border-color: var(--splash-primary-color);
}

.form-check-info .form-check-input {
  border-color: var(--splash-info-color);
}
.form-check-info .form-check-input:checked {
  background-color: var(--splash-info-color);
  border-color: var(--splash-info-color);
}
.form-check-info .form-check-input:focus {
  border-color: var(--splash-info-color);
}

.form-check-success .form-check-input {
  border-color: var(--splash-success-color);
}
.form-check-success .form-check-input:checked {
  background-color: var(--splash-success-color);
  border-color: var(--splash-success-color);
}
.form-check-success .form-check-input:focus {
  border-color: var(--splash-success-color);
}

.form-check-danger .form-check-input {
  border-color: var(--splash-danger-color);
}
.form-check-danger .form-check-input:checked {
  background-color: var(--splash-danger-color);
  border-color: var(--splash-danger-color);
}
.form-check-danger .form-check-input:focus {
  border-color: var(--splash-danger-color);
}

.form-select {
  color: var(--splash-black-color);
  border-color: #dedee4;
  padding: 15px 20px;
  cursor: pointer;
  font-size: 14px;
  line-height: 1;
  background-size: 20px 14px;
  background-position: right 20px center;
}
.form-select:focus {
  border-color: var(--splash-primary-color);
}

.form-select-lg {
  padding-top: 18px;
  padding-bottom: 18px;
}

.form-select-sm {
  padding-top: 12px;
  padding-bottom: 12px;
}

.form-range::-webkit-slider-thumb {
  background: var(--splash-primary-color);
  box-shadow: none !important;
}
.form-range::-moz-range-thumb {
  background: var(--splash-primary-color);
  box-shadow: none !important;
}
.form-range::-ms-thumb {
  background: var(--splash-primary-color);
  box-shadow: none !important;
}

.form-floating > .form-control, .form-floating > .form-control-plaintext, .form-floating > .form-select {
  height: 52px;
  padding-left: 15px;
  padding-right: 15px;
}

.form-floating > .form-select {
  height: 55px;
}

.form-floating > label {
  padding-top: 15px;
  padding-left: 15px;
  padding-right: 15px;
  padding-bottom: 15px;
}

.form-floating:not(.form-control:disabled)::before {
  display: none;
}

.dark .form-control {
  background-color: var(--splash-black-color);
  color: var(--splash-white-color);
  border-color: #45445e;
}
.dark .form-control::placeholder {
  color: #BCBBC7;
}
.dark .form-control:focus::placeholder {
  color: transparent;
}
.dark .form-select {
  --bs-form-select-bg-img: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
  color: var(--splash-white-color);
  background-color: #34334a;
  border-color: #45445e;
}
.dark .form-select:focus {
  border-color: var(--splash-primary-color);
}
.dark .form-check-input {
  --bs-form-check-bg: var(--splash-black-color);
}
.dark .form-range[type=range]::-webkit-slider-runnable-track {
  background: var(--splash-black-color);
}
.dark .input-group-text {
  color: var(--splash-white-color);
  background-color: var(--splash-black-color);
  border-color: #45445e;
}
.dark .form-floating > .form-control::placeholder, .dark .form-floating > .form-control-plaintext::placeholder, .dark .form-floating > .form-select::placeholder {
  color: transparent;
}

@media only screen and (max-width: 767px) {
  .form-control {
    padding: 12px 15px;
  }
  textarea.form-control {
    padding-top: 15px;
  }
  .form-check-input {
    margin-top: -0.5px;
  }
  .form-select {
    padding: 12px 15px;
    background-position: right 15px center;
  }
}
.accordion {
  --bs-accordion-btn-padding-x: 20px;
  --bs-accordion-btn-padding-y: 15px;
  --bs-accordion-btn-icon-width: 12px;
  --bs-accordion-border-color: #eeeeee;
  --bs-accordion-active-bg: #EFEEF9;
  --bs-accordion-active-color: var(--splash-primary-color);
}

.accordion-item {
  margin-bottom: 20px;
}
.accordion-item:last-child {
  margin-bottom: 0;
}

.accordion-button {
  text-align: start;
}

.dark .accordion {
  --bs-accordion-border-color: #45445e;
  --bs-accordion-bg: var(--splash-black-color);
  --bs-accordion-active-bg: #34334a ;
}
.dark .accordion-button::after {
  filter: invert(1);
}

.nav-tabs {
  --bs-nav-tabs-border-color: #eeeeee;
  --bs-nav-tabs-link-active-border-color: #eeeeee #eeeeee var(--bs-body-bg);
}

.nav {
  --bs-nav-link-padding-x: 30px;
  --bs-nav-link-padding-y: 10px;
  --bs-nav-link-font-size: 16px;
  --bs-nav-link-font-weight: 500;
  --bs-nav-link-color: var(--splash-primary-color);
  --bs-nav-link-hover-color: var(--splash-primary-color);
}

.dark .nav-tabs {
  --bs-nav-tabs-border-color: #45445e;
  --bs-nav-tabs-link-active-color: var(--splash-white-color);
  --bs-nav-tabs-link-active-border-color: #45445e #45445e var(--splash-black-color);
  --bs-nav-tabs-link-active-bg: var(--splash-black-color);
  --bs-nav-tabs-link-hover-border-color: #45445e #45445e #45445e;
}
.dark .form-text {
  color: #BCBBC7;
}
.dark .navbar-brand {
  color: var(--splash-white-color);
}

.progress, .progress-stacked {
  --bs-progress-height: 12px;
  --bs-progress-border-radius: 30px;
  --bs-progress-font-size: 14px;
  --bs-progress-bg: #F2F1F9;
  --bs-progress-bar-bg: var(--splash-primary-color);
}

.progress-bar {
  border-radius: var(--bs-progress-border-radius);
}

.dark .progress, .dark .progress-stacked {
  --bs-progress-bg: #4b4a63;
}

.dropdown-menu {
  --bs-dropdown-link-color: #8E8DA2;
  --bs-dropdown-divider-bg: #eeeeee;
  --bs-dropdown-border-color: #d8e2ef;
  --bs-dropdown-font-size: 14px;
  --bs-dropdown-border-radius: 0;
  --bs-dropdown-box-shadow: 0px 4px 34px rgba(101, 96, 240, 0.15);
  --bs-dropdown-link-active-bg: var(--splash-primary-color);
  --bs-dropdown-padding-y: 11px;
  --bs-dropdown-item-padding-y: 8px;
  --bs-dropdown-item-padding-x: 20px;
  text-align: start;
  box-shadow: var(--bs-dropdown-box-shadow);
}

.dropdown-item {
  font-weight: 500;
  transition: var(--transition);
}

.dropdown-menu-dark {
  --bs-dropdown-color: #dee2e6;
  --bs-dropdown-border-color: rgba(68, 75, 82, 1);
  --bs-dropdown-link-color: #dee2e6;
  --bs-dropdown-link-hover-color: var(--splash-white-color);
  --bs-dropdown-divider-bg: rgba(68, 75, 82, 1);
  --bs-dropdown-link-active-color: var(--splash-white-color);
  --bs-dropdown-link-active-bg: var(--splash-primary-color);
  --bs-dropdown-link-disabled-color: #adb5bd;
  --bs-dropdown-header-color: #adb5bd;
}

.dark .dropdown-menu {
  color: var(--splash-white-color);
}

.breadcrumb {
  --bs-breadcrumb-divider-color: #807F9C;
  --bs-breadcrumb-item-active-color: #807F9C;
}
.breadcrumb.style-two .breadcrumb-item + .breadcrumb-item::before {
  top: 1px;
}

.breadcrumb-item + .breadcrumb-item::before {
  position: relative;
  top: -1px;
}

.dark .breadcrumb {
  --bs-breadcrumb-divider-color: #45445e;
  --bs-breadcrumb-item-active-color: #BCBBC7;
}

.modal {
  --bs-modal-width: 785px;
  --bs-modal-border-radius: 0;
}

.modal-content {
  border: none;
}

.modal-header {
  border-bottom: 1px dashed #d9e9ef;
}

.dark .modal {
  --bs-modal-bg: var(--splash-black-color);
}
.dark .modal .modal-header {
  border-bottom-color: #45445e;
}
.dark .modal .btn-close {
  filter: invert(1);
}
.dark .modal .modal-footer {
  border-color: #45445e;
}
.dark .offcanvas {
  color: var(--splash-white-color);
  background-color: var(--splash-black-color);
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .modal-dialog {
    max-width: 540px;
  }
}
.card.box-shadow {
  box-shadow: 0px 4px 34px rgba(101, 96, 240, 0.1);
}
.card .card-head.box-shadow {
  box-shadow: 0px 4px 34px rgba(101, 96, 240, 0.1);
}
.card .card-head .search-box {
  width: 280px;
}
.card .card-head .search-box .form-control {
  background: #F5F4FA;
  padding-top: 14px;
  padding-bottom: 14px;
}
.card .card-head .search-box button {
  top: 50%;
  right: 20px;
  line-height: 1;
  margin-top: 1px;
  font-size: 17px;
  position: absolute;
  transform: translateY(-50%);
}
.card .card-head .dot-btn {
  margin-right: -7px;
  color: #A09FB0;
  font-size: 20px;
}
.card .card-head .dot-btn:hover {
  color: var(--splash-primary-color);
}
.card .card-head .select-calendar {
  width: 155px;
}
.card .card-head .select-calendar .icon {
  top: 50%;
  left: 15px;
  color: #A09FB0;
  position: absolute;
  transform: translateY(-50%);
}
.card .card-head .select-calendar .form-control {
  cursor: pointer;
  padding: 10.9px 15px 10.9px 38px;
  border-color: rgba(101, 96, 240, 0.3);
}
.card .card-head .project-select.form-select {
  width: 190px;
  padding-top: 15px;
  padding-bottom: 15px;
  background-size: 20px 12px;
  background-position: right 18px center;
}
.card .card-head .reviews-select {
  background: #F5F4FA;
  padding: 14px 15px;
}
.card .card-head .reviews-select span {
  padding-right: 15px;
}
.card .card-head .reviews-select span::before {
  top: 0;
  right: 0;
  width: 1px;
  content: "";
  height: 17px;
  position: absolute;
  background: #DCDCE6;
}
.card .card-head .reviews-select .form-select {
  padding: 0 0 0 15px;
  width: 95px;
  background-size: 18px 12px;
  background-position: right 0 center;
}
.card .card-head.border-bottom {
  border-bottom: 1px dashed #d9e9ef !important;
}
.card .card-body .card-select {
  color: var(--splash-secondary-color);
  border: 1px solid #d6d4ec;
  border-radius: 4px;
}
.card .card-body .card-select .form-select {
  background-position: right 0 center;
  background-size: 16px 12px;
}

.dark .card {
  --bs-card-bg: #34334a;
  border-color: #45445e;
  color: var(--splash-white-color);
}
.dark .card.box-shadow {
  box-shadow: unset;
}
.dark .card .card-head.box-shadow {
  box-shadow: unset;
  border-bottom: 1px dashed #45445e;
}
.dark .card .card-head .search-box .form-control {
  background-color: var(--splash-black-color);
}
.dark .card .card-head .dot-btn {
  color: #BCBBC7;
}
.dark .card .card-head .dot-btn:hover {
  color: var(--splash-primary-color);
}
.dark .card .card-head .select-calendar .icon {
  color: #BCBBC7;
}
.dark .card .card-head .reviews-select {
  background: var(--splash-black-color);
}
.dark .card .card-head .reviews-select span::before {
  background: #45445e;
}
.dark .card .card-head.border-bottom {
  border-bottom-color: #45445e !important;
}
.dark .card .card-body .card-select {
  color: #BCBBC7 !important;
  border-color: #45445e;
}

@media only screen and (max-width: 767px) {
  .card .card-head .search-box {
    width: 100%;
  }
  .card .card-head .search-box .form-control {
    padding-top: 13px;
    padding-bottom: 13px;
  }
  .card .card-head .search-box button {
    right: 15px;
    font-size: 16px;
  }
  .card .card-head .select-calendar {
    width: auto;
  }
  .card .card-head .select-calendar .icon {
    margin-top: 0.5px;
  }
  .card .card-head .select-calendar .form-control {
    padding: 12px 15px 12px 38px;
  }
  .card .card-head .project-select.form-select {
    width: 180px;
    padding-top: 14px;
    padding-bottom: 14px;
  }
  .card .card-head .reviews-select {
    padding: 13px 15px;
  }
  .card .card-head .reviews-select .form-select {
    width: 100%;
  }
  .card .card-body .card-select .form-select {
    background-position: right 0 center;
    background-size: 16px 12px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .card .card-head .project-select.form-select {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .card .card-head .reviews-select {
    padding: 13.5px 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .card .card-head .search-box {
    width: 275px;
  }
  .card .card-head .select-calendar {
    width: 165px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .card .card-head .search-box {
    width: 250px;
  }
  .card .card-head .select-calendar {
    width: 170px;
  }
}
@media only screen and (min-width: 1600px) {
  .card .card-head .search-box {
    width: 340px;
  }
  .card .card-head .reviews-select .form-select {
    width: 130px;
  }
}
.pagination {
  --bs-pagination-active-bg: var(--splash-primary-color);
  --bs-pagination-active-border-color: var(--splash-primary-color);
  --bs-pagination-color: var(--splash-muted-color);
  --bs-pagination-font-size: 14px;
  --bs-pagination-border-color: #F2F1F9;
  --bs-pagination-border-radius: 4px;
  --bs-pagination-hover-color: var(--splash-primary-color);
  --bs-pagination-focus-color: var(--splash-primary-color);
}

.pagination-area .pagination .page-item {
  margin-left: 3px;
  margin-right: 3px;
}
.pagination-area .pagination .page-item .page-link {
  padding: 0;
  width: 35px;
  height: 35px;
  margin-left: 0;
  font-weight: 600;
  line-height: 34px;
  text-align: center;
  box-shadow: unset !important;
  transition: var(--transition);
  border-radius: var(--bs-pagination-border-radius);
}
.pagination-area .pagination .page-item .page-link i {
  top: 3px;
  line-height: 1;
  font-size: 16px;
  position: relative;
  transition: var(--transition);
  color: var(--splash-primary-color);
}
.pagination-area .pagination .page-item .page-link:hover {
  color: var(--splash-white-color);
  background-color: var(--splash-primary-color);
  border-color: var(--bs-pagination-active-border-color);
}
.pagination-area .pagination .page-item .page-link:hover i {
  color: var(--splash-white-color);
}

.dark .pagination {
  --bs-pagination-border-color: #45445e;
  --bs-pagination-bg: var(--splash-black-color);
}

.list-group {
  --bs-list-group-item-padding-x: 15px;
  --bs-list-group-item-padding-y: 12px;
  --bs-list-group-active-bg: var(--splash-primary-color);
  --bs-list-group-active-border-color: var(--splash-primary-color);
}

.simple-list-example-scrollspy .active {
  background-color: #f5f5f5;
}

.dark .list-group {
  --bs-list-group-bg: var(--splash-black-color);
  --bs-list-group-color: var(--splash-white-color);
  --bs-list-group-border-color: #45445e;
  --bs-list-group-disabled-color: #BCBBC7;
  --bs-list-group-disabled-bg: #403f58;
}
.dark .simple-list-example-scrollspy .active {
  background-color: #f5f5f5;
}

.nav-pills {
  --bs-nav-pills-link-active-bg: var(--splash-primary-color);
}

.custom-tooltip {
  --bs-tooltip-bg: var(--bs-primary);
}

.timeline::before {
  top: 0;
  left: 50%;
  width: 2px;
  content: "";
  height: 100%;
  margin: 0 0 0 -1px;
  position: absolute;
  background: rgba(0, 0, 0, 0.03);
}
.timeline .event {
  margin-bottom: 25px;
}
.timeline .event:after {
  display: block;
  content: "";
  clear: both;
}
.timeline .event .inner {
  width: 45%;
  float: left;
  text-align: end;
  border-radius: 5px;
}
.timeline .event .inner .date {
  margin: 0 0 0 -25px;
  border-radius: 50%;
  position: absolute;
  line-height: 50px;
  height: 50px;
  width: 50px;
  left: 50%;
  top: 0;
}
.timeline .event .inner .d-flex {
  justify-content: end;
}
.timeline .event:nth-child(2n+2) .inner {
  float: right;
  text-align: start;
}
.timeline .event:nth-child(2n+2) .inner .d-flex {
  justify-content: unset;
}

@media only screen and (max-width: 767px) {
  .timeline::before {
    left: 19px;
  }
  .timeline .event .inner {
    width: 100%;
    float: unset;
    text-align: start;
    padding-left: 55px;
  }
  .timeline .event .inner .date {
    line-height: 38px;
    height: 40px;
    width: 40px;
    margin: 0;
    left: 0;
  }
  .timeline .event .inner .d-flex {
    justify-content: unset;
  }
  .timeline .event:nth-child(2n+2) .inner {
    float: unset;
    text-align: start;
  }
}
.dark .toast {
  --bs-toast-color: var(--splash-white-color);
  --bs-toast-bg: var(--splash-black-color);
  --bs-toast-header-bg: rgba(var(--splash-black-color), 0.85);
}

.tree-container .treejs .treejs-nodes {
  margin-bottom: 0;
  padding-left: 0;
}
.tree-container .treejs .treejs-nodes .treejs-label {
  margin-left: 3px;
}
.tree-container .treejs .treejs-nodes .treejs-node .treejs-nodes {
  padding-left: 20px;
}
.tree-container .treejs .treejs-nodes .treejs-node .treejs-nodes .treejs-node {
  margin-top: 10px;
}
.tree-container .treejs .treejs-node__halfchecked > .treejs-checkbox:before {
  background-color: var(--splash-primary-color);
  border-color: var(--splash-primary-color);
}
.tree-container .treejs .treejs-node__checked > .treejs-checkbox:before {
  background-color: var(--splash-primary-color);
  border-color: var(--splash-primary-color);
}
.tree-container .treejs .treejs-checkbox:hover:before {
  box-shadow: unset;
}

.dark .tree-container .treejs .treejs-node__disabled {
  color: rgba(255, 255, 255, 0.25);
}
.dark .figure-caption {
  color: var(--splash-white-color);
}

.loader,
.loader:before,
.loader:after {
  background: var(--splash-body-bg);
  -webkit-animation: load1 1s infinite ease-in-out;
  animation: load1 1s infinite ease-in-out;
  width: 1em;
  height: 4em;
}

.loader {
  color: var(--splash-primary-color);
  text-indent: -9999em;
  margin: 88px auto;
  position: relative;
  font-size: 11px;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

.loader:before,
.loader:after {
  position: absolute;
  top: 0;
  content: "";
}

.loader:before {
  left: -1.5em;
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.loader:after {
  left: 1.5em;
}

@-webkit-keyframes load1 {
  0%, 80%, 100% {
    box-shadow: 0 0;
    height: 4em;
  }
  40% {
    box-shadow: 0 -2em;
    height: 5em;
  }
}
@keyframes load1 {
  0%, 80%, 100% {
    box-shadow: 0 0;
    height: 4em;
  }
  40% {
    box-shadow: 0 -2em;
    height: 5em;
  }
}
.h-100vh {
  height: 100vh !important;
}

.h-130 {
  height: 130px !important;
}

.h-110 {
  height: 110px !important;
}

.h-90 {
  height: 90px !important;
}

.h-70 {
  height: 70px !important;
}

.h-50 {
  height: 50px !important;
}

.h-10 {
  height: 10px !important;
}

.h-6 {
  height: 6px !important;
}

.p-0 {
  padding: 0 !important;
}

.ps-0 {
  padding-left: 0 !important;
}

.pe-0 {
  padding-right: 0 !important;
}

.pt-0 {
  padding-top: 0 !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.p-1 {
  padding: 0.25rem !important;
}

.ps-1 {
  padding-left: 0.25rem !important;
}

.pe-1 {
  padding-right: 0.25rem !important;
}

.pt-1 {
  padding-top: 0.25rem !important;
}

.pb-1 {
  padding-bottom: 0.25rem !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.ps-2 {
  padding-left: 0.5rem !important;
}

.pe-2 {
  padding-right: 0.5rem !important;
}

.pt-2 {
  padding-top: 0.5rem !important;
}

.pb-2 {
  padding-bottom: 0.5rem !important;
}

.p-3 {
  padding: 1rem !important;
}

.ps-3 {
  padding-left: 1rem !important;
}

.pe-3 {
  padding-right: 1rem !important;
}

.pt-3 {
  padding-top: 1rem !important;
}

.pb-3 {
  padding-bottom: 1rem !important;
}

.p-4 {
  padding: 1.5rem !important;
}

.ps-4 {
  padding-left: 1.5rem !important;
}

.pe-4 {
  padding-right: 1.5rem !important;
}

.pt-4 {
  padding-top: 1.5rem !important;
}

.pb-4 {
  padding-bottom: 1.5rem !important;
}

.p-5 {
  padding: 5px !important;
}

.ps-5 {
  padding-left: 5px !important;
}

.pe-5 {
  padding-right: 5px !important;
}

.pt-5 {
  padding-top: 5px !important;
}

.pb-5 {
  padding-bottom: 5px !important;
}

.p-6 {
  padding: 6px !important;
}

.ps-6 {
  padding-left: 6px !important;
}

.pe-6 {
  padding-right: 6px !important;
}

.pt-6 {
  padding-top: 6px !important;
}

.pb-6 {
  padding-bottom: 6px !important;
}

.p-7 {
  padding: 7px !important;
}

.ps-7 {
  padding-left: 7px !important;
}

.pe-7 {
  padding-right: 7px !important;
}

.pt-7 {
  padding-top: 7px !important;
}

.pb-7 {
  padding-bottom: 7px !important;
}

.p-8 {
  padding: 8px !important;
}

.ps-8 {
  padding-left: 8px !important;
}

.pe-8 {
  padding-right: 8px !important;
}

.pt-8 {
  padding-top: 8px !important;
}

.pb-8 {
  padding-bottom: 8px !important;
}

.p-10 {
  padding: 10px !important;
}

.ps-10 {
  padding-left: 10px !important;
}

.pe-10 {
  padding-right: 10px !important;
}

.pt-10 {
  padding-top: 10px !important;
}

.pb-10 {
  padding-bottom: 10px !important;
}

.p-11 {
  padding: 11px !important;
}

.ps-11 {
  padding-left: 11px !important;
}

.pe-11 {
  padding-right: 11px !important;
}

.pt-11 {
  padding-top: 11px !important;
}

.pb-11 {
  padding-bottom: 11px !important;
}

.p-12 {
  padding: 12px !important;
}

.ps-12 {
  padding-left: 12px !important;
}

.pe-12 {
  padding-right: 12px !important;
}

.pt-12 {
  padding-top: 12px !important;
}

.pb-12 {
  padding-bottom: 12px !important;
}

.p-15 {
  padding: 15px !important;
}

.ps-15 {
  padding-left: 15px !important;
}

.pe-15 {
  padding-right: 15px !important;
}

.pt-15 {
  padding-top: 15px !important;
}

.pb-15 {
  padding-bottom: 15px !important;
}

.pt-18 {
  padding-top: 18px !important;
}

.pb-18 {
  padding-bottom: 18px !important;
}

.p-20 {
  padding: 20px !important;
}

.ps-20 {
  padding-left: 20px !important;
}

.pe-20 {
  padding-right: 20px !important;
}

.pt-20 {
  padding-top: 20px !important;
}

.pb-20 {
  padding-bottom: 20px !important;
}

.p-25 {
  padding: 25px !important;
}

.ps-25 {
  padding-left: 25px !important;
}

.pe-25 {
  padding-right: 25px !important;
}

.pt-25 {
  padding-top: 25px !important;
}

.pb-25 {
  padding-bottom: 25px !important;
}

.p-30 {
  padding: 30px !important;
}

.ps-30 {
  padding-left: 30px !important;
}

.pe-30 {
  padding-right: 30px !important;
}

.pt-30 {
  padding-top: 30px !important;
}

.pb-30 {
  padding-bottom: 30px !important;
}

.p-35 {
  padding: 35px !important;
}

.ps-35 {
  padding-left: 35px !important;
}

.pe-35 {
  padding-right: 35px !important;
}

.pt-35 {
  padding-top: 35px !important;
}

.pb-35 {
  padding-bottom: 35px !important;
}

.p-40 {
  padding: 40px !important;
}

.ps-40 {
  padding-left: 40px !important;
}

.pe-40 {
  padding-right: 40px !important;
}

.pt-40 {
  padding-top: 40px !important;
}

.pb-40 {
  padding-bottom: 40px !important;
}

.p-45 {
  padding: 45px !important;
}

.ps-45 {
  padding-left: 45px !important;
}

.pe-45 {
  padding-right: 45px !important;
}

.pt-45 {
  padding-top: 45px !important;
}

.pb-45 {
  padding-bottom: 45px !important;
}

.p-50 {
  padding: 50px !important;
}

.ps-50 {
  padding-left: 50px !important;
}

.pe-50 {
  padding-right: 50px !important;
}

.pt-50 {
  padding-top: 50px !important;
}

.pb-50 {
  padding-bottom: 50px !important;
}

.m-0 {
  margin: 0 !important;
}

.ms-0 {
  margin-left: 0 !important;
}

.me-0 {
  margin-right: 0 !important;
}

.mt-0 {
  margin-top: 0 !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.m-1 {
  margin: 0.25rem !important;
}

.ms-1 {
  margin-left: 0.25rem !important;
}

.me-1 {
  margin-right: 0.25rem !important;
}

.mt-1 {
  margin-top: 0.25rem !important;
}

.mb-1 {
  margin-bottom: 0.25rem !important;
}

.m-2 {
  margin: 0.5rem !important;
}

.ms-2 {
  margin-left: 0.5rem !important;
}

.me-2 {
  margin-right: 0.5rem !important;
}

.mt-2 {
  margin-top: 0.5rem !important;
}

.mb-2 {
  margin-bottom: 0.5rem !important;
}

.m-3 {
  margin: 1rem !important;
}

.ms-3 {
  margin-left: 1rem !important;
}

.me-3 {
  margin-right: 1rem !important;
}

.mt-3 {
  margin-top: 1rem !important;
}

.mb-3 {
  margin-bottom: 1rem !important;
}

.m-4 {
  margin: 1.5rem !important;
}

.ms-4 {
  margin-left: 1.5rem !important;
}

.me-4 {
  margin-right: 1.5rem !important;
}

.mt-4 {
  margin-top: 1.5rem !important;
}

.mb-4 {
  margin-bottom: 1.5rem !important;
}

.m-5 {
  margin: 5px !important;
}

.ms-5 {
  margin-left: 5px !important;
}

.me-5 {
  margin-right: 5px !important;
}

.mt-5 {
  margin-top: 5px !important;
}

.mb-5 {
  margin-bottom: 5px !important;
}

.m-6 {
  margin: 6px !important;
}

.ms-6 {
  margin-left: 6px !important;
}

.me-6 {
  margin-right: 6px !important;
}

.mt-6 {
  margin-top: 6px !important;
}

.mb-6 {
  margin-bottom: 6px !important;
}

.m-7 {
  margin: 7px !important;
}

.ms-7 {
  margin-left: 7px !important;
}

.me-7 {
  margin-right: 7px !important;
}

.mt-7 {
  margin-top: 7px !important;
}

.mb-7 {
  margin-bottom: 7px !important;
}

.m-8 {
  margin: 8px !important;
}

.ms-8 {
  margin-left: 8px !important;
}

.me-8 {
  margin-right: 8px !important;
}

.mt-8 {
  margin-top: 8px !important;
}

.mb-8 {
  margin-bottom: 8px !important;
}

.m-10 {
  margin: 10px !important;
}

.ms-10 {
  margin-left: 10px !important;
}

.me-10 {
  margin-right: 10px !important;
}

.mt-10 {
  margin-top: 10px !important;
}

.mb-10 {
  margin-bottom: 10px !important;
}

.m-11 {
  margin: 11px !important;
}

.ms-11 {
  margin-left: 11px !important;
}

.me-11 {
  margin-right: 11px !important;
}

.mt-11 {
  margin-top: 11px !important;
}

.mb-11 {
  margin-bottom: 11px !important;
}

.m-12 {
  margin: 12px !important;
}

.ms-12 {
  margin-left: 12px !important;
}

.me-12 {
  margin-right: 12px !important;
}

.mt-12 {
  margin-top: 12px !important;
}

.mb-12 {
  margin-bottom: 12px !important;
}

.m-15 {
  margin: 15px !important;
}

.ms-15 {
  margin-left: 15px !important;
}

.me-15 {
  margin-right: 15px !important;
}

.mt-15 {
  margin-top: 15px !important;
}

.mb-15 {
  margin-bottom: 15px !important;
}

.m-20 {
  margin: 20px !important;
}

.ms-20 {
  margin-left: 20px !important;
}

.me-20 {
  margin-right: 20px !important;
}

.mt-20 {
  margin-top: 20px !important;
}

.mb-20 {
  margin-bottom: 20px !important;
}

.m-25 {
  margin: 25px !important;
}

.ms-25 {
  margin-left: 25px !important;
}

.me-25 {
  margin-right: 25px !important;
}

.mt-25 {
  margin-top: 25px !important;
}

.mb-25 {
  margin-bottom: 25px !important;
}

.m-30 {
  margin: 30px !important;
}

.ms-30 {
  margin-left: 30px !important;
}

.me-30 {
  margin-right: 30px !important;
}

.mt-30 {
  margin-top: 30px !important;
}

.mb-30 {
  margin-bottom: 30px !important;
}

.m-35 {
  margin: 35px !important;
}

.ms-35 {
  margin-left: 35px !important;
}

.me-35 {
  margin-right: 35px !important;
}

.mt-35 {
  margin-top: 35px !important;
}

.mb-35 {
  margin-bottom: 35px !important;
}

.m-40 {
  margin: 40px !important;
}

.ms-40 {
  margin-left: 40px !important;
}

.me-40 {
  margin-right: 40px !important;
}

.mt-40 {
  margin-top: 40px !important;
}

.mb-40 {
  margin-bottom: 40px !important;
}

.m-45 {
  margin: 45px !important;
}

.ms-45 {
  margin-left: 45px !important;
}

.me-45 {
  margin-right: 45px !important;
}

.mt-45 {
  margin-top: 45px !important;
}

.mb-45 {
  margin-bottom: 45px !important;
}

.m-50 {
  margin: 50px !important;
}

.ms-50 {
  margin-left: 50px !important;
}

.me-50 {
  margin-right: 50px !important;
}

.mt-50 {
  margin-top: 50px !important;
}

.mb-50 {
  margin-bottom: 50px !important;
}

.ms-100 {
  margin-left: 100px !important;
}

.w-130 {
  width: 130px !important;
}

.w-110 {
  width: 110px !important;
}

.w-90 {
  width: 90px !important;
}

.w-70 {
  width: 70px !important;
}

.w-50 {
  width: 50px !important;
}

.w-10 {
  width: 10px !important;
}

.w-6 {
  width: 6px !important;
}

.fw-black {
  font-weight: 900;
}

.fs-30 {
  font-size: 30px !important;
}

.fs-25 {
  font-size: 25px !important;
}

.fs-20 {
  font-size: 20px !important;
}

.fs-18 {
  font-size: 18px !important;
}

.fs-16 {
  font-size: 16px !important;
}

.fs-15 {
  font-size: 15px !important;
}

.fs-14 {
  font-size: 14px !important;
}

.fs-13 {
  font-size: 13px !important;
}

.fs-12 {
  font-size: 12px !important;
}

.fs-11 {
  font-size: 11px !important;
}

.fs-10 {
  font-size: 10px !important;
}

.top-1 {
  top: 1px !important;
}

.top-2 {
  top: 2px !important;
}

.top-3 {
  top: 3px !important;
}

.top-4 {
  top: 4px !important;
}

.top-5 {
  top: 5px !important;
}

@media (min-width: 576px) {
  .p-sm-0 {
    padding: 0 !important;
  }
  .ps-sm-0 {
    padding-left: 0 !important;
  }
  .pe-sm-0 {
    padding-right: 0 !important;
  }
  .pt-sm-0 {
    padding-top: 0 !important;
  }
  .pb-sm-0 {
    padding-bottom: 0 !important;
  }
  .p-sm-1 {
    padding: 0.25rem !important;
  }
  .ps-sm-1 {
    padding-left: 0.25rem !important;
  }
  .pe-sm-1 {
    padding-right: 0.25rem !important;
  }
  .pt-sm-1 {
    padding-top: 0.25rem !important;
  }
  .pb-sm-1 {
    padding-bottom: 0.25rem !important;
  }
  .p-sm-2 {
    padding: 0.5rem !important;
  }
  .ps-sm-2 {
    padding-left: 0.5rem !important;
  }
  .pe-sm-2 {
    padding-right: 0.5rem !important;
  }
  .pt-sm-2 {
    padding-top: 0.5rem !important;
  }
  .pb-sm-2 {
    padding-bottom: 0.5rem !important;
  }
  .p-sm-3 {
    padding: 1rem !important;
  }
  .ps-sm-3 {
    padding-left: 1rem !important;
  }
  .pe-sm-3 {
    padding-right: 1rem !important;
  }
  .pt-sm-3 {
    padding-top: 1rem !important;
  }
  .pb-sm-3 {
    padding-bottom: 1rem !important;
  }
  .p-sm-4 {
    padding: 1.5rem !important;
  }
  .ps-sm-4 {
    padding-left: 1.5rem !important;
  }
  .pe-sm-4 {
    padding-right: 1.5rem !important;
  }
  .pt-sm-4 {
    padding-top: 1.5rem !important;
  }
  .pb-sm-4 {
    padding-bottom: 1.5rem !important;
  }
  .p-sm-5 {
    padding: 5px !important;
  }
  .ps-sm-5 {
    padding-left: 5px !important;
  }
  .pe-sm-5 {
    padding-right: 5px !important;
  }
  .pt-sm-5 {
    padding-top: 5px !important;
  }
  .pb-sm-5 {
    padding-bottom: 5px !important;
  }
  .p-sm-8 {
    padding: 8px !important;
  }
  .ps-sm-8 {
    padding-left: 8px !important;
  }
  .pe-sm-8 {
    padding-right: 8px !important;
  }
  .pt-sm-8 {
    padding-top: 8px !important;
  }
  .pb-sm-8 {
    padding-bottom: 8px !important;
  }
  .p-sm-10 {
    padding: 10px !important;
  }
  .ps-sm-10 {
    padding-left: 10px !important;
  }
  .pe-sm-10 {
    padding-right: 10px !important;
  }
  .pt-sm-10 {
    padding-top: 10px !important;
  }
  .pb-sm-10 {
    padding-bottom: 10px !important;
  }
  .p-sm-11 {
    padding: 11px !important;
  }
  .ps-sm-11 {
    padding-left: 11px !important;
  }
  .pe-sm-11 {
    padding-right: 11px !important;
  }
  .pt-sm-11 {
    padding-top: 11px !important;
  }
  .pb-sm-11 {
    padding-bottom: 11px !important;
  }
  .p-sm-12 {
    padding: 12px !important;
  }
  .ps-sm-12 {
    padding-left: 12px !important;
  }
  .pe-sm-12 {
    padding-right: 12px !important;
  }
  .pt-sm-12 {
    padding-top: 12px !important;
  }
  .pb-sm-12 {
    padding-bottom: 12px !important;
  }
  .p-sm-15 {
    padding: 15px !important;
  }
  .ps-sm-15 {
    padding-left: 15px !important;
  }
  .pe-sm-15 {
    padding-right: 15px !important;
  }
  .pt-sm-15 {
    padding-top: 15px !important;
  }
  .pb-sm-15 {
    padding-bottom: 15px !important;
  }
  .pt-sm-18 {
    padding-top: 18px !important;
  }
  .pb-sm-18 {
    padding-bottom: 18px !important;
  }
  .p-sm-20 {
    padding: 20px !important;
  }
  .ps-sm-20 {
    padding-left: 20px !important;
  }
  .pe-sm-20 {
    padding-right: 20px !important;
  }
  .pt-sm-20 {
    padding-top: 20px !important;
  }
  .pb-sm-20 {
    padding-bottom: 20px !important;
  }
  .p-sm-25 {
    padding: 25px !important;
  }
  .ps-sm-25 {
    padding-left: 25px !important;
  }
  .pe-sm-25 {
    padding-right: 25px !important;
  }
  .pt-sm-25 {
    padding-top: 25px !important;
  }
  .pb-sm-25 {
    padding-bottom: 25px !important;
  }
  .p-sm-30 {
    padding: 30px !important;
  }
  .ps-sm-30 {
    padding-left: 30px !important;
  }
  .pe-sm-30 {
    padding-right: 30px !important;
  }
  .pt-sm-30 {
    padding-top: 30px !important;
  }
  .pb-sm-30 {
    padding-bottom: 30px !important;
  }
  .p-sm-35 {
    padding: 35px !important;
  }
  .ps-sm-35 {
    padding-left: 35px !important;
  }
  .pe-sm-35 {
    padding-right: 35px !important;
  }
  .pt-sm-35 {
    padding-top: 35px !important;
  }
  .pb-sm-35 {
    padding-bottom: 35px !important;
  }
  .p-sm-40 {
    padding: 40px !important;
  }
  .ps-sm-40 {
    padding-left: 40px !important;
  }
  .pe-sm-40 {
    padding-right: 40px !important;
  }
  .pt-sm-40 {
    padding-top: 40px !important;
  }
  .pb-sm-40 {
    padding-bottom: 40px !important;
  }
  .p-sm-45 {
    padding: 45px !important;
  }
  .ps-sm-45 {
    padding-left: 45px !important;
  }
  .pe-sm-45 {
    padding-right: 45px !important;
  }
  .pt-sm-45 {
    padding-top: 45px !important;
  }
  .pb-sm-45 {
    padding-bottom: 45px !important;
  }
  .p-sm-50 {
    padding: 50px !important;
  }
  .ps-sm-50 {
    padding-left: 50px !important;
  }
  .pe-sm-50 {
    padding-right: 50px !important;
  }
  .pt-sm-50 {
    padding-top: 50px !important;
  }
  .pb-sm-50 {
    padding-bottom: 50px !important;
  }
  .m-sm-0 {
    margin: 0 !important;
  }
  .ms-sm-0 {
    margin-left: 0 !important;
  }
  .me-sm-0 {
    margin-right: 0 !important;
  }
  .mt-sm-0 {
    margin-top: 0 !important;
  }
  .mb-sm-0 {
    margin-bottom: 0 !important;
  }
  .m-sm-1 {
    margin: 0.25rem !important;
  }
  .ms-sm-1 {
    margin-left: 0.25rem !important;
  }
  .me-sm-1 {
    margin-right: 0.25rem !important;
  }
  .mt-sm-1 {
    margin-top: 0.25rem !important;
  }
  .mb-sm-1 {
    margin-bottom: 0.25rem !important;
  }
  .m-sm-2 {
    margin: 0.5rem !important;
  }
  .ms-sm-2 {
    margin-left: 0.5rem !important;
  }
  .me-sm-2 {
    margin-right: 0.5rem !important;
  }
  .mt-sm-2 {
    margin-top: 0.5rem !important;
  }
  .mb-sm-2 {
    margin-bottom: 0.5rem !important;
  }
  .m-sm-3 {
    margin: 1rem !important;
  }
  .ms-sm-3 {
    margin-left: 1rem !important;
  }
  .me-sm-3 {
    margin-right: 1rem !important;
  }
  .mt-sm-3 {
    margin-top: 1rem !important;
  }
  .mb-sm-3 {
    margin-bottom: 1rem !important;
  }
  .m-sm-4 {
    margin: 1.5rem !important;
  }
  .ms-sm-4 {
    margin-left: 1.5rem !important;
  }
  .me-sm-4 {
    margin-right: 1.5rem !important;
  }
  .mt-sm-4 {
    margin-top: 1.5rem !important;
  }
  .mb-sm-4 {
    margin-bottom: 1.5rem !important;
  }
  .m-sm-5 {
    margin: 5px !important;
  }
  .ms-sm-5 {
    margin-left: 5px !important;
  }
  .me-sm-5 {
    margin-right: 5px !important;
  }
  .mt-sm-5 {
    margin-top: 5px !important;
  }
  .mb-sm-5 {
    margin-bottom: 5px !important;
  }
  .m-sm-8 {
    margin: 8px !important;
  }
  .ms-sm-8 {
    margin-left: 8px !important;
  }
  .me-sm-8 {
    margin-right: 8px !important;
  }
  .mt-sm-8 {
    margin-top: 8px !important;
  }
  .mb-sm-8 {
    margin-bottom: 8px !important;
  }
  .m-sm-10 {
    margin: 10px !important;
  }
  .ms-sm-10 {
    margin-left: 10px !important;
  }
  .me-sm-10 {
    margin-right: 10px !important;
  }
  .mt-sm-10 {
    margin-top: 10px !important;
  }
  .mb-sm-10 {
    margin-bottom: 10px !important;
  }
  .m-sm-11 {
    margin: 11px !important;
  }
  .ms-sm-11 {
    margin-left: 11px !important;
  }
  .me-sm-11 {
    margin-right: 11px !important;
  }
  .mt-sm-11 {
    margin-top: 11px !important;
  }
  .mb-sm-11 {
    margin-bottom: 11px !important;
  }
  .m-sm-12 {
    margin: 12px !important;
  }
  .ms-sm-12 {
    margin-left: 12px !important;
  }
  .me-sm-12 {
    margin-right: 12px !important;
  }
  .mt-sm-12 {
    margin-top: 12px !important;
  }
  .mb-sm-12 {
    margin-bottom: 12px !important;
  }
  .m-sm-15 {
    margin: 15px !important;
  }
  .ms-sm-15 {
    margin-left: 15px !important;
  }
  .me-sm-15 {
    margin-right: 15px !important;
  }
  .mt-sm-15 {
    margin-top: 15px !important;
  }
  .mb-sm-15 {
    margin-bottom: 15px !important;
  }
  .m-sm-20 {
    margin: 20px !important;
  }
  .ms-sm-20 {
    margin-left: 20px !important;
  }
  .me-sm-20 {
    margin-right: 20px !important;
  }
  .mt-sm-20 {
    margin-top: 20px !important;
  }
  .mb-sm-20 {
    margin-bottom: 20px !important;
  }
  .m-sm-25 {
    margin: 25px !important;
  }
  .ms-sm-25 {
    margin-left: 25px !important;
  }
  .me-sm-25 {
    margin-right: 25px !important;
  }
  .mt-sm-25 {
    margin-top: 25px !important;
  }
  .mb-sm-25 {
    margin-bottom: 25px !important;
  }
  .m-sm-30 {
    margin: 30px !important;
  }
  .ms-sm-30 {
    margin-left: 30px !important;
  }
  .me-sm-30 {
    margin-right: 30px !important;
  }
  .mt-sm-30 {
    margin-top: 30px !important;
  }
  .mb-sm-30 {
    margin-bottom: 30px !important;
  }
  .m-sm-35 {
    margin: 35px !important;
  }
  .ms-sm-35 {
    margin-left: 35px !important;
  }
  .me-sm-35 {
    margin-right: 35px !important;
  }
  .mt-sm-35 {
    margin-top: 35px !important;
  }
  .mb-sm-35 {
    margin-bottom: 35px !important;
  }
  .m-sm-40 {
    margin: 40px !important;
  }
  .ms-sm-40 {
    margin-left: 40px !important;
  }
  .me-sm-40 {
    margin-right: 40px !important;
  }
  .mt-sm-40 {
    margin-top: 40px !important;
  }
  .mb-sm-40 {
    margin-bottom: 40px !important;
  }
  .m-sm-45 {
    margin: 45px !important;
  }
  .ms-sm-45 {
    margin-left: 45px !important;
  }
  .me-sm-45 {
    margin-right: 45px !important;
  }
  .mt-sm-45 {
    margin-top: 45px !important;
  }
  .mb-sm-45 {
    margin-bottom: 45px !important;
  }
  .m-sm-50 {
    margin: 50px !important;
  }
  .ms-sm-50 {
    margin-left: 50px !important;
  }
  .me-sm-50 {
    margin-right: 50px !important;
  }
  .mt-sm-50 {
    margin-top: 50px !important;
  }
  .mb-sm-50 {
    margin-bottom: 50px !important;
  }
  .ms-sm-100 {
    margin-left: 100px !important;
  }
  .fs-sm-30 {
    font-size: 30px !important;
  }
  .fs-sm-25 {
    font-size: 25px !important;
  }
  .fs-sm-20 {
    font-size: 20px !important;
  }
  .fs-sm-18 {
    font-size: 18px !important;
  }
  .fs-sm-16 {
    font-size: 16px !important;
  }
  .fs-sm-15 {
    font-size: 15px !important;
  }
  .fs-sm-14 {
    font-size: 14px !important;
  }
  .fs-sm-13 {
    font-size: 13px !important;
  }
}
@media (min-width: 768px) {
  .p-md-0 {
    padding: 0 !important;
  }
  .ps-md-0 {
    padding-left: 0 !important;
  }
  .pe-md-0 {
    padding-right: 0 !important;
  }
  .pt-md-0 {
    padding-top: 0 !important;
  }
  .pb-md-0 {
    padding-bottom: 0 !important;
  }
  .p-md-1 {
    padding: 0.25rem !important;
  }
  .ps-md-1 {
    padding-left: 0.25rem !important;
  }
  .pe-md-1 {
    padding-right: 0.25rem !important;
  }
  .pt-md-1 {
    padding-top: 0.25rem !important;
  }
  .pb-md-1 {
    padding-bottom: 0.25rem !important;
  }
  .p-md-2 {
    padding: 0.5rem !important;
  }
  .ps-md-2 {
    padding-left: 0.5rem !important;
  }
  .pe-md-2 {
    padding-right: 0.5rem !important;
  }
  .pt-md-2 {
    padding-top: 0.5rem !important;
  }
  .pb-md-2 {
    padding-bottom: 0.5rem !important;
  }
  .p-md-3 {
    padding: 1rem !important;
  }
  .ps-md-3 {
    padding-left: 1rem !important;
  }
  .pe-md-3 {
    padding-right: 1rem !important;
  }
  .pt-md-3 {
    padding-top: 1rem !important;
  }
  .pb-md-3 {
    padding-bottom: 1rem !important;
  }
  .p-md-4 {
    padding: 1.5rem !important;
  }
  .ps-md-4 {
    padding-left: 1.5rem !important;
  }
  .pe-md-4 {
    padding-right: 1.5rem !important;
  }
  .pt-md-4 {
    padding-top: 1.5rem !important;
  }
  .pb-md-4 {
    padding-bottom: 1.5rem !important;
  }
  .p-md-5 {
    padding: 5px !important;
  }
  .ps-md-5 {
    padding-left: 5px !important;
  }
  .pe-md-5 {
    padding-right: 5px !important;
  }
  .pt-md-5 {
    padding-top: 5px !important;
  }
  .pb-md-5 {
    padding-bottom: 5px !important;
  }
  .p-md-8 {
    padding: 8px !important;
  }
  .ps-md-8 {
    padding-left: 8px !important;
  }
  .pe-md-8 {
    padding-right: 8px !important;
  }
  .pt-md-8 {
    padding-top: 8px !important;
  }
  .pb-md-8 {
    padding-bottom: 8px !important;
  }
  .p-md-10 {
    padding: 10px !important;
  }
  .ps-md-10 {
    padding-left: 10px !important;
  }
  .pe-md-10 {
    padding-right: 10px !important;
  }
  .pt-md-10 {
    padding-top: 10px !important;
  }
  .pb-md-10 {
    padding-bottom: 10px !important;
  }
  .p-md-11 {
    padding: 11px !important;
  }
  .ps-md-11 {
    padding-left: 11px !important;
  }
  .pe-md-11 {
    padding-right: 11px !important;
  }
  .pt-md-11 {
    padding-top: 11px !important;
  }
  .pb-md-11 {
    padding-bottom: 11px !important;
  }
  .p-md-12 {
    padding: 12px !important;
  }
  .ps-md-12 {
    padding-left: 12px !important;
  }
  .pe-md-12 {
    padding-right: 12px !important;
  }
  .pt-md-12 {
    padding-top: 12px !important;
  }
  .pb-md-12 {
    padding-bottom: 12px !important;
  }
  .p-md-15 {
    padding: 15px !important;
  }
  .ps-md-15 {
    padding-left: 15px !important;
  }
  .pe-md-15 {
    padding-right: 15px !important;
  }
  .pt-md-15 {
    padding-top: 15px !important;
  }
  .pb-md-15 {
    padding-bottom: 15px !important;
  }
  .pt-md-18 {
    padding-top: 18px !important;
  }
  .pb-md-18 {
    padding-bottom: 18px !important;
  }
  .p-md-20 {
    padding: 20px !important;
  }
  .ps-md-20 {
    padding-left: 20px !important;
  }
  .pe-md-20 {
    padding-right: 20px !important;
  }
  .pt-md-20 {
    padding-top: 20px !important;
  }
  .pb-md-20 {
    padding-bottom: 20px !important;
  }
  .p-md-25 {
    padding: 25px !important;
  }
  .ps-md-25 {
    padding-left: 25px !important;
  }
  .pe-md-25 {
    padding-right: 25px !important;
  }
  .pt-md-25 {
    padding-top: 25px !important;
  }
  .pb-md-25 {
    padding-bottom: 25px !important;
  }
  .p-md-30 {
    padding: 30px !important;
  }
  .ps-md-30 {
    padding-left: 30px !important;
  }
  .pe-md-30 {
    padding-right: 30px !important;
  }
  .pt-md-30 {
    padding-top: 30px !important;
  }
  .pb-md-30 {
    padding-bottom: 30px !important;
  }
  .p-md-35 {
    padding: 35px !important;
  }
  .ps-md-35 {
    padding-left: 35px !important;
  }
  .pe-md-35 {
    padding-right: 35px !important;
  }
  .pt-md-35 {
    padding-top: 35px !important;
  }
  .pb-md-35 {
    padding-bottom: 35px !important;
  }
  .p-md-40 {
    padding: 40px !important;
  }
  .ps-md-40 {
    padding-left: 40px !important;
  }
  .pe-md-40 {
    padding-right: 40px !important;
  }
  .pt-md-40 {
    padding-top: 40px !important;
  }
  .pb-md-40 {
    padding-bottom: 40px !important;
  }
  .p-md-45 {
    padding: 45px !important;
  }
  .ps-md-45 {
    padding-left: 45px !important;
  }
  .pe-md-45 {
    padding-right: 45px !important;
  }
  .pt-md-45 {
    padding-top: 45px !important;
  }
  .pb-md-45 {
    padding-bottom: 45px !important;
  }
  .p-md-50 {
    padding: 50px !important;
  }
  .ps-md-50 {
    padding-left: 50px !important;
  }
  .pe-md-50 {
    padding-right: 50px !important;
  }
  .pt-md-50 {
    padding-top: 50px !important;
  }
  .pb-md-50 {
    padding-bottom: 50px !important;
  }
  .m-md-0 {
    margin: 0 !important;
  }
  .ms-md-0 {
    margin-left: 0 !important;
  }
  .me-md-0 {
    margin-right: 0 !important;
  }
  .mt-md-0 {
    margin-top: 0 !important;
  }
  .mb-md-0 {
    margin-bottom: 0 !important;
  }
  .m-md-1 {
    margin: 0.25rem !important;
  }
  .ms-md-1 {
    margin-left: 0.25rem !important;
  }
  .me-md-1 {
    margin-right: 0.25rem !important;
  }
  .mt-md-1 {
    margin-top: 0.25rem !important;
  }
  .mb-md-1 {
    margin-bottom: 0.25rem !important;
  }
  .m-md-2 {
    margin: 0.5rem !important;
  }
  .ms-md-2 {
    margin-left: 0.5rem !important;
  }
  .me-md-2 {
    margin-right: 0.5rem !important;
  }
  .mt-md-2 {
    margin-top: 0.5rem !important;
  }
  .mb-md-2 {
    margin-bottom: 0.5rem !important;
  }
  .m-md-3 {
    margin: 1rem !important;
  }
  .ms-md-3 {
    margin-left: 1rem !important;
  }
  .me-md-3 {
    margin-right: 1rem !important;
  }
  .mt-md-3 {
    margin-top: 1rem !important;
  }
  .mb-md-3 {
    margin-bottom: 1rem !important;
  }
  .m-md-4 {
    margin: 1.5rem !important;
  }
  .ms-md-4 {
    margin-left: 1.5rem !important;
  }
  .me-md-4 {
    margin-right: 1.5rem !important;
  }
  .mt-md-4 {
    margin-top: 1.5rem !important;
  }
  .mb-md-4 {
    margin-bottom: 1.5rem !important;
  }
  .m-md-5 {
    margin: 5px !important;
  }
  .ms-md-5 {
    margin-left: 5px !important;
  }
  .me-md-5 {
    margin-right: 5px !important;
  }
  .mt-md-5 {
    margin-top: 5px !important;
  }
  .mb-md-5 {
    margin-bottom: 5px !important;
  }
  .m-md-8 {
    margin: 8px !important;
  }
  .ms-md-8 {
    margin-left: 8px !important;
  }
  .me-md-8 {
    margin-right: 8px !important;
  }
  .mt-md-8 {
    margin-top: 8px !important;
  }
  .mb-md-8 {
    margin-bottom: 8px !important;
  }
  .m-md-10 {
    margin: 10px !important;
  }
  .ms-md-10 {
    margin-left: 10px !important;
  }
  .me-md-10 {
    margin-right: 10px !important;
  }
  .mt-md-10 {
    margin-top: 10px !important;
  }
  .mb-md-10 {
    margin-bottom: 10px !important;
  }
  .m-md-11 {
    margin: 11px !important;
  }
  .ms-md-11 {
    margin-left: 11px !important;
  }
  .me-md-11 {
    margin-right: 11px !important;
  }
  .mt-md-11 {
    margin-top: 11px !important;
  }
  .mb-md-11 {
    margin-bottom: 11px !important;
  }
  .m-md-12 {
    margin: 12px !important;
  }
  .ms-md-12 {
    margin-left: 12px !important;
  }
  .me-md-12 {
    margin-right: 12px !important;
  }
  .mt-md-12 {
    margin-top: 12px !important;
  }
  .mb-md-12 {
    margin-bottom: 12px !important;
  }
  .m-md-15 {
    margin: 15px !important;
  }
  .ms-md-15 {
    margin-left: 15px !important;
  }
  .me-md-15 {
    margin-right: 15px !important;
  }
  .mt-md-15 {
    margin-top: 15px !important;
  }
  .mb-md-15 {
    margin-bottom: 15px !important;
  }
  .m-md-20 {
    margin: 20px !important;
  }
  .ms-md-20 {
    margin-left: 20px !important;
  }
  .me-md-20 {
    margin-right: 20px !important;
  }
  .mt-md-20 {
    margin-top: 20px !important;
  }
  .mb-md-20 {
    margin-bottom: 20px !important;
  }
  .m-md-25 {
    margin: 25px !important;
  }
  .ms-md-25 {
    margin-left: 25px !important;
  }
  .me-md-25 {
    margin-right: 25px !important;
  }
  .mt-md-25 {
    margin-top: 25px !important;
  }
  .mb-md-25 {
    margin-bottom: 25px !important;
  }
  .m-md-30 {
    margin: 30px !important;
  }
  .ms-md-30 {
    margin-left: 30px !important;
  }
  .me-md-30 {
    margin-right: 30px !important;
  }
  .mt-md-30 {
    margin-top: 30px !important;
  }
  .mb-md-30 {
    margin-bottom: 30px !important;
  }
  .m-md-35 {
    margin: 35px !important;
  }
  .ms-md-35 {
    margin-left: 35px !important;
  }
  .me-md-35 {
    margin-right: 35px !important;
  }
  .mt-md-35 {
    margin-top: 35px !important;
  }
  .mb-md-35 {
    margin-bottom: 35px !important;
  }
  .m-md-40 {
    margin: 40px !important;
  }
  .ms-md-40 {
    margin-left: 40px !important;
  }
  .me-md-40 {
    margin-right: 40px !important;
  }
  .mt-md-40 {
    margin-top: 40px !important;
  }
  .mb-md-40 {
    margin-bottom: 40px !important;
  }
  .m-md-45 {
    margin: 45px !important;
  }
  .ms-md-45 {
    margin-left: 45px !important;
  }
  .me-md-45 {
    margin-right: 45px !important;
  }
  .mt-md-45 {
    margin-top: 45px !important;
  }
  .mb-md-45 {
    margin-bottom: 45px !important;
  }
  .m-md-50 {
    margin: 50px !important;
  }
  .ms-md-50 {
    margin-left: 50px !important;
  }
  .me-md-50 {
    margin-right: 50px !important;
  }
  .mt-md-50 {
    margin-top: 50px !important;
  }
  .mb-md-50 {
    margin-bottom: 50px !important;
  }
  .ms-md-100 {
    margin-left: 100px !important;
  }
  .fs-md-30 {
    font-size: 30px !important;
  }
  .fs-md-25 {
    font-size: 25px !important;
  }
  .fs-md-20 {
    font-size: 20px !important;
  }
  .fs-md-18 {
    font-size: 18px !important;
  }
  .fs-md-16 {
    font-size: 16px !important;
  }
  .fs-md-15 {
    font-size: 15px !important;
  }
  .fs-md-14 {
    font-size: 14px !important;
  }
  .fs-md-13 {
    font-size: 13px !important;
  }
}
@media (min-width: 992px) {
  .p-lg-0 {
    padding: 0 !important;
  }
  .ps-lg-0 {
    padding-left: 0 !important;
  }
  .pe-lg-0 {
    padding-right: 0 !important;
  }
  .pt-lg-0 {
    padding-top: 0 !important;
  }
  .pb-lg-0 {
    padding-bottom: 0 !important;
  }
  .p-lg-1 {
    padding: 0.25rem !important;
  }
  .ps-lg-1 {
    padding-left: 0.25rem !important;
  }
  .pe-lg-1 {
    padding-right: 0.25rem !important;
  }
  .pt-lg-1 {
    padding-top: 0.25rem !important;
  }
  .pb-lg-1 {
    padding-bottom: 0.25rem !important;
  }
  .p-lg-2 {
    padding: 0.5rem !important;
  }
  .ps-lg-2 {
    padding-left: 0.5rem !important;
  }
  .pe-lg-2 {
    padding-right: 0.5rem !important;
  }
  .pt-lg-2 {
    padding-top: 0.5rem !important;
  }
  .pb-lg-2 {
    padding-bottom: 0.5rem !important;
  }
  .p-lg-3 {
    padding: 1rem !important;
  }
  .ps-lg-3 {
    padding-left: 1rem !important;
  }
  .pe-lg-3 {
    padding-right: 1rem !important;
  }
  .pt-lg-3 {
    padding-top: 1rem !important;
  }
  .pb-lg-3 {
    padding-bottom: 1rem !important;
  }
  .p-lg-4 {
    padding: 1.5rem !important;
  }
  .ps-lg-4 {
    padding-left: 1.5rem !important;
  }
  .pe-lg-4 {
    padding-right: 1.5rem !important;
  }
  .pt-lg-4 {
    padding-top: 1.5rem !important;
  }
  .pb-lg-4 {
    padding-bottom: 1.5rem !important;
  }
  .p-lg-5 {
    padding: 5px !important;
  }
  .ps-lg-5 {
    padding-left: 5px !important;
  }
  .pe-lg-5 {
    padding-right: 5px !important;
  }
  .pt-lg-5 {
    padding-top: 5px !important;
  }
  .pb-lg-5 {
    padding-bottom: 5px !important;
  }
  .p-lg-8 {
    padding: 8px !important;
  }
  .ps-lg-8 {
    padding-left: 8px !important;
  }
  .pe-lg-8 {
    padding-right: 8px !important;
  }
  .pt-lg-8 {
    padding-top: 8px !important;
  }
  .pb-lg-8 {
    padding-bottom: 8px !important;
  }
  .p-lg-10 {
    padding: 10px !important;
  }
  .ps-lg-10 {
    padding-left: 10px !important;
  }
  .pe-lg-10 {
    padding-right: 10px !important;
  }
  .pt-lg-10 {
    padding-top: 10px !important;
  }
  .pb-lg-10 {
    padding-bottom: 10px !important;
  }
  .p-lg-11 {
    padding: 11px !important;
  }
  .ps-lg-11 {
    padding-left: 11px !important;
  }
  .pe-lg-11 {
    padding-right: 11px !important;
  }
  .pt-lg-11 {
    padding-top: 11px !important;
  }
  .pb-lg-11 {
    padding-bottom: 11px !important;
  }
  .p-lg-12 {
    padding: 12px !important;
  }
  .ps-lg-12 {
    padding-left: 12px !important;
  }
  .pe-lg-12 {
    padding-right: 12px !important;
  }
  .pt-lg-12 {
    padding-top: 12px !important;
  }
  .pb-lg-12 {
    padding-bottom: 12px !important;
  }
  .p-lg-15 {
    padding: 15px !important;
  }
  .ps-lg-15 {
    padding-left: 15px !important;
  }
  .pe-lg-15 {
    padding-right: 15px !important;
  }
  .pt-lg-15 {
    padding-top: 15px !important;
  }
  .pb-lg-15 {
    padding-bottom: 15px !important;
  }
  .pt-lg-18 {
    padding-top: 18px !important;
  }
  .pb-lg-18 {
    padding-bottom: 18px !important;
  }
  .p-lg-20 {
    padding: 20px !important;
  }
  .ps-lg-20 {
    padding-left: 20px !important;
  }
  .pe-lg-20 {
    padding-right: 20px !important;
  }
  .pt-lg-20 {
    padding-top: 20px !important;
  }
  .pb-lg-20 {
    padding-bottom: 20px !important;
  }
  .p-lg-25 {
    padding: 25px !important;
  }
  .ps-lg-25 {
    padding-left: 25px !important;
  }
  .pe-lg-25 {
    padding-right: 25px !important;
  }
  .pt-lg-25 {
    padding-top: 25px !important;
  }
  .pb-lg-25 {
    padding-bottom: 25px !important;
  }
  .p-lg-30 {
    padding: 30px !important;
  }
  .ps-lg-30 {
    padding-left: 30px !important;
  }
  .pe-lg-30 {
    padding-right: 30px !important;
  }
  .pt-lg-30 {
    padding-top: 30px !important;
  }
  .pb-lg-30 {
    padding-bottom: 30px !important;
  }
  .p-lg-35 {
    padding: 35px !important;
  }
  .ps-lg-35 {
    padding-left: 35px !important;
  }
  .pe-lg-35 {
    padding-right: 35px !important;
  }
  .pt-lg-35 {
    padding-top: 35px !important;
  }
  .pb-lg-35 {
    padding-bottom: 35px !important;
  }
  .p-lg-40 {
    padding: 40px !important;
  }
  .ps-lg-40 {
    padding-left: 40px !important;
  }
  .pe-lg-40 {
    padding-right: 40px !important;
  }
  .pt-lg-40 {
    padding-top: 40px !important;
  }
  .pb-lg-40 {
    padding-bottom: 40px !important;
  }
  .p-lg-45 {
    padding: 45px !important;
  }
  .ps-lg-45 {
    padding-left: 45px !important;
  }
  .pe-lg-45 {
    padding-right: 45px !important;
  }
  .pt-lg-45 {
    padding-top: 45px !important;
  }
  .pb-lg-45 {
    padding-bottom: 45px !important;
  }
  .p-lg-50 {
    padding: 50px !important;
  }
  .ps-lg-50 {
    padding-left: 50px !important;
  }
  .pe-lg-50 {
    padding-right: 50px !important;
  }
  .pt-lg-50 {
    padding-top: 50px !important;
  }
  .pb-lg-50 {
    padding-bottom: 50px !important;
  }
  .m-lg-0 {
    margin: 0 !important;
  }
  .ms-lg-0 {
    margin-left: 0 !important;
  }
  .me-lg-0 {
    margin-right: 0 !important;
  }
  .mt-lg-0 {
    margin-top: 0 !important;
  }
  .mb-lg-0 {
    margin-bottom: 0 !important;
  }
  .m-lg-1 {
    margin: 0.25rem !important;
  }
  .ms-lg-1 {
    margin-left: 0.25rem !important;
  }
  .me-lg-1 {
    margin-right: 0.25rem !important;
  }
  .mt-lg-1 {
    margin-top: 0.25rem !important;
  }
  .mb-lg-1 {
    margin-bottom: 0.25rem !important;
  }
  .m-lg-2 {
    margin: 0.5rem !important;
  }
  .ms-lg-2 {
    margin-left: 0.5rem !important;
  }
  .me-lg-2 {
    margin-right: 0.5rem !important;
  }
  .mt-lg-2 {
    margin-top: 0.5rem !important;
  }
  .mb-lg-2 {
    margin-bottom: 0.5rem !important;
  }
  .m-lg-3 {
    margin: 1rem !important;
  }
  .ms-lg-3 {
    margin-left: 1rem !important;
  }
  .me-lg-3 {
    margin-right: 1rem !important;
  }
  .mt-lg-3 {
    margin-top: 1rem !important;
  }
  .mb-lg-3 {
    margin-bottom: 1rem !important;
  }
  .m-lg-4 {
    margin: 1.5rem !important;
  }
  .ms-lg-4 {
    margin-left: 1.5rem !important;
  }
  .me-lg-4 {
    margin-right: 1.5rem !important;
  }
  .mt-lg-4 {
    margin-top: 1.5rem !important;
  }
  .mb-lg-4 {
    margin-bottom: 1.5rem !important;
  }
  .m-lg-5 {
    margin: 5px !important;
  }
  .ms-lg-5 {
    margin-left: 5px !important;
  }
  .me-lg-5 {
    margin-right: 5px !important;
  }
  .mt-lg-5 {
    margin-top: 5px !important;
  }
  .mb-lg-5 {
    margin-bottom: 5px !important;
  }
  .m-lg-8 {
    margin: 8px !important;
  }
  .ms-lg-8 {
    margin-left: 8px !important;
  }
  .me-lg-8 {
    margin-right: 8px !important;
  }
  .mt-lg-8 {
    margin-top: 8px !important;
  }
  .mb-lg-8 {
    margin-bottom: 8px !important;
  }
  .m-lg-10 {
    margin: 10px !important;
  }
  .ms-lg-10 {
    margin-left: 10px !important;
  }
  .me-lg-10 {
    margin-right: 10px !important;
  }
  .mt-lg-10 {
    margin-top: 10px !important;
  }
  .mb-lg-10 {
    margin-bottom: 10px !important;
  }
  .m-lg-11 {
    margin: 11px !important;
  }
  .ms-lg-11 {
    margin-left: 11px !important;
  }
  .me-lg-11 {
    margin-right: 11px !important;
  }
  .mt-lg-11 {
    margin-top: 11px !important;
  }
  .mb-lg-11 {
    margin-bottom: 11px !important;
  }
  .m-lg-12 {
    margin: 12px !important;
  }
  .ms-lg-12 {
    margin-left: 12px !important;
  }
  .me-lg-12 {
    margin-right: 12px !important;
  }
  .mt-lg-12 {
    margin-top: 12px !important;
  }
  .mb-lg-12 {
    margin-bottom: 12px !important;
  }
  .m-lg-15 {
    margin: 15px !important;
  }
  .ms-lg-15 {
    margin-left: 15px !important;
  }
  .me-lg-15 {
    margin-right: 15px !important;
  }
  .mt-lg-15 {
    margin-top: 15px !important;
  }
  .mb-lg-15 {
    margin-bottom: 15px !important;
  }
  .m-lg-20 {
    margin: 20px !important;
  }
  .ms-lg-20 {
    margin-left: 20px !important;
  }
  .me-lg-20 {
    margin-right: 20px !important;
  }
  .mt-lg-20 {
    margin-top: 20px !important;
  }
  .mb-lg-20 {
    margin-bottom: 20px !important;
  }
  .m-lg-25 {
    margin: 25px !important;
  }
  .ms-lg-25 {
    margin-left: 25px !important;
  }
  .me-lg-25 {
    margin-right: 25px !important;
  }
  .mt-lg-25 {
    margin-top: 25px !important;
  }
  .mb-lg-25 {
    margin-bottom: 25px !important;
  }
  .m-lg-30 {
    margin: 30px !important;
  }
  .ms-lg-30 {
    margin-left: 30px !important;
  }
  .me-lg-30 {
    margin-right: 30px !important;
  }
  .mt-lg-30 {
    margin-top: 30px !important;
  }
  .mb-lg-30 {
    margin-bottom: 30px !important;
  }
  .m-lg-35 {
    margin: 35px !important;
  }
  .ms-lg-35 {
    margin-left: 35px !important;
  }
  .me-lg-35 {
    margin-right: 35px !important;
  }
  .mt-lg-35 {
    margin-top: 35px !important;
  }
  .mb-lg-35 {
    margin-bottom: 35px !important;
  }
  .m-lg-40 {
    margin: 40px !important;
  }
  .ms-lg-40 {
    margin-left: 40px !important;
  }
  .me-lg-40 {
    margin-right: 40px !important;
  }
  .mt-lg-40 {
    margin-top: 40px !important;
  }
  .mb-lg-40 {
    margin-bottom: 40px !important;
  }
  .m-lg-45 {
    margin: 45px !important;
  }
  .ms-lg-45 {
    margin-left: 45px !important;
  }
  .me-lg-45 {
    margin-right: 45px !important;
  }
  .mt-lg-45 {
    margin-top: 45px !important;
  }
  .mb-lg-45 {
    margin-bottom: 45px !important;
  }
  .m-lg-50 {
    margin: 50px !important;
  }
  .ms-lg-50 {
    margin-left: 50px !important;
  }
  .me-lg-50 {
    margin-right: 50px !important;
  }
  .mt-lg-50 {
    margin-top: 50px !important;
  }
  .mb-lg-50 {
    margin-bottom: 50px !important;
  }
  .ms-lg-100 {
    margin-left: 100px !important;
  }
  .fs-lg-30 {
    font-size: 30px !important;
  }
  .fs-lg-25 {
    font-size: 25px !important;
  }
  .fs-lg-20 {
    font-size: 20px !important;
  }
  .fs-lg-18 {
    font-size: 18px !important;
  }
  .fs-lg-16 {
    font-size: 16px !important;
  }
  .fs-lg-15 {
    font-size: 15px !important;
  }
  .fs-lg-14 {
    font-size: 14px !important;
  }
  .fs-lg-13 {
    font-size: 13px !important;
  }
}
@media (min-width: 1200px) {
  .p-xl-0 {
    padding: 0 !important;
  }
  .ps-xl-0 {
    padding-left: 0 !important;
  }
  .pe-xl-0 {
    padding-right: 0 !important;
  }
  .pt-xl-0 {
    padding-top: 0 !important;
  }
  .pb-xl-0 {
    padding-bottom: 0 !important;
  }
  .p-xl-1 {
    padding: 0.25rem !important;
  }
  .ps-xl-1 {
    padding-left: 0.25rem !important;
  }
  .pe-xl-1 {
    padding-right: 0.25rem !important;
  }
  .pt-xl-1 {
    padding-top: 0.25rem !important;
  }
  .pb-xl-1 {
    padding-bottom: 0.25rem !important;
  }
  .p-xl-2 {
    padding: 0.5rem !important;
  }
  .ps-xl-2 {
    padding-left: 0.5rem !important;
  }
  .pe-xl-2 {
    padding-right: 0.5rem !important;
  }
  .pt-xl-2 {
    padding-top: 0.5rem !important;
  }
  .pb-xl-2 {
    padding-bottom: 0.5rem !important;
  }
  .p-xl-3 {
    padding: 1rem !important;
  }
  .ps-xl-3 {
    padding-left: 1rem !important;
  }
  .pe-xl-3 {
    padding-right: 1rem !important;
  }
  .pt-xl-3 {
    padding-top: 1rem !important;
  }
  .pb-xl-3 {
    padding-bottom: 1rem !important;
  }
  .p-xl-4 {
    padding: 1.5rem !important;
  }
  .ps-xl-4 {
    padding-left: 1.5rem !important;
  }
  .pe-xl-4 {
    padding-right: 1.5rem !important;
  }
  .pt-xl-4 {
    padding-top: 1.5rem !important;
  }
  .pb-xl-4 {
    padding-bottom: 1.5rem !important;
  }
  .p-xl-5 {
    padding: 5px !important;
  }
  .ps-xl-5 {
    padding-left: 5px !important;
  }
  .pe-xl-5 {
    padding-right: 5px !important;
  }
  .pt-xl-5 {
    padding-top: 5px !important;
  }
  .pb-xl-5 {
    padding-bottom: 5px !important;
  }
  .p-xl-8 {
    padding: 8px !important;
  }
  .ps-xl-8 {
    padding-left: 8px !important;
  }
  .pe-xl-8 {
    padding-right: 8px !important;
  }
  .pt-xl-8 {
    padding-top: 8px !important;
  }
  .pb-xl-8 {
    padding-bottom: 8px !important;
  }
  .p-xl-10 {
    padding: 10px !important;
  }
  .ps-xl-10 {
    padding-left: 10px !important;
  }
  .pe-xl-10 {
    padding-right: 10px !important;
  }
  .pt-xl-10 {
    padding-top: 10px !important;
  }
  .pb-xl-10 {
    padding-bottom: 10px !important;
  }
  .p-xl-11 {
    padding: 11px !important;
  }
  .ps-xl-11 {
    padding-left: 11px !important;
  }
  .pe-xl-11 {
    padding-right: 11px !important;
  }
  .pt-xl-11 {
    padding-top: 11px !important;
  }
  .pb-xl-11 {
    padding-bottom: 11px !important;
  }
  .p-xl-12 {
    padding: 12px !important;
  }
  .ps-xl-12 {
    padding-left: 12px !important;
  }
  .pe-xl-12 {
    padding-right: 12px !important;
  }
  .pt-xl-12 {
    padding-top: 12px !important;
  }
  .pb-xl-12 {
    padding-bottom: 12px !important;
  }
  .p-xl-15 {
    padding: 15px !important;
  }
  .ps-xl-15 {
    padding-left: 15px !important;
  }
  .pe-xl-15 {
    padding-right: 15px !important;
  }
  .pt-xl-15 {
    padding-top: 15px !important;
  }
  .pb-xl-15 {
    padding-bottom: 15px !important;
  }
  .pt-xl-18 {
    padding-top: 18px !important;
  }
  .pb-xl-18 {
    padding-bottom: 18px !important;
  }
  .p-xl-20 {
    padding: 20px !important;
  }
  .ps-xl-20 {
    padding-left: 20px !important;
  }
  .pe-xl-20 {
    padding-right: 20px !important;
  }
  .pt-xl-20 {
    padding-top: 20px !important;
  }
  .pb-xl-20 {
    padding-bottom: 20px !important;
  }
  .p-xl-25 {
    padding: 25px !important;
  }
  .ps-xl-25 {
    padding-left: 25px !important;
  }
  .pe-xl-25 {
    padding-right: 25px !important;
  }
  .pt-xl-25 {
    padding-top: 25px !important;
  }
  .pb-xl-25 {
    padding-bottom: 25px !important;
  }
  .p-xl-30 {
    padding: 30px !important;
  }
  .ps-xl-30 {
    padding-left: 30px !important;
  }
  .pe-xl-30 {
    padding-right: 30px !important;
  }
  .pt-xl-30 {
    padding-top: 30px !important;
  }
  .pb-xl-30 {
    padding-bottom: 30px !important;
  }
  .p-xl-35 {
    padding: 35px !important;
  }
  .ps-xl-35 {
    padding-left: 35px !important;
  }
  .pe-xl-35 {
    padding-right: 35px !important;
  }
  .pt-xl-35 {
    padding-top: 35px !important;
  }
  .pb-xl-35 {
    padding-bottom: 35px !important;
  }
  .p-xl-40 {
    padding: 40px !important;
  }
  .ps-xl-40 {
    padding-left: 40px !important;
  }
  .pe-xl-40 {
    padding-right: 40px !important;
  }
  .pt-xl-40 {
    padding-top: 40px !important;
  }
  .pb-xl-40 {
    padding-bottom: 40px !important;
  }
  .p-xl-45 {
    padding: 45px !important;
  }
  .ps-xl-45 {
    padding-left: 45px !important;
  }
  .pe-xl-45 {
    padding-right: 45px !important;
  }
  .pt-xl-45 {
    padding-top: 45px !important;
  }
  .pb-xl-45 {
    padding-bottom: 45px !important;
  }
  .p-xl-50 {
    padding: 50px !important;
  }
  .ps-xl-50 {
    padding-left: 50px !important;
  }
  .pe-xl-50 {
    padding-right: 50px !important;
  }
  .pt-xl-50 {
    padding-top: 50px !important;
  }
  .pb-xl-50 {
    padding-bottom: 50px !important;
  }
  .m-xl-0 {
    margin: 0 !important;
  }
  .ms-xl-0 {
    margin-left: 0 !important;
  }
  .me-xl-0 {
    margin-right: 0 !important;
  }
  .mt-xl-0 {
    margin-top: 0 !important;
  }
  .mb-xl-0 {
    margin-bottom: 0 !important;
  }
  .m-xl-1 {
    margin: 0.25rem !important;
  }
  .ms-xl-1 {
    margin-left: 0.25rem !important;
  }
  .me-xl-1 {
    margin-right: 0.25rem !important;
  }
  .mt-xl-1 {
    margin-top: 0.25rem !important;
  }
  .mb-xl-1 {
    margin-bottom: 0.25rem !important;
  }
  .m-xl-2 {
    margin: 0.5rem !important;
  }
  .ms-xl-2 {
    margin-left: 0.5rem !important;
  }
  .me-xl-2 {
    margin-right: 0.5rem !important;
  }
  .mt-xl-2 {
    margin-top: 0.5rem !important;
  }
  .mb-xl-2 {
    margin-bottom: 0.5rem !important;
  }
  .m-xl-3 {
    margin: 1rem !important;
  }
  .ms-xl-3 {
    margin-left: 1rem !important;
  }
  .me-xl-3 {
    margin-right: 1rem !important;
  }
  .mt-xl-3 {
    margin-top: 1rem !important;
  }
  .mb-xl-3 {
    margin-bottom: 1rem !important;
  }
  .m-xl-4 {
    margin: 1.5rem !important;
  }
  .ms-xl-4 {
    margin-left: 1.5rem !important;
  }
  .me-xl-4 {
    margin-right: 1.5rem !important;
  }
  .mt-xl-4 {
    margin-top: 1.5rem !important;
  }
  .mb-xl-4 {
    margin-bottom: 1.5rem !important;
  }
  .m-xl-5 {
    margin: 5px !important;
  }
  .ms-xl-5 {
    margin-left: 5px !important;
  }
  .me-xl-5 {
    margin-right: 5px !important;
  }
  .mt-xl-5 {
    margin-top: 5px !important;
  }
  .mb-xl-5 {
    margin-bottom: 5px !important;
  }
  .m-xl-8 {
    margin: 8px !important;
  }
  .ms-xl-8 {
    margin-left: 8px !important;
  }
  .me-xl-8 {
    margin-right: 8px !important;
  }
  .mt-xl-8 {
    margin-top: 8px !important;
  }
  .mb-xl-8 {
    margin-bottom: 8px !important;
  }
  .m-xl-10 {
    margin: 10px !important;
  }
  .ms-xl-10 {
    margin-left: 10px !important;
  }
  .me-xl-10 {
    margin-right: 10px !important;
  }
  .mt-xl-10 {
    margin-top: 10px !important;
  }
  .mb-xl-10 {
    margin-bottom: 10px !important;
  }
  .m-xl-11 {
    margin: 11px !important;
  }
  .ms-xl-11 {
    margin-left: 11px !important;
  }
  .me-xl-11 {
    margin-right: 11px !important;
  }
  .mt-xl-11 {
    margin-top: 11px !important;
  }
  .mb-xl-10 {
    margin-bottom: 10px !important;
  }
  .m-xl-12 {
    margin: 12px !important;
  }
  .ms-xl-12 {
    margin-left: 12px !important;
  }
  .me-xl-12 {
    margin-right: 12px !important;
  }
  .mt-xl-12 {
    margin-top: 12px !important;
  }
  .mb-xl-12 {
    margin-bottom: 12px !important;
  }
  .m-xl-15 {
    margin: 15px !important;
  }
  .ms-xl-15 {
    margin-left: 15px !important;
  }
  .me-xl-15 {
    margin-right: 15px !important;
  }
  .mt-xl-15 {
    margin-top: 15px !important;
  }
  .mb-xl-15 {
    margin-bottom: 15px !important;
  }
  .m-xl-20 {
    margin: 20px !important;
  }
  .ms-xl-20 {
    margin-left: 20px !important;
  }
  .me-xl-20 {
    margin-right: 20px !important;
  }
  .mt-xl-20 {
    margin-top: 20px !important;
  }
  .mb-xl-20 {
    margin-bottom: 20px !important;
  }
  .m-xl-25 {
    margin: 25px !important;
  }
  .ms-xl-25 {
    margin-left: 25px !important;
  }
  .me-xl-25 {
    margin-right: 25px !important;
  }
  .mt-xl-25 {
    margin-top: 25px !important;
  }
  .mb-xl-25 {
    margin-bottom: 25px !important;
  }
  .m-xl-30 {
    margin: 30px !important;
  }
  .ms-xl-30 {
    margin-left: 30px !important;
  }
  .me-xl-30 {
    margin-right: 30px !important;
  }
  .mt-xl-30 {
    margin-top: 30px !important;
  }
  .mb-xl-30 {
    margin-bottom: 30px !important;
  }
  .m-xl-35 {
    margin: 35px !important;
  }
  .ms-xl-35 {
    margin-left: 35px !important;
  }
  .me-xl-35 {
    margin-right: 35px !important;
  }
  .mt-xl-35 {
    margin-top: 35px !important;
  }
  .mb-xl-35 {
    margin-bottom: 35px !important;
  }
  .m-xl-40 {
    margin: 40px !important;
  }
  .ms-xl-40 {
    margin-left: 40px !important;
  }
  .me-xl-40 {
    margin-right: 40px !important;
  }
  .mt-xl-40 {
    margin-top: 40px !important;
  }
  .mb-xl-40 {
    margin-bottom: 40px !important;
  }
  .m-xl-45 {
    margin: 45px !important;
  }
  .ms-xl-45 {
    margin-left: 45px !important;
  }
  .me-xl-45 {
    margin-right: 45px !important;
  }
  .mt-xl-45 {
    margin-top: 45px !important;
  }
  .mb-xl-45 {
    margin-bottom: 45px !important;
  }
  .m-xl-50 {
    margin: 50px !important;
  }
  .ms-xl-50 {
    margin-left: 50px !important;
  }
  .me-xl-50 {
    margin-right: 50px !important;
  }
  .mt-xl-50 {
    margin-top: 50px !important;
  }
  .mb-xl-50 {
    margin-bottom: 50px !important;
  }
  .ms-xl-100 {
    margin-left: 100px !important;
  }
  .fs-xl-30 {
    font-size: 30px !important;
  }
  .fs-xl-25 {
    font-size: 25px !important;
  }
  .fs-xl-20 {
    font-size: 20px !important;
  }
  .fs-xl-18 {
    font-size: 18px !important;
  }
  .fs-xl-16 {
    font-size: 16px !important;
  }
  .fs-xl-15 {
    font-size: 15px !important;
  }
  .fs-xl-14 {
    font-size: 14px !important;
  }
  .fs-xl-13 {
    font-size: 13px !important;
  }
}
@media (min-width: 1400px) {
  .p-xxl-0 {
    padding: 0 !important;
  }
  .ps-xxl-0 {
    padding-left: 0 !important;
  }
  .pe-xxl-0 {
    padding-right: 0 !important;
  }
  .pt-xxl-0 {
    padding-top: 0 !important;
  }
  .pb-xxl-0 {
    padding-bottom: 0 !important;
  }
  .p-xxl-1 {
    padding: 0.25rem !important;
  }
  .ps-xxl-1 {
    padding-left: 0.25rem !important;
  }
  .pe-xxl-1 {
    padding-right: 0.25rem !important;
  }
  .pt-xxl-1 {
    padding-top: 0.25rem !important;
  }
  .pb-xxl-1 {
    padding-bottom: 0.25rem !important;
  }
  .p-xxl-2 {
    padding: 0.5rem !important;
  }
  .ps-xxl-2 {
    padding-left: 0.5rem !important;
  }
  .pe-xxl-2 {
    padding-right: 0.5rem !important;
  }
  .pt-xxl-2 {
    padding-top: 0.5rem !important;
  }
  .pb-xxl-2 {
    padding-bottom: 0.5rem !important;
  }
  .p-xxl-3 {
    padding: 1rem !important;
  }
  .ps-xxl-3 {
    padding-left: 1rem !important;
  }
  .pe-xxl-3 {
    padding-right: 1rem !important;
  }
  .pt-xxl-3 {
    padding-top: 1rem !important;
  }
  .pb-xxl-3 {
    padding-bottom: 1rem !important;
  }
  .p-xxl-4 {
    padding: 1.5rem !important;
  }
  .ps-xxl-4 {
    padding-left: 1.5rem !important;
  }
  .pe-xxl-4 {
    padding-right: 1.5rem !important;
  }
  .pt-xxl-4 {
    padding-top: 1.5rem !important;
  }
  .pb-xxl-4 {
    padding-bottom: 1.5rem !important;
  }
  .p-xxl-5 {
    padding: 5px !important;
  }
  .ps-xxl-5 {
    padding-left: 5px !important;
  }
  .pe-xxl-5 {
    padding-right: 5px !important;
  }
  .pt-xxl-5 {
    padding-top: 5px !important;
  }
  .pb-xxl-5 {
    padding-bottom: 5px !important;
  }
  .p-xxl-8 {
    padding: 8px !important;
  }
  .ps-xxl-8 {
    padding-left: 8px !important;
  }
  .pe-xxl-8 {
    padding-right: 8px !important;
  }
  .pt-xxl-8 {
    padding-top: 8px !important;
  }
  .pb-xxl-8 {
    padding-bottom: 8px !important;
  }
  .p-xxl-10 {
    padding: 10px !important;
  }
  .ps-xxl-10 {
    padding-left: 10px !important;
  }
  .pe-xxl-10 {
    padding-right: 10px !important;
  }
  .pt-xxl-10 {
    padding-top: 10px !important;
  }
  .pb-xxl-10 {
    padding-bottom: 10px !important;
  }
  .p-xxl-11 {
    padding: 11px !important;
  }
  .ps-xxl-11 {
    padding-left: 11px !important;
  }
  .pe-xxl-11 {
    padding-right: 11px !important;
  }
  .pt-xxl-11 {
    padding-top: 11px !important;
  }
  .pb-xxl-11 {
    padding-bottom: 11px !important;
  }
  .p-xxl-12 {
    padding: 12px !important;
  }
  .ps-xxl-12 {
    padding-left: 12px !important;
  }
  .pe-xxl-12 {
    padding-right: 12px !important;
  }
  .pt-xxl-12 {
    padding-top: 12px !important;
  }
  .pb-xxl-12 {
    padding-bottom: 12px !important;
  }
  .p-xxl-15 {
    padding: 15px !important;
  }
  .ps-xxl-15 {
    padding-left: 15px !important;
  }
  .pe-xxl-15 {
    padding-right: 15px !important;
  }
  .pt-xxl-15 {
    padding-top: 15px !important;
  }
  .pb-xxl-15 {
    padding-bottom: 15px !important;
  }
  .pt-xxl-18 {
    padding-top: 18px !important;
  }
  .pb-xxl-18 {
    padding-bottom: 18px !important;
  }
  .p-xxl-20 {
    padding: 20px !important;
  }
  .ps-xxl-20 {
    padding-left: 20px !important;
  }
  .pe-xxl-20 {
    padding-right: 20px !important;
  }
  .pt-xxl-20 {
    padding-top: 20px !important;
  }
  .pb-xxl-20 {
    padding-bottom: 20px !important;
  }
  .p-xxl-25 {
    padding: 25px !important;
  }
  .ps-xxl-25 {
    padding-left: 25px !important;
  }
  .pe-xxl-25 {
    padding-right: 25px !important;
  }
  .pt-xxl-25 {
    padding-top: 25px !important;
  }
  .pb-xxl-25 {
    padding-bottom: 25px !important;
  }
  .p-xxl-30 {
    padding: 30px !important;
  }
  .ps-xxl-30 {
    padding-left: 30px !important;
  }
  .pe-xxl-30 {
    padding-right: 30px !important;
  }
  .pt-xxl-30 {
    padding-top: 30px !important;
  }
  .pb-xxl-30 {
    padding-bottom: 30px !important;
  }
  .p-xxl-35 {
    padding: 35px !important;
  }
  .ps-xxl-35 {
    padding-left: 35px !important;
  }
  .pe-xxl-35 {
    padding-right: 35px !important;
  }
  .pt-xxl-35 {
    padding-top: 35px !important;
  }
  .pb-xxl-35 {
    padding-bottom: 35px !important;
  }
  .p-xxl-40 {
    padding: 40px !important;
  }
  .ps-xxl-40 {
    padding-left: 40px !important;
  }
  .pe-xxl-40 {
    padding-right: 40px !important;
  }
  .pt-xxl-40 {
    padding-top: 40px !important;
  }
  .pb-xxl-40 {
    padding-bottom: 40px !important;
  }
  .p-xxl-45 {
    padding: 45px !important;
  }
  .ps-xxl-45 {
    padding-left: 45px !important;
  }
  .pe-xxl-45 {
    padding-right: 45px !important;
  }
  .pt-xxl-45 {
    padding-top: 45px !important;
  }
  .pb-xxl-45 {
    padding-bottom: 45px !important;
  }
  .p-xxl-50 {
    padding: 50px !important;
  }
  .ps-xxl-50 {
    padding-left: 50px !important;
  }
  .pe-xxl-50 {
    padding-right: 50px !important;
  }
  .pt-xxl-50 {
    padding-top: 50px !important;
  }
  .pb-xxl-50 {
    padding-bottom: 50px !important;
  }
  .m-xxl-0 {
    margin: 0 !important;
  }
  .ms-xxl-0 {
    margin-left: 0 !important;
  }
  .me-xxl-0 {
    margin-right: 0 !important;
  }
  .mt-xxl-0 {
    margin-top: 0 !important;
  }
  .mb-xxl-0 {
    margin-bottom: 0 !important;
  }
  .m-xxl-1 {
    margin: 0.25rem !important;
  }
  .ms-xxl-1 {
    margin-left: 0.25rem !important;
  }
  .me-xxl-1 {
    margin-right: 0.25rem !important;
  }
  .mt-xxl-1 {
    margin-top: 0.25rem !important;
  }
  .mb-xxl-1 {
    margin-bottom: 0.25rem !important;
  }
  .m-xxl-2 {
    margin: 0.5rem !important;
  }
  .ms-xxl-2 {
    margin-left: 0.5rem !important;
  }
  .me-xxl-2 {
    margin-right: 0.5rem !important;
  }
  .mt-xxl-2 {
    margin-top: 0.5rem !important;
  }
  .mb-xxl-2 {
    margin-bottom: 0.5rem !important;
  }
  .m-xxl-3 {
    margin: 1rem !important;
  }
  .ms-xxl-3 {
    margin-left: 1rem !important;
  }
  .me-xxl-3 {
    margin-right: 1rem !important;
  }
  .mt-xxl-3 {
    margin-top: 1rem !important;
  }
  .mb-xxl-3 {
    margin-bottom: 1rem !important;
  }
  .m-xxl-4 {
    margin: 1.5rem !important;
  }
  .ms-xxl-4 {
    margin-left: 1.5rem !important;
  }
  .me-xxl-4 {
    margin-right: 1.5rem !important;
  }
  .mt-xxl-4 {
    margin-top: 1.5rem !important;
  }
  .mb-xxl-4 {
    margin-bottom: 1.5rem !important;
  }
  .m-xxl-5 {
    margin: 5px !important;
  }
  .ms-xxl-5 {
    margin-left: 5px !important;
  }
  .me-xxl-5 {
    margin-right: 5px !important;
  }
  .mt-xxl-5 {
    margin-top: 5px !important;
  }
  .mb-xxl-5 {
    margin-bottom: 5px !important;
  }
  .m-xxl-8 {
    margin: 8px !important;
  }
  .ms-xxl-8 {
    margin-left: 8px !important;
  }
  .me-xxl-8 {
    margin-right: 8px !important;
  }
  .mt-xxl-8 {
    margin-top: 8px !important;
  }
  .mb-xxl-8 {
    margin-bottom: 8px !important;
  }
  .m-xxl-10 {
    margin: 10px !important;
  }
  .ms-xxl-10 {
    margin-left: 10px !important;
  }
  .me-xxl-10 {
    margin-right: 10px !important;
  }
  .mt-xxl-10 {
    margin-top: 10px !important;
  }
  .mb-xxl-10 {
    margin-bottom: 10px !important;
  }
  .m-xxl-11 {
    margin: 11px !important;
  }
  .ms-xxl-11 {
    margin-left: 11px !important;
  }
  .me-xxl-11 {
    margin-right: 11px !important;
  }
  .mt-xxl-11 {
    margin-top: 11px !important;
  }
  .mb-xxl-11 {
    margin-bottom: 11px !important;
  }
  .m-xxl-12 {
    margin: 12px !important;
  }
  .ms-xxl-12 {
    margin-left: 12px !important;
  }
  .me-xxl-12 {
    margin-right: 12px !important;
  }
  .mt-xxl-12 {
    margin-top: 12px !important;
  }
  .mb-xxl-12 {
    margin-bottom: 12px !important;
  }
  .m-xxl-15 {
    margin: 15px !important;
  }
  .ms-xxl-15 {
    margin-left: 15px !important;
  }
  .me-xxl-15 {
    margin-right: 15px !important;
  }
  .mt-xxl-15 {
    margin-top: 15px !important;
  }
  .mb-xxl-15 {
    margin-bottom: 15px !important;
  }
  .m-xxl-20 {
    margin: 20px !important;
  }
  .ms-xxl-20 {
    margin-left: 20px !important;
  }
  .me-xxl-20 {
    margin-right: 20px !important;
  }
  .mt-xxl-20 {
    margin-top: 20px !important;
  }
  .mb-xxl-20 {
    margin-bottom: 20px !important;
  }
  .m-xxl-25 {
    margin: 25px !important;
  }
  .ms-xxl-25 {
    margin-left: 25px !important;
  }
  .me-xxl-25 {
    margin-right: 25px !important;
  }
  .mt-xxl-25 {
    margin-top: 25px !important;
  }
  .mb-xxl-25 {
    margin-bottom: 25px !important;
  }
  .m-xxl-30 {
    margin: 30px !important;
  }
  .ms-xxl-30 {
    margin-left: 30px !important;
  }
  .me-xxl-30 {
    margin-right: 30px !important;
  }
  .mt-xxl-30 {
    margin-top: 30px !important;
  }
  .mb-xxl-30 {
    margin-bottom: 30px !important;
  }
  .m-xxl-35 {
    margin: 35px !important;
  }
  .ms-xxl-35 {
    margin-left: 35px !important;
  }
  .me-xxl-35 {
    margin-right: 35px !important;
  }
  .mt-xxl-35 {
    margin-top: 35px !important;
  }
  .mb-xxl-35 {
    margin-bottom: 35px !important;
  }
  .m-xxl-40 {
    margin: 40px !important;
  }
  .ms-xxl-40 {
    margin-left: 40px !important;
  }
  .me-xxl-40 {
    margin-right: 40px !important;
  }
  .mt-xxl-40 {
    margin-top: 40px !important;
  }
  .mb-xxl-40 {
    margin-bottom: 40px !important;
  }
  .m-xxl-45 {
    margin: 45px !important;
  }
  .ms-xxl-45 {
    margin-left: 45px !important;
  }
  .me-xxl-45 {
    margin-right: 45px !important;
  }
  .mt-xxl-45 {
    margin-top: 45px !important;
  }
  .mb-xxl-45 {
    margin-bottom: 45px !important;
  }
  .m-xxl-50 {
    margin: 50px !important;
  }
  .ms-xxl-50 {
    margin-left: 50px !important;
  }
  .me-xxl-50 {
    margin-right: 50px !important;
  }
  .mt-xxl-50 {
    margin-top: 50px !important;
  }
  .mb-xxl-50 {
    margin-bottom: 50px !important;
  }
  .ms-xxl-100 {
    margin-left: 100px !important;
  }
  .fs-xxl-30 {
    font-size: 30px !important;
  }
  .fs-xxl-25 {
    font-size: 25px !important;
  }
  .fs-xxl-20 {
    font-size: 20px !important;
  }
  .fs-xxl-18 {
    font-size: 18px !important;
  }
  .fs-xxl-16 {
    font-size: 16px !important;
  }
  .fs-xxl-15 {
    font-size: 15px !important;
  }
  .fs-xxl-14 {
    font-size: 14px !important;
  }
  .fs-xxl-13 {
    font-size: 13px !important;
  }
}
@media (min-width: 1600px) {
  .col-xxxl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-xxxl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-xxxl-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-xxxl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-xxxl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-xxxl-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-xxxl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-xxxl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-xxxl-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-xxxl-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-xxxl-11 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-xxxl-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .mb-xxxl-25 {
    margin-bottom: 25px !important;
  }
  .fs-xxxl-30 {
    font-size: 30px !important;
  }
  .fs-xxxl-25 {
    font-size: 25px !important;
  }
  .fs-xxxl-20 {
    font-size: 20px !important;
  }
  .fs-xxxl-18 {
    font-size: 18px !important;
  }
  .fs-xxxl-16 {
    font-size: 16px !important;
  }
  .fs-xxxl-15 {
    font-size: 15px !important;
  }
  .fs-xxxl-14 {
    font-size: 14px !important;
  }
  .fs-xxxl-13 {
    font-size: 13px !important;
  }
  .p-xxxl-0 {
    padding: 0 !important;
  }
  .ps-xxxl-0 {
    padding-left: 0 !important;
  }
  .pe-xxxl-0 {
    padding-right: 0 !important;
  }
  .pt-xxxl-0 {
    padding-top: 0 !important;
  }
  .pb-xxxl-0 {
    padding-bottom: 0 !important;
  }
  .p-xxxl-1 {
    padding: 0.25rem !important;
  }
  .ps-xxxl-1 {
    padding-left: 0.25rem !important;
  }
  .pe-xxxl-1 {
    padding-right: 0.25rem !important;
  }
  .pt-xxxl-1 {
    padding-top: 0.25rem !important;
  }
  .pb-xxxl-1 {
    padding-bottom: 0.25rem !important;
  }
  .p-xxxl-2 {
    padding: 0.5rem !important;
  }
  .ps-xxxl-2 {
    padding-left: 0.5rem !important;
  }
  .pe-xxxl-2 {
    padding-right: 0.5rem !important;
  }
  .pt-xxxl-2 {
    padding-top: 0.5rem !important;
  }
  .pb-xxxl-2 {
    padding-bottom: 0.5rem !important;
  }
  .p-xxxl-3 {
    padding: 1rem !important;
  }
  .ps-xxxl-3 {
    padding-left: 1rem !important;
  }
  .pe-xxxl-3 {
    padding-right: 1rem !important;
  }
  .pt-xxxl-3 {
    padding-top: 1rem !important;
  }
  .pb-xxxl-3 {
    padding-bottom: 1rem !important;
  }
  .p-xxxl-4 {
    padding: 1.5rem !important;
  }
  .ps-xxxl-4 {
    padding-left: 1.5rem !important;
  }
  .pe-xxxl-4 {
    padding-right: 1.5rem !important;
  }
  .pt-xxxl-4 {
    padding-top: 1.5rem !important;
  }
  .pb-xxxl-4 {
    padding-bottom: 1.5rem !important;
  }
  .p-xxxl-5 {
    padding: 5px !important;
  }
  .ps-xxxl-5 {
    padding-left: 5px !important;
  }
  .pe-xxxl-5 {
    padding-right: 5px !important;
  }
  .pt-xxxl-5 {
    padding-top: 5px !important;
  }
  .pb-xxxl-5 {
    padding-bottom: 5px !important;
  }
  .p-xxxl-8 {
    padding: 8px !important;
  }
  .ps-xxxl-8 {
    padding-left: 8px !important;
  }
  .pe-xxxl-8 {
    padding-right: 8px !important;
  }
  .pt-xxxl-8 {
    padding-top: 8px !important;
  }
  .pb-xxxl-8 {
    padding-bottom: 8px !important;
  }
  .p-xxxl-10 {
    padding: 10px !important;
  }
  .ps-xxxl-10 {
    padding-left: 10px !important;
  }
  .pe-xxxl-10 {
    padding-right: 10px !important;
  }
  .pt-xxxl-10 {
    padding-top: 10px !important;
  }
  .pb-xxxl-10 {
    padding-bottom: 10px !important;
  }
  .p-xxxl-11 {
    padding: 11px !important;
  }
  .ps-xxxl-11 {
    padding-left: 11px !important;
  }
  .pe-xxxl-11 {
    padding-right: 11px !important;
  }
  .pt-xxxl-11 {
    padding-top: 11px !important;
  }
  .pb-xxxl-11 {
    padding-bottom: 11px !important;
  }
  .p-xxxl-12 {
    padding: 12px !important;
  }
  .ps-xxxl-12 {
    padding-left: 12px !important;
  }
  .pe-xxxl-12 {
    padding-right: 12px !important;
  }
  .pt-xxxl-12 {
    padding-top: 12px !important;
  }
  .pb-xxxl-12 {
    padding-bottom: 12px !important;
  }
  .p-xxxl-15 {
    padding: 15px !important;
  }
  .ps-xxxl-15 {
    padding-left: 15px !important;
  }
  .pe-xxxl-15 {
    padding-right: 15px !important;
  }
  .pt-xxxl-15 {
    padding-top: 15px !important;
  }
  .pb-xxxl-15 {
    padding-bottom: 15px !important;
  }
  .pt-xxxl-18 {
    padding-top: 18px !important;
  }
  .pb-xxxl-18 {
    padding-bottom: 18px !important;
  }
  .p-xxxl-20 {
    padding: 20px !important;
  }
  .ps-xxxl-20 {
    padding-left: 20px !important;
  }
  .pe-xxxl-20 {
    padding-right: 20px !important;
  }
  .pt-xxxl-20 {
    padding-top: 20px !important;
  }
  .pb-xxxl-20 {
    padding-bottom: 20px !important;
  }
  .p-xxxl-25 {
    padding: 25px !important;
  }
  .ps-xxxl-25 {
    padding-left: 25px !important;
  }
  .pe-xxxl-25 {
    padding-right: 25px !important;
  }
  .pt-xxxl-25 {
    padding-top: 25px !important;
  }
  .pb-xxxl-25 {
    padding-bottom: 25px !important;
  }
  .p-xxxl-30 {
    padding: 30px !important;
  }
  .ps-xxxl-30 {
    padding-left: 30px !important;
  }
  .pe-xxxl-30 {
    padding-right: 30px !important;
  }
  .pt-xxxl-30 {
    padding-top: 30px !important;
  }
  .pb-xxxl-30 {
    padding-bottom: 30px !important;
  }
  .p-xxxl-35 {
    padding: 35px !important;
  }
  .ps-xxxl-35 {
    padding-left: 35px !important;
  }
  .pe-xxxl-35 {
    padding-right: 35px !important;
  }
  .pt-xxxl-35 {
    padding-top: 35px !important;
  }
  .pb-xxxl-35 {
    padding-bottom: 35px !important;
  }
  .p-xxxl-40 {
    padding: 40px !important;
  }
  .ps-xxxl-40 {
    padding-left: 40px !important;
  }
  .pe-xxxl-40 {
    padding-right: 40px !important;
  }
  .pt-xxxl-40 {
    padding-top: 40px !important;
  }
  .pb-xxxl-40 {
    padding-bottom: 40px !important;
  }
  .p-xxxl-45 {
    padding: 45px !important;
  }
  .ps-xxxl-45 {
    padding-left: 45px !important;
  }
  .pe-xxxl-45 {
    padding-right: 45px !important;
  }
  .pt-xxxl-45 {
    padding-top: 45px !important;
  }
  .pb-xxxl-45 {
    padding-bottom: 45px !important;
  }
  .p-xxxl-50 {
    padding: 50px !important;
  }
  .ps-xxxl-50 {
    padding-left: 50px !important;
  }
  .pe-xxxl-50 {
    padding-right: 50px !important;
  }
  .pt-xxxl-50 {
    padding-top: 50px !important;
  }
  .pb-xxxl-50 {
    padding-bottom: 50px !important;
  }
  .m-xxxl-0 {
    margin: 0 !important;
  }
  .ms-xxxl-0 {
    margin-left: 0 !important;
  }
  .me-xxxl-0 {
    margin-right: 0 !important;
  }
  .mt-xxxl-0 {
    margin-top: 0 !important;
  }
  .mb-xxxl-0 {
    margin-bottom: 0 !important;
  }
  .m-xxxl-1 {
    margin: 0.25rem !important;
  }
  .ms-xxxl-1 {
    margin-left: 0.25rem !important;
  }
  .me-xxxl-1 {
    margin-right: 0.25rem !important;
  }
  .mt-xxxl-1 {
    margin-top: 0.25rem !important;
  }
  .mb-xxxl-1 {
    margin-bottom: 0.25rem !important;
  }
  .m-xxxl-2 {
    margin: 0.5rem !important;
  }
  .ms-xxxl-2 {
    margin-left: 0.5rem !important;
  }
  .me-xxxl-2 {
    margin-right: 0.5rem !important;
  }
  .mt-xxxl-2 {
    margin-top: 0.5rem !important;
  }
  .mb-xxxl-2 {
    margin-bottom: 0.5rem !important;
  }
  .m-xxxl-3 {
    margin: 1rem !important;
  }
  .ms-xxxl-3 {
    margin-left: 1rem !important;
  }
  .me-xxxl-3 {
    margin-right: 1rem !important;
  }
  .mt-xxxl-3 {
    margin-top: 1rem !important;
  }
  .mb-xxxl-3 {
    margin-bottom: 1rem !important;
  }
  .m-xxxl-4 {
    margin: 1.5rem !important;
  }
  .ms-xxxl-4 {
    margin-left: 1.5rem !important;
  }
  .me-xxxl-4 {
    margin-right: 1.5rem !important;
  }
  .mt-xxxl-4 {
    margin-top: 1.5rem !important;
  }
  .mb-xxxl-4 {
    margin-bottom: 1.5rem !important;
  }
  .m-xxxl-5 {
    margin: 5px !important;
  }
  .ms-xxxl-5 {
    margin-left: 5px !important;
  }
  .me-xxxl-5 {
    margin-right: 5px !important;
  }
  .mt-xxxl-5 {
    margin-top: 5px !important;
  }
  .mb-xxxl-5 {
    margin-bottom: 5px !important;
  }
  .m-xxxl-8 {
    margin: 8px !important;
  }
  .ms-xxxl-8 {
    margin-left: 8px !important;
  }
  .me-xxxl-8 {
    margin-right: 8px !important;
  }
  .mt-xxxl-8 {
    margin-top: 8px !important;
  }
  .mb-xxxl-8 {
    margin-bottom: 8px !important;
  }
  .m-xxxl-10 {
    margin: 10px !important;
  }
  .ms-xxxl-10 {
    margin-left: 10px !important;
  }
  .me-xxxl-10 {
    margin-right: 10px !important;
  }
  .mt-xxxl-10 {
    margin-top: 10px !important;
  }
  .mb-xxxl-10 {
    margin-bottom: 10px !important;
  }
  .m-xxxl-11 {
    margin: 11px !important;
  }
  .ms-xxxl-11 {
    margin-left: 11px !important;
  }
  .me-xxxl-11 {
    margin-right: 11px !important;
  }
  .mt-xxxl-11 {
    margin-top: 11px !important;
  }
  .mb-xxxl-10 {
    margin-bottom: 10px !important;
  }
  .m-xxxl-12 {
    margin: 12px !important;
  }
  .ms-xxxl-12 {
    margin-left: 12px !important;
  }
  .me-xxxl-12 {
    margin-right: 12px !important;
  }
  .mt-xxxl-12 {
    margin-top: 12px !important;
  }
  .mb-xxxl-12 {
    margin-bottom: 12px !important;
  }
  .m-xxxl-15 {
    margin: 15px !important;
  }
  .ms-xxxl-15 {
    margin-left: 15px !important;
  }
  .me-xxxl-15 {
    margin-right: 15px !important;
  }
  .mt-xxxl-15 {
    margin-top: 15px !important;
  }
  .mb-xxxl-15 {
    margin-bottom: 15px !important;
  }
  .m-xxxl-20 {
    margin: 20px !important;
  }
  .ms-xxxl-20 {
    margin-left: 20px !important;
  }
  .me-xxxl-20 {
    margin-right: 20px !important;
  }
  .mt-xxxl-20 {
    margin-top: 20px !important;
  }
  .mb-xxxl-20 {
    margin-bottom: 20px !important;
  }
  .m-xxxl-25 {
    margin: 25px !important;
  }
  .ms-xxxl-25 {
    margin-left: 25px !important;
  }
  .me-xxxl-25 {
    margin-right: 25px !important;
  }
  .mt-xxxl-25 {
    margin-top: 25px !important;
  }
  .mb-xxxl-25 {
    margin-bottom: 25px !important;
  }
  .m-xxxl-30 {
    margin: 30px !important;
  }
  .ms-xxxl-30 {
    margin-left: 30px !important;
  }
  .me-xxxl-30 {
    margin-right: 30px !important;
  }
  .mt-xxxl-30 {
    margin-top: 30px !important;
  }
  .mb-xxxl-30 {
    margin-bottom: 30px !important;
  }
  .m-xxxl-35 {
    margin: 35px !important;
  }
  .ms-xxxl-35 {
    margin-left: 35px !important;
  }
  .me-xxxl-35 {
    margin-right: 35px !important;
  }
  .mt-xxxl-35 {
    margin-top: 35px !important;
  }
  .mb-xxxl-35 {
    margin-bottom: 35px !important;
  }
  .m-xxxl-40 {
    margin: 40px !important;
  }
  .ms-xxxl-40 {
    margin-left: 40px !important;
  }
  .me-xxxl-40 {
    margin-right: 40px !important;
  }
  .mt-xxxl-40 {
    margin-top: 40px !important;
  }
  .mb-xxxl-40 {
    margin-bottom: 40px !important;
  }
  .m-xxxl-45 {
    margin: 45px !important;
  }
  .ms-xxxl-45 {
    margin-left: 45px !important;
  }
  .me-xxxl-45 {
    margin-right: 45px !important;
  }
  .mt-xxxl-45 {
    margin-top: 45px !important;
  }
  .mb-xxxl-45 {
    margin-bottom: 45px !important;
  }
  .m-xxxl-50 {
    margin: 50px !important;
  }
  .ms-xxxl-50 {
    margin-left: 50px !important;
  }
  .me-xxxl-50 {
    margin-right: 50px !important;
  }
  .mt-xxxl-50 {
    margin-top: 50px !important;
  }
  .mb-xxxl-50 {
    margin-bottom: 50px !important;
  }
  .ms-xxxl-100 {
    margin-left: 100px !important;
  }
  .order-xxxl-1 {
    order: 1 !important;
  }
  .order-xxxl-2 {
    order: 2 !important;
  }
}
.header-area {
  top: 25px;
  right: 25px;
  left: 275px;
  z-index: 91;
  height: auto;
}
.header-area .header-left-side {
  position: relative;
}
.header-area .header-left-side button.header-burger-menu {
  margin-right: 22px;
  cursor: pointer;
  font-size: 22px;
  top: 3px;
  border: none;
}
.header-area .header-left-side button.header-burger-menu:hover {
  color: var(--splash-primary-color);
}
.header-area .header-left-side .search-box {
  width: 300px;
}
.header-area .header-left-side .search-box .form-control {
  background-color: #f5f4fa;
  padding-left: 15px;
  padding-right: 15px;
}
.header-area .header-left-side .search-box .default-btn {
  z-index: 6;
}
.header-area .header-right-side .dropdown {
  margin-left: 15px;
  margin-right: 15px;
}
.header-area .header-right-side .dropdown .dropdown-toggle {
  top: 6px;
  font-size: 21px;
  color: var(--splash-paragraph-color);
}
.header-area .header-right-side .dropdown .dropdown-toggle .dot-badge {
  top: -9px;
  right: -8px;
  width: 17px;
  height: 17px;
  font-size: 10px;
  line-height: 17px;
}
.header-area .header-right-side .dropdown .dropdown-toggle::after {
  display: none;
}
.header-area .header-right-side .dropdown .dropdown-toggle:hover {
  color: var(--splash-primary-color);
}
.header-area .header-right-side .dropdown .dropdown-menu {
  width: 375px;
  font-size: 14px;
  box-shadow: 0px 4px 34px rgba(101, 96, 240, 0.1);
  margin-top: 19px !important;
  margin-right: -10px !important;
  padding-top: 30px;
  padding-bottom: 25px;
}
.header-area .header-right-side .dropdown .dropdown-menu .title {
  padding-left: 28px;
  padding-right: 28px;
  padding-bottom: 27px;
}
.header-area .header-right-side .dropdown .dropdown-menu .title .link-btn {
  font-size: 13px;
}
.header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li {
  border-bottom: 1px dashed #e7e2e2;
  padding: 18px 28px 18px 87px;
  letter-spacing: 0.01em;
}
.header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li .icon {
  top: 50%;
  left: 28px;
  width: 44px;
  height: 44px;
  font-size: 20px;
  background: #f2f1f9;
  transform: translateY(-50%);
  color: var(--splash-primary-color);
}
.header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li .icon i,
.header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li .icon img {
  left: 0;
  right: 0;
  top: 50%;
  line-height: 1;
  position: absolute;
  transform: translateY(-50%);
  margin-left: auto;
  margin-right: auto;
}
.header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li img {
  top: 50%;
  left: 28px;
  transform: translateY(-50%);
}
.header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li span {
  font-size: 15px;
  margin-bottom: 3px;
}
.header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li span span {
  font-size: 12px;
}
.header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li .unread {
  top: 50%;
  right: 25px;
  transform: translateY(-50%);
}
.header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li:nth-child(2) .icon, .header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li:nth-child(5) .icon, .header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li:nth-child(8) .icon {
  background: #f3f7f9;
  color: #39b2de;
}
.header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li:nth-child(3) .icon, .header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li:nth-child(6) .icon, .header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li:nth-child(9) .icon {
  background: #f1faf8;
  color: #06b48a;
}
.header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li:first-child {
  border-top: 1px dashed #e7e2e2;
}
.header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li:last-child {
  border-bottom: 0;
}
.header-area .header-right-side .dropdown .dropdown-menu .dropdown-footer {
  padding-top: 18px;
}
.header-area .header-right-side .dropdown .dropdown-menu .dropdown-footer .link-btn {
  font-size: 13px;
}
.header-area .header-right-side .dropdown .dropdown-menu .dropdown-footer .link-btn::before {
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  width: 100%;
  content: "";
  position: absolute;
  transition: var(--transition);
  background: var(--splash-primary-color);
}
.header-area .header-right-side .dropdown .dropdown-menu .dropdown-footer .link-btn:hover::before {
  transform: scaleX(0);
}
.header-area .header-right-side .dropdown.apps-dropdown {
  margin-right: 8px;
}
.header-area .header-right-side .dropdown.apps-dropdown .dropdown-menu {
  width: 185px;
  margin-top: 19px !important;
}
.header-area .header-right-side .dropdown.apps-dropdown .dropdown-menu .dropdown-body li {
  padding-left: 75px;
}
.header-area .header-right-side .dropdown.apps-dropdown .dropdown-menu .dropdown-body li .icon {
  width: 36px;
  height: 36px;
}
.header-area .header-right-side .dropdown.language-dropdown {
  margin-right: 18px;
}
.header-area .header-right-side .dropdown.language-dropdown .dropdown-toggle {
  top: 2px;
  font-size: 14px;
  padding-left: 27px;
  padding-right: 17px;
}
.header-area .header-right-side .dropdown.language-dropdown .dropdown-toggle i {
  left: 0;
  top: 50%;
  line-height: 1;
  font-size: 20px;
  margin-top: 0.5px;
  position: absolute;
  transform: translateY(-50%);
  color: var(--splash-primary-color);
}
.header-area .header-right-side .dropdown.language-dropdown .dropdown-toggle::before {
  right: 0;
  top: 50%;
  line-height: 1;
  content: "\e9fe";
  position: absolute;
  transform: translateY(-50%);
  font-family: Phosphor-Bold;
  font-size: 12px;
}
.header-area .header-right-side .dropdown.language-dropdown .dropdown-toggle:hover {
  color: var(--splash-primary-color);
}
.header-area .header-right-side .dropdown.language-dropdown .dropdown-menu {
  width: 220px;
  margin-top: 27px !important;
}
.header-area .header-right-side .dropdown.language-dropdown .dropdown-menu .dropdown-body li {
  padding-left: 70px;
}
.header-area .header-right-side .dropdown.notification-dropdown .dropdown-menu {
  margin-right: -30px !important;
}
.header-area .header-right-side .dropdown.profile-dropdown {
  margin-left: 20px;
}
.header-area .header-right-side .dropdown.profile-dropdown .dropdown-toggle {
  top: 0;
}
.header-area .header-right-side .dropdown.profile-dropdown .dropdown-menu {
  margin-top: 13px !important;
  padding: 15px 0;
  width: 200px;
}
.header-area .header-right-side .dropdown.profile-dropdown .dropdown-menu .dropdown-body li {
  border: none;
  padding: 7px 20px 7px 44px;
}
.header-area .header-right-side .dropdown.profile-dropdown .dropdown-menu .dropdown-body li i {
  top: 50%;
  left: 20px;
  line-height: 1;
  margin-top: 0.5px;
  position: absolute;
  transform: translateY(-50%);
  color: var(--splash-primary-color);
}
.header-area .header-right-side .dropdown.profile-dropdown .dropdown-menu .dropdown-body li:hover {
  background-color: var(--splash-body-bg);
}
.header-area .header-right-side .dropdown:last-child {
  margin-right: 0;
}
.header-area .header-right-side .dropdown:first-child {
  margin-left: 0;
}
.header-area .header-right-side .dark-swtich-btn {
  margin-right: 4px;
}
.header-area .header-right-side .dark-swtich-btn .switch-toggle {
  top: 1px;
  width: 36px;
  height: 36px;
  font-size: 17px;
  background-color: #f2f1f9;
  color: var(--splash-primary-color);
}
.header-area .header-right-side .dark-swtich-btn .switch-toggle i {
  left: 0;
  right: 0;
  top: 50%;
  line-height: 1;
  margin-top: 1px;
  position: absolute;
  transform: translateY(-50%);
}
.header-area .header-right-side .dark-swtich-btn .switch-toggle:hover {
  background-color: var(--splash-primary-color);
  color: var(--splash-white-color);
}
.header-area.sticky {
  top: 0;
  box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15);
}

.sidebar-hide .header-area {
  left: 85px;
}
.sidebar-hide .header-area.sticky {
  left: 85px;
}

.dark .header-area .header-left-side .search-box .form-control {
  background-color: var(--splash-black-color);
}
.dark .header-area .header-right-side .dropdown .dropdown-toggle {
  color: #bcbbc7;
}
.dark .header-area .header-right-side .dropdown .dropdown-toggle:hover {
  color: var(--splash-primary-color);
}
.dark .header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li {
  border-bottom-color: #45445e;
}
.dark .header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li:first-child {
  border-top-color: #45445e;
}
.dark .header-area .header-right-side .dropdown.profile-dropdown .dropdown-menu .dropdown-body li:hover {
  background-color: var(--splash-black-color);
}
.dark .header-area .header-right-side .dark-swtich-btn .switch-toggle {
  background-color: var(--splash-black-color);
}
.dark .header-area .header-right-side .dark-swtich-btn .switch-toggle:hover {
  background-color: var(--splash-primary-color);
}
.dark .header-area.sticky {
  box-shadow: unset;
}

@media only screen and (max-width: 767px) {
  .header-area {
    top: 15px;
    left: 15px;
    right: 15px;
  }
  .header-area .header-left-side {
    margin-bottom: 10px;
  }
  .header-area .header-left-side button.header-burger-menu {
    margin-right: 15px;
    font-size: 20px;
  }
  .header-area .header-left-side .search-box {
    width: 225px;
  }
  .header-area .header-left-side .search-box .form-control {
    padding: 10px 12px;
  }
  .header-area .header-right-side .dropdown {
    margin-left: 10px;
    margin-right: 10px;
  }
  .header-area .header-right-side .dropdown .dropdown-toggle {
    top: 6px;
    font-size: 19px;
  }
  .header-area .header-right-side .dropdown .dropdown-toggle .dot-badge {
    top: -6px;
    right: -6px;
    width: 14px;
    height: 14px;
    font-size: 9px;
    line-height: 14px;
  }
  .header-area .header-right-side .dropdown .dropdown-menu {
    width: 280px;
    left: auto !important;
    right: auto !important;
    margin-left: -21px !important;
    margin-top: 17px !important;
    margin-right: 0 !important;
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .header-area .header-right-side .dropdown .dropdown-menu .title {
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 15px;
  }
  .header-area .header-right-side .dropdown .dropdown-menu .dropdown-body {
    font-size: 13px;
  }
  .header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li {
    padding: 15px 15px 15px 67px;
  }
  .header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li .icon {
    left: 15px;
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
  .header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li img {
    left: 15px;
  }
  .header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li span {
    font-size: 14px;
  }
  .header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li .unread {
    right: 15px;
  }
  .header-area .header-right-side .dropdown .dropdown-menu .dropdown-footer {
    padding-top: 12px;
  }
  .header-area .header-right-side .dropdown.apps-dropdown {
    margin-right: 8px;
  }
  .header-area .header-right-side .dropdown.apps-dropdown .dropdown-menu {
    width: 155px;
    margin-top: 17px !important;
  }
  .header-area .header-right-side .dropdown.apps-dropdown .dropdown-menu .dropdown-body li {
    padding-left: 62px;
  }
  .header-area .header-right-side .dropdown.email-dropdown .dropdown-menu .dropdown-body li img {
    width: 40px;
    height: 40px;
  }
  .header-area .header-right-side .dropdown.language-dropdown {
    margin-right: 10px;
  }
  .header-area .header-right-side .dropdown.language-dropdown .dropdown-toggle {
    top: 2px;
    font-size: 13px;
    padding-left: 0;
    padding-right: 14px;
  }
  .header-area .header-right-side .dropdown.language-dropdown .dropdown-toggle i {
    display: none;
  }
  .header-area .header-right-side .dropdown.language-dropdown .dropdown-toggle::before {
    margin-top: 1px;
    font-size: 10px;
  }
  .header-area .header-right-side .dropdown.language-dropdown .dropdown-menu {
    width: 170px;
    margin-top: 24px !important;
  }
  .header-area .header-right-side .dropdown.language-dropdown .dropdown-menu .dropdown-body li {
    padding-left: 55px;
  }
  .header-area .header-right-side .dropdown.notification-dropdown .dropdown-menu {
    margin-right: 0 !important;
  }
  .header-area .header-right-side .dropdown.profile-dropdown {
    margin-left: 10px;
  }
  .header-area .header-right-side .dropdown.profile-dropdown .dropdown-toggle img {
    width: 40px;
    height: 40px;
  }
  .header-area .header-right-side .dropdown.profile-dropdown .dropdown-menu {
    left: auto !important;
    right: 0 !important;
    padding: 6px 0;
    width: 150px;
  }
  .header-area .header-right-side .dropdown.profile-dropdown .dropdown-menu .dropdown-body li {
    border: none;
    padding: 5px 15px 5px 38px;
  }
  .header-area .header-right-side .dropdown.profile-dropdown .dropdown-menu .dropdown-body li i {
    left: 15px;
  }
  .header-area .header-right-side .dark-swtich-btn {
    margin-right: 2px;
  }
  .header-area .header-right-side .dark-swtich-btn .switch-toggle {
    top: 3px;
    width: 30px;
    height: 30px;
    font-size: 15px;
  }
  .sidebar-hide .header-area {
    left: 15px;
  }
  .sidebar-hide .header-area.sticky {
    left: 15px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .header-area .header-left-side .search-box {
    width: 300px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .header-area {
    left: 25px;
    right: 25px;
  }
  .header-area .header-left-side button.header-burger-menu {
    margin-right: 18px;
    font-size: 20px;
  }
  .header-area .header-left-side .search-box {
    width: 290px;
  }
  .header-area .header-right-side .dropdown {
    margin-left: 12px;
    margin-right: 12px;
  }
  .header-area .header-right-side .dropdown .dropdown-toggle {
    top: 4px;
    font-size: 20px;
  }
  .header-area .header-right-side .dropdown .dropdown-toggle .dot-badge {
    top: -7px;
    right: -5px;
    width: 15px;
    height: 15px;
    font-size: 9px;
    line-height: 15px;
  }
  .header-area .header-right-side .dropdown .dropdown-menu {
    width: 300px;
    padding-top: 20px;
    padding-bottom: 20px;
  }
  .header-area .header-right-side .dropdown .dropdown-menu .title {
    padding-left: 20px;
    padding-right: 20px;
    padding-bottom: 20px;
  }
  .header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li {
    padding: 18px 20px 18px 78px;
  }
  .header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li .icon {
    left: 20px;
  }
  .header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li img {
    left: 20px;
  }
  .header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li span {
    font-size: 14px;
  }
  .header-area .header-right-side .dropdown .dropdown-menu .dropdown-body li .unread {
    right: 20px;
  }
  .header-area .header-right-side .dropdown .dropdown-menu .dropdown-footer {
    padding-top: 15px;
  }
  .header-area .header-right-side .dropdown.apps-dropdown {
    margin-right: 8px;
  }
  .header-area .header-right-side .dropdown.apps-dropdown .dropdown-menu {
    width: 150px;
  }
  .header-area .header-right-side .dropdown.apps-dropdown .dropdown-menu .dropdown-body li {
    padding-left: 65px;
  }
  .header-area .header-right-side .dropdown.language-dropdown {
    margin-right: 12px;
  }
  .header-area .header-right-side .dropdown.language-dropdown .dropdown-toggle {
    top: 2px;
    padding-left: 24px;
    padding-right: 15px;
  }
  .header-area .header-right-side .dropdown.language-dropdown .dropdown-toggle i {
    font-size: 18px;
    margin-top: 0;
  }
  .header-area .header-right-side .dropdown.language-dropdown .dropdown-menu {
    width: 200px;
  }
  .header-area .header-right-side .dropdown.language-dropdown .dropdown-menu .dropdown-body li {
    padding-left: 63px;
  }
  .header-area .header-right-side .dropdown.profile-dropdown {
    margin-left: 12px;
  }
  .header-area .header-right-side .dark-swtich-btn {
    margin-right: 5px;
  }
  .header-area .header-right-side .dark-swtich-btn .switch-toggle {
    top: 1px;
    width: 35px;
    height: 35px;
    font-size: 16px;
  }
  .sidebar-hide .header-area {
    left: 25px;
  }
  .sidebar-hide .header-area.sticky {
    left: 25px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .header-area {
    left: 25px;
    right: 25px;
  }
  .header-area .header-right-side .dropdown {
    margin-left: 15px;
    margin-right: 15px;
  }
  .header-area .header-right-side .dropdown .dropdown-toggle {
    top: 5px;
    font-size: 20px;
  }
  .header-area .header-right-side .dropdown.language-dropdown .dropdown-toggle {
    top: 3px;
  }
  .header-area .header-right-side .dark-swtich-btn .switch-toggle {
    top: 1px;
  }
  .sidebar-hide .header-area {
    left: 25px;
  }
  .sidebar-hide .header-area.sticky {
    left: 25px;
  }
}
@media only screen and (min-width: 1600px) {
  .header-area {
    left: 320px;
    right: 40px;
  }
  .sidebar-hide .header-area {
    left: 100px;
  }
  .sidebar-hide .header-area.sticky {
    left: 100px;
  }
}
.sidebar-area {
  width: 250px;
  z-index: 99;
}
.sidebar-area .logo {
  z-index: 99;
  margin-right: 5px;
  padding-top: 30px;
  padding-left: 30px;
  padding-right: 30px;
}
.sidebar-area .logo a span {
  font-size: 22px;
  letter-spacing: 0.04em;
}
.sidebar-area .logo .sidebar-burger-menu {
  top: 50%;
  opacity: 0;
  right: 15px;
  font-size: 22px;
  cursor: pointer;
  visibility: hidden;
  transform: translateY(-50%);
  transition: var(--transition);
  color: var(--splash-white-color);
}
.sidebar-area .border-top,
.sidebar-area .border-bottom {
  border-color: #3c3a56 !important;
  padding-top: 30px;
}
.sidebar-area .sidebar-menu {
  height: 100%;
  overflow: hidden;
  overflow-y: scroll;
  padding-top: 129px;
  padding-left: 20px;
  padding-right: 20px;
  padding-bottom: 30px;
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item {
  margin-bottom: 22px;
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-button {
  text-decoration: none;
  padding: 0 25px 0 40px;
  letter-spacing: 0.01em;
  transition: var(--transition);
  color: var(--splash-white-color);
  font-size: 16px;
  font-weight: 400;
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-button i {
  top: 50%;
  left: 10px;
  font-size: 18px;
  line-height: 0.01;
  position: absolute;
  transform: translateY(-50%);
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-button::after {
  top: 50%;
  right: 10px;
  width: auto;
  height: auto;
  margin-left: 0;
  content: "\f105";
  color: #bcbbc7;
  position: absolute;
  background: unset !important;
  transform: translateY(-50%) rotate(180deg);
  font-family: flaticon;
  font-size: 12px;
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-button:hover {
  color: var(--splash-white-color);
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-button.collapsed {
  color: #bcbbc7;
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-button.collapsed:hover {
  color: var(--splash-white-color);
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-button.collapsed::after {
  transform: translateY(-50%);
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .sidebar-nav-link {
  color: #bcbbc7;
  position: relative;
  text-decoration: none;
  letter-spacing: 0.01em;
  padding: 0 25px 0 40px;
  transition: var(--transition);
  font-size: 16px;
  font-weight: 400;
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .sidebar-nav-link i {
  top: 50%;
  left: 10px;
  font-size: 18px;
  line-height: 0.01;
  position: absolute;
  transform: translateY(-50%);
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .sidebar-nav-link.active, .sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .sidebar-nav-link:hover {
  color: var(--splash-white-color);
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-collapse .accordion-body {
  padding-left: 0;
  padding-right: 0;
  padding-bottom: 0;
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-collapse .accordion-body .sidebar-sub-menu .sidebar-sub-menu-item {
  margin-bottom: 2px;
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-collapse .accordion-body .sidebar-sub-menu .sidebar-sub-menu-item .sidebar-sub-menu-link {
  display: block;
  color: #bcbbc7;
  position: relative;
  border-radius: 2px;
  text-decoration: none;
  letter-spacing: 0.01em;
  transition: var(--transition);
  padding-top: 11px;
  padding-left: 40px;
  padding-bottom: 11px;
  font-size: 15px;
  font-weight: 400;
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-collapse .accordion-body .sidebar-sub-menu .sidebar-sub-menu-item .sidebar-sub-menu-link::before {
  top: 50%;
  left: 17px;
  width: 5px;
  height: 5px;
  content: "";
  border-radius: 50%;
  position: absolute;
  background: #a09fb0;
  transform: translateY(-50%);
  transition: var(--transition);
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-collapse .accordion-body .sidebar-sub-menu .sidebar-sub-menu-item .sidebar-sub-menu-link .new-badge {
  top: -1px;
  z-index: 1;
  padding: 0 5px;
  font-size: 11px;
  margin-left: 8px;
  position: relative;
  background: #6fd3f7;
  color: var(--splash-black-color);
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-collapse .accordion-body .sidebar-sub-menu .sidebar-sub-menu-item .sidebar-sub-menu-link .new-badge::before {
  top: 50%;
  width: 8px;
  height: 8px;
  content: "";
  z-index: -1;
  left: -2.5px;
  position: absolute;
  background: #6fd3f7;
  transform: translateY(-50%) rotate(45deg);
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-collapse .accordion-body .sidebar-sub-menu .sidebar-sub-menu-item .sidebar-sub-menu-link.active {
  color: var(--splash-white-color);
  background-color: var(--splash-primary-color);
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-collapse .accordion-body .sidebar-sub-menu .sidebar-sub-menu-item .sidebar-sub-menu-link.active::before {
  background: var(--splash-white-color);
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-collapse .accordion-body .sidebar-sub-menu .sidebar-sub-menu-item .sidebar-sub-menu-link:hover {
  color: var(--splash-white-color);
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-collapse .accordion-body .sidebar-sub-menu .sidebar-sub-menu-item .sidebar-sub-menu-link:hover::before {
  background: var(--splash-white-color);
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-collapse .accordion-body .sidebar-sub-menu .sidebar-sub-menu-item .accordion-button {
  padding-top: 11px;
  padding-bottom: 11px;
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-collapse .accordion-body .sidebar-sub-menu .sidebar-sub-menu-item .accordion-button::before {
  top: 50%;
  left: 17px;
  width: 5px;
  height: 5px;
  content: "";
  border-radius: 50%;
  position: absolute;
  background: #a09fb0;
  transform: translateY(-50%);
  transition: var(--transition);
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-collapse .accordion-body .sidebar-sub-menu .sidebar-sub-menu-item .accordion-collapse .accordion-body {
  padding-top: 0;
  padding-left: 24px;
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-collapse .accordion-body .sidebar-sub-menu .sidebar-sub-menu-item:last-child {
  margin-bottom: 0;
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item.sub-title span {
  letter-spacing: 0.05em;
  color: #706e93;
  font-size: 12px;
  margin-top: 32px;
  margin-bottom: 25px;
  padding-left: 10px;
  padding-right: 10px;
}
.sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item:last-child {
  margin-bottom: 0;
}
.sidebar-area .sidebar-menu::-webkit-scrollbar {
  -webkit-appearance: none;
}
.sidebar-area .sidebar-menu::-webkit-scrollbar:vertical {
  width: 5px;
}
.sidebar-area .sidebar-menu::-webkit-scrollbar:horizontal {
  height: 5px;
}
.sidebar-area .sidebar-menu::-webkit-scrollbar-thumb {
  border-radius: 50px;
  background: #6d6c7d;
}
.sidebar-area .sidebar-menu::-webkit-scrollbar-track {
  background: #0d0c1d;
}

.dark .sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-button::after {
  filter: unset;
}

@media only screen and (max-width: 767px) {
  .sidebar-area {
    left: -100% !important;
  }
  .sidebar-area .logo {
    padding-top: 20px;
    padding-left: 20px;
    padding-right: 20px;
  }
  .sidebar-area .logo a span {
    font-size: 20px;
  }
  .sidebar-area .logo .sidebar-burger-menu {
    opacity: 1;
    font-size: 20px;
    visibility: visible;
  }
  .sidebar-area .border-top,
  .sidebar-area .border-bottom {
    padding-top: 20px;
  }
  .sidebar-area .sidebar-menu {
    padding-top: 90px;
    padding-left: 10px;
    padding-right: 10px;
    padding-bottom: 20px;
  }
  .sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item {
    margin-bottom: 20px;
  }
  .sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-button {
    padding: 0 25px 0 38px;
    font-size: 15px;
  }
  .sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-button i {
    left: 10px;
  }
  .sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .sidebar-nav-link {
    padding: 0 25px 0 38px;
    font-size: 15px;
  }
  .sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .sidebar-nav-link i {
    left: 10px;
  }
  .sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-collapse .accordion-body .sidebar-sub-menu .sidebar-sub-menu-item .sidebar-sub-menu-link {
    font-size: 14px;
    padding-top: 10px;
    padding-left: 38px;
    padding-bottom: 10px;
  }
  .sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-collapse .accordion-body .sidebar-sub-menu .sidebar-sub-menu-item .accordion-button {
    padding-top: 10px;
    padding-bottom: 10px;
  }
  .sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-collapse .accordion-body .sidebar-sub-menu .sidebar-sub-menu-item .accordion-collapse .accordion-body {
    padding-left: 20px;
  }
  .sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item.sub-title span {
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .sidebar-area.active {
    left: 0 !important;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .sidebar-area {
    left: -100% !important;
  }
  .sidebar-area .logo .sidebar-burger-menu {
    opacity: 1;
    visibility: visible;
  }
  .sidebar-hide .sidebar-area.active,
  .sidebar-show .sidebar-area.active {
    left: 0 !important;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .sidebar-area {
    left: -100% !important;
  }
  .sidebar-area .logo .sidebar-burger-menu {
    opacity: 1;
    visibility: visible;
  }
  .sidebar-hide .sidebar-area.active,
  .sidebar-show .sidebar-area.active {
    left: 0 !important;
  }
}
@media only screen and (min-width: 1200px) {
  .sidebar-area.active {
    width: 60px;
  }
  .sidebar-area.active .logo {
    margin-right: 0;
    padding-left: 15px;
    padding-right: 15px;
  }
  .sidebar-area.active .logo a span {
    display: none;
  }
  .sidebar-area.active .sidebar-menu {
    overflow-y: hidden;
    padding-top: 120px;
    padding-left: 19px;
    padding-right: 15px;
  }
  .sidebar-area.active .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-button {
    padding: 0;
    width: 20px;
    height: 20px;
    text-align: center;
  }
  .sidebar-area.active .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-button i {
    left: 0;
    right: 0;
  }
  .sidebar-area.active .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-button::after {
    display: none;
  }
  .sidebar-area.active .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-button .title {
    display: none;
  }
  .sidebar-area.active .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .sidebar-nav-link {
    padding: 0;
    width: 20px;
    height: 20px;
    text-align: center;
  }
  .sidebar-area.active .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .sidebar-nav-link i {
    left: 0;
    right: 0;
  }
  .sidebar-area.active .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .sidebar-nav-link .title {
    display: none;
  }
  .sidebar-area.active .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-collapse .accordion-body {
    display: none;
  }
  .sidebar-area.active .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item.sub-title {
    display: none;
  }
  .sidebar-area.active:hover {
    width: 250px;
  }
  .sidebar-area.active:hover .logo {
    margin-right: 5px;
    padding-left: 30px;
    padding-right: 30px;
  }
  .sidebar-area.active:hover .logo a span {
    display: block;
  }
  .sidebar-area.active:hover .logo .sidebar-burger-menu {
    opacity: 1;
    visibility: visible;
  }
  .sidebar-area.active:hover .sidebar-menu {
    overflow-y: scroll;
    padding-top: 129px;
    padding-left: 20px;
    padding-right: 20px;
  }
  .sidebar-area.active:hover .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-button {
    width: auto;
    height: auto;
    text-align: start;
    padding: 0 25px 0 40px;
  }
  .sidebar-area.active:hover .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-button i {
    left: 10px;
    right: auto;
  }
  .sidebar-area.active:hover .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-button::after {
    display: block;
  }
  .sidebar-area.active:hover .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-button .title {
    display: block;
  }
  .sidebar-area.active:hover .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .sidebar-nav-link {
    width: auto;
    height: auto;
    text-align: start;
    padding: 0 25px 0 40px;
  }
  .sidebar-area.active:hover .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .sidebar-nav-link i {
    left: 10px;
    right: auto;
  }
  .sidebar-area.active:hover .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .sidebar-nav-link .title {
    display: block;
  }
  .sidebar-area.active:hover .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-collapse .accordion-body {
    display: block;
  }
  .sidebar-area.active:hover .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item.sub-title {
    display: block;
  }
}
@media only screen and (min-width: 1600px) {
  .sidebar-area {
    width: 280px;
  }
  .sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .accordion-button i {
    line-height: 1;
  }
  .sidebar-area .sidebar-menu .sidebar-navbar-nav .sidebar-nav-item .sidebar-nav-link i {
    line-height: 1;
  }
  .sidebar-hide .sidebar-area {
    width: 60px;
  }
  .sidebar-hide .sidebar-area:hover {
    width: 280px;
  }
}
.welcome-box {
  background: linear-gradient(180deg, rgba(187, 196, 245, 0.6) 0%, rgba(183, 193, 244, 0.6) 48.44%, rgba(175, 184, 239, 0.6) 71.87%, rgba(172, 182, 237, 0.6) 100%);
}
.welcome-box .card-body {
  padding-left: 30px;
}
.welcome-box .title {
  top: -12px;
}
.welcome-box ul {
  margin-top: 45px;
}
.welcome-box ul li {
  padding-left: 28px;
  padding-right: 25px;
}
.welcome-box ul li span {
  font-size: 24px;
}
.welcome-box ul li::before {
  top: 0;
  right: 0;
  bottom: 0;
  content: "";
  width: 1.5px;
  margin-top: 3px;
  position: absolute;
  background: rgba(255, 255, 255, 0.3);
}
.welcome-box ul li:first-child {
  padding-left: 0;
}
.welcome-box ul li:last-child {
  padding-right: 0;
}
.welcome-box ul li:last-child::before {
  display: none;
}

.stats-box .card-body {
  padding-top: 28px;
  padding-bottom: 28px;
}
.stats-box .icon {
  width: 68px;
  height: 68px;
  font-size: 30px;
  background: #F8F8FB;
}
.stats-box .icon i {
  left: 0;
  right: 0;
  top: 50%;
  line-height: 1;
  margin-top: 1.5px;
  position: absolute;
  transform: translateY(-50%);
}
.stats-box h4 {
  font-size: 24px;
}

.expected-earnings-box h4 {
  font-size: 24px;
}
.expected-earnings-box .list li {
  margin-bottom: 2px;
  padding-left: 17px;
}
.expected-earnings-box .list li::before {
  left: 0;
  top: 50%;
  width: 10px;
  content: "";
  height: 10px;
  border-radius: 50%;
  position: absolute;
  transform: translateY(-50%);
  background: var(--splash-info-color);
}
.expected-earnings-box .list li:nth-child(2)::before, .expected-earnings-box .list li:nth-child(5)::before {
  background: var(--splash-success-color);
}
.expected-earnings-box .list li:nth-child(3)::before, .expected-earnings-box .list li:nth-child(6)::before {
  background: var(--splash-primary-color);
}
.expected-earnings-box .list li:last-child {
  margin-bottom: 0;
}
.expected-earnings-box .chart {
  top: 50px;
  right: -65px;
  position: absolute;
}

.daily-sales-box h4 {
  font-size: 24px;
}
.daily-sales-box .chart {
  border-bottom: 1px dashed #d9e9ef;
}

.monthly-revenue-box h4 {
  font-size: 24px;
}

.monthly-new-customers-box .card-body {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.monthly-new-customers-box h4 {
  font-size: 24px;
}
.monthly-new-customers-box .users-list div {
  width: 45px;
  height: 45px;
  margin-right: -20px;
  border: 2px solid var(--splash-white-color);
  filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));
}
.monthly-new-customers-box .users-list div:last-child {
  margin-right: 0;
}

.sales-pos-location-box .list li {
  margin-bottom: 5px;
  padding-left: 17px;
}
.sales-pos-location-box .list li::before {
  left: 0;
  top: 50%;
  width: 10px;
  content: "";
  height: 10px;
  margin-top: -0.5px;
  border-radius: 50%;
  position: absolute;
  transform: translateY(-50%);
  background: var(--splash-primary-color);
}
.sales-pos-location-box .list li:nth-child(2)::before {
  background: var(--splash-success-color);
}
.sales-pos-location-box .list li:last-child {
  margin-bottom: 0;
}
.sales-pos-location-box .card-body.pb-0 {
  padding-bottom: 0 !important;
}

.website-visitors .card-body.pb-0 {
  padding-bottom: 0 !important;
}

.top-selling-products-box .list li {
  border-bottom: 0.5px dashed #f0f0f0;
  padding-bottom: 12px;
  margin-bottom: 12px;
}
.top-selling-products-box .list li:last-child {
  margin-bottom: 0;
}

.table-responsive {
  margin-bottom: 0;
}

.dark .welcome-box {
  background: #34334a;
}
.dark .welcome-box ul li::before {
  background: rgba(255, 255, 255, 0.05);
}
.dark .stats-box .icon {
  background: var(--splash-black-color);
}
.dark .daily-sales-box .chart {
  border-bottom-color: #45445e;
}
.dark .monthly-new-customers-box .users-list div {
  border-color: #45445e;
}
.dark .top-selling-products-box .list li {
  border-bottom-color: #45445e;
}

@media only screen and (max-width: 767px) {
  .welcome-box .card-body {
    padding-left: 15px;
  }
  .welcome-box .title {
    top: 0;
  }
  .welcome-box ul {
    margin-top: 15px;
  }
  .welcome-box ul li {
    padding-left: 13px;
    padding-right: 13px;
  }
  .welcome-box ul li span {
    font-size: 20px;
  }
  .expected-earnings-box h4 {
    font-size: 20px;
  }
  .expected-earnings-box .list li::before {
    margin-top: -0.5px;
  }
  .expected-earnings-box .chart {
    top: 10px;
    right: -80px;
  }
  .daily-sales-box h4 {
    font-size: 20px;
  }
  .monthly-revenue-box h4 {
    font-size: 20px;
  }
  .monthly-new-customers-box .card-body {
    display: block;
  }
  .monthly-new-customers-box h4 {
    font-size: 20px;
  }
  .monthly-new-customers-box .users-list {
    margin-top: 15px;
  }
  .monthly-new-customers-box .users-list div {
    width: 35px;
    height: 35px;
    border-width: 1px;
    margin-right: -15px;
  }
  .stats-box .card-body {
    padding-top: 20px;
    padding-bottom: 20px;
  }
  .stats-box h4 {
    font-size: 20px;
  }
  .top-selling-products-box .list li {
    padding-bottom: 10px;
    margin-bottom: 10px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .welcome-box .card-body {
    padding-left: 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .welcome-box .card-body {
    padding-left: 25px;
  }
  .welcome-box .title {
    top: -15px;
  }
  .welcome-box ul {
    margin-top: 25px;
  }
  .stats-box .card-body {
    padding-top: 25px;
    padding-bottom: 25px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .monthly-new-customers-box .card-body {
    display: block;
  }
  .monthly-new-customers-box .users-list {
    margin-top: 25px;
  }
}
@media only screen and (min-width: 1600px) {
  .welcome-box .card-body {
    padding-left: 50px;
  }
  .stats-box .icon {
    width: 78px;
    height: 78px;
  }
  .monthly-revenue-box .mt-25 {
    margin-top: 35px !important;
  }
  .monthly-new-customers-box .card-body {
    display: block;
  }
  .monthly-new-customers-box .users-list {
    margin-top: 33px;
  }
}
.stats-item h4 {
  font-size: 20px;
}
.stats-item .icon {
  width: 78px;
  height: 78px;
  font-size: 32px;
  background: #F8F8FB;
}
.stats-item .icon i {
  left: 0;
  right: 0;
  top: 50%;
  line-height: 1;
  margin-top: 1.5px;
  position: absolute;
  transform: translateY(-50%);
}
.stats-item .users-list div {
  width: 45px;
  height: 45px;
  margin-right: -20px;
  border: 2px solid var(--splash-white-color);
  filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));
}
.stats-item .users-list div:last-child {
  margin-right: 0;
}
.stats-item .chart {
  margin: -25px -10px -15px 0;
}

.to-do-list-box .to-do-list .to-do-list-item {
  border-top: 1px dashed #d9e9ef;
  padding-top: 19.1px;
  padding-bottom: 19.1px;
}
.to-do-list-box .to-do-list .to-do-list-item .dropdown-toggle {
  color: #A09FB0;
  font-size: 20px;
}
.to-do-list-box .to-do-list .to-do-list-item .dropdown-toggle::after {
  display: none;
}
.to-do-list-box .to-do-list .to-do-list-item .dropdown-toggle:hover {
  color: var(--splash-primary-color);
}
.to-do-list-box .to-do-list .to-do-list-item .action-buttons button {
  font-size: 20px;
}
.to-do-list-box .to-do-list .to-do-list-item .action-buttons button:hover {
  color: var(--splash-primary-color) !important;
}
.to-do-list-box .to-do-list .to-do-list-item:last-child {
  padding-bottom: 0;
}
.to-do-list-box .to-do-list.style-two .to-do-list-item {
  padding-top: 15.3px;
  padding-bottom: 15.3px;
}
.to-do-list-box .to-do-list.style-two .to-do-list-item:last-child {
  padding-bottom: 0;
}
.to-do-list-box .to-do-list.style-two .to-do-list-item .form-check-primary .form-check-input {
  position: relative;
  top: 2px;
}
.to-do-list-box .to-do-list.style-three .to-do-list-item {
  padding-top: 13px;
  padding-bottom: 13px;
}
.to-do-list-box .to-do-list.style-three .to-do-list-item:last-child {
  padding-bottom: 0;
}

.active-tasks-box .active-tasks-list .active-task-list-item {
  border-top: 1px dashed #d9e9ef;
  padding-top: 16.8px;
  padding-bottom: 16.8px;
}
.active-tasks-box .active-tasks-list .active-task-list-item .task-info {
  background: #F3F7F9;
  border-radius: 50px;
  margin-left: 5%;
}
.active-tasks-box .active-tasks-list .active-task-list-item .task-info .users-list {
  padding: 4px;
  margin-right: 100px;
  border-radius: 50px;
}
.active-tasks-box .active-tasks-list .active-task-list-item .task-info .users-list div {
  width: 33px;
  height: 33px;
  margin-right: -10px;
  border: 1px solid var(--splash-white-color);
}
.active-tasks-box .active-tasks-list .active-task-list-item .task-info .users-list div:last-child {
  margin-right: 0;
}
.active-tasks-box .active-tasks-list .active-task-list-item:nth-child(2) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(7) .task-info {
  margin-left: 25%;
}
.active-tasks-box .active-tasks-list .active-task-list-item:nth-child(3) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(8) .task-info {
  margin-left: 35%;
}
.active-tasks-box .active-tasks-list .active-task-list-item:nth-child(4) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(9) .task-info {
  margin-left: 10%;
}
.active-tasks-box .active-tasks-list .active-task-list-item:nth-child(5) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(10) .task-info {
  margin-left: 30%;
}
.active-tasks-box .active-tasks-list .active-task-list-item:last-child {
  padding-bottom: 0;
}

.working-schedule-box .schedule-date-list {
  border-bottom: 1px dashed #d9e9ef;
  padding-bottom: 20px;
}
.working-schedule-box .schedule-date-list li {
  border-radius: 30px;
  padding-top: 18px;
  padding-bottom: 18px;
  margin-left: 5px;
  margin-right: 5px;
}
.working-schedule-box .schedule-date-list li.active {
  background-color: var(--splash-primary-color);
  color: var(--splash-white-color) !important;
}
.working-schedule-box .schedule-date-list li.active span {
  color: var(--splash-white-color) !important;
}
.working-schedule-box .info-list li {
  margin-right: 15px;
}
.working-schedule-box .info-list li:last-child {
  margin-right: 0;
}
.working-schedule-box .schedule-list .content h6 {
  font-size: 18px;
}
.working-schedule-box .schedule-list .content .icon {
  width: 28px;
  height: 28px;
}
.working-schedule-box .schedule-list .content .icon i {
  left: 0;
  right: 0;
  top: 50%;
  line-height: 1;
  margin-top: 1px;
  position: absolute;
  transform: translateY(-50%);
}
.working-schedule-box .schedule-list .list-item {
  margin-bottom: 10px;
}
.working-schedule-box .schedule-list .list-item::before {
  top: 0;
  left: 0;
  bottom: 0;
  width: 4px;
  content: "";
  position: absolute;
}
.working-schedule-box .schedule-list .list-item.bg-f2f1f9::before {
  background: var(--splash-primary-color);
}
.working-schedule-box .schedule-list .list-item.bg-f3f7f9::before {
  background: var(--splash-info-light-color);
}
.working-schedule-box .schedule-list .list-item.bg-ecf3f2::before {
  background: var(--splash-success-color);
}
.working-schedule-box .schedule-list .list-item:last-child {
  margin-bottom: 0;
}

.recent-activity-box .list li {
  padding-left: 48px;
  padding-right: 20px;
  padding-bottom: 15px;
}
.recent-activity-box .list li .icon {
  top: 3px;
  z-index: 1;
  width: 35px;
  height: 35px;
  font-size: 17px;
  line-height: 37px;
  color: var(--splash-info-light-color);
  border: 1px dashed var(--splash-info-light-color);
}
.recent-activity-box .list li .icon::before {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 2px;
  z-index: -1;
  content: "";
  border-radius: 50%;
  position: absolute;
  background: #F3F7F9;
}
.recent-activity-box .list li .link-btn {
  top: 50%;
  color: #AAA8CD;
  transform: translateY(-50%);
}
.recent-activity-box .list li .link-btn:hover {
  color: var(--splash-primary-color);
}
.recent-activity-box .list li::before {
  top: 10px;
  left: 17px;
  content: "";
  height: 100%;
  position: absolute;
  border-left: 1px dashed #d9e9ef;
}
.recent-activity-box .list li:last-child {
  padding-bottom: 0;
}
.recent-activity-box .list li:last-child::before {
  display: none;
}
.recent-activity-box .list li:nth-child(2) .icon, .recent-activity-box .list li:nth-child(6) .icon {
  color: var(--splash-primary-color);
  border-color: var(--splash-primary-color);
}
.recent-activity-box .list li:nth-child(3) .icon, .recent-activity-box .list li:nth-child(7) .icon {
  color: var(--splash-success-color);
  border-color: var(--splash-success-color);
}
.recent-activity-box .list li:nth-child(4) .icon, .recent-activity-box .list li:nth-child(8) .icon {
  color: var(--splash-danger-color);
  border-color: var(--splash-danger-color);
}

.dark .stats-item .icon {
  background: var(--splash-black-color);
}
.dark .stats-item .users-list div {
  border-color: #45445e;
}
.dark .working-schedule-box .schedule-date-list {
  border-bottom-color: #45445e;
}
.dark .recent-activity-box .list li .icon::before {
  background: var(--splash-black-color);
}
.dark .recent-activity-box .list li .link-btn {
  color: #BCBBC7;
}
.dark .recent-activity-box .list li .link-btn:hover {
  color: var(--splash-primary-color);
}
.dark .recent-activity-box .list li::before {
  border-left-color: #45445e;
}
.dark .to-do-list-box .to-do-list .to-do-list-item {
  border-top-color: #45445e;
}
.dark .to-do-list-box .to-do-list .to-do-list-item .dropdown-toggle {
  color: #BCBBC7;
}
.dark .to-do-list-box .to-do-list .to-do-list-item .dropdown-toggle:hover {
  color: var(--splash-primary-color);
}
.dark .active-tasks-box .active-tasks-list .active-task-list-item {
  border-top-color: #45445e;
}
.dark .active-tasks-box .active-tasks-list .active-task-list-item .task-info {
  background: var(--splash-black-color);
}
.dark .active-tasks-box .active-tasks-list .active-task-list-item .task-info .users-list div {
  border-color: #45445e;
}

@media only screen and (max-width: 767px) {
  .stats-item .icon {
    width: 75px;
    height: 75px;
    font-size: 30px;
  }
  .stats-item .icon i {
    margin-top: 1.5px;
  }
  .stats-item .users-list div {
    width: 40px;
    height: 40px;
    margin-right: -15px;
  }
  .stats-item .chart {
    margin-bottom: -25px;
  }
  .working-schedule-box .schedule-date-list {
    padding-bottom: 15px;
  }
  .working-schedule-box .schedule-date-list li {
    padding-top: 15px;
    padding-bottom: 15px;
    margin-left: 0;
    margin-right: 0;
  }
  .working-schedule-box .info-list li {
    margin-right: 10px;
  }
  .working-schedule-box .schedule-list .content h6 {
    font-size: 16px;
  }
  .working-schedule-box .schedule-list .content .icon {
    width: 25px;
    height: 25px;
  }
  .to-do-list-box .to-do-list .to-do-list-item {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .to-do-list-box .to-do-list .to-do-list-item .action-buttons button {
    font-size: 16px;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item .task-info {
    margin-left: 0;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item .task-info .users-list {
    margin-right: 0;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item .task-info .users-list div {
    width: 30px;
    height: 30px;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(2) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(7) .task-info {
    margin-left: 0;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(3) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(8) .task-info {
    margin-left: 0;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(4) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(9) .task-info {
    margin-left: 0;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(5) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(10) .task-info {
    margin-left: 0;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .working-schedule-box .schedule-date-list li {
    margin-left: 5px;
    margin-right: 5px;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item .task-info {
    margin-left: 5%;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item .task-info .users-list {
    margin-right: 50px;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(2) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(7) .task-info {
    margin-left: 25%;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(3) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(8) .task-info {
    margin-left: 20%;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(4) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(9) .task-info {
    margin-left: 10%;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(5) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(10) .task-info {
    margin-left: 25%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .working-schedule-box .schedule-date-list li {
    margin-left: 23px;
    margin-right: 23px;
  }
  .to-do-list-box .to-do-list .to-do-list-item {
    padding-top: 19px;
    padding-bottom: 19px;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item .task-info .users-list {
    margin-right: 50px;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(2) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(7) .task-info {
    margin-left: 20%;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(3) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(8) .task-info {
    margin-left: 40%;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(4) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(9) .task-info {
    margin-left: 8%;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(5) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(10) .task-info {
    margin-left: 40%;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .working-schedule-box .schedule-date-list {
    padding-left: 120px !important;
    padding-right: 120px;
  }
  .working-schedule-box .schedule-date-list li {
    margin-left: 20px;
    margin-right: 20px;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item {
    padding-top: 17px;
    padding-bottom: 17px;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item .task-info .users-list {
    margin-right: 50px;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(2) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(7) .task-info {
    margin-left: 15%;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(3) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(8) .task-info {
    margin-left: 20%;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(4) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(9) .task-info {
    margin-left: 5%;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(5) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(10) .task-info {
    margin-left: 25%;
  }
  .recent-activity-box .list li {
    padding-bottom: 19px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .working-schedule-box .schedule-date-list {
    padding-left: 100px !important;
    padding-right: 100px;
  }
  .working-schedule-box .schedule-date-list li {
    margin-left: 20px;
    margin-right: 20px;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item .task-info {
    margin-left: 3%;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item .task-info .users-list {
    margin-right: 30px;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(2) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(7) .task-info {
    margin-left: 15%;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(3) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(8) .task-info {
    margin-left: 20%;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(4) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(9) .task-info {
    margin-left: 10%;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(5) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(10) .task-info {
    margin-left: 22%;
  }
}
@media only screen and (min-width: 1600px) {
  .stats-item h4 {
    font-size: 24px;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item {
    padding-top: 17px;
    padding-bottom: 17px;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(3) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(8) .task-info {
    margin-left: 45%;
  }
  .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(5) .task-info, .active-tasks-box .active-tasks-list .active-task-list-item:nth-child(10) .task-info {
    margin-left: 40%;
  }
  .recent-activity-box .list li {
    padding-left: 52px;
    padding-right: 45px;
    padding-bottom: 18.5px;
  }
  .recent-activity-box .list li .icon {
    top: 3px;
    width: 38px;
    height: 38px;
    font-size: 18px;
    line-height: 40px;
  }
  .recent-activity-box .list li .icon::before {
    margin: 3px;
  }
  .recent-activity-box .list li::before {
    top: 10px;
    left: 19px;
  }
  .to-do-list-box .to-do-list.style-three .to-do-list-item {
    padding-top: 14.6px;
    padding-bottom: 14.6px;
  }
}
.welcome-support-desk-box h2 {
  font-size: 25px;
}
.welcome-support-desk-box .list {
  margin-top: 30px;
}
.welcome-support-desk-box .list h4 {
  font-size: 20px;
}
.welcome-support-desk-box .list.row {
  --bs-gutter-x: 10px;
}

.time-duration-box .card-body {
  padding: 40px 20px;
}
.time-duration-box .card-body .icon {
  z-index: 1;
  font-size: 50px;
  margin-bottom: 25px;
}
.time-duration-box .card-body .icon::before {
  background: #F3F7F9;
  border-radius: 10px;
  position: absolute;
  height: 40px;
  content: "";
  width: 40px;
  z-index: -1;
  left: 25px;
  top: 25px;
}
.time-duration-box .card-body .content {
  margin-bottom: 40px;
}
.time-duration-box .card-body .content h2 {
  font-size: 25px;
}
.time-duration-box .card-body .chart {
  top: 15px;
  right: 20px;
  max-width: 150px;
  position: absolute;
}

.number-of-tickets-box .info {
  margin-top: 20px;
}
.number-of-tickets-box .info li {
  margin-left: 10px;
  margin-right: 10px;
}
.number-of-tickets-box .info li:first-child {
  margin-left: 0;
}
.number-of-tickets-box .info li:last-child {
  margin-right: 0;
}
.number-of-tickets-box .info h4 {
  font-size: 22px;
}

.top-performer-box .list li {
  border-bottom: 1px dashed #d9e9ef;
  padding-bottom: 17px;
  margin-bottom: 17px;
}
.top-performer-box .list li img {
  width: 50px;
  height: 50px;
}
.top-performer-box .list li .title {
  margin-left: 10px;
}
.top-performer-box .list li:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.unresolved-tickets-box .list li {
  border-bottom: 1px dashed #d9e9ef;
  margin-bottom: 20px;
  padding-left: 18px;
  padding-bottom: 18px;
}
.unresolved-tickets-box .list li:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.customer-satisfaction-box .list {
  border-top: 1px dashed #d9e9ef;
  padding-top: 21px;
  margin-top: 21px;
}
.customer-satisfaction-box .list li {
  margin-left: 10px;
  margin-right: 10px;
}
.customer-satisfaction-box .list li h4 {
  font-size: 20px;
}
.customer-satisfaction-box .list li:first-child {
  margin-left: 0;
}
.customer-satisfaction-box .list li:last-child {
  margin-right: 0;
}

.ticket-grid-box .item {
  padding: 20px;
  margin-bottom: 10px;
  border: 1px solid #d9e9ef;
}
.ticket-grid-box .item .form-select {
  line-height: 1.5;
  border-color: var(--bs-border-color);
  padding: 0.375rem 2.25rem 0.375rem 0.75rem;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
}
.ticket-grid-box .item .form-select:focus {
  border-color: var(--splash-primary-color);
}
.ticket-grid-box .item:hover {
  border-color: #F5F4FA;
  background: #F5F4FA;
}
.ticket-grid-box .item:last-child {
  margin-bottom: 0;
}

.dark .time-duration-box .card-body .icon::before {
  background: var(--splash-black-color);
}
.dark .top-performer-box .list li {
  border-bottom-color: #45445e;
}
.dark .unresolved-tickets-box .list li {
  border-bottom-color: #45445e;
}
.dark .customer-satisfaction-box .list {
  border-top-color: #45445e;
}
.dark .ticket-grid-box .item {
  border-color: #45445e;
}
.dark .ticket-grid-box .item .form-select {
  border-color: #45445e;
}
.dark .ticket-grid-box .item:hover {
  border-color: var(--splash-black-color);
  background: var(--splash-black-color);
}

@media only screen and (max-width: 767px) {
  .welcome-support-desk-box h2 {
    font-size: 20px;
  }
  .welcome-support-desk-box .list {
    margin-top: 10px;
  }
  .welcome-support-desk-box .list h4 {
    font-size: 18px;
  }
  .time-duration-box .card-body {
    padding: 15px;
  }
  .time-duration-box .card-body .icon {
    font-size: 40px;
    margin-bottom: 20px;
  }
  .time-duration-box .card-body .icon::before {
    height: 35px;
    width: 35px;
    left: 15px;
    top: 15px;
  }
  .time-duration-box .card-body .content {
    margin-bottom: 15px;
  }
  .time-duration-box .card-body .content h2 {
    font-size: 18px;
  }
  .time-duration-box .card-body .chart {
    top: -10px;
    right: 15px;
  }
  .number-of-tickets-box .info {
    margin-top: 0;
  }
  .number-of-tickets-box .info li {
    margin-top: 15px;
  }
  .top-performer-box .list li {
    padding-bottom: 15px;
    margin-bottom: 15px;
  }
  .unresolved-tickets-box .list {
    text-align: center;
  }
  .unresolved-tickets-box .list li {
    display: inline-block;
    border-bottom: none;
    margin-bottom: 0;
    padding-left: 5px;
    padding-bottom: 5px;
  }
  .customer-satisfaction-box .list {
    padding-top: 15px;
    margin-top: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .welcome-support-desk-box .list {
    margin-top: 20px;
  }
  .time-duration-box .card-body .content {
    margin-bottom: 20px;
  }
  .top-performer-box .list li {
    padding-bottom: 15px;
    margin-bottom: 15px;
  }
  .unresolved-tickets-box .list li {
    padding-left: 10px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .time-duration-box .card-body .content {
    margin-bottom: 25px;
  }
  .time-duration-box .card-body p {
    max-width: 330px;
  }
  .top-performer-box .list li {
    padding-bottom: 15px;
    margin-bottom: 15px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .time-duration-box .card-body .content {
    margin-bottom: 25px;
  }
  .time-duration-box .card-body p {
    max-width: 330px;
  }
  .top-performer-box .list li {
    padding-bottom: 15px;
    margin-bottom: 15px;
  }
}
@media only screen and (min-width: 1600px) {
  .welcome-support-desk-box h2 {
    font-size: 30px;
  }
  .welcome-support-desk-box .content {
    padding-right: 20px;
  }
  .welcome-support-desk-box .list {
    margin-top: 30px;
  }
  .welcome-support-desk-box .list h4 {
    font-size: 24px;
  }
  .time-duration-box .card-body {
    padding: 37px 30px;
  }
  .time-duration-box .card-body .icon {
    font-size: 55px;
  }
  .time-duration-box .card-body .content {
    margin-bottom: 40px;
  }
  .time-duration-box .card-body .content span {
    font-size: 16px;
  }
  .time-duration-box .card-body .content h2 {
    font-size: 30px;
  }
  .number-of-tickets-box .info {
    margin-top: 32px;
  }
  .number-of-tickets-box .info li {
    margin-left: 30px;
    margin-right: 30px;
  }
  .number-of-tickets-box .info h4 {
    font-size: 24px;
  }
  .top-performer-box .list li {
    padding-bottom: 15.6px;
    margin-bottom: 15.6px;
  }
  .top-performer-box .list li img {
    width: 55px;
    height: 55px;
  }
  .top-performer-box .list li .title {
    margin-left: 15px;
  }
  .customer-satisfaction-box .list {
    padding-top: 20px;
    margin-top: 20px;
  }
  .customer-satisfaction-box .list li {
    margin-left: 20px;
    margin-right: 20px;
  }
  .customer-satisfaction-box .list li h4 {
    font-size: 24px;
  }
}
.welcome-lms-courses-box {
  background: linear-gradient(92.21deg, #3E39D9 1.38%, #6560F0 100%);
}
.welcome-lms-courses-box .card-body {
  padding-top: 0;
  padding-bottom: 0;
}
.welcome-lms-courses-box h2 {
  font-size: 30px;
}
.welcome-lms-courses-box span {
  color: #CCCCE1;
}
.welcome-lms-courses-box p {
  color: #CFCFD8;
}
.welcome-lms-courses-box .list {
  margin-top: 20px;
}
.welcome-lms-courses-box .list h4 {
  font-size: 24px;
}
.welcome-lms-courses-box .list.row {
  --bs-gutter-x: 10px;
}

.todays-course-box .content {
  padding-left: 15px;
}
.todays-course-box .content a {
  font-size: 14px;
}
.todays-course-box .swiper-pagination {
  bottom: 0;
  top: auto;
}
.todays-course-box .swiper-pagination .swiper-pagination-bullet {
  width: 6px;
  opacity: 1;
  height: 6px;
  background: #E7E6EE;
  margin-left: 4px;
  margin-right: 4px;
}
.todays-course-box .swiper-pagination .swiper-pagination-bullet:first-child {
  margin-left: 0;
}
.todays-course-box .swiper-pagination .swiper-pagination-bullet:last-child {
  margin-right: 0;
}
.todays-course-box .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: var(--splash-primary-color);
}

.todays-event-box {
  background: rgba(111, 211, 247, 0.3) !important;
}
.todays-event-box .content {
  padding-left: 15px;
}
.todays-event-box .content a {
  font-size: 14px;
}
.todays-event-box .card-body {
  bottom: 0;
  top: auto;
}
.todays-event-box .card-body .swiper-pagination-bullet {
  width: 6px;
  opacity: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.6);
  margin-left: 4px;
  margin-right: 4px;
}
.todays-event-box .card-body .swiper-pagination-bullet:first-child {
  margin-left: 0;
}
.todays-event-box .card-body .swiper-pagination-bullet:last-child {
  margin-right: 0;
}
.todays-event-box .card-body .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: var(--splash-primary-color);
}

.categories-box .list li {
  border-bottom: 1px dashed #d9e9ef;
  padding-top: 17px;
  padding-bottom: 17px;
}
.categories-box .list li:first-child {
  border-top: 1px dashed #d9e9ef;
}
.categories-box .list li .title a {
  margin-bottom: 6px;
}
.categories-box .list li .link-btn {
  width: 30px;
  height: 30px;
  font-size: 16px;
  border-radius: 2px;
  background: #F7F7F9;
  color: var(--splash-primary-color);
}
.categories-box .list li .link-btn i {
  left: 0;
  right: 0;
  top: 50%;
  line-height: 1;
  position: absolute;
  transform: translateY(-50%);
}
.categories-box .list li .link-btn:hover {
  background-color: var(--splash-primary-color);
  color: var(--splash-white-color);
}

.courses-list-box {
  background: rgba(101, 96, 240, 0.15);
}
.courses-list-box .card-body {
  padding: 30px;
}
.courses-list-box .card-body .title .icon {
  width: 65px;
  height: 65px;
}
.courses-list-box .card-body .title .icon img {
  top: 50%;
  transform: translateY(-50%);
}
.courses-list-box .card-body .title h6 {
  left: 0;
  top: 50%;
  right: 0;
  padding-left: 80px;
  position: absolute;
  transform: translateY(-50%);
}
.courses-list-box .card-body .list li {
  border-radius: 30px;
  margin-right: 8px;
  padding: 6px 12px;
}
.courses-list-box .card-body .list li:last-child {
  margin-right: 0;
}
.courses-list-box .link-btn {
  background: rgba(255, 255, 255, 0.45);
  padding-top: 12px;
  padding-left: 30px;
  padding-right: 30px;
  padding-bottom: 12px;
}
.courses-list-box .link-btn span {
  line-height: 1.3;
}
.courses-list-box .link-btn span::before {
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  content: "";
  position: absolute;
  transition: var(--transition);
  background: var(--splash-black-color);
}
.courses-list-box .link-btn i {
  top: 50%;
  right: 30px;
  line-height: 1;
  font-size: 20px;
  position: absolute;
  transform: translateY(-50%);
}
.courses-list-box .link-btn.text-primary span::before {
  background: var(--splash-primary-color);
}
.courses-list-box .link-btn.text-success span::before {
  background: var(--splash-success-color);
}
.courses-list-box.bg2 {
  background: rgba(6, 180, 138, 0.15);
}
.courses-list-box.bg2 .link-btn {
  background: rgba(255, 255, 255, 0.45);
}

.students-progress-box .list li {
  border-bottom: 1px dashed #d9e9ef;
  padding-top: 20.6px;
  padding-bottom: 20.6px;
}
.students-progress-box .list li:first-child {
  border-top: 1px dashed #d9e9ef;
}

.performance-box .list {
  margin-top: 25px;
}
.performance-box .list li {
  padding-left: 8px;
  margin-left: 8px;
  margin-right: 8px;
}
.performance-box .list li::before {
  top: 0;
  left: 0;
  bottom: 0;
  width: 2px;
  content: "";
  position: absolute;
  background: #f9f9f9;
}
.performance-box .list li.text-primary::before {
  background: var(--splash-primary-color);
}
.performance-box .list li.text-info::before {
  background: var(--splash-info-color);
}
.performance-box .list li.text-success::before {
  background: var(--splash-success-color);
}
.performance-box .list li:first-child {
  margin-left: 0;
}
.performance-box .list li:last-child {
  margin-right: 0;
}

.dark .todays-course-box .todays-course-swiper-pagination .swiper-pagination-bullet {
  background: var(--splash-black-color);
}
.dark .todays-course-box .todays-course-swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: var(--splash-primary-color);
}
.dark .todays-event-box .todays-event-swiper-pagination .swiper-pagination-bullet {
  background: var(--splash-black-color);
}
.dark .todays-event-box .todays-event-swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: var(--splash-primary-color);
}
.dark .categories-box .list li {
  border-bottom-color: #45445e;
}
.dark .categories-box .list li:first-child {
  border-top-color: #45445e;
}
.dark .categories-box .list li .link-btn {
  background: var(--splash-black-color);
}
.dark .categories-box .list li .link-btn:hover {
  background-color: var(--splash-primary-color);
}
.dark .students-progress-box .list li {
  border-bottom-color: #45445e;
}
.dark .students-progress-box .list li svg circle:first-child {
  stroke: var(--splash-black-color);
}
.dark .students-progress-box .list li:first-child {
  border-top-color: #45445e;
}
.dark .courses-list-box {
  background: rgba(101, 96, 240, 0.05);
}
.dark .courses-list-box .link-btn {
  background: rgba(255, 255, 255, 0.04);
}
.dark .courses-list-box.bg2 {
  background: rgba(6, 180, 138, 0.05);
}
.dark .courses-list-box.bg2 .link-btn {
  background: rgba(255, 255, 255, 0.04);
}

@media only screen and (max-width: 767px) {
  .welcome-lms-courses-box {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .welcome-lms-courses-box h2 {
    font-size: 20px;
  }
  .welcome-lms-courses-box .list {
    margin-top: -15px;
  }
  .welcome-lms-courses-box .list h4 {
    font-size: 18px;
  }
  .courses-list-box .card-body {
    padding: 15px;
  }
  .courses-list-box .card-body .title .icon {
    width: 65px;
    height: 65px;
    margin-bottom: 15px;
  }
  .courses-list-box .card-body .title h6 {
    top: 0;
    padding-left: 0;
    position: relative;
    transform: translateY(0);
  }
  .courses-list-box .card-body .list li {
    padding: 3px 9px;
    margin-right: 5px;
  }
  .courses-list-box .link-btn {
    padding-top: 10px;
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 10px;
  }
  .courses-list-box .link-btn i {
    right: 15px;
  }
  .performance-box .list {
    margin-top: 0;
  }
  .performance-box .list li {
    margin-top: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .welcome-lms-courses-box {
    padding-top: 25px;
  }
  .courses-list-box .card-body {
    padding: 25px;
  }
  .courses-list-box .link-btn {
    padding-left: 25px;
    padding-right: 25px;
  }
  .courses-list-box .link-btn i {
    right: 25px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .welcome-lms-courses-box {
    padding-top: 30px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .welcome-lms-courses-box {
    padding-top: 30px;
  }
}
@media only screen and (min-width: 1600px) {
  .welcome-lms-courses-box .content {
    padding-right: 30px;
  }
  .welcome-lms-courses-box .main-image {
    margin-left: auto;
    max-width: 570px;
  }
  .todays-course-box .content {
    padding-left: 15px;
  }
  .todays-course-box .content a {
    font-size: 16px;
  }
  .todays-event-box .content {
    padding-left: 15px;
  }
  .todays-event-box .content a {
    font-size: 16px;
  }
  .performance-box .list li {
    padding-left: 8px;
    margin-left: 12px;
    margin-right: 12px;
  }
  .students-progress-box .list li {
    padding-top: 19.2px;
    padding-bottom: 19.2px;
  }
}
.crm-stats-box .card-body {
  padding: 30px 25px;
}
.crm-stats-box .card-body .icon i {
  width: 30px;
  height: 30px;
  display: none;
  line-height: 34px;
  text-align: center;
  border-radius: 50%;
  background: #F8F8FB;
}
.crm-stats-box .card-body h3 {
  font-size: 30px;
}
.crm-stats-box .card-body .chart {
  top: 35px;
  position: relative;
  margin-bottom: -40px;
  margin-right: -15px;
}
.crm-stats-box .card-body .chart.style-two {
  top: 15px;
}

.call-stats-box {
  background-color: #CBE8F8 !important;
  padding: 15px;
}
.call-stats-box .card-body {
  border: 2px dashed var(--splash-white-color);
  padding-top: 35px;
  padding-left: 15px;
  padding-right: 15px;
  padding-bottom: 35px;
}
.call-stats-box .card-body .icon {
  top: 50%;
  width: 78px;
  right: 15px;
  height: 78px;
  display: none;
  background: #F8F8FB;
  transform: translateY(-50%);
}
.call-stats-box .card-body .icon img {
  top: 50%;
  transform: translateY(-50%);
}
.call-stats-box .card-body h3 {
  font-size: 30px;
  margin-top: 8px;
  margin-bottom: 15px;
}
.call-stats-box .card-body h3 span {
  font-size: 20px;
  margin-left: 2px;
}
.call-stats-box hr {
  opacity: 1;
  border-color: rgba(255, 255, 255, 0.5);
  margin-top: 15px;
  margin-bottom: 15px;
}

.most-lead-box .list li {
  border-bottom: 1px dashed #d9e9ef;
  padding-top: 15.7px;
  padding-bottom: 15.7px;
}
.most-lead-box .list li .icon {
  width: 46px;
  height: 46px;
  font-size: 20px;
  background: #F2F1F9;
}
.most-lead-box .list li .icon i {
  top: 50%;
  margin-top: 1px;
  transform: translateY(-50%);
}
.most-lead-box .list li .icon.text-info {
  background: #F3F7F9;
}
.most-lead-box .list li .icon.text-success {
  background: #F1FAF8;
}
.most-lead-box .list li .icon.text-danger {
  background: #FAF7F7;
}
.most-lead-box .list li .progress {
  height: 10px;
}
.most-lead-box .list li:first-child {
  padding-top: 0;
}
.most-lead-box .list li:last-child {
  padding-bottom: 0;
  border-bottom: none;
}

.campaign-contact-box {
  background: #8E8DA2 !important;
  padding: 15px;
}
.campaign-contact-box .card-body {
  border: 2px dashed rgba(255, 255, 255, 0.2);
  padding: 25px;
}
.campaign-contact-box .card-body h2 {
  font-size: 26px;
  line-height: 1.3;
}
.campaign-contact-box .card-body h2 a {
  font-size: 20px;
}
.campaign-contact-box .card-body img {
  margin-top: 28px;
}

.revenue-country-box .list {
  margin-right: -15px;
}
.revenue-country-box .list li {
  border-bottom: 1px dashed #d9e9ef;
  padding-top: 11.7px;
  padding-bottom: 11.7px;
}
.revenue-country-box .list li .progress {
  height: 6px;
  margin-top: 3px;
}
.revenue-country-box .list li:first-child {
  padding-top: 0;
}
.revenue-country-box .list li:last-child {
  padding-bottom: 0;
  border-bottom: none;
}

.campaigns-box .card-body {
  padding: 30px 20px;
}
.campaigns-box .list {
  margin-top: -25px;
}
.campaigns-box .list li {
  padding-left: 8px;
  margin-top: 25px;
  margin-left: 7px;
  margin-right: 7px;
}
.campaigns-box .list li::before {
  background: var(--splash-primary-color);
  position: absolute;
  content: "";
  width: 2px;
  bottom: 0;
  left: 0;
  top: 0;
}
.campaigns-box .list li.text-primary::before {
  background: var(--splash-primary-color);
}
.campaigns-box .list li.text-info::before {
  background: var(--splash-info-color);
}
.campaigns-box .list li.text-success::before {
  background: var(--splash-success-color);
}
.campaigns-box .list li:first-child {
  margin-left: 0;
}
.campaigns-box .list li:last-child {
  margin-right: 0;
}

.dark .call-stats-box {
  background-color: #3f3d59 !important;
}
.dark .call-stats-box .card-body {
  border-color: var(--splash-black-color);
}
.dark .call-stats-box .card-body .icon {
  background: var(--splash-black-color);
}
.dark .call-stats-box hr {
  border-color: #45445e;
}
.dark .most-lead-box .list li {
  border-bottom-color: #45445e;
}
.dark .most-lead-box .list li .icon {
  background: var(--splash-black-color);
}
.dark .most-lead-box .list li .icon.text-info {
  background: var(--splash-black-color);
}
.dark .most-lead-box .list li .icon.text-success {
  background: var(--splash-black-color);
}
.dark .most-lead-box .list li .icon.text-danger {
  background: var(--splash-black-color);
}
.dark .revenue-country-box .list li {
  border-bottom-color: #45445e;
}

@media only screen and (max-width: 767px) {
  .crm-stats-box .card-body {
    padding: 15px;
  }
  .crm-stats-box .card-body h3 {
    font-size: 20px;
  }
  .crm-stats-box .card-body .chart {
    top: 0;
    margin-bottom: -30px;
    margin-top: -15px;
    margin-right: 0;
  }
  .crm-stats-box .card-body .chart.style-two {
    top: 0;
    margin-top: 15px;
    margin-bottom: -15px;
  }
  .most-lead-box .list li {
    padding-top: 12px;
    padding-bottom: 12px;
  }
  .most-lead-box .list li .icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
  .most-lead-box .list li .progress {
    margin-top: 12px;
    margin-bottom: 10px;
  }
  .campaign-contact-box .card-body {
    padding: 15px;
  }
  .campaign-contact-box .card-body h2 {
    font-size: 20px;
  }
  .campaign-contact-box .card-body h2 a {
    font-size: 17px;
  }
  .campaign-contact-box .card-body img {
    margin-top: 20px;
  }
  .revenue-country-box .list {
    margin-right: 0;
    margin-bottom: 15px !important;
  }
  .revenue-country-box .list li {
    padding-top: 12px;
    padding-bottom: 12px;
  }
  .call-stats-box .card-body {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .call-stats-box .card-body .icon {
    width: 60px;
    height: 60px;
    display: block;
  }
  .call-stats-box .card-body h3 {
    font-size: 20px;
    margin-top: 5px;
    margin-bottom: 10px;
  }
  .call-stats-box .card-body h3 span {
    font-size: 16px;
    margin-left: 0;
  }
  .call-stats-box hr {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .crm-stats-box .card-body {
    padding: 25px;
  }
  .crm-stats-box .card-body .icon i {
    width: 30px;
    height: 30px;
    display: none;
    line-height: 34px;
    text-align: center;
    border-radius: 50%;
    background: #F8F8FB;
  }
  .crm-stats-box .card-body h3 {
    font-size: 25px;
  }
  .crm-stats-box .card-body .chart {
    top: 0;
    margin-bottom: -30px;
    margin-right: 0;
  }
  .crm-stats-box .card-body .chart.style-two {
    top: 0;
  }
  .revenue-country-box .list {
    margin-right: 0;
    margin-right: 0;
    margin-bottom: 25px !important;
  }
  .revenue-country-box .list li {
    padding-top: 12px;
    padding-bottom: 12px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .campaign-contact-box .card-body h2 {
    font-size: 25px;
  }
  .campaign-contact-box .card-body img {
    margin-top: 50px;
  }
  .revenue-country-box .list li {
    padding-top: 11.3px;
    padding-bottom: 11.3px;
  }
  .call-stats-box .card-body .icon {
    display: block;
  }
}
@media only screen and (min-width: 1600px) {
  .crm-stats-box .card-body {
    padding: 40px 30px;
  }
  .crm-stats-box .card-body .icon i {
    display: block;
  }
  .call-stats-box .card-body .icon {
    display: block;
  }
  .most-lead-box .list li {
    padding-top: 20px;
    padding-bottom: 20px;
  }
  .revenue-country-box .list li {
    padding-top: 15.1px;
    padding-bottom: 15.1px;
  }
  .campaigns-box .card-body {
    padding: 30px;
  }
  .campaigns-box .list li {
    padding-left: 10px;
    margin-left: 15px;
    margin-right: 15px;
  }
  .campaign-contact-box .card-body img {
    margin-top: 24px;
  }
}
.chart .apexcharts-tooltip.apexcharts-theme-light {
  box-shadow: 2px 1px 22px rgba(101, 96, 240, 0.25);
  background: var(--splash-white-color);
  border-radius: 2px;
  border: none;
}
.chart .apexcharts-tooltip.apexcharts-theme-light .apexcharts-tooltip-title {
  background: var(--splash-white-color);
  color: var(--splash-black-color);
  border-bottom-color: #F5F5FB;
  padding: 10px 15px;
  font-weight: 700;
}
.chart .apexcharts-tooltip.apexcharts-theme-dark .apexcharts-tooltip-series-group {
  color: var(--splash-white-color);
}
.chart .apexcharts-tooltip-series-group {
  padding: 0 15px;
  font-weight: 500;
  text-align: start;
  color: var(--splash-muted-color);
}
.chart .apexcharts-tooltip-series-group.apexcharts-active {
  padding-bottom: 0;
}
.chart .apexcharts-tooltip-series-group:last-child {
  padding-bottom: 5px;
}
.chart .apexcharts-xaxistooltip {
  box-shadow: 2px 1px 22px rgba(101, 96, 240, 0.15);
  background: var(--splash-white-color);
  padding: 5px 10px;
  font-weight: 700;
  border: none;
}
.chart .apexcharts-xaxistooltip::before {
  display: none;
}
.chart .apexcharts-xaxistooltip-bottom::after {
  display: none;
}
.chart .apexcharts-pie .apexcharts-text {
  fill: #A09FB0;
  font-size: 12px;
  font-family: var(--bs-font-sans-serif) !important;
}
.chart#salesByPosLocationChart {
  margin-bottom: -70px;
}
.chart#websiteVisitorsChart {
  margin: 0 0 -22px -15px;
}
.chart #averageDailySalesChart {
  margin: -54px 0 -30px auto;
  max-width: 205px;
}
.chart#weeklySalesChart {
  margin-bottom: -25px;
}
.chart#taskOverviewChart {
  margin-bottom: -25px;
}
.chart#numberOfTicketsChart {
  margin: 0 0 -30px 0;
}
.chart#unresolvedTicketsChart {
  margin: 0 0 -25px 0;
}
.chart#averageEnrollmentRateChart {
  margin-bottom: -20px;
}

.dark .chart .apexcharts-legend-text {
  color: #BCBBC7 !important;
}
.dark .chart .apexcharts-xaxis-label {
  color: #BCBBC7 !important;
}
.dark .chart .apexcharts-gridline {
  stroke: #45445e;
}
.dark .chart .apexcharts-radar-series polygon, .dark .chart .apexcharts-radar-series line {
  stroke: #45445e;
}
.dark .chart .apexcharts-datalabel {
  fill: #BCBBC7 !important;
}
.dark .chart .apexcharts-grid-borders polygon, .dark .chart .apexcharts-grid-borders line {
  stroke: #45445e;
}
.dark .chart .apexcharts-yaxis polygon, .dark .chart .apexcharts-yaxis line {
  stroke: #45445e;
}
.dark .chart .apexcharts-pie-series path {
  stroke: #34334a;
}
.dark .chart .apexcharts-pie line, .dark .chart .apexcharts-pie circle {
  stroke: #45445e;
}

.calendar-card .fc .fc-toolbar.fc-header-toolbar {
  margin-bottom: 20px;
}
.calendar-card .fc .fc-toolbar-title {
  font-size: 22px;
  font-weight: 600;
}
.calendar-card .fc .fc-button-group .fc-button {
  box-shadow: unset !important;
  transition: var(--transition);
}
.calendar-card .fc .fc-button-group .fc-prev-button {
  margin-right: 5px;
}
.calendar-card .fc .fc-button-group .fc-prev-button, .calendar-card .fc .fc-button-group .fc-next-button {
  font-size: 14px;
  background-color: transparent;
  border-radius: 0.25em !important;
  color: var(--splash-muted-color);
  border: 1px solid rgba(101, 96, 240, 0.15);
}
.calendar-card .fc .fc-button-group .fc-prev-button .fc-icon, .calendar-card .fc .fc-button-group .fc-next-button .fc-icon {
  position: relative;
  top: -1px;
}
.calendar-card .fc .fc-button-group .fc-prev-button:hover, .calendar-card .fc .fc-button-group .fc-next-button:hover {
  background-color: var(--splash-primary-color);
  border-color: var(--splash-primary-color);
  color: var(--splash-white-color);
}
.calendar-card .fc .fc-button-group .fc-dayGridWeek-button, .calendar-card .fc .fc-button-group .fc-dayGridMonth-button, .calendar-card .fc .fc-button-group .fc-dayGridDay-button {
  font-size: 14px;
  margin-left: 5px !important;
}
.calendar-card .fc .fc-button-group .fc-dayGridWeek-button:hover, .calendar-card .fc .fc-button-group .fc-dayGridMonth-button:hover, .calendar-card .fc .fc-button-group .fc-dayGridDay-button:hover {
  background-color: var(--splash-primary-color);
  border-color: var(--splash-primary-color);
  color: var(--splash-white-color);
}
.calendar-card .fc .fc-button-group .fc-dayGridMonth-button {
  margin-left: 0;
}
.calendar-card .fc .fc-today-button {
  color: var(--splash-black-color) !important;
  background-color: #F9F9F9 !important;
  border-color: #F9F9F9 !important;
  box-shadow: unset !important;
  text-transform: capitalize;
  font-weight: 500;
}
.calendar-card .fc a {
  text-decoration: none;
}
.calendar-card .fc .fc-daygrid-day-top {
  color: var(--splash-black-color);
  font-size: 16px;
  font-weight: 500;
}
.calendar-card .fc .fc-daygrid-day-top a {
  color: var(--splash-black-color);
  padding-right: 15px;
  padding-top: 15px;
  padding-bottom: 0;
  padding-left: 0;
}
.calendar-card .fc .fc-daygrid-day-events {
  margin-top: 0;
}
.calendar-card .fc .fc-daygrid-event {
  color: var(--splash-black-color);
  font-size: 13px;
}
.calendar-card .fc .fc-daygrid-event-dot {
  border-color: var(--splash-primary-color);
}
.calendar-card .fc .fc-h-event {
  background-color: var(--splash-primary-color);
  border-color: var(--splash-primary-color);
  border-radius: 30px !important;
  padding: 5px 20px;
}
.calendar-card .fc .fc-h-event .fc-event-title {
  font-size: 13px;
  font-weight: 500;
}
.calendar-card .fc .fc-daygrid-day-bottom {
  font-size: 13px;
  font-weight: 500;
}
.calendar-card .fc .fc-daygrid-more-link {
  color: var(--splash-black-color);
}
.calendar-card .fc .fc-daygrid-more-link:hover {
  color: var(--splash-primary-color);
  background-color: rgba(0, 0, 0, 0.05);
}
.calendar-card .fc tbody tr td:nth-child(4) .fc-h-event {
  background-color: var(--splash-warning-color) !important;
  border-color: var(--splash-warning-color) !important;
}
.calendar-card .fc tbody tr td:nth-child(7) .fc-h-event {
  background-color: var(--splash-info-color) !important;
  border-color: var(--splash-info-color) !important;
}
.calendar-card .fc tbody tr:nth-child(2) td .fc-h-event {
  background-color: var(--splash-success-color);
  border-color: var(--splash-success-color);
}
.calendar-card .fc tbody tr:nth-child(4) td .fc-h-event {
  background-color: var(--splash-info-color) !important;
  border-color: var(--splash-info-color) !important;
}
.calendar-card .fc .fc-daygrid-dot-event.fc-event-mirror, .calendar-card .fc .fc-daygrid-dot-event:hover {
  background-color: rgba(0, 0, 0, 0.05);
}
.calendar-card .fc-direction-ltr .fc-daygrid-event.fc-event-start, .calendar-card .fc-direction-ltr .fc-daygrid-event .fc-event-end, .calendar-card .fc-direction-rtl .fc-daygrid-event.fc-event-start, .calendar-card .fc-direction-rtl .fc-daygrid-event .fc-event-end {
  margin-top: 5px;
}
.calendar-card .fc-theme-standard .fc-scrollgrid {
  border: 0.5px dashed #D2CFE4;
}
.calendar-card .fc-theme-standard td, .calendar-card .fc-theme-standard th {
  border: 0.5px solid #F4F3F8;
}
.calendar-card .fc-theme-standard thead tr th {
  border: none;
  padding: 10px;
  color: #9C9AB6;
  background: #F9F9F9;
  text-transform: uppercase;
  font-size: 13px;
  font-weight: 700;
}
.calendar-card .fc-theme-standard thead tr th a {
  padding: 0;
  color: #9C9AB6;
}
.calendar-card .fc-theme-standard .fc-popover {
  border: none;
  box-shadow: 0px 4px 34px rgba(101, 96, 240, 0.15);
}
.calendar-card .fc-theme-standard .fc-popover-header {
  padding: 10px 15px;
  background-color: #F9F9F9;
}
.calendar-card .fc-theme-standard .fc-popover-title {
  color: var(--splash-black-color);
  margin-left: 0;
  margin-right: 2px;
  font-size: 14px;
  font-weight: 700;
}

.fc .fc-button-primary {
  background-color: transparent;
  border-radius: 0.25em !important;
  color: var(--splash-muted-color);
  border: 1px solid rgba(101, 96, 240, 0.15);
  margin-left: 5px !important;
}
.fc .fc-button-primary:first-child {
  margin-left: 0 !important;
}
.fc .fc-button-primary:not(:disabled).fc-button-active, .fc .fc .fc-button-primary:not(:disabled):active {
  background-color: transparent;
  border-radius: 0.25em !important;
  color: var(--splash-muted-color);
  border: 1px solid rgba(101, 96, 240, 0.15);
}

.dark .calendar-card .fc .fc-today-button {
  background-color: var(--splash-black-color) !important;
  border-color: var(--splash-black-color) !important;
  color: var(--splash-white-color) !important;
}
.dark .calendar-card .fc .fc-daygrid-day-top {
  color: var(--splash-white-color);
}
.dark .calendar-card .fc .fc-daygrid-day-top a {
  color: var(--splash-white-color);
}
.dark .calendar-card .fc .fc-daygrid-event {
  color: var(--splash-white-color);
}
.dark .calendar-card .fc-theme-standard .fc-scrollgrid {
  border-color: #45445e;
}
.dark .calendar-card .fc-theme-standard td, .dark .calendar-card .fc-theme-standard th {
  border-color: #45445e;
}
.dark .calendar-card .fc-theme-standard thead tr th {
  background: var(--splash-black-color);
}
.dark .calendar-card .fc-theme-standard thead tr th a {
  color: var(--splash-white-color);
}

@media only screen and (max-width: 767px) {
  .calendar-card .fc .fc-toolbar {
    display: block;
    text-align: center;
  }
  .calendar-card .fc .fc-toolbar-title {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}
.chat-sidebar .card-body {
  padding: 15px;
}
.chat-sidebar .card-body .search-box {
  margin-bottom: 15px;
}
.chat-sidebar .card-body .search-box .form-control {
  background: #F5F4FA;
  padding-top: 14px;
  padding-bottom: 14px;
}
.chat-sidebar .card-body .search-box button {
  top: 50%;
  right: 20px;
  line-height: 1;
  margin-top: 1px;
  font-size: 17px;
  position: absolute;
  transform: translateY(-50%);
}
.chat-sidebar .card-body .chat-tabs {
  display: flex;
  flex-wrap: wrap;
}
.chat-sidebar .card-body .chat-tabs a {
  flex: 1 0 0%;
  border-radius: 4px;
  padding: 10px 15px;
  color: var(--splash-muted-color);
  border: 1px solid rgba(101, 96, 240, 0.3);
  margin-left: 2px;
  margin-right: 2px;
}
.chat-sidebar .card-body .chat-tabs a:hover, .chat-sidebar .card-body .chat-tabs a.active {
  background-color: var(--splash-primary-color);
  border-color: var(--splash-primary-color);
  color: var(--splash-white-color);
}
.chat-sidebar .card-body .chat-tabs a:first-child {
  margin-left: 0;
}
.chat-sidebar .card-body .chat-tabs a:last-child {
  margin-right: 0;
}
.chat-sidebar .card-body .create-new-group-btn {
  margin-top: 20px;
  margin-left: -15px;
  margin-right: -15px;
}
.chat-sidebar .card-body .create-new-group-btn button {
  padding-top: 17px;
  padding-left: 15px;
  padding-bottom: 17px;
  padding-right: 35px;
}
.chat-sidebar .card-body .create-new-group-btn button i {
  top: 50%;
  line-height: 1;
  font-size: 14px;
  position: absolute;
  transform: translateY(-50%);
  margin-left: 8px;
  margin-top: 1.5px;
}
.chat-sidebar .card-body .chat-users-list {
  height: 705px;
  overflow: hidden;
  overflow-y: scroll;
  margin-top: 30px;
  margin-left: -15px;
  margin-right: -15px;
}
.chat-sidebar .card-body .chat-users-list .single-user-item {
  z-index: 1;
  cursor: pointer;
  border-bottom: 1px dashed #d9e9ef;
  padding-top: 20px;
  padding-left: 15px;
  padding-right: 45px;
  padding-bottom: 20px;
}
.chat-sidebar .card-body .chat-users-list .single-user-item .title {
  margin-bottom: 2px;
}
.chat-sidebar .card-body .chat-users-list .single-user-item .dot-badge {
  top: 50%;
  right: 15px;
  width: 23px;
  height: 23px;
  line-height: 23px;
  position: absolute;
  transform: translateY(-50%);
}
.chat-sidebar .card-body .chat-users-list .single-user-item:first-child {
  border-top: 1px dashed #d9e9ef;
}
.chat-sidebar .card-body .chat-users-list .single-user-item::before {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  z-index: -1;
  margin: 4px;
  content: "";
  visibility: hidden;
  position: absolute;
  background: #efeef9;
  transition: var(--transition);
}
.chat-sidebar .card-body .chat-users-list .single-user-item:hover::before, .chat-sidebar .card-body .chat-users-list .single-user-item.active::before {
  opacity: 1;
  visibility: visible;
}
.chat-sidebar .card-body .chat-users-list::-webkit-scrollbar {
  -webkit-appearance: none;
}
.chat-sidebar .card-body .chat-users-list::-webkit-scrollbar:vertical {
  width: 5px;
}
.chat-sidebar .card-body .chat-users-list::-webkit-scrollbar:horizontal {
  height: 5px;
}
.chat-sidebar .card-body .chat-users-list::-webkit-scrollbar-thumb {
  border-radius: 50px;
  background: #6D6C7D;
}
.chat-sidebar .card-body .chat-users-list::-webkit-scrollbar-track {
  background: #DCDCE3;
}
.chat-sidebar .card-body .chat-users-list .box {
  border-bottom: 1px dashed #d9e9ef;
  padding-bottom: 15px;
  margin-top: 30px;
  padding-left: 15px;
  padding-right: 15px;
}
.chat-sidebar .card-body .chat-users-list .box span {
  width: 40px;
  height: 40px;
  line-height: 37px;
  background: #F2F1F9;
  border: 1px solid var(--splash-primary-color);
}
.chat-sidebar .card-body .chat-users-list .box:first-child {
  margin-top: 0;
}

.chat-card .card-head .users-list div {
  width: 40px;
  height: 40px;
  margin-right: -15px;
  border: 2px solid var(--splash-white-color);
  filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));
}
.chat-card .card-head .users-list div:last-child {
  margin-right: 0;
}
.chat-card .card-body .chat-list {
  height: 640px;
  overflow: hidden;
  overflow-y: scroll;
  padding-right: 25px;
}
.chat-card .card-body .chat-list .chat-item {
  margin-bottom: 10px;
  padding-left: 60px;
  max-width: 700px;
}
.chat-card .card-body .chat-list .chat-item .user {
  position: absolute;
  left: 0;
  top: 0;
}
.chat-card .card-body .chat-list .chat-item .message {
  margin-bottom: 10px;
  padding-right: 70px;
  display: inline-block;
}
.chat-card .card-body .chat-list .chat-item .message .inner {
  padding: 20px;
  background: #F3F2FA;
  border-radius: 0 10px 10px 10px;
}
.chat-card .card-body .chat-list .chat-item .message .inner p {
  margin-bottom: 12px;
  color: #72708A;
}
.chat-card .card-body .chat-list .chat-item .message .inner p:last-child {
  margin-bottom: 0;
}
.chat-card .card-body .chat-list .chat-item .message .dropdown {
  top: 50%;
  z-index: 2;
  right: 40px;
  position: absolute;
  transform: translateY(-50%);
}
.chat-card .card-body .chat-list .chat-item .message .emoji {
  right: 0;
  top: 50%;
  margin-top: 3px;
  position: absolute;
  transform: translateY(-50%);
}
.chat-card .card-body .chat-list .chat-item .message .emoji button {
  font-size: 22px;
  color: var(--splash-muted-color);
}
.chat-card .card-body .chat-list .chat-item .message .emoji button:hover {
  color: var(--splash-primary-color);
}
.chat-card .card-body .chat-list .chat-item .message:last-child {
  margin-bottom: 0;
}
.chat-card .card-body .chat-list .chat-item.right {
  padding-left: 0;
  text-align: end;
  margin-left: auto;
}
.chat-card .card-body .chat-list .chat-item.right .message {
  padding-right: 0;
  padding-left: 70px;
}
.chat-card .card-body .chat-list .chat-item.right .message .inner {
  border-radius: 10px 10px 0px 10px;
  background: var(--splash-primary-color);
}
.chat-card .card-body .chat-list .chat-item.right .message .inner p {
  color: #F1F0F5;
}
.chat-card .card-body .chat-list .chat-item.right .message .dropdown {
  right: auto;
  left: 35px;
}
.chat-card .card-body .chat-list .chat-item.right .message .emoji {
  right: auto;
  left: 0;
}
.chat-card .card-body .chat-list .chat-item:last-child {
  margin-bottom: 0;
}
.chat-card .card-body .chat-list .chat-item.info {
  max-width: 100%;
  z-index: 1;
  margin-top: 30px;
  margin-bottom: 30px;
}
.chat-card .card-body .chat-list .chat-item.info::before {
  left: 0;
  top: 50%;
  right: 0;
  z-index: -1;
  height: 1px;
  content: "";
  position: absolute;
  transform: translateY(-50%);
  border-bottom: 1px dashed #D2CFE4;
}
.chat-card .card-body .chat-list::-webkit-scrollbar {
  -webkit-appearance: none;
}
.chat-card .card-body .chat-list::-webkit-scrollbar:vertical {
  width: 5px;
}
.chat-card .card-body .chat-list::-webkit-scrollbar:horizontal {
  height: 5px;
}
.chat-card .card-body .chat-list::-webkit-scrollbar-thumb {
  border-radius: 50px;
  background: #6D6C7D;
}
.chat-card .card-body .chat-list::-webkit-scrollbar-track {
  background: #DCDCE3;
}
.chat-card .card-body .write-your-message {
  padding-right: 220px;
  margin-top: 25px;
}
.chat-card .card-body .write-your-message .write-message {
  padding: 16px 20px 16px 50px;
  background: #F5F4FA;
  border-radius: 10px;
}
.chat-card .card-body .write-your-message .write-message .input-message {
  border: none;
  background-color: transparent;
  border-left: 1px solid #A4A3B0;
  padding-top: 3px;
  padding-left: 11px;
  padding-bottom: 3px;
  padding-right: 10px;
}
.chat-card .card-body .write-your-message .write-message .input-message::placeholder {
  color: #8E8DA1;
  transition: var(--transition);
}
.chat-card .card-body .write-your-message .write-message .input-message:focus::placeholder {
  color: transparent;
}
.chat-card .card-body .write-your-message .write-message button {
  top: 50%;
  left: 15px;
  font-size: 25px;
  margin-top: 3px;
  position: absolute;
  transform: translateY(-50%);
  color: var(--splash-muted-color);
}
.chat-card .card-body .write-your-message .write-message button:hover {
  color: var(--splash-primary-color);
}
.chat-card .card-body .write-your-message .buttons-list {
  top: 50%;
  right: 0;
  position: absolute;
  transform: translateY(-50%);
}
.chat-card .card-body .write-your-message .buttons-list button {
  width: 59px;
  height: 59px;
  font-size: 22px;
  margin-right: 10px;
  border-radius: 10px;
  background: #F5F4FA;
  color: var(--splash-muted-color);
}
.chat-card .card-body .write-your-message .buttons-list button i {
  left: 0;
  right: 0;
  top: 50%;
  line-height: 1;
  margin-top: 1px;
  position: absolute;
  transform: translateY(-50%);
}
.chat-card .card-body .write-your-message .buttons-list button:last-child {
  margin-right: 0;
}
.chat-card .card-body .write-your-message .buttons-list button.active, .chat-card .card-body .write-your-message .buttons-list button:hover {
  background-color: var(--splash-success-color);
  color: var(--splash-white-color);
}
.chat-card.chat-group-card .card-body .chat-list {
  height: 718px;
}

.dark .chat-sidebar .card-body .search-box .form-control {
  background: var(--splash-black-color);
}
.dark .chat-sidebar .card-body .chat-tabs a {
  color: #BCBBC7;
}
.dark .chat-sidebar .card-body .chat-tabs a:hover, .dark .chat-sidebar .card-body .chat-tabs a.active {
  color: var(--splash-white-color);
}
.dark .chat-sidebar .card-body .chat-users-list .single-user-item {
  border-bottom-color: #45445e;
}
.dark .chat-sidebar .card-body .chat-users-list .single-user-item:first-child {
  border-top-color: #45445e;
}
.dark .chat-sidebar .card-body .chat-users-list .single-user-item::before {
  background: var(--splash-black-color);
}
.dark .chat-sidebar .card-body .chat-users-list::-webkit-scrollbar-thumb {
  background: #6D6C7D;
}
.dark .chat-sidebar .card-body .chat-users-list::-webkit-scrollbar-track {
  background: #0D0C1D;
}
.dark .chat-sidebar .card-body .chat-users-list .box {
  border-bottom-color: #45445e;
}
.dark .chat-sidebar .card-body .chat-users-list .box span {
  background: var(--splash-black-color);
}
.dark .chat-card .card-head .users-list div {
  border-color: #45445e;
}
.dark .chat-card .card-body .chat-list .chat-item .message .inner {
  background: var(--splash-black-color);
}
.dark .chat-card .card-body .chat-list .chat-item .message .inner p {
  color: #BCBBC7;
}
.dark .chat-card .card-body .chat-list .chat-item .message .emoji button {
  color: #BCBBC7;
}
.dark .chat-card .card-body .chat-list .chat-item .message .emoji button:hover {
  color: var(--splash-primary-color);
}
.dark .chat-card .card-body .chat-list .chat-item.right .message .inner {
  background: var(--splash-primary-color);
}
.dark .chat-card .card-body .chat-list .chat-item.right .message .inner p {
  color: #F1F0F5;
}
.dark .chat-card .card-body .chat-list .chat-item.info::before {
  border-bottom-color: #45445e;
}
.dark .chat-card .card-body .chat-list::-webkit-scrollbar-thumb {
  background: #6D6C7D;
}
.dark .chat-card .card-body .chat-list::-webkit-scrollbar-track {
  background: #0D0C1D;
}
.dark .chat-card .card-body .write-your-message .write-message {
  background: var(--splash-black-color);
}
.dark .chat-card .card-body .write-your-message .write-message .input-message {
  border-left-color: #45445e;
}
.dark .chat-card .card-body .write-your-message .write-message .input-message::placeholder {
  color: #BCBBC7;
}
.dark .chat-card .card-body .write-your-message .write-message .input-message:focus::placeholder {
  color: transparent;
}
.dark .chat-card .card-body .write-your-message .write-message button {
  color: #BCBBC7;
}
.dark .chat-card .card-body .write-your-message .write-message button:hover {
  color: var(--splash-primary-color);
}
.dark .chat-card .card-body .write-your-message .buttons-list button {
  background: var(--splash-black-color);
  color: #BCBBC7;
}
.dark .chat-card .card-body .write-your-message .buttons-list button.active, .dark .chat-card .card-body .write-your-message .buttons-list button:hover {
  background-color: var(--splash-success-color);
  color: var(--splash-white-color);
}

@media only screen and (max-width: 767px) {
  .chat-sidebar .card-body .search-box .form-control {
    padding-top: 13px;
    padding-bottom: 13px;
  }
  .chat-sidebar .card-body .search-box button {
    right: 15px;
    font-size: 15px;
  }
  .chat-sidebar .card-body .chat-tabs a {
    padding: 8px 15px;
  }
  .chat-sidebar .card-body .create-new-group-btn {
    margin-top: 15px;
  }
  .chat-sidebar .card-body .create-new-group-btn button {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .chat-sidebar .card-body .chat-users-list {
    height: 500px;
    margin-top: 15px;
    margin-left: -15px;
    margin-right: -15px;
  }
  .chat-sidebar .card-body .chat-users-list .single-user-item {
    padding-top: 15px;
    padding-left: 15px;
    padding-right: 35px;
    padding-bottom: 15px;
  }
  .chat-sidebar .card-body .chat-users-list .single-user-item .dot-badge {
    right: 12px;
    width: 20px;
    height: 20px;
    line-height: 20px;
  }
  .chat-sidebar .card-body .chat-users-list .single-user-item::before {
    margin: 3px;
  }
  .chat-sidebar .card-body .chat-users-list .box {
    margin-top: 20px;
  }
  .chat-card .card-body .chat-list {
    height: 500px;
    padding-right: 10px;
  }
  .chat-card .card-body .chat-list .chat-item {
    padding-left: 0;
    max-width: 100%;
  }
  .chat-card .card-body .chat-list .chat-item .user {
    position: relative;
    margin-bottom: 11px;
  }
  .chat-card .card-body .chat-list .chat-item .message {
    padding-right: 0;
  }
  .chat-card .card-body .chat-list .chat-item .message .inner {
    padding: 12px;
  }
  .chat-card .card-body .chat-list .chat-item .message .dropdown {
    top: 0;
    right: 0;
    position: relative;
    display: inline-block;
    transform: translateY(0);
  }
  .chat-card .card-body .chat-list .chat-item .message .emoji {
    top: 4px;
    right: 0;
    margin-top: 5px;
    position: relative;
    display: inline-block;
    transform: translateY(0);
    margin-top: 0;
    margin-left: 5px;
  }
  .chat-card .card-body .chat-list .chat-item.right {
    margin-left: 0;
  }
  .chat-card .card-body .chat-list .chat-item.right .message {
    padding-right: 0;
    padding-left: 0;
  }
  .chat-card .card-body .chat-list .chat-item.right .message .dropdown {
    right: 0;
    left: 0;
  }
  .chat-card .card-body .chat-list .chat-item.right .message .emoji {
    right: 0;
    left: 0;
  }
  .chat-card .card-body .chat-list .chat-item.info {
    margin-top: 10px;
    margin-bottom: 10px;
  }
  .chat-card .card-body .write-your-message {
    padding-right: 0;
    margin-top: 15px;
  }
  .chat-card .card-body .write-your-message .write-message {
    padding: 15px 15px 15px 40px;
  }
  .chat-card .card-body .write-your-message .write-message .input-message {
    padding-top: 2px;
    padding-bottom: 2px;
  }
  .chat-card .card-body .write-your-message .write-message button {
    left: 10px;
    font-size: 20px;
  }
  .chat-card .card-body .write-your-message .buttons-list {
    top: 0;
    margin-top: 12px;
    position: relative;
    transform: translateY(0);
  }
  .chat-card .card-body .write-your-message .buttons-list button {
    width: 50px;
    height: 50px;
    font-size: 20px;
    margin-right: 5px;
  }
  .chat-card .card-body .write-your-message .buttons-list button i {
    margin-top: 1px;
  }
  .chat-card.chat-group-card .card-body .chat-list {
    height: 500px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .chat-sidebar .card-body .create-new-group-btn {
    margin-top: 15px;
  }
  .chat-sidebar .card-body .chat-users-list {
    height: 550px;
    margin-top: 15px;
  }
  .chat-card .card-body .chat-list {
    height: 600px;
  }
  .chat-card .card-body .chat-list .chat-item {
    max-width: 600px;
  }
  .chat-card.chat-group-card .card-body .chat-list {
    height: 600px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .chat-sidebar .card-body .chat-users-list .single-user-item .title span {
    display: block;
    margin-left: 0 !important;
    margin-bottom: 3px;
    margin-top: 3px;
  }
  .chat-card .card-body .chat-list .chat-item {
    max-width: 545px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .chat-sidebar .card-body .chat-users-list .single-user-item .title span {
    display: block;
    margin-left: 0 !important;
    margin-bottom: 3px;
    margin-top: 3px;
  }
}
.flatpickr-calendar {
  border-color: #d8e2ef;
  box-shadow: 0px 4px 34px rgba(101, 96, 240, 0.15);
}
.flatpickr-calendar .flatpickr-months {
  background-color: #f4f4f4;
}
.flatpickr-calendar .flatpickr-months .flatpickr-prev-month, .flatpickr-calendar .flatpickr-months .flatpickr-next-month {
  padding: 0;
  width: 30px;
  height: 45px;
  line-height: 45px;
  text-align: center;
}
.flatpickr-calendar .flatpickr-months .flatpickr-month {
  height: 45px;
}
.flatpickr-calendar .flatpickr-current-month {
  line-height: 45px;
  font-size: 120%;
  height: 45px;
  padding: 0;
}
.flatpickr-calendar .flatpickr-current-month .flatpickr-monthDropdown-months {
  font-weight: 700;
}
.flatpickr-calendar .flatpickr-current-month .flatpickr-monthDropdown-months:hover {
  background: transparent;
}
.flatpickr-calendar .numInputWrapper span {
  background: var(--splash-white-color);
}
.flatpickr-calendar .numInputWrapper:hover {
  background: transparent;
}
.flatpickr-calendar .flatpickr-current-month input.cur-year {
  font-weight: 700;
}
.flatpickr-calendar .flatpickr-day {
  font-weight: 600;
  line-height: 37px;
}
.flatpickr-calendar .flatpickr-day.selected, .flatpickr-calendar .flatpickr-day.startRange, .flatpickr-calendar .flatpickr-day.endRange, .flatpickr-calendar .flatpickr-day.selected.inRange, .flatpickr-calendar .flatpickr-day.startRange.inRange, .flatpickr-calendar .flatpickr-day.endRange.inRange, .flatpickr-calendar .flatpickr-day.selected:focus, .flatpickr-calendar .flatpickr-day.startRange:focus, .flatpickr-calendar .flatpickr-day.endRange:focus, .flatpickr-calendar .flatpickr-day.selected:hover, .flatpickr-calendar .flatpickr-day.startRange:hover, .flatpickr-calendar .flatpickr-day.endRange:hover, .flatpickr-calendar .flatpickr-day.selected.prevMonthDay, .flatpickr-calendar .flatpickr-day.startRange.prevMonthDay, .flatpickr-calendar .flatpickr-day.endRange.prevMonthDay, .flatpickr-calendar .flatpickr-day.selected.nextMonthDay, .flatpickr-calendar .flatpickr-day.startRange.nextMonthDay, .flatpickr-calendar .flatpickr-day.endRange.nextMonthDay {
  background: var(--splash-primary-color);
  border-color: var(--splash-primary-color);
}
.flatpickr-calendar span.flatpickr-weekday {
  color: var(--splash-black-color);
  font-weight: 700;
  font-size: 14px;
}
.flatpickr-calendar.arrowTop:before, .flatpickr-calendar.arrowTop:after {
  display: none;
}

.createNewModal .btn-close {
  top: 20px;
  padding: 0;
  right: 20px;
  border-radius: 0;
  position: absolute;
}

.lead-info-box .profile-info img {
  width: 100px;
  height: 100px;
}
.lead-info-box .profile-info .title {
  margin-left: 18px;
}
.lead-info-box .border-top {
  border-top: 1px dashed #d9e9ef !important;
}
.lead-info-box .info li {
  margin-bottom: 25px;
  padding-left: 65px;
}
.lead-info-box .info li .icon {
  left: 0;
  top: 50%;
  width: 50px;
  height: 50px;
  font-size: 20px;
  position: absolute;
  background: #ECF3F2;
  transform: translateY(-50%);
}
.lead-info-box .info li .icon i {
  left: 0;
  right: 0;
  top: 50%;
  line-height: 1;
  margin-top: 1px;
  position: absolute;
  transform: translateY(-50%);
}
.lead-info-box .info li:last-child {
  margin-bottom: 0;
}

.leads-details-tabs .nav.nav-tabs .nav-item {
  flex: 1 0 0%;
}
.leads-details-tabs .nav.nav-tabs .nav-item .nav-link {
  background-color: var(--splash-white-color);
  color: var(--splash-black-color);
  padding: 25px 15px;
  margin-bottom: 0;
}
.leads-details-tabs .nav.nav-tabs .nav-item .nav-link.active {
  background-color: var(--splash-primary-color);
  color: var(--splash-white-color);
}
.leads-details-tabs .to-do-list-box .form-check .form-check-input:checked + label {
  text-decoration: line-through;
}
.leads-details-tabs .attachments-box .list li {
  border-top: 1px dashed #d9e9ef;
  padding-top: 20px;
  padding-bottom: 20px;
}
.leads-details-tabs .attachments-box .list li:last-child {
  padding-bottom: 0;
}

.dark .lead-info-box .border-top {
  border-top-color: #45445e !important;
}
.dark .lead-info-box .info li .icon {
  background: var(--splash-black-color);
}
.dark .leads-details-tabs .nav.nav-tabs .nav-item .nav-link {
  background-color: #34334a;
  color: var(--splash-white-color);
}
.dark .leads-details-tabs .nav.nav-tabs .nav-item .nav-link.active {
  background-color: var(--splash-primary-color);
}
.dark .leads-details-tabs .attachments-box .list li {
  border-top-color: #45445e !important;
}

@media only screen and (max-width: 767px) {
  .lead-info-box .profile-info .title {
    margin-left: 0;
  }
  .lead-info-box .info li {
    margin-bottom: 15px;
  }
  .leads-details-tabs .nav.nav-tabs .nav-item .nav-link {
    font-size: 14px;
    padding: 10px 15px;
  }
  .leads-details-tabs .attachments-box .list li {
    padding-top: 15px;
    padding-bottom: 15px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .lead-info-box .profile-info .title {
    margin-left: 18px;
  }
  .leads-details-tabs .nav.nav-tabs .nav-item .nav-link {
    padding: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .lead-info-box .info li {
    margin-bottom: 20px;
  }
  .leads-details-tabs .nav.nav-tabs .nav-item .nav-link {
    padding: 20px 15px;
  }
  .leads-details-tabs .attachments-box .list li {
    padding-top: 15px;
    padding-bottom: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .leads-details-tabs .nav.nav-tabs .nav-item .nav-link {
    padding: 20px 15px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .lead-info-box .profile-info.d-sm-flex {
    display: block !important;
  }
  .lead-info-box .profile-info .title {
    margin-left: 0;
    margin-top: 12px !important;
  }
}
@media only screen and (min-width: 1600px) {
  .lead-info-box .profile-info img {
    width: 150px;
    height: 150px;
  }
  .lead-info-box .profile-info .title {
    margin-left: 25px;
  }
}
.emails-sidebar-box .search-box .form-control {
  background: #F5F4FA;
  padding-top: 14px;
  padding-bottom: 14px;
}
.emails-sidebar-box .search-box button {
  top: 50%;
  right: 20px;
  line-height: 1;
  margin-top: 1px;
  font-size: 17px;
  position: absolute;
  transform: translateY(-50%);
}
.emails-sidebar-box .list li {
  border-bottom: 1px dashed #d9e9ef;
  padding-top: 5px;
  padding-bottom: 5px;
}
.emails-sidebar-box .list li a {
  position: relative;
  z-index: 1;
  padding-top: 15px;
  padding-bottom: 15px;
}
.emails-sidebar-box .list li a .title {
  position: relative;
  padding-left: 30px;
}
.emails-sidebar-box .list li a .title i {
  left: 0;
  top: 50%;
  line-height: 1;
  font-size: 18px;
  color: #79788E;
  position: absolute;
  transform: translateY(-50%);
}
.emails-sidebar-box .list li a .dot-badge {
  top: 50%;
  right: 0;
  width: 25px;
  height: 25px;
  line-height: 25px;
  position: absolute;
  transform: translateY(-50%);
}
.emails-sidebar-box .list li a::before {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  content: "";
  visibility: hidden;
  position: absolute;
  background: #EFEEF9;
  transition: var(--transition);
  margin-left: -20px;
  margin-right: -20px;
}
.emails-sidebar-box .list li a:hover::before, .emails-sidebar-box .list li a.active::before {
  opacity: 1;
  visibility: visible;
}
.emails-sidebar-box .list li:first-child {
  border-top: 1px dashed #d9e9ef;
}

.emails-list-box .card-head .left .form-check .form-check-input {
  border-color: #BFBECA;
}
.emails-list-box .card-head .left .form-check .form-check-input:checked {
  border-color: var(--splash-primary-color);
}
.emails-list-box .card-head .left button {
  font-size: 20px;
}
.emails-list-box .card-head .right .info-list li {
  margin-left: 5px;
  margin-right: 5px;
}
.emails-list-box .card-head .right .info-list li button {
  font-size: 18px;
}
.emails-list-box .card-head .right .info-list li:first-child {
  margin-left: 0;
}
.emails-list-box .card-head .right .info-list li:last-child {
  margin-right: 0;
}
.emails-list-box .emails-list .email-item {
  border-bottom: 1px dashed #d9e9ef;
  padding-left: 25px;
  padding-top: 14.9px;
  padding-right: 25px;
  padding-bottom: 14.9px;
}
.emails-list-box .emails-list .email-item .email-info .form-check .form-check-input {
  border-color: #BFBECA;
}
.emails-list-box .emails-list .email-item .email-info .form-check .form-check-input:checked {
  border-color: var(--splash-primary-color);
}
.emails-list-box .emails-list .email-item .email-info .badge {
  display: inline-block;
  border-radius: 30px;
  padding-top: 5px;
  padding-bottom: 5px;
}
.emails-list-box .emails-list .email-item .email-title p {
  white-space: nowrap;
  overflow: hidden;
}
.emails-list-box .emails-list .email-item .email-title .buttons-list {
  margin-top: 10px;
}
.emails-list-box .emails-list .email-item .email-title .buttons-list a {
  border: 1px solid #D0CFDD;
  padding: 5px 12px 5px 35px;
  border-radius: 20px;
  margin-right: 10px;
}
.emails-list-box .emails-list .email-item .email-title .buttons-list a i {
  top: 50%;
  left: 12px;
  line-height: 1;
  font-size: 17px;
  margin-top: 1px;
  position: absolute;
  transform: translateY(-50%);
  color: var(--splash-primary-color);
}
.emails-list-box .emails-list .email-item .email-title .buttons-list a:hover {
  border-color: var(--splash-primary-color);
}
.emails-list-box .emails-list .email-item .email-title .buttons-list a:last-child {
  margin-right: 0;
}
.emails-list-box .emails-list .email-item .info-list {
  top: 18px;
  z-index: 2;
  opacity: 0;
  right: 25px;
  padding-left: 15px;
  visibility: hidden;
  position: absolute;
  transition: var(--transition);
  background-color: var(--splash-white-color);
}
.emails-list-box .emails-list .email-item .info-list li {
  margin-left: 5px;
  margin-right: 5px;
}
.emails-list-box .emails-list .email-item .info-list li button {
  font-size: 18px;
}
.emails-list-box .emails-list .email-item .info-list li:first-child {
  margin-left: 0;
}
.emails-list-box .emails-list .email-item .info-list li:last-child {
  margin-right: 0;
}
.emails-list-box .emails-list .email-item .time {
  margin-left: -10px;
}
.emails-list-box .emails-list .email-item .link-btn {
  margin-left: 85px;
}
.emails-list-box .emails-list .email-item.bg-color {
  z-index: 1;
  position: relative;
}
.emails-list-box .emails-list .email-item.bg-color::before {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  content: "";
  position: absolute;
  background: #F9F9FC;
  margin-top: 3px;
  margin-bottom: 3px;
}
.emails-list-box .emails-list .email-item.bg-color .info-list {
  background: #F9F9FC;
}
.emails-list-box .emails-list .email-item:first-child {
  border-top: 1px dashed #d9e9ef;
}
.emails-list-box .emails-list .email-item:hover .info-list {
  opacity: 1;
  visibility: visible;
}

.compose-email-modal {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  z-index: 99;
  position: fixed;
  visibility: hidden;
  transition: var(--transition);
}
.compose-email-modal .compose-email-modal-dialog {
  right: 25px;
  bottom: 25px;
  width: 770px;
  position: absolute;
  background-color: var(--splash-white-color);
  box-shadow: 0px 4px 34px rgba(101, 96, 240, 0.3);
}
.compose-email-modal .compose-email-modal-dialog .compose-email-modal-header {
  box-shadow: 0px 4px 34px rgba(101, 96, 240, 0.1);
}
.compose-email-modal.active {
  opacity: 1;
  visibility: visible;
}

.dark .emails-sidebar-box .search-box .form-control {
  background: var(--splash-black-color);
}
.dark .emails-sidebar-box .list li {
  border-bottom-color: #45445e;
}
.dark .emails-sidebar-box .list li a .title i {
  color: #BCBBC7;
}
.dark .emails-sidebar-box .list li a::before {
  background: var(--splash-black-color);
}
.dark .emails-sidebar-box .list li:first-child {
  border-top-color: #45445e;
}
.dark .emails-list-box .card-head .left .form-check .form-check-input {
  border-color: #45445e;
}
.dark .emails-list-box .card-head .left .form-check .form-check-input:checked {
  border-color: var(--splash-primary-color);
}
.dark .emails-list-box .emails-list .email-item {
  border-bottom-color: #45445e;
}
.dark .emails-list-box .emails-list .email-item .email-info .form-check .form-check-input {
  border-color: #45445e;
}
.dark .emails-list-box .emails-list .email-item .email-info .form-check .form-check-input:checked {
  border-color: var(--splash-primary-color);
}
.dark .emails-list-box .emails-list .email-item .email-title .buttons-list a {
  border-color: #45445e;
}
.dark .emails-list-box .emails-list .email-item .email-title .buttons-list a:hover {
  border-color: var(--splash-primary-color);
}
.dark .emails-list-box .emails-list .email-item .info-list {
  background-color: var(--splash-black-color);
}
.dark .emails-list-box .emails-list .email-item.bg-color::before {
  background: #222136;
}
.dark .emails-list-box .emails-list .email-item.bg-color .info-list {
  background: #222136;
}
.dark .emails-list-box .emails-list .email-item:first-child {
  border-top-color: #45445e;
}
.dark .compose-email-modal .compose-email-modal-dialog {
  background-color: var(--splash-black-color);
  box-shadow: unset;
}
.dark .compose-email-modal .compose-email-modal-dialog .compose-email-modal-header {
  box-shadow: unset;
  border-bottom: 1px dashed #45445e;
}
.dark .ql-toolbar.ql-snow {
  border-color: #45445e;
  color: var(--splash-white-color);
}
.dark .ql-toolbar.ql-snow .ql-picker {
  color: #BCBBC7;
}
.dark .ql-toolbar.ql-snow .ql-stroke {
  stroke: #BCBBC7;
}
.dark .ql-toolbar.ql-snow .ql-fill {
  stroke: #BCBBC7;
}
.dark .ql-editor.ql-blank::before {
  color: #BCBBC7;
}
.dark .ql-container.ql-snow {
  border-color: #45445e;
}

@media only screen and (max-width: 767px) {
  .emails-sidebar-box .search-box .form-control {
    padding-top: 13px;
    padding-bottom: 13px;
  }
  .emails-sidebar-box .search-box button {
    right: 15px;
    font-size: 15px;
  }
  .emails-sidebar-box .list li a {
    padding-top: 12px;
    padding-bottom: 12px;
  }
  .emails-sidebar-box .list li a .title {
    padding-left: 25px;
  }
  .emails-sidebar-box .list li a .title i {
    font-size: 15px;
  }
  .emails-sidebar-box .list li a .dot-badge {
    width: 20px;
    height: 20px;
    line-height: 20px;
  }
  .emails-sidebar-box .list li a::before {
    margin-left: -10px;
    margin-right: -10px;
  }
  .emails-list-box .card-head .right button {
    top: -4px !important;
    font-size: 18px;
  }
  .emails-list-box .emails-list {
    max-height: unset;
    overflow-y: hidden;
  }
  .emails-list-box .emails-list .email-item {
    padding-top: 15px;
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 15px;
  }
  .emails-list-box .emails-list .email-item .email-info {
    margin-bottom: 8px;
  }
  .emails-list-box .emails-list .email-item .email-title {
    margin-bottom: 5px;
  }
  .emails-list-box .emails-list .email-item .email-title .buttons-list {
    margin-top: 5px;
  }
  .emails-list-box .emails-list .email-item .email-title .buttons-list a {
    margin-top: 5px;
  }
  .emails-list-box .emails-list .email-item .info-list {
    display: none;
  }
  .emails-list-box .emails-list .email-item .time {
    margin-left: 0;
  }
  .emails-list-box .emails-list .email-item .link-btn {
    margin-left: 0;
    margin-top: 45px;
  }
  .compose-email-modal .compose-email-modal-dialog {
    left: 0;
    right: 0;
    bottom: 0;
    width: auto;
    margin-left: 5px;
    margin-right: 5px;
  }
  .compose-email-modal .compose-email-modal-dialog .compose-email-modal-body #editor-container {
    height: 100px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .emails-list-box .card-head .right button {
    top: 1px !important;
  }
  .emails-list-box .emails-list .email-item {
    padding-left: 20px;
    padding-right: 20px;
  }
  .compose-email-modal .compose-email-modal-dialog {
    margin-left: 15px;
    margin-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .emails-list-box .emails-list {
    max-height: unset;
    overflow-y: hidden;
  }
  .emails-list-box .emails-list .email-item {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .emails-list-box .emails-list .email-item .email-info {
    margin-bottom: 11px;
  }
  .emails-list-box .emails-list .email-item .email-title {
    margin-bottom: 8px;
  }
  .emails-list-box .emails-list .email-item .info-list {
    top: 50%;
    transform: translateY(-50%);
  }
  .emails-list-box .emails-list .email-item .time {
    margin-left: 0;
  }
  .emails-list-box .emails-list .email-item .link-btn {
    margin-left: 0;
    margin-top: 45px;
  }
  .compose-email-modal .compose-email-modal-dialog {
    width: 680px;
  }
  .compose-email-modal .compose-email-modal-dialog .compose-email-modal-body #editor-container {
    height: 100px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .emails-list-box .emails-list .email-item {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .emails-list-box .emails-list .email-item .email-info {
    margin-bottom: 11px;
  }
  .emails-list-box .emails-list .email-item .email-title {
    margin-bottom: 6px;
  }
  .emails-list-box .emails-list .email-item .info-list {
    top: 50%;
    transform: translateY(-50%);
  }
  .emails-list-box .emails-list .email-item .time {
    margin-left: 0;
  }
  .emails-list-box .emails-list .email-item .link-btn {
    margin-left: 0;
    margin-top: 45px;
  }
  .compose-email-modal .compose-email-modal-dialog {
    width: 730px;
  }
  .compose-email-modal .compose-email-modal-dialog .compose-email-modal-body #editor-container {
    height: 100px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .emails-list-box .emails-list .email-item {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .emails-list-box .emails-list .email-item .email-info {
    margin-bottom: 11px;
  }
  .emails-list-box .emails-list .email-item .email-title {
    margin-bottom: 6px;
  }
  .emails-list-box .emails-list .email-item .info-list {
    top: 50%;
    transform: translateY(-50%);
  }
  .emails-list-box .emails-list .email-item .time {
    margin-left: 0;
  }
  .emails-list-box .emails-list .email-item .link-btn {
    margin-left: 0;
    margin-top: 45px;
  }
}
@media only screen and (min-width: 1600px) {
  .emails-list-box .emails-list .email-item .email-info .badge {
    margin-right: 35px;
  }
  .emails-list-box .emails-list .email-item .time {
    margin-left: 0;
  }
  .compose-email-modal .compose-email-modal-dialog {
    right: 40px;
    bottom: 40px;
  }
}
.read-email-box .card-head .link-btn {
  position: relative;
  margin-right: 5px;
  color: #787786;
  font-size: 20px;
  top: 1px;
}
.read-email-box .card-body .email-info {
  margin-bottom: 30px;
}
.read-email-box .card-body .email-info .title {
  padding-right: 15px;
}
.read-email-box .card-body .email-info .title span span {
  padding-left: 10px;
  padding-right: 10px;
}
.read-email-box .card-body .email-info .title span span i {
  position: absolute;
  margin-top: 3px;
  left: -3px;
  top: 0;
}
.read-email-box .card-body .email-info .title span span i:last-child {
  left: auto;
  right: -3px;
}
.read-email-box .card-body .email-info .title .info {
  padding-right: 12px;
}
.read-email-box .card-body .email-info .title .info .dropdown-toggle {
  top: 50%;
  right: -15px;
  font-size: 13px;
  position: absolute;
  transform: translateY(-50%);
  margin-right: -2px;
  margin-top: -8px;
}
.read-email-box .card-body .email-info .info-list li {
  margin-left: 5px;
  margin-right: 5px;
}
.read-email-box .card-body .email-info .info-list li button {
  font-size: 18px;
}
.read-email-box .card-body .email-info .info-list li:first-child {
  margin-left: 0;
}
.read-email-box .card-body .email-info .info-list li:last-child {
  margin-right: 0;
}
.read-email-box .card-body .email-details {
  padding-left: 55px;
}
.read-email-box .card-body .email-details .h1, .read-email-box .card-body .email-details .h2, .read-email-box .card-body .email-details .h3, .read-email-box .card-body .email-details .h4, .read-email-box .card-body .email-details .h5, .read-email-box .card-body .email-details .h6, .read-email-box .card-body .email-details h1, .read-email-box .card-body .email-details h2, .read-email-box .card-body .email-details h3, .read-email-box .card-body .email-details h4, .read-email-box .card-body .email-details h5, .read-email-box .card-body .email-details h6 {
  margin-bottom: 13px;
}
.read-email-box .card-body .email-details .h1:not(:first-child), .read-email-box .card-body .email-details .h2:not(:first-child), .read-email-box .card-body .email-details .h3:not(:first-child), .read-email-box .card-body .email-details .h4:not(:first-child), .read-email-box .card-body .email-details .h5:not(:first-child), .read-email-box .card-body .email-details .h6:not(:first-child), .read-email-box .card-body .email-details h1:not(:first-child), .read-email-box .card-body .email-details h2:not(:first-child), .read-email-box .card-body .email-details h3:not(:first-child), .read-email-box .card-body .email-details h4:not(:first-child), .read-email-box .card-body .email-details h5:not(:first-child), .read-email-box .card-body .email-details h6:not(:first-child) {
  margin-top: 25px;
}
.read-email-box .card-body .email-details .h1:last-child, .read-email-box .card-body .email-details .h2:last-child, .read-email-box .card-body .email-details .h3:last-child, .read-email-box .card-body .email-details .h4:last-child, .read-email-box .card-body .email-details .h5:last-child, .read-email-box .card-body .email-details .h6:last-child, .read-email-box .card-body .email-details h1:last-child, .read-email-box .card-body .email-details h2:last-child, .read-email-box .card-body .email-details h3:last-child, .read-email-box .card-body .email-details h4:last-child, .read-email-box .card-body .email-details h5:last-child, .read-email-box .card-body .email-details h6:last-child {
  margin-bottom: 0;
}
.read-email-box .card-body .email-details p {
  margin-bottom: 15px;
}
.read-email-box .card-body .email-details p:last-child {
  margin-bottom: 0;
}
.read-email-box .card-body .email-buttons {
  margin-top: 25px;
  border-top: 1px dashed #d9e9ef;
  padding-top: 25px;
  padding-left: 55px;
}
.read-email-box .card-body .email-buttons button {
  margin-right: 10px;
}
.read-email-box .card-body .email-buttons button:last-child {
  margin-right: 0;
}

.dark .read-email-box .card-head .link-btn {
  color: #BCBBC7;
}
.dark .read-email-box .card-body .email-buttons {
  border-top-color: #45445e;
}

@media only screen and (max-width: 767px) {
  .read-email-box .card-body .email-info {
    margin-bottom: 15px;
  }
  .read-email-box .card-body .email-info .title {
    padding-right: 0;
  }
  .read-email-box .card-body .email-info .title span span {
    display: block;
    padding-left: 0;
    padding-right: 0;
    padding-top: 1px;
  }
  .read-email-box .card-body .email-info .title span span i {
    display: none;
  }
  .read-email-box .card-body .email-info .info-list li {
    margin-left: 4px;
    margin-right: 4px;
  }
  .read-email-box .card-body .email-info .info-list li button {
    font-size: 17px;
  }
  .read-email-box .card-body .email-details {
    padding-left: 0;
  }
  .read-email-box .card-body .email-details .h1, .read-email-box .card-body .email-details .h2, .read-email-box .card-body .email-details .h3, .read-email-box .card-body .email-details .h4, .read-email-box .card-body .email-details .h5, .read-email-box .card-body .email-details .h6, .read-email-box .card-body .email-details h1, .read-email-box .card-body .email-details h2, .read-email-box .card-body .email-details h3, .read-email-box .card-body .email-details h4, .read-email-box .card-body .email-details h5, .read-email-box .card-body .email-details h6 {
    margin-bottom: 12px;
  }
  .read-email-box .card-body .email-details .h1:not(:first-child), .read-email-box .card-body .email-details .h2:not(:first-child), .read-email-box .card-body .email-details .h3:not(:first-child), .read-email-box .card-body .email-details .h4:not(:first-child), .read-email-box .card-body .email-details .h5:not(:first-child), .read-email-box .card-body .email-details .h6:not(:first-child), .read-email-box .card-body .email-details h1:not(:first-child), .read-email-box .card-body .email-details h2:not(:first-child), .read-email-box .card-body .email-details h3:not(:first-child), .read-email-box .card-body .email-details h4:not(:first-child), .read-email-box .card-body .email-details h5:not(:first-child), .read-email-box .card-body .email-details h6:not(:first-child) {
    margin-top: 20px;
  }
  .read-email-box .card-body .email-buttons {
    margin-top: 15px;
    padding-top: 15px;
    padding-left: 0;
  }
}
.products-sidebar-filter .title {
  border-bottom: 1px dashed #d9e9ef;
  margin-bottom: 25px;
  padding: 25px 30px;
}
.products-sidebar-filter .sidebar-item {
  padding-left: 30px;
  padding-right: 30px;
  padding-bottom: 50px;
}
.products-sidebar-filter .sidebar-item h6 {
  margin-bottom: 20px;
}
.products-sidebar-filter .sidebar-item .search-box .form-control {
  background: #F5F4FA;
  padding-top: 14px;
  padding-bottom: 14px;
}
.products-sidebar-filter .sidebar-item .search-box button {
  top: 50%;
  right: 15px;
  line-height: 1;
  margin-top: 1px;
  font-size: 17px;
  position: absolute;
  transform: translateY(-50%);
}
.products-sidebar-filter .sidebar-item .previous-searches-list {
  margin-bottom: -10px;
}
.products-sidebar-filter .sidebar-item .previous-searches-list .item {
  padding: 4px 22px 4px 8px;
  background: #F7F7F9;
  border-radius: 2px;
  margin-right: 10px;
  margin-bottom: 10px;
}
.products-sidebar-filter .sidebar-item .previous-searches-list .item button {
  top: 50%;
  right: 8px;
  line-height: 1;
  color: #8E8DA2;
  font-size: 10px;
  margin-top: 1px;
  position: absolute;
  transform: translateY(-50%);
}
.products-sidebar-filter .sidebar-item .previous-searches-list .item button:hover {
  color: red;
}
.products-sidebar-filter .sidebar-item .clear-btn {
  line-height: 1.2;
}
.products-sidebar-filter .sidebar-item .clear-btn::before {
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  content: "";
  position: absolute;
  transition: var(--transition);
  background: var(--splash-black-color);
}
.products-sidebar-filter .sidebar-item .clear-btn:hover::before {
  transform: scaleX(0);
}
.products-sidebar-filter .sidebar-item .categories-list li {
  border-bottom: 1px solid #EEEDF2;
  padding-top: 10px;
  padding-bottom: 10px;
}
.products-sidebar-filter .sidebar-item .categories-list li:first-child {
  padding-top: 0;
}
.products-sidebar-filter .sidebar-item .brands-list li {
  border-bottom: 1px solid #EEEDF2;
  padding-top: 10px;
  padding-bottom: 10px;
}
.products-sidebar-filter .sidebar-item .brands-list li:first-child {
  padding-top: 0;
}
.products-sidebar-filter .sidebar-item .see-more-btn {
  line-height: 1.25;
}
.products-sidebar-filter .sidebar-item .see-more-btn::before {
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  content: "";
  position: absolute;
  transition: var(--transition);
  background: var(--splash-primary-color);
}
.products-sidebar-filter .sidebar-item .see-more-btn:hover::before {
  transform: scaleX(0);
}
.products-sidebar-filter .sidebar-item .ratings-list li {
  border-bottom: 1px solid #EEEDF2;
  padding-top: 10px;
  padding-bottom: 10px;
}
.products-sidebar-filter .sidebar-item .ratings-list li span i {
  top: 1px;
  line-height: 1;
  color: #F3C44C;
  margin-right: 4px;
  position: relative;
}
.products-sidebar-filter .sidebar-item .ratings-list li:first-child {
  padding-top: 0;
}
.products-sidebar-filter .sidebar-item .pricing-filter .range-slider {
  position: relative;
  height: 5px;
}
.products-sidebar-filter .sidebar-item .pricing-filter .range-slider input[type=range] {
  -webkit-appearance: none;
  background: transparent;
  border-radius: 5px;
  position: absolute;
  width: 100%;
  left: 0;
  top: 0;
}
.products-sidebar-filter .sidebar-item .pricing-filter .range-slider input[type=range]::-webkit-slider-thumb {
  background: var(--splash-primary-color);
  -webkit-appearance: none;
  position: relative;
  border-radius: 50%;
  margin-top: -5px;
  cursor: pointer;
  height: 14px;
  width: 14px;
  z-index: 1;
}
.products-sidebar-filter .sidebar-item .pricing-filter .range-slider input[type=range]::-webkit-slider-runnable-track {
  width: 100%;
  height: 5px;
  border: none;
  border-radius: 5px;
  background: #f4f4f4;
}
.products-sidebar-filter .sidebar-item .pricing-filter .price-content {
  margin-top: 12px;
}

.single-product-box .card-body .image .fav {
  color: #EF2929;
  font-size: 18px;
  height: 38px;
  width: 38px;
  right: 15px;
  top: 15px;
}
.single-product-box .card-body .image .fav i {
  left: 0;
  right: 0;
  top: 50%;
  line-height: 1;
  margin-top: 1px;
  position: absolute;
  transform: translateY(-50%);
}
.single-product-box .card-body .content .reviews .rating {
  color: #F3C44C;
}
.single-product-box .card-body .content .reviews .rating i {
  margin-right: 4px;
}
.single-product-box .card-body .content .add-to-cart-btn {
  background-color: transparent;
  padding: 11px 35px 11px 20px;
  border: 1px solid #EDEBF3;
}
.single-product-box .card-body .content .add-to-cart-btn i {
  top: 50%;
  line-height: 1;
  font-size: 16px;
  position: absolute;
  transform: translateY(-50%);
  color: var(--splash-primary-color);
  margin-left: 7px;
}
.single-product-box .card-body .content .add-to-cart-btn:hover {
  background-color: var(--splash-primary-color);
  border-color: var(--splash-primary-color);
  color: var(--splash-white-color);
}
.single-product-box .card-body .content .add-to-cart-btn:hover i {
  color: var(--splash-white-color);
}

.dark .products-sidebar-filter .title {
  border-bottom-color: #45445e;
}
.dark .products-sidebar-filter .sidebar-item .search-box .form-control {
  background: var(--splash-black-color);
}
.dark .products-sidebar-filter .sidebar-item .previous-searches-list .item {
  background: var(--splash-black-color);
}
.dark .products-sidebar-filter .sidebar-item .previous-searches-list .item button {
  color: #BCBBC7;
}
.dark .products-sidebar-filter .sidebar-item .previous-searches-list .item button:hover {
  color: red;
}
.dark .products-sidebar-filter .sidebar-item .clear-btn::before {
  background: var(--splash-white-color);
}
.dark .products-sidebar-filter .sidebar-item .categories-list li {
  border-bottom-color: #45445e;
}
.dark .products-sidebar-filter .sidebar-item .brands-list li {
  border-bottom-color: #45445e;
}
.dark .products-sidebar-filter .sidebar-item .ratings-list li {
  border-bottom-color: #45445e;
}
.dark .products-sidebar-filter .sidebar-item .pricing-filter .range-slider input[type=range]::-webkit-slider-thumb {
  background: var(--splash-primary-color);
}
.dark .products-sidebar-filter .sidebar-item .pricing-filter .range-slider input[type=range]::-webkit-slider-runnable-track {
  background: var(--splash-black-color);
}
.dark .single-product-box .card-body .content .add-to-cart-btn {
  border-color: #45445e;
}
.dark .single-product-box .card-body .content .add-to-cart-btn:hover {
  border-color: var(--splash-primary-color);
}

@media only screen and (max-width: 767px) {
  .products-sidebar-filter .title {
    margin-bottom: 20px;
    padding: 15px;
  }
  .products-sidebar-filter .sidebar-item {
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 20px;
  }
  .products-sidebar-filter .sidebar-item h6 {
    margin-bottom: 15px;
  }
  .products-sidebar-filter .sidebar-item .search-box .form-control {
    padding-top: 13px;
    padding-bottom: 13px;
  }
  .products-sidebar-filter .sidebar-item .search-box button {
    font-size: 15px;
  }
  .products-sidebar-filter .sidebar-item .previous-searches-list {
    margin-bottom: -5px;
  }
  .products-sidebar-filter .sidebar-item .previous-searches-list .item {
    margin-right: 5px;
    margin-bottom: 5px;
  }
  .products-sidebar-filter .sidebar-item .categories-list li {
    padding-top: 9px;
    padding-bottom: 9px;
  }
  .products-sidebar-filter .sidebar-item .brands-list li {
    padding-top: 9px;
    padding-bottom: 9px;
  }
  .products-sidebar-filter .sidebar-item .ratings-list li {
    padding-top: 9px;
    padding-bottom: 9px;
  }
  .single-product-box .card-body .image .fav {
    font-size: 16px;
    height: 35px;
    width: 35px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .products-sidebar-filter .title {
    padding: 20px 25px;
  }
  .products-sidebar-filter .sidebar-item {
    padding-left: 25px;
    padding-right: 25px;
    padding-bottom: 25px;
  }
  .products-sidebar-filter .sidebar-item h6 {
    margin-bottom: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .products-sidebar-filter .title {
    padding: 25px;
  }
  .products-sidebar-filter .sidebar-item {
    padding-left: 25px;
    padding-right: 25px;
    padding-bottom: 25px;
  }
  .products-sidebar-filter .sidebar-item h6 {
    margin-bottom: 15px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .products-sidebar-filter .title {
    padding: 25px;
  }
  .products-sidebar-filter .sidebar-item {
    padding-left: 25px;
    padding-right: 25px;
    padding-bottom: 25px;
  }
  .products-sidebar-filter .sidebar-item h6 {
    margin-bottom: 15px;
  }
}
.add-product-box .ql-container {
  max-height: 150px;
  height: auto;
}
.add-product-box .input-group .input-group-text {
  padding: 10px 18px;
  border-right: none;
  background: #F5F4FA;
  border-color: #dedee4;
}
.add-product-box .input-group .form-control {
  border-left: none;
}
.add-product-box .file-upload {
  border: 1px solid #dedee4;
  padding: 55px 15px;
}
.add-product-box .file-upload i {
  line-height: 1;
  font-size: 35px;
  margin-bottom: 5px;
  color: var(--splash-primary-color);
}
.add-product-box .file-upload span span::before {
  left: 0;
  right: 0;
  height: 1px;
  content: "";
  bottom: -2px;
  position: absolute;
  background: var(--splash-black-color);
}
.add-product-box .file-upload input {
  cursor: pointer;
}

.dark .add-product-box .input-group .input-group-text {
  background: var(--splash-black-color);
  border-color: #45445e;
}
.dark .add-product-box .file-upload {
  border-color: #45445e;
}
.dark .add-product-box .file-upload span span::before {
  background: var(--splash-white-color);
}

.product-details-box .card-body {
  padding: 30px;
}
.product-details-box .card-body .productDetailsSwiper {
  height: 600px;
}
.product-details-box .card-body .productDetailsSwiper img {
  -o-object-fit: cover;
  object-fit: cover;
  display: block;
  height: 100%;
  width: 100%;
}
.product-details-box .card-body .productDetailsThumbSwiper {
  height: 600px;
  padding-right: 50px;
}
.product-details-box .card-body .productDetailsThumbSwiper img {
  -o-object-fit: cover;
  object-fit: cover;
  display: block;
  height: 100%;
  width: 100%;
}
.product-details-box .card-body .product-details-content {
  margin-left: -40px;
}
.product-details-box .card-body .product-details-content h3 {
  margin-bottom: 12px;
  line-height: 1.3;
  font-size: 24px;
}
.product-details-box .card-body .product-details-content .reviews .rating {
  color: #F3C44C;
}
.product-details-box .card-body .product-details-content .reviews .rating i {
  margin-right: 4px;
}
.product-details-box .card-body .product-details-content p {
  margin-bottom: 15px;
}
.product-details-box .card-body .product-details-content p:last-child {
  margin-bottom: 0;
}
.product-details-box .card-body .product-details-content .info {
  margin-top: 20px;
  margin-bottom: 20px;
}
.product-details-box .card-body .product-details-content .info li {
  margin-bottom: 5px;
}
.product-details-box .card-body .product-details-content .info li span {
  width: 100px;
}
.product-details-box .card-body .product-details-content .info li:last-child {
  margin-bottom: 0;
}
.product-details-box .card-body .product-details-content .box {
  padding: 15px;
  margin-bottom: 15px;
  border: 1px solid #DEDEE4;
}
.product-details-box .card-body .product-details-content .box p a::before {
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  content: "";
  position: absolute;
  background: var(--splash-black-color);
}
.product-details-box .card-body .product-details-content .buttons-list {
  margin-top: 20px;
}
.product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter {
  position: relative;
  width: 135px;
}
.product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter input {
  border: 1px solid var(--splash-primary-color);
  color: var(--splash-black-color);
  -moz-appearance: textfield;
  background: #F3F2FA;
  border-radius: 4px;
  text-align: center;
  display: block;
  height: 43px;
  width: 100%;
  font-size: 15px;
  font-weight: 700;
}
.product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter input::placeholder {
  color: var(--splash-black-color);
}
.product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter input::-webkit-outer-spin-button, .product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter button {
  color: var(--splash-black-color);
  background-color: transparent;
  transition: var(--transition);
  font-weight: 900;
  position: absolute;
  font-size: 11px;
  line-height: 1;
  left: 20px;
  padding: 0;
  border: 0;
  bottom: 0;
  top: 0;
}
.product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter button i {
  position: relative;
  top: 1px;
}
.product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter button:last-child {
  left: auto;
  right: 20px;
}
.product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter button:hover {
  color: var(--splash-primary-color);
}
.product-details-box .card-body .product-details-content .buttons-list .fav-btn {
  color: #8E8DA1;
  padding-left: 24px;
}
.product-details-box .card-body .product-details-content .buttons-list .fav-btn i {
  left: 0;
  top: 50%;
  line-height: 1;
  font-size: 18px;
  color: #EF2929;
  position: absolute;
  transform: translateY(-50%);
}
.product-details-box .card-body .product-details-content .buttons-list .fav-btn span::before {
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  content: "";
  position: absolute;
  background: #8E8DA1;
}
.product-details-box .card-body .product-details-tabs .nav.nav-tabs {
  background: var(--splash-white-color);
  box-shadow: 0px 4px 34px rgba(101, 96, 240, 0.1);
  margin-top: 50px;
  margin-bottom: 40px;
}
.product-details-box .card-body .product-details-tabs .nav.nav-tabs .nav-item {
  margin-right: 15px;
}
.product-details-box .card-body .product-details-tabs .nav.nav-tabs .nav-item .nav-link {
  padding: 20px 30px;
  margin-bottom: 0;
  color: #8E8DA1;
  font-size: 18px;
}
.product-details-box .card-body .product-details-tabs .nav.nav-tabs .nav-item .nav-link::before {
  left: 0;
  width: 0;
  bottom: 0;
  content: "";
  height: 3px;
  position: absolute;
  transition: var(--transition);
  background: var(--splash-primary-color);
}
.product-details-box .card-body .product-details-tabs .nav.nav-tabs .nav-item .nav-link:hover, .product-details-box .card-body .product-details-tabs .nav.nav-tabs .nav-item .nav-link.active {
  color: var(--splash-black-color);
}
.product-details-box .card-body .product-details-tabs .nav.nav-tabs .nav-item .nav-link:hover::before, .product-details-box .card-body .product-details-tabs .nav.nav-tabs .nav-item .nav-link.active::before {
  width: 100%;
}
.product-details-box .card-body .product-details-tabs .tab-content .content {
  padding-right: 30px;
}
.product-details-box .card-body .product-details-tabs .tab-content .content p {
  margin-bottom: 20px;
}
.product-details-box .card-body .product-details-tabs .tab-content .content p:last-child {
  margin-bottom: 0;
}
.product-details-box .card-body .product-details-tabs .tab-content .content ul {
  margin-top: 25px;
  margin-bottom: 25px;
}
.product-details-box .card-body .product-details-tabs .tab-content .content ul li {
  margin-bottom: 13px;
  padding-left: 18px;
}
.product-details-box .card-body .product-details-tabs .tab-content .content ul li::before {
  left: 0;
  top: 50%;
  width: 10px;
  content: "";
  height: 10px;
  border-radius: 50%;
  position: absolute;
  transform: translateY(-50%);
  background: var(--splash-primary-color);
}
.product-details-box .card-body .product-details-tabs .tab-content .content ul li:last-child {
  margin-bottom: 0;
}
.product-details-box .card-body .product-details-tabs .tab-content .card {
  margin-left: auto;
  margin-right: auto;
}
.product-details-box .card-body .product-details-tabs .tab-content .product-reviews .reviews .rating {
  color: #F3C44C;
}
.product-details-box .card-body .product-details-tabs .tab-content .rating-count .progress {
  height: 7px;
}
.product-details-box .card-body .product-details-tabs .tab-content .reviews-list {
  margin-top: 25px;
  margin-bottom: -25px;
}
.product-details-box .card-body .product-details-tabs .tab-content .reviews-list .rating {
  line-height: 1;
  color: #F3C44C;
}

.dark .product-details-box .card-body .product-details-content .box {
  border-color: #45445e;
}
.dark .product-details-box .card-body .product-details-content .box p a::before {
  background: var(--splash-white-color);
}
.dark .product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter input {
  color: var(--splash-white-color);
  background: var(--splash-black-color);
}
.dark .product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter input::placeholder {
  color: var(--splash-white-color);
}
.dark .product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter button {
  color: var(--splash-white-color);
}
.dark .product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter button:hover {
  color: var(--splash-primary-color);
}
.dark .product-details-box .card-body .product-details-content .buttons-list .fav-btn {
  color: #BCBBC7;
}
.dark .product-details-box .card-body .product-details-content .buttons-list .fav-btn span::before {
  background: #45445e;
}
.dark .product-details-box .card-body .product-details-tabs .nav.nav-tabs {
  background: var(--splash-black-color);
  box-shadow: unset;
}
.dark .product-details-box .card-body .product-details-tabs .nav.nav-tabs .nav-item .nav-link {
  color: #BCBBC7;
}
.dark .product-details-box .card-body .product-details-tabs .nav.nav-tabs .nav-item .nav-link:hover, .dark .product-details-box .card-body .product-details-tabs .nav.nav-tabs .nav-item .nav-link.active {
  color: var(--splash-white-color);
}

@media only screen and (max-width: 767px) {
  .product-details-box .card-body {
    padding: 15px;
  }
  .product-details-box .card-body .productDetailsSwiper {
    height: auto;
  }
  .product-details-box .card-body .productDetailsThumbSwiper {
    height: auto;
    padding-right: 0;
    margin-top: 15px;
  }
  .product-details-box .card-body .product-details-content {
    margin-left: 0;
    margin-top: 15px;
  }
  .product-details-box .card-body .product-details-content h3 {
    margin-bottom: 10px;
    font-size: 19px;
  }
  .product-details-box .card-body .product-details-content h3 br {
    display: none;
  }
  .product-details-box .card-body .product-details-content .info {
    margin-top: 12px;
    margin-bottom: 12px;
  }
  .product-details-box .card-body .product-details-content .info li span {
    width: 80px;
  }
  .product-details-box .card-body .product-details-content .box {
    padding: 12px;
  }
  .product-details-box .card-body .product-details-content .buttons-list {
    margin-top: 12px;
  }
  .product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter {
    width: 120px;
  }
  .product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter input {
    height: 41px;
  }
  .product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter button {
    left: 15px;
  }
  .product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter button:last-child {
    right: 15px;
  }
  .product-details-box .card-body .product-details-tabs .nav.nav-tabs {
    margin-top: 20px;
    margin-bottom: 25px;
  }
  .product-details-box .card-body .product-details-tabs .nav.nav-tabs .nav-item {
    margin-right: 5px;
  }
  .product-details-box .card-body .product-details-tabs .nav.nav-tabs .nav-item .nav-link {
    padding: 12px 20px;
    font-size: 15px;
  }
  .product-details-box .card-body .product-details-tabs .tab-content .content {
    padding-right: 0;
  }
  .product-details-box .card-body .product-details-tabs .tab-content .content p {
    margin-bottom: 15px;
  }
  .product-details-box .card-body .product-details-tabs .tab-content .content ul {
    margin-top: 15px;
    margin-bottom: 15px;
  }
  .product-details-box .card-body .product-details-tabs .tab-content .content ul li {
    margin-bottom: 12px;
  }
  .product-details-box .card-body .product-details-tabs .tab-content .card {
    margin-top: 20px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .product-details-box .card-body {
    padding: 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .product-details-box .card-body {
    padding: 25px;
  }
  .product-details-box .card-body .productDetailsSwiper {
    height: 500px;
  }
  .product-details-box .card-body .productDetailsThumbSwiper {
    height: 500px;
    padding-right: 0;
  }
  .product-details-box .card-body .product-details-content {
    margin-left: 0;
    margin-top: 20px;
  }
  .product-details-box .card-body .product-details-content h3 {
    margin-bottom: 10px;
    font-size: 21px;
  }
  .product-details-box .card-body .product-details-content h3 br {
    display: none;
  }
  .product-details-box .card-body .product-details-content .info {
    margin-top: 15px;
    margin-bottom: 15px;
  }
  .product-details-box .card-body .product-details-content .buttons-list {
    margin-top: 15px;
  }
  .product-details-box .card-body .product-details-tabs .nav.nav-tabs {
    margin-top: 30px;
    margin-bottom: 30px;
  }
  .product-details-box .card-body .product-details-tabs .nav.nav-tabs .nav-item {
    margin-right: 10px;
  }
  .product-details-box .card-body .product-details-tabs .nav.nav-tabs .nav-item .nav-link {
    padding: 15px 30px;
    font-size: 16px;
  }
  .product-details-box .card-body .product-details-tabs .tab-content .content {
    padding-right: 0;
  }
  .product-details-box .card-body .product-details-tabs .tab-content .content p {
    margin-bottom: 15px;
  }
  .product-details-box .card-body .product-details-tabs .tab-content .content ul {
    margin-top: 15px;
    margin-bottom: 15px;
  }
  .product-details-box .card-body .product-details-tabs .tab-content .content ul li {
    margin-bottom: 12px;
  }
  .product-details-box .card-body .product-details-tabs .tab-content .card {
    margin-top: 25px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-details-box .card-body .productDetailsThumbSwiper {
    padding-right: 40px;
  }
  .product-details-box .card-body .product-details-content {
    margin-left: -40px;
  }
  .product-details-box .card-body .product-details-content h3 {
    font-size: 21px;
  }
  .product-details-box .card-body .product-details-content h3 br {
    display: none;
  }
  .product-details-box .card-body .product-details-content .info {
    margin-top: 15px;
    margin-bottom: 15px;
  }
  .product-details-box .card-body .product-details-content .box {
    padding: 13px;
  }
  .product-details-box .card-body .product-details-content .buttons-list {
    margin-top: 15px;
  }
  .product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter {
    width: 100px;
  }
  .product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter button {
    left: 12px;
  }
  .product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter button:last-child {
    right: 12px;
  }
  .product-details-box .card-body .product-details-content .buttons-list .fav-btn {
    padding-left: 20px;
  }
  .product-details-box .card-body .product-details-content .buttons-list .fav-btn i {
    font-size: 16px;
  }
  .product-details-box .card-body .product-details-tabs .nav.nav-tabs {
    margin-top: 40px;
    margin-bottom: 30px;
  }
  .product-details-box .card-body .product-details-tabs .nav.nav-tabs .nav-item .nav-link {
    padding: 15px 30px;
    font-size: 17px;
  }
  .product-details-box .card-body .product-details-tabs .tab-content .content {
    padding-right: 0;
  }
  .product-details-box .card-body .product-details-tabs .tab-content .content p {
    margin-bottom: 15px;
  }
  .product-details-box .card-body .product-details-tabs .tab-content .content ul {
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .product-details-box .card-body .product-details-tabs .tab-content .content ul li {
    margin-bottom: 12px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .product-details-box .card-body .productDetailsThumbSwiper {
    padding-right: 35px;
  }
  .product-details-box .card-body .product-details-content {
    margin-left: -40px;
  }
  .product-details-box .card-body .product-details-content h3 {
    font-size: 22px;
    margin-bottom: 10px;
  }
  .product-details-box .card-body .product-details-content h3 br {
    display: none;
  }
  .product-details-box .card-body .product-details-content .info {
    margin-top: 15px;
    margin-bottom: 15px;
  }
  .product-details-box .card-body .product-details-content .box {
    padding: 13px;
  }
  .product-details-box .card-body .product-details-content .buttons-list {
    margin-top: 15px;
  }
  .product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter {
    width: 100px;
  }
  .product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter button {
    left: 15px;
  }
  .product-details-box .card-body .product-details-content .buttons-list .input-counter .number-counter button:last-child {
    left: auto;
    right: 15px;
  }
  .product-details-box .card-body .product-details-content .buttons-list .fav-btn {
    padding-left: 20px;
  }
  .product-details-box .card-body .product-details-content .buttons-list .fav-btn i {
    font-size: 15px;
  }
  .product-details-box .card-body .product-details-tabs .nav.nav-tabs {
    margin-top: 40px;
    margin-bottom: 35px;
  }
  .product-details-box .card-body .product-details-tabs .nav.nav-tabs .nav-item .nav-link {
    padding: 18px 30px;
  }
  .product-details-box .card-body .product-details-tabs .tab-content .content {
    padding-right: 0;
  }
  .product-details-box .card-body .product-details-tabs .tab-content .content p {
    margin-bottom: 15px;
  }
  .product-details-box .card-body .product-details-tabs .tab-content .content ul {
    margin-top: 20px;
    margin-bottom: 20px;
  }
}
@media only screen and (min-width: 1600px) {
  .product-details-box .card-body {
    padding: 50px;
  }
  .product-details-box .card-body .productDetailsSwiper {
    height: 725px;
  }
  .product-details-box .card-body .productDetailsThumbSwiper {
    height: 725px;
    padding-right: 90px;
  }
  .product-details-box .card-body .product-details-content {
    margin-left: -10px;
  }
  .product-details-box .card-body .product-details-content .info {
    margin-top: 25px;
    margin-bottom: 25px;
  }
  .product-details-box .card-body .product-details-content .box {
    padding: 18px;
    margin-bottom: 18px;
  }
  .product-details-box .card-body .product-details-content .buttons-list {
    margin-top: 25px;
  }
  .product-details-box .card-body .product-details-tabs .tab-content {
    padding-left: 30px;
  }
  .product-details-box .card-body .product-details-tabs .tab-content .content {
    padding-right: 100px;
  }
  .product-details-box .card-body .product-details-tabs .tab-content .card {
    max-width: 370px;
  }
}
.seller-card .card-body {
  padding: 30px;
}
.seller-card .card-body .icon {
  background: #F2F1F9;
  height: 80px;
  width: 80px;
}
.seller-card .card-body .icon img {
  top: 50%;
  transform: translateY(-50%);
}

.dark .seller-card .card-body .icon {
  background: var(--splash-black-color);
}

@media only screen and (max-width: 767px) {
  .seller-card .card-body {
    padding: 15px;
  }
  .seller-card .card-body .icon {
    height: 60px;
    width: 60px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .seller-card .card-body {
    padding: 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .seller-card .card-body {
    padding: 25px;
  }
  .seller-card .card-body .icon {
    height: 70px;
    width: 70px;
  }
}
.order-details-box {
  padding-top: 30px;
  padding-bottom: 30px;
}
.order-details-box .card-head {
  border-bottom: 1px dashed #d9e9ef;
  padding-left: 30px;
  padding-right: 30px;
  padding-bottom: 28px;
}
.order-details-box .card-head i {
  font-size: 32px;
  line-height: 0.01;
}
.order-details-box .card-head h5 {
  font-size: 20px;
}
.order-details-box .card-body {
  padding: 0 30px;
}
.order-details-box .card-body .list li {
  border-bottom: 1px dashed #d9e9ef;
  padding-top: 20px;
  padding-bottom: 20px;
}
.order-details-box .card-body .list li .title {
  padding-left: 30px;
}
.order-details-box .card-body .list li .title i {
  left: 0;
  top: 50%;
  line-height: 1;
  font-size: 20px;
  color: #A09FB0;
  position: absolute;
  transform: translateY(-50%);
}
.order-details-box .card-body .order-summary-list {
  border-bottom: 1px dashed #d9e9ef;
  padding-bottom: 15px;
}
.order-details-box .card-body .order-summary-list li {
  border-bottom: 1px dashed #d9e9ef;
  padding-top: 21.1px;
  padding-bottom: 21.1px;
}
.order-details-box .card-body .table > :not(caption) > * > *, .order-details-box .card-body table > :not(caption) > * > * {
  padding-top: 20px;
  padding-bottom: 20px;
}
.order-details-box .card-body .payment-address-list {
  margin-top: 20px;
}
.order-details-box .card-body .payment-address-list li {
  margin-bottom: 12px;
  padding-left: 80px;
}
.order-details-box .card-body .payment-address-list li span {
  top: 0;
  left: 0;
  position: absolute;
}
.order-details-box .card-body .payment-address-list li:last-child {
  margin-bottom: 0;
}
.order-details-box .card-body .track-order-list {
  display: flex;
  flex-wrap: wrap;
  margin-top: 25px;
}
.order-details-box .card-body .track-order-list li {
  flex: 1 0 0%;
  z-index: 1;
}
.order-details-box .card-body .track-order-list li::before {
  left: 0;
  right: 0;
  top: 12px;
  content: "";
  z-index: -1;
  margin-left: 6px;
  position: absolute;
  border-style: dashed;
  border-left-width: 0;
  border-top-width: 1px;
  border-right-width: 0;
  border-bottom-width: 0;
  border-color: var(--splash-primary-color);
}
.order-details-box .card-body .track-order-list li .dot {
  width: 25px;
  height: 25px;
  position: relative;
  border: 1px solid var(--splash-primary-color);
}
.order-details-box .card-body .track-order-list li .dot::before {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  content: "";
  margin: 5px;
  display: none;
  position: absolute;
  border-radius: 50%;
  background: var(--splash-primary-color);
}
.order-details-box .card-body .track-order-list li.active .dot::before {
  display: block;
}

.dark .order-details-box .card-head {
  border-bottom-color: #45445e;
}
.dark .order-details-box .card-body .list li {
  border-bottom-color: #45445e;
}
.dark .order-details-box .card-body .list li .title i {
  color: #BCBBC7;
}
.dark .order-details-box .card-body .order-summary-list {
  border-bottom-color: #45445e;
}
.dark .order-details-box .card-body .order-summary-list li {
  border-bottom-color: #45445e;
}

@media only screen and (max-width: 767px) {
  .order-details-box {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .order-details-box .card-head {
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 15px;
  }
  .order-details-box .card-head i {
    font-size: 25px;
  }
  .order-details-box .card-head h5 {
    font-size: 15px;
  }
  .order-details-box .card-body {
    padding: 0 15px;
  }
  .order-details-box .card-body .list li {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .order-details-box .card-body .list li .title {
    padding-left: 25px;
  }
  .order-details-box .card-body .list li .title i {
    font-size: 15px;
  }
  .order-details-box .card-body .order-summary-list li {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .order-details-box .card-body .table > :not(caption) > * > *, .order-details-box .card-body table > :not(caption) > * > * {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .order-details-box .card-body .payment-address-list {
    margin-top: 15px;
  }
  .order-details-box .card-body .payment-address-list li {
    padding-left: 70px;
  }
  .order-details-box .card-body .track-order-list {
    margin-top: 15px;
    flex-wrap: unset;
    display: block;
  }
  .order-details-box .card-body .track-order-list li {
    flex: unset;
    padding-left: 30px;
    padding-top: 15px;
  }
  .order-details-box .card-body .track-order-list li::before {
    top: 0;
    bottom: 0;
    right: auto;
    margin-left: 12px;
    border-left-width: 1px;
    border-top-width: 0;
  }
  .order-details-box .card-body .track-order-list li .dot {
    margin-left: -30px;
  }
  .order-details-box .card-body .track-order-list li:first-child {
    padding-top: 0;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .order-details-box {
    padding-top: 20px;
    padding-bottom: 20px;
  }
  .order-details-box .card-head {
    padding-left: 20px;
    padding-right: 20px;
    padding-bottom: 20px;
  }
  .order-details-box .card-body {
    padding: 0 20px;
  }
  .order-details-box .card-body .payment-address-list {
    margin-top: 20px;
  }
  .order-details-box .card-body .track-order-list {
    margin-top: 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .order-details-box {
    padding-top: 25px;
    padding-bottom: 25px;
  }
  .order-details-box .card-head {
    padding-left: 25px;
    padding-right: 25px;
    padding-bottom: 25px;
  }
  .order-details-box .card-head i {
    font-size: 28px;
  }
  .order-details-box .card-head h5 {
    font-size: 17px;
  }
  .order-details-box .card-body {
    padding: 0 25px;
  }
  .order-details-box .card-body .list li {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .order-details-box .card-body .order-summary-list li {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .order-details-box .card-body .table > :not(caption) > * > *, .order-details-box .card-body table > :not(caption) > * > * {
    padding-top: 15px;
    padding-bottom: 15px;
  }
}
.shopping-cart-box .card-body {
  padding: 0;
}
.shopping-cart-box .card-body .table > :not(caption) > * > *, .shopping-cart-box .card-body table > :not(caption) > * > * {
  padding-top: 20px;
  padding-bottom: 20px;
}
.shopping-cart-box .card-body .table > :not(caption) > * > * .number-counter, .shopping-cart-box .card-body table > :not(caption) > * > * .number-counter {
  position: relative;
  width: 110px;
}
.shopping-cart-box .card-body .table > :not(caption) > * > * .number-counter input, .shopping-cart-box .card-body table > :not(caption) > * > * .number-counter input {
  border: 1px solid var(--splash-primary-color);
  color: var(--splash-black-color);
  -moz-appearance: textfield;
  background: #F3F2FA;
  border-radius: 4px;
  text-align: center;
  display: block;
  height: 38px;
  width: 100%;
  font-size: 15px;
  font-weight: 700;
}
.shopping-cart-box .card-body .table > :not(caption) > * > * .number-counter input::placeholder, .shopping-cart-box .card-body table > :not(caption) > * > * .number-counter input::placeholder {
  color: var(--splash-black-color);
}
.shopping-cart-box .card-body .table > :not(caption) > * > * .number-counter input::-webkit-outer-spin-button, .shopping-cart-box .card-body .table > :not(caption) > * > * .number-counter input::-webkit-inner-spin-button, .shopping-cart-box .card-body table > :not(caption) > * > * .number-counter input::-webkit-outer-spin-button, .shopping-cart-box .card-body table > :not(caption) > * > * .number-counter input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.shopping-cart-box .card-body .table > :not(caption) > * > * .number-counter button, .shopping-cart-box .card-body table > :not(caption) > * > * .number-counter button {
  color: var(--splash-black-color);
  background-color: transparent;
  transition: var(--transition);
  font-weight: 900;
  position: absolute;
  font-size: 11px;
  line-height: 1;
  left: 15px;
  padding: 0;
  border: 0;
  bottom: 0;
  top: 0;
}
.shopping-cart-box .card-body .table > :not(caption) > * > * .number-counter button i, .shopping-cart-box .card-body table > :not(caption) > * > * .number-counter button i {
  position: relative;
  top: 1px;
}
.shopping-cart-box .card-body .table > :not(caption) > * > * .number-counter button:last-child, .shopping-cart-box .card-body table > :not(caption) > * > * .number-counter button:last-child {
  left: auto;
  right: 15px;
}
.shopping-cart-box .card-body .table > :not(caption) > * > * .number-counter button:hover, .shopping-cart-box .card-body table > :not(caption) > * > * .number-counter button:hover {
  color: var(--splash-primary-color);
}
.shopping-cart-box .card-body .table > :not(caption) > * > *:first-child, .shopping-cart-box .card-body table > :not(caption) > * > *:first-child {
  padding-left: 30px;
}
.shopping-cart-box .card-body .table > :not(caption) > * > *:last-child, .shopping-cart-box .card-body table > :not(caption) > * > *:last-child {
  padding-right: 30px;
}
.shopping-cart-box .card-body form {
  padding: 30px;
}

.order-summary-box {
  padding-top: 30px;
  padding-bottom: 30px;
}
.order-summary-box .card-head {
  border-bottom: 1px dashed #d9e9ef;
  padding-left: 30px;
  padding-right: 30px;
  padding-bottom: 28px;
}
.order-summary-box .card-head i {
  font-size: 32px;
  line-height: 0.01;
}
.order-summary-box .card-head h5 {
  font-size: 20px;
}
.order-summary-box .card-body {
  padding: 0 30px;
}
.order-summary-box .card-body .table > :not(caption) > * > *, .order-summary-box .card-body table > :not(caption) > * > * {
  padding-top: 20px;
  padding-bottom: 20px;
}
.order-summary-box .card-body .table > :not(caption) > *:last-child th, .order-summary-box .card-body .table > :not(caption) > *:last-child td, .order-summary-box .card-body table > :not(caption) > *:last-child th, .order-summary-box .card-body table > :not(caption) > *:last-child td {
  border-color: #8E8DA2;
}
.order-summary-box .card-body .order-summary-list {
  border-bottom: 1px dashed #d9e9ef;
  padding-bottom: 15px;
}
.order-summary-box .card-body .order-summary-list li {
  border-bottom: 1px dashed #d9e9ef;
  padding-top: 21.1px;
  padding-bottom: 21.1px;
}
.order-summary-box .card-body .coupon-code {
  margin-top: 30px;
}
.order-summary-box .card-body .coupon-code .box {
  background: #ECF3F2;
  border-radius: 2px;
  padding: 10px 15px;
}
.order-summary-box .card-body .coupon-code .search-box {
  margin-top: 15px;
}
.order-summary-box .card-body .coupon-code .search-box .form-control {
  background: #F5F4FA;
}

.dark .shopping-cart-box .card-body .table > :not(caption) > * > * .number-counter input, .dark .shopping-cart-box .card-body table > :not(caption) > * > * .number-counter input {
  background: var(--splash-black-color);
}
.dark .shopping-cart-box .card-body .table > :not(caption) > * > * .number-counter input::placeholder, .dark .shopping-cart-box .card-body table > :not(caption) > * > * .number-counter input::placeholder {
  color: var(--splash-white-color);
}
.dark .shopping-cart-box .card-body .table > :not(caption) > * > * .number-counter button, .dark .shopping-cart-box .card-body table > :not(caption) > * > * .number-counter button {
  color: var(--splash-white-color);
}
.dark .shopping-cart-box .card-body .table > :not(caption) > * > * .number-counter button:hover, .dark .shopping-cart-box .card-body table > :not(caption) > * > * .number-counter button:hover {
  color: var(--splash-primary-color);
}
.dark .order-summary-box .card-head {
  border-bottom-color: #45445e;
}
.dark .order-summary-box .card-body .table > :not(caption) > *:last-child th, .dark .order-summary-box .card-body .table > :not(caption) > *:last-child td, .dark .order-summary-box .card-body table > :not(caption) > *:last-child th, .dark .order-summary-box .card-body table > :not(caption) > *:last-child td {
  border-color: #45445e;
}
.dark .order-summary-box .card-body .order-summary-list {
  border-bottom-color: #45445e;
}
.dark .order-summary-box .card-body .order-summary-list li {
  border-bottom-color: #45445e;
}
.dark .order-summary-box .card-body .coupon-code .box {
  background: var(--splash-black-color);
}
.dark .order-summary-box .card-body .coupon-code .search-box .form-control {
  background: var(--splash-black-color);
}

@media only screen and (max-width: 767px) {
  .shopping-cart-box .card-body .table > :not(caption) > * > *, .shopping-cart-box .card-body table > :not(caption) > * > * {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .shopping-cart-box .card-body .table > :not(caption) > * > *:first-child, .shopping-cart-box .card-body table > :not(caption) > * > *:first-child {
    padding-left: 15px;
  }
  .shopping-cart-box .card-body .table > :not(caption) > * > *:last-child, .shopping-cart-box .card-body table > :not(caption) > * > *:last-child {
    padding-right: 15px;
  }
  .shopping-cart-box .card-body form {
    padding: 15px;
  }
  .order-summary-box {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .order-summary-box .card-head {
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 15px;
  }
  .order-summary-box .card-head i {
    font-size: 25px;
  }
  .order-summary-box .card-head h5 {
    font-size: 15px;
  }
  .order-summary-box .card-body {
    padding: 0 15px;
  }
  .order-summary-box .card-body .order-summary-list li {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .order-summary-box .card-body .table > :not(caption) > * > *, .order-summary-box .card-body table > :not(caption) > * > * {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .order-summary-box .card-body .coupon-code {
    margin-top: 15px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .shopping-cart-box .card-body .table > :not(caption) > * > *:first-child, .shopping-cart-box .card-body table > :not(caption) > * > *:first-child {
    padding-left: 20px;
  }
  .shopping-cart-box .card-body .table > :not(caption) > * > *:last-child, .shopping-cart-box .card-body table > :not(caption) > * > *:last-child {
    padding-right: 20px;
  }
  .shopping-cart-box .card-body form {
    padding: 20px;
  }
  .order-summary-box {
    padding-top: 20px;
    padding-bottom: 20px;
  }
  .order-summary-box .card-head {
    padding-left: 20px;
    padding-right: 20px;
    padding-bottom: 20px;
  }
  .order-summary-box .card-body {
    padding: 0 20px;
  }
  .order-summary-box .card-body .coupon-code {
    margin-top: 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .shopping-cart-box .card-body .table > :not(caption) > * > *:first-child, .shopping-cart-box .card-body table > :not(caption) > * > *:first-child {
    padding-left: 25px;
  }
  .shopping-cart-box .card-body .table > :not(caption) > * > *:last-child, .shopping-cart-box .card-body table > :not(caption) > * > *:last-child {
    padding-right: 25px;
  }
  .shopping-cart-box .card-body form {
    padding: 25px;
  }
  .order-summary-box {
    padding-top: 25px;
    padding-bottom: 25px;
  }
  .order-summary-box .card-head {
    padding-left: 25px;
    padding-right: 25px;
    padding-bottom: 25px;
  }
  .order-summary-box .card-head i {
    font-size: 28px;
  }
  .order-summary-box .card-head h5 {
    font-size: 17px;
  }
  .order-summary-box .card-body {
    padding: 0 25px;
  }
  .order-summary-box .card-body .order-summary-list li {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .order-summary-box .card-body .table > :not(caption) > * > *, .order-summary-box .card-body table > :not(caption) > * > * {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .order-summary-box .card-body .coupon-code {
    margin-top: 25px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .shopping-cart-box .card-body .table > :not(caption) > * > *:first-child, .shopping-cart-box .card-body table > :not(caption) > * > *:first-child {
    padding-left: 25px;
  }
  .shopping-cart-box .card-body .table > :not(caption) > * > *:last-child, .shopping-cart-box .card-body table > :not(caption) > * > *:last-child {
    padding-right: 25px;
  }
  .shopping-cart-box .card-body form {
    padding: 25px;
  }
  .order-summary-box {
    padding-top: 25px;
    padding-bottom: 25px;
  }
  .order-summary-box .card-head {
    padding-left: 25px;
    padding-right: 25px;
    padding-bottom: 25px;
  }
  .order-summary-box .card-body {
    padding: 0 25px;
  }
  .order-summary-box .card-body .coupon-code {
    margin-top: 25px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .shopping-cart-box .card-body .table > :not(caption) > * > *:first-child, .shopping-cart-box .card-body table > :not(caption) > * > *:first-child {
    padding-left: 25px;
  }
  .shopping-cart-box .card-body .table > :not(caption) > * > *:last-child, .shopping-cart-box .card-body table > :not(caption) > * > *:last-child {
    padding-right: 25px;
  }
  .shopping-cart-box .card-body form {
    padding: 25px;
  }
  .order-summary-box {
    padding-top: 25px;
    padding-bottom: 25px;
  }
  .order-summary-box .card-head {
    padding-left: 25px;
    padding-right: 25px;
    padding-bottom: 25px;
  }
  .order-summary-box .card-body {
    padding: 0 25px;
  }
  .order-summary-box .card-body .coupon-code {
    margin-top: 25px;
  }
}
@media only screen and (min-width: 1600px) {
  .shopping-cart-box .card-body .table > :not(caption) > * > *.product-title, .shopping-cart-box .card-body table > :not(caption) > * > *.product-title {
    padding-right: 0;
  }
}
.checkout-box .card-body {
  padding: 0;
}
.checkout-box .card-body .nav.nav-tabs {
  padding: 5px;
  box-shadow: 0px 4px 34px rgba(101, 96, 240, 0.1);
}
.checkout-box .card-body .nav.nav-tabs .nav-item {
  flex: 1 0 0%;
}
.checkout-box .card-body .nav.nav-tabs .nav-item .nav-link {
  color: var(--splash-muted-color);
  transition: var(--transition);
  padding: 18px 0 18px 33px;
  margin-bottom: 0;
}
.checkout-box .card-body .nav.nav-tabs .nav-item .nav-link i {
  top: 50%;
  line-height: 1;
  font-size: 22px;
  margin-left: -33px;
  position: absolute;
  transform: translateY(-50%);
}
.checkout-box .card-body .nav.nav-tabs .nav-item .nav-link:hover, .checkout-box .card-body .nav.nav-tabs .nav-item .nav-link.active {
  background-color: var(--splash-primary-color);
  color: var(--splash-white-color);
}
.checkout-box .card-body .tab-content .accordion .accordion-item {
  border: 1px solid #F2F1F9;
  margin-bottom: 25px;
  padding: 25px;
}
.checkout-box .card-body .tab-content .accordion .accordion-item .accordion-button {
  padding: 0 0 0 45px;
}
.checkout-box .card-body .tab-content .accordion .accordion-item .accordion-button img {
  box-shadow: 0px 4px 34px rgba(101, 96, 240, 0.1);
  margin-right: 10px;
}
.checkout-box .card-body .tab-content .accordion .accordion-item .accordion-button img:last-child {
  margin-right: 0;
}
.checkout-box .card-body .tab-content .accordion .accordion-item .accordion-button .dot {
  left: 0;
  top: 50%;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  position: absolute;
  transform: translateY(-50%);
  border: 1px solid var(--splash-primary-color);
}
.checkout-box .card-body .tab-content .accordion .accordion-item .accordion-button .dot::before {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  content: "";
  margin: 4px;
  position: absolute;
  border-radius: 50%;
  transition: var(--transition);
  background: var(--splash-primary-color);
}
.checkout-box .card-body .tab-content .accordion .accordion-item .accordion-button::after {
  display: none;
}
.checkout-box .card-body .tab-content .accordion .accordion-item .accordion-button.collapsed .dot::before {
  opacity: 0;
  visibility: hidden;
}
.checkout-box .card-body .tab-content .accordion .accordion-item .accordion-collapse .accordion-body {
  margin-top: 25px;
}
.checkout-box .card-body .tab-content .accordion .accordion-item:last-child {
  margin-bottom: 0;
}
.checkout-box .card-body .tab-content .box {
  background: #F2F1F9;
}
.checkout-box .card-body .tab-content .box .inner-box {
  box-shadow: 0px 4px 34px rgba(101, 96, 240, 0.1);
  max-width: 530px;
}
.checkout-box .card-body .tab-content .box .inner-box ul li {
  margin-bottom: 12px;
  padding-left: 95px;
}
.checkout-box .card-body .tab-content .box .inner-box ul li span {
  top: 0;
  left: 0;
  position: absolute;
}
.checkout-box .card-body .tab-content .box .inner-box ul li:last-child {
  margin-bottom: 0;
}
.checkout-box .card-body .tab-content .box .payment-method img {
  box-shadow: 0px 4px 34px rgba(101, 96, 240, 0.1);
}

.dark .checkout-box .card-body .nav.nav-tabs {
  box-shadow: unset;
  border-bottom: 1px dashed #45445e !important;
}
.dark .checkout-box .card-body .nav.nav-tabs .nav-item .nav-link {
  color: #BCBBC7;
}
.dark .checkout-box .card-body .nav.nav-tabs .nav-item .nav-link:hover, .dark .checkout-box .card-body .nav.nav-tabs .nav-item .nav-link.active {
  color: var(--splash-white-color);
}
.dark .checkout-box .card-body .tab-content .accordion .accordion-item {
  border-color: #45445e;
}
.dark .checkout-box .card-body .tab-content .accordion .accordion-item .accordion-button.bg-white {
  background-color: #2b2a3f !important;
}
.dark .checkout-box .card-body .tab-content .box {
  background: var(--splash-black-color);
}
.dark .checkout-box .card-body .tab-content .box .inner-box {
  box-shadow: unset;
}
.dark .checkout-box .card-body .tab-content .box .payment-method img {
  box-shadow: unset;
}

@media only screen and (max-width: 767px) {
  .checkout-box .card-body .nav.nav-tabs .nav-item .nav-link {
    padding: 15px 10px;
  }
  .checkout-box .card-body .nav.nav-tabs .nav-item .nav-link i {
    top: 0;
    display: block;
    font-size: 20px;
    position: relative;
    transform: translateY(0);
    margin-left: 0;
    margin-bottom: 2px;
  }
  .checkout-box .card-body .tab-content .accordion .accordion-item {
    margin-bottom: 15px;
    padding: 15px;
  }
  .checkout-box .card-body .tab-content .accordion .accordion-item .accordion-button {
    padding: 0 0 0 20px;
  }
  .checkout-box .card-body .tab-content .accordion .accordion-item .accordion-button img {
    margin-right: 0;
    width: 28px;
  }
  .checkout-box .card-body .tab-content .accordion .accordion-item .accordion-button .dot {
    width: 15px;
    height: 15px;
  }
  .checkout-box .card-body .tab-content .accordion .accordion-item .accordion-button .dot::before {
    margin: 3px;
  }
  .checkout-box .card-body .tab-content .accordion .accordion-item .accordion-collapse .accordion-body {
    margin-top: 15px;
  }
  .checkout-box .card-body .tab-content .box .inner-box {
    max-width: 100%;
  }
  .checkout-box .card-body .tab-content .box .inner-box ul li {
    margin-bottom: 12px;
    padding-left: 0;
  }
  .checkout-box .card-body .tab-content .box .inner-box ul li span {
    display: block;
    margin-bottom: 3px;
    position: relative;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .checkout-box .card-body .tab-content .accordion .accordion-item {
    padding: 20px;
  }
  .checkout-box .card-body .tab-content .accordion .accordion-item .accordion-collapse .accordion-body {
    margin-top: 20px;
  }
  .checkout-box .card-body .tab-content .box .inner-box ul li {
    margin-bottom: 12px;
    padding-left: 0;
  }
  .checkout-box .card-body .tab-content .box .inner-box ul li span {
    display: block;
    margin-bottom: 3px;
    position: relative;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .checkout-box .card-body .tab-content .accordion .accordion-item {
    margin-bottom: 20px;
    padding: 15px 20px;
  }
  .checkout-box .card-body .tab-content .accordion .accordion-item .accordion-button {
    padding: 0 0 0 35px;
  }
  .checkout-box .card-body .tab-content .accordion .accordion-item .accordion-button .dot {
    width: 25px;
    height: 25px;
  }
  .checkout-box .card-body .tab-content .accordion .accordion-item .accordion-button .dot::before {
    margin: 3px;
  }
  .checkout-box .card-body .tab-content .accordion .accordion-item .accordion-collapse .accordion-body {
    margin-top: 15px;
  }
}
.manage-reviews-box .card-body {
  padding: 25px 0;
}
.manage-reviews-box .card-body table > :not(caption) > * > *, .manage-reviews-box .card-body .table > :not(caption) > * > * {
  padding-top: 20px;
  padding-bottom: 20px;
}
.manage-reviews-box .card-body table > :not(caption) > * > *:nth-child(2) .rating, .manage-reviews-box .card-body .table > :not(caption) > * > *:nth-child(2) .rating {
  width: 270px;
}
.manage-reviews-box .card-body table > :not(caption) > * > *:nth-child(2).product-title, .manage-reviews-box .card-body .table > :not(caption) > * > *:nth-child(2).product-title {
  padding-right: 0;
  white-space: normal;
}
.manage-reviews-box .card-body table > :not(caption) > * > *:nth-child(2).product-title span, .manage-reviews-box .card-body .table > :not(caption) > * > *:nth-child(2).product-title span {
  width: 270px;
}
.manage-reviews-box .card-body .product-box {
  background: #F5F4F9;
}
.manage-reviews-box .card-body .product-box .image {
  height: 100%;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}
.manage-reviews-box .card-body .product-box .image img {
  display: none;
}
.manage-reviews-box .card-body .product-box .content {
  padding: 40px 20px;
}
.manage-reviews-box .card-body .product-box .content h5 {
  line-height: 1.4;
}
.manage-reviews-box .card-body .box {
  background: #F5F4F9;
}
.manage-reviews-box .card-body .manage-rating .rating i {
  line-height: 1;
  font-size: 15px;
  color: #F3C44C;
}
.manage-reviews-box .card-body .manage-rating .progress {
  padding: 2px;
  margin-top: 2px;
}

.dark .manage-reviews-box .card-body .product-box {
  background: var(--splash-black-color);
}
.dark .manage-reviews-box .card-body .box {
  background: var(--splash-black-color);
}

@media only screen and (max-width: 767px) {
  .manage-reviews-box .card-body {
    padding: 15px 0;
  }
  .manage-reviews-box .card-body table > :not(caption) > * > *, .manage-reviews-box .card-body .table > :not(caption) > * > * {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .manage-reviews-box .card-body .product-box .image {
    height: auto;
    background-image: unset !important;
  }
  .manage-reviews-box .card-body .product-box .image img {
    display: inline-block;
  }
  .manage-reviews-box .card-body .product-box .content {
    padding: 20px 15px;
  }
  .manage-reviews-box .card-body .manage-rating .rating i {
    font-size: 14px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .manage-reviews-box .card-body {
    padding: 20px 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .manage-reviews-box .card-body table > :not(caption) > * > *, .manage-reviews-box .card-body .table > :not(caption) > * > * {
    padding-top: 15px;
    padding-bottom: 15px;
  }
}
@media only screen and (min-width: 1600px) {
  .manage-reviews-box .card-body .product-box .content {
    padding: 40px 25px;
  }
}
.manage-reviews-box .card-body {
  padding: 25px 0;
}
.manage-reviews-box .card-body table > :not(caption) > * > *, .manage-reviews-box .card-body .table > :not(caption) > * > * {
  padding-top: 20px;
  padding-bottom: 20px;
}
.manage-reviews-box .card-body table > :not(caption) > * > *:nth-child(2) .rating, .manage-reviews-box .card-body .table > :not(caption) > * > *:nth-child(2) .rating {
  width: 270px;
}
.manage-reviews-box .card-body table > :not(caption) > * > *:nth-child(2).product-title, .manage-reviews-box .card-body .table > :not(caption) > * > *:nth-child(2).product-title {
  padding-right: 0;
  white-space: normal;
}
.manage-reviews-box .card-body table > :not(caption) > * > *:nth-child(2).product-title span, .manage-reviews-box .card-body .table > :not(caption) > * > *:nth-child(2).product-title span {
  width: 270px;
}
.manage-reviews-box .card-body .product-box {
  background: #F5F4F9;
}
.manage-reviews-box .card-body .product-box .image {
  height: 100%;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}
.manage-reviews-box .card-body .product-box .image img {
  display: none;
}
.manage-reviews-box .card-body .product-box .content {
  padding: 40px 20px;
}
.manage-reviews-box .card-body .product-box .content h5 {
  line-height: 1.4;
}
.manage-reviews-box .card-body .box {
  background: #F5F4F9;
}
.manage-reviews-box .card-body .manage-rating .rating i {
  line-height: 1;
  font-size: 15px;
  color: #F3C44C;
}
.manage-reviews-box .card-body .manage-rating .progress {
  padding: 2px;
  margin-top: 2px;
}

.dark .manage-reviews-box .card-body .product-box {
  background: var(--splash-black-color);
}
.dark .manage-reviews-box .card-body .box {
  background: var(--splash-black-color);
}

@media only screen and (max-width: 767px) {
  .manage-reviews-box .card-body {
    padding: 15px 0;
  }
  .manage-reviews-box .card-body table > :not(caption) > * > *, .manage-reviews-box .card-body .table > :not(caption) > * > * {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .manage-reviews-box .card-body .product-box .image {
    height: auto;
    background-image: unset !important;
  }
  .manage-reviews-box .card-body .product-box .image img {
    display: inline-block;
  }
  .manage-reviews-box .card-body .product-box .content {
    padding: 20px 15px;
  }
  .manage-reviews-box .card-body .manage-rating .rating i {
    font-size: 14px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .manage-reviews-box .card-body {
    padding: 20px 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .manage-reviews-box .card-body table > :not(caption) > * > *, .manage-reviews-box .card-body .table > :not(caption) > * > * {
    padding-top: 15px;
    padding-bottom: 15px;
  }
}
@media only screen and (min-width: 1600px) {
  .manage-reviews-box .card-body .product-box .content {
    padding: 40px 25px;
  }
}
.order-tracking-box .card-body .track-order-list {
  display: flex;
  flex-wrap: wrap;
  margin-top: 60px;
  margin-bottom: 60px;
}
.order-tracking-box .card-body .track-order-list li {
  flex: 1 0 0%;
  z-index: 1;
}
.order-tracking-box .card-body .track-order-list li::before {
  left: 0;
  right: 0;
  top: 12px;
  content: "";
  z-index: -1;
  margin-left: 6px;
  position: absolute;
  border-style: dashed;
  border-left-width: 0;
  border-top-width: 1px;
  border-right-width: 0;
  border-bottom-width: 0;
  border-color: var(--splash-primary-color);
}
.order-tracking-box .card-body .track-order-list li .dot {
  width: 25px;
  height: 25px;
  position: relative;
  border: 1px solid var(--splash-primary-color);
}
.order-tracking-box .card-body .track-order-list li .dot::before {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  content: "";
  margin: 5px;
  display: none;
  position: absolute;
  border-radius: 50%;
  background: var(--splash-primary-color);
}
.order-tracking-box .card-body .track-order-list li.active .dot::before {
  display: block;
}

@media only screen and (max-width: 767px) {
  .order-tracking-box .card-body .track-order-list {
    flex-wrap: unset;
    display: block;
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .order-tracking-box .card-body .track-order-list li {
    flex: unset;
    padding-left: 30px;
    padding-top: 15px;
  }
  .order-tracking-box .card-body .track-order-list li::before {
    top: 0;
    bottom: 0;
    right: auto;
    margin-left: 12px;
    border-left-width: 1px;
    border-top-width: 0;
  }
  .order-tracking-box .card-body .track-order-list li .dot {
    margin-left: -30px;
  }
  .order-tracking-box .card-body .track-order-list li:first-child {
    padding-top: 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .order-tracking-box .card-body .track-order-list {
    margin-top: 35px;
    margin-bottom: 35px;
  }
}
.invoice-details-box .white-logo {
  display: none;
}
.invoice-details-box .invoice-info {
  padding-top: 40px;
  border-top: 1px dashed #D2CFE4;
  margin-top: 40px;
  margin-bottom: 40px;
}
.invoice-details-box .table thead tr th {
  border-bottom-color: #D2CFE4;
}
.invoice-details-box .table tbody tr th, .invoice-details-box .table tbody tr td {
  padding-top: 30px;
  padding-bottom: 30px;
}
.invoice-details-box .order-summary-list {
  border-bottom: 1px dashed #d9e9ef;
  padding-bottom: 10px;
  max-width: 455px;
  margin-left: auto;
  margin-right: 50px;
}
.invoice-details-box .order-summary-list li {
  border-bottom: 1px dashed #d9e9ef;
  padding-top: 18px;
  padding-bottom: 18px;
}

.dark .invoice-details-box .white-logo {
  display: inline;
}
.dark .invoice-details-box .black-logo {
  display: none;
}
.dark .invoice-details-box .invoice-info {
  border-top-color: #45445e;
}
.dark .invoice-details-box .table thead tr th {
  border-bottom-color: #45445e;
}
.dark .invoice-details-box .order-summary-list {
  border-bottom-color: #45445e;
}
.dark .invoice-details-box .order-summary-list li {
  border-bottom-color: #45445e;
}

@media only screen and (max-width: 767px) {
  .invoice-details-box .invoice-info {
    padding-top: 20px;
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .invoice-details-box .table tbody tr th, .invoice-details-box .table tbody tr td {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .invoice-details-box .order-summary-list {
    padding-bottom: 8px;
    max-width: 100%;
    margin-left: 0;
    margin-right: 0;
  }
  .invoice-details-box .order-summary-list li {
    padding-top: 15px;
    padding-bottom: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .invoice-details-box .invoice-info {
    padding-top: 25px;
    margin-top: 25px;
    margin-bottom: 25px;
  }
  .invoice-details-box .table tbody tr th, .invoice-details-box .table tbody tr td {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .invoice-details-box .order-summary-list {
    max-width: 100%;
    margin-left: 0;
    margin-right: 0;
  }
  .invoice-details-box .order-summary-list li {
    padding-top: 15px;
    padding-bottom: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .invoice-details-box .invoice-info {
    padding-top: 35px;
    margin-top: 35px;
    margin-bottom: 35px;
  }
  .invoice-details-box .table tbody tr th, .invoice-details-box .table tbody tr td {
    padding-top: 20px;
    padding-bottom: 20px;
  }
  .invoice-details-box .order-summary-list {
    margin-right: 25px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .invoice-details-box .order-summary-list {
    margin-right: 10px;
  }
}
@media only screen and (min-width: 1600px) {
  .invoice-details-box .order-summary-list {
    margin-right: 95px;
  }
}
.contact-card .info-list {
  margin-top: 20px;
}
.contact-card .info-list li {
  margin-bottom: 10px;
  padding-left: 85px;
}
.contact-card .info-list li span {
  top: 0;
  left: 0;
  position: absolute;
}
.contact-card .info-list li:last-child {
  margin-bottom: 0;
}

@media only screen and (max-width: 767px) {
  .contact-card .info-list {
    margin-top: 15px;
  }
  .contact-card .info-list li {
    padding-left: 75px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .contact-card .info-list {
    margin-top: 15px;
  }
  .contact-card .info-list li {
    padding-left: 80px;
  }
}
.project-card .card-body .progress {
  height: 9px;
}
.project-card .card-body .info li {
  margin-bottom: 10px;
}
.project-card .card-body .info li span {
  width: 85px;
}
.project-card .card-body .info li .users-list div {
  width: 33px;
  height: 33px;
  margin-right: -10px;
  border: 2px solid var(--splash-white-color);
  filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));
}
.project-card .card-body .info li .users-list div:last-child {
  margin-right: 0;
}
.project-card .card-body .info li:last-child {
  margin-bottom: 0;
}
.project-card .card-body .features-list li {
  margin-bottom: 12px;
  padding-left: 18px;
}
.project-card .card-body .features-list li::before {
  left: 0;
  top: 50%;
  content: "";
  width: 10px;
  height: 10px;
  position: absolute;
  border-radius: 50%;
  transform: translateY(-50%);
  background: var(--splash-primary-color);
}
.project-card .card-body .features-list li:last-child {
  margin-bottom: 0;
}

.project-card .card-body .info {
  padding-top: 30px;
  padding-left: 30px;
  padding-right: 30px;
  padding-bottom: 5px;
}
.project-card .card-body .info .info-card {
  margin-bottom: 25px;
  padding-top: 8px;
  padding-left: 78px;
  padding-bottom: 8px;
}
.project-card .card-body .info .info-card .icon {
  top: 50%;
  width: 65px;
  height: 65px;
  font-size: 25px;
  transform: translateY(-50%);
}
.project-card .card-body .info .info-card .icon i {
  left: 0;
  top: 50%;
  right: 0;
  line-height: 1;
  margin-top: 1px;
  position: absolute;
  transform: translateY(-50%);
}

.project-comments-card .card-body .title {
  border-bottom: 1px dashed #D2CFE4;
}
.project-comments-card .card-body .list .item {
  border-bottom: 1px dashed #d9e9ef;
  padding-top: 36px;
  padding-bottom: 36px;
}
.project-comments-card .card-body .list .item p {
  margin-bottom: 15px;
}
.project-comments-card .card-body .list .item p:last-child {
  margin-bottom: 0;
}
.project-comments-card .card-body .list .item .buttons-list a {
  border: 1px solid #D0CFDD;
  padding: 5px 12px 5px 35px;
  border-radius: 20px;
  margin-right: 10px;
}
.project-comments-card .card-body .list .item .buttons-list a i {
  top: 50%;
  left: 12px;
  line-height: 1;
  font-size: 17px;
  margin-top: 1px;
  position: absolute;
  transform: translateY(-50%);
  color: var(--splash-primary-color);
}
.project-comments-card .card-body .list .item .buttons-list a:hover {
  border-color: var(--splash-primary-color);
}
.project-comments-card .card-body .list .item .buttons-list a:last-child {
  margin-right: 0;
}
.project-comments-card .card-body .list .item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}
.project-comments-card .card-body .list .item:first-child {
  padding-top: 0;
}
.project-comments-card .card-body .list .item .more-conversation {
  left: 50%;
  bottom: -9px;
  padding: 0 20px;
  position: absolute;
  transform: translateX(-50%);
}
.project-comments-card .card-body .list .item .item {
  margin-left: 120px;
}
.project-comments-card .card-body .list .item .item::before {
  top: 15px;
  left: -47px;
  content: "";
  width: 30px;
  height: 40px;
  position: absolute;
  border-left: 1px dashed #B5B1CA;
  border-bottom: 1px dashed #B5B1CA;
}
.project-comments-card .card-body .write-your-comment {
  padding-right: 220px;
}
.project-comments-card .card-body .write-your-comment .write-comment {
  padding: 16px 20px 16px 50px;
  background: #F5F4FA;
  border-radius: 10px;
}
.project-comments-card .card-body .write-your-comment .write-comment .input-comment {
  border: none;
  background-color: transparent;
  border-left: 1px solid #A4A3B0;
  padding-top: 3px;
  padding-left: 10px;
  padding-bottom: 3px;
  padding-right: 10px;
}
.project-comments-card .card-body .write-your-comment .write-comment .input-comment::placeholder {
  color: #8E8DA1;
  transition: var(--transition);
}
.project-comments-card .card-body .write-your-comment .write-comment .input-comment:focus::placeholder {
  color: transparent;
}
.project-comments-card .card-body .write-your-comment .write-comment button {
  top: 50%;
  left: 15px;
  font-size: 25px;
  margin-top: 3px;
  position: absolute;
  transform: translateY(-50%);
  color: var(--splash-muted-color);
}
.project-comments-card .card-body .write-your-comment .write-comment button:hover {
  color: var(--splash-primary-color);
}
.project-comments-card .card-body .write-your-comment .buttons-list {
  top: 50%;
  right: 0;
  position: absolute;
  transform: translateY(-50%);
}
.project-comments-card .card-body .write-your-comment .buttons-list button {
  width: 59px;
  height: 59px;
  font-size: 22px;
  margin-right: 10px;
  border-radius: 10px;
  background: #F5F4FA;
  color: var(--splash-primary-color);
}
.project-comments-card .card-body .write-your-comment .buttons-list button i {
  left: 0;
  right: 0;
  top: 50%;
  line-height: 1;
  margin-top: 1px;
  position: absolute;
  transform: translateY(-50%);
}
.project-comments-card .card-body .write-your-comment .buttons-list button:last-child {
  margin-right: 0;
}
.project-comments-card .card-body .write-your-comment .buttons-list button.active, .project-comments-card .card-body .write-your-comment .buttons-list button:hover {
  background-color: var(--splash-primary-color);
  color: var(--splash-white-color);
}

.team-members-card .card-body ul li {
  border-bottom: 1px dashed #d9e9ef;
  padding-top: 15px;
  padding-bottom: 15px;
}
.team-members-card .card-body ul li img {
  border: 2px solid var(--splash-white-color);
  filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));
}
.team-members-card .card-body ul li:last-child {
  border-bottom: none;
  padding-bottom: 0;
}
.team-members-card .card-body ul li:first-child {
  padding-top: 0;
}
.team-members-card .card-body .show-more-btn {
  border: 1px solid #D2CFE4;
  background-color: transparent;
  color: var(--splash-primary-color);
}
.team-members-card .card-body .show-more-btn:hover {
  color: var(--splash-white-color);
  border-color: var(--splash-primary-color);
  background-color: var(--splash-primary-color);
}

.attachments-card .card-body .list li {
  border-bottom: 1px dashed #d9e9ef;
  padding-top: 14.7px;
  padding-bottom: 14.7px;
}
.attachments-card .card-body .list li .icon {
  width: 50px;
  height: 50px;
  font-size: 20px;
  background: #F8F8FB;
}
.attachments-card .card-body .list li .icon i {
  left: 0;
  right: 0;
  top: 50%;
  line-height: 1;
  margin-top: 1px;
  position: absolute;
  transform: translateY(-50%);
}
.attachments-card .card-body .list li:last-child {
  border-bottom: none;
  padding-bottom: 0;
}
.attachments-card .card-body .list li:first-child {
  padding-top: 0;
}

.dark .project-comments-card .card-body .title {
  border-bottom-color: #45445e;
}
.dark .project-comments-card .card-body .list .item {
  border-bottom-color: #45445e;
}
.dark .project-comments-card .card-body .list .item .buttons-list a {
  border-color: #45445e;
}
.dark .project-comments-card .card-body .list .item .buttons-list a:hover {
  border-color: var(--splash-primary-color);
}
.dark .project-comments-card .card-body .list .item .item::before {
  border-color: #45445e;
}
.dark .project-comments-card .card-body .write-your-comment .write-comment {
  background: var(--splash-black-color);
}
.dark .project-comments-card .card-body .write-your-comment .write-comment .input-comment {
  border-left-color: #45445e;
}
.dark .project-comments-card .card-body .write-your-comment .write-comment .input-comment::placeholder {
  color: #BCBBC7;
}
.dark .project-comments-card .card-body .write-your-comment .write-comment .input-comment:focus::placeholder {
  color: transparent;
}
.dark .project-comments-card .card-body .write-your-comment .write-comment button {
  color: #BCBBC7;
}
.dark .project-comments-card .card-body .write-your-comment .write-comment button:hover {
  color: var(--splash-primary-color);
}
.dark .project-comments-card .card-body .write-your-comment .buttons-list button {
  background: var(--splash-black-color);
  color: #BCBBC7;
}
.dark .project-comments-card .card-body .write-your-comment .buttons-list button.active, .dark .project-comments-card .card-body .write-your-comment .buttons-list button:hover {
  background-color: var(--splash-primary-color);
  color: var(--splash-white-color);
}
.dark .team-members-card .card-body ul li {
  border-bottom-color: #45445e;
}
.dark .team-members-card .card-body ul li img {
  border-color: #45445e;
}
.dark .team-members-card .card-body .show-more-btn {
  border-color: #45445e;
}
.dark .team-members-card .card-body .show-more-btn:hover {
  border-color: var(--splash-primary-color);
}
.dark .attachments-card .card-body .list li {
  border-bottom-color: #45445e;
}
.dark .attachments-card .card-body .list li .icon {
  background: var(--splash-black-color);
}

@media only screen and (max-width: 767px) {
  .project-card .card-body .info {
    padding-top: 20px;
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 5px;
  }
  .project-card .card-body .info .info-card {
    margin-bottom: 15px;
  }
  .attachments-card .card-body .list li {
    padding-top: 12px;
    padding-bottom: 12px;
  }
  .project-comments-card .card-body .list .item {
    padding-top: 25px;
    padding-bottom: 25px;
  }
  .project-comments-card .card-body .list .item .buttons-list a {
    padding: 5px 10px 5px 30px;
    margin-right: 5px;
  }
  .project-comments-card .card-body .list .item .buttons-list a i {
    left: 10px;
    font-size: 15px;
  }
  .project-comments-card .card-body .list .item .more-conversation {
    padding: 0 15px;
  }
  .project-comments-card .card-body .list .item .item {
    margin-left: 15px;
  }
  .project-comments-card .card-body .list .item .item::before {
    top: 5px;
    left: -14px;
    width: 13px;
  }
  .project-comments-card .card-body .write-your-comment {
    padding-right: 0;
  }
  .project-comments-card .card-body .write-your-comment .write-comment {
    padding: 15px 15px 15px 40px;
  }
  .project-comments-card .card-body .write-your-comment .write-comment .input-comment {
    padding-top: 2px;
    padding-bottom: 2px;
  }
  .project-comments-card .card-body .write-your-comment .write-comment button {
    left: 10px;
    font-size: 20px;
  }
  .project-comments-card .card-body .write-your-comment .buttons-list {
    top: 0;
    margin-top: 12px;
    position: relative;
    transform: translateY(0);
  }
  .project-comments-card .card-body .write-your-comment .buttons-list button {
    width: 50px;
    height: 50px;
    font-size: 20px;
    margin-right: 5px;
  }
  .project-comments-card .card-body .write-your-comment .buttons-list button i {
    margin-top: 1px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .project-card .card-body .info {
    padding-top: 25px;
    padding-left: 25px;
    padding-right: 25px;
    padding-bottom: 5px;
  }
  .project-card .card-body .info .info-card {
    margin-bottom: 20px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .project-card .card-body .info {
    padding-bottom: 0;
    padding-top: 25px;
    padding-left: 20px;
    padding-right: 20px;
  }
  .project-card .card-body .info .info-card {
    margin-bottom: 20px;
    padding-top: 7px;
    padding-left: 72px;
    padding-bottom: 7px;
  }
  .project-card .card-body .info .info-card .icon {
    width: 60px;
    height: 60px;
    font-size: 22px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .project-card .card-body .info {
    padding-left: 20px;
    padding-right: 20px;
  }
  .project-card .card-body .info .info-card {
    margin-bottom: 25px;
    padding-top: 7px;
    padding-left: 72px;
    padding-bottom: 7px;
  }
  .project-card .card-body .info .info-card .icon {
    width: 60px;
    height: 60px;
    font-size: 22px;
  }
}
.projects-teams-card .card-body .progress {
  height: 9px;
}
.projects-teams-card .card-body .info li {
  margin-bottom: 10px;
}
.projects-teams-card .card-body .info li .user img {
  border: 2px solid var(--splash-white-color);
  filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));
}
.projects-teams-card .card-body .info li .users-list div {
  width: 33px;
  height: 33px;
  margin-right: -10px;
  border: 2px solid var(--splash-white-color);
  filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));
}
.projects-teams-card .card-body .info li .users-list div:last-child {
  margin-right: 0;
}
.projects-teams-card .card-body .info li:last-child {
  margin-bottom: 0;
}
.projects-teams-card .card-body .view-btn {
  background: #F2F1F9;
}
.projects-teams-card .card-body .view-btn span {
  line-height: 1.3;
}
.projects-teams-card .card-body .view-btn span::before {
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  content: "";
  position: absolute;
  transition: var(--transition);
  background: var(--splash-primary-color);
}
.projects-teams-card .card-body .view-btn i {
  line-height: 0.01;
  font-size: 20px;
}
.projects-teams-card .card-body .view-btn:hover span::before {
  transform: scaleX(0);
}

.dark .projects-teams-card .card-body .info li .user img {
  border-color: #45445e;
}
.dark .projects-teams-card .card-body .info li .users-list div {
  border-color: #45445e;
}
.dark .projects-teams-card .card-body .view-btn {
  background: var(--splash-black-color);
}

.create-new-project-box .ql-container {
  min-height: 150px;
  height: auto;
}
.create-new-project-box .input-group .input-group-text {
  padding: 10px 18px;
  border-right: none;
  background: #F5F4FA;
  border-color: #dedee4;
}
.create-new-project-box .input-group .form-control {
  border-left: none;
}
.create-new-project-box .file-upload {
  border: 1px solid #dedee4;
  padding: 55px 15px;
}
.create-new-project-box .file-upload i {
  line-height: 1;
  font-size: 35px;
  margin-bottom: 5px;
  color: var(--splash-primary-color);
}
.create-new-project-box .file-upload span span::before {
  left: 0;
  right: 0;
  height: 1px;
  content: "";
  bottom: -2px;
  position: absolute;
  background: var(--splash-black-color);
}
.create-new-project-box .file-upload input {
  cursor: pointer;
}
.create-new-project-box .members-list div {
  margin-top: 10px;
  margin-right: 5px;
}
.create-new-project-box .members-list img {
  border: 1.5px solid var(--splash-white-color);
  filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));
}
.create-new-project-box .members-list button {
  font-size: 8px;
  margin-left: 3px;
}
.create-new-project-box .members-list button:hover {
  color: red;
}

.dark .create-new-project-box .input-group .input-group-text {
  background: var(--splash-black-color);
  border-color: #45445e;
}
.dark .create-new-project-box .file-upload {
  border-color: #45445e;
}
.dark .create-new-project-box .file-upload span span::before {
  background: var(--splash-white-color);
}
.dark .create-new-project-box .members-list img {
  border-color: #45445e;
}

.ticket-preview-box .card-head .buttons-list button {
  border: 1px solid rgba(101, 96, 240, 0.3);
  padding: 9px 35px 9px 15px;
  margin-right: 5px;
}
.ticket-preview-box .card-head .buttons-list button i {
  top: 50%;
  right: 15px;
  line-height: 1;
  margin-top: 1px;
  color: #79788E;
  position: absolute;
  transform: translateY(-50%);
}
.ticket-preview-box .card-head .buttons-list button:hover {
  border-color: var(--splash-primary-color);
}
.ticket-preview-box .card-head .buttons-list button:last-child {
  margin-right: 0;
}
.ticket-preview-box .card-body .ticket-preview-list .item {
  border-bottom: 1px dashed #d9e9ef;
  padding-top: 50px;
  padding-bottom: 50px;
}
.ticket-preview-box .card-body .ticket-preview-list .item p {
  margin-bottom: 15px;
}
.ticket-preview-box .card-body .ticket-preview-list .item p:last-child {
  margin-bottom: 0;
}
.ticket-preview-box .card-body .ticket-preview-list .item .buttons-list a {
  border: 1px solid #D0CFDD;
  padding: 5px 12px 5px 35px;
  border-radius: 20px;
  margin-right: 10px;
}
.ticket-preview-box .card-body .ticket-preview-list .item .buttons-list a i {
  top: 50%;
  left: 12px;
  line-height: 1;
  font-size: 17px;
  margin-top: 1px;
  position: absolute;
  transform: translateY(-50%);
  color: var(--splash-primary-color);
}
.ticket-preview-box .card-body .ticket-preview-list .item .buttons-list a:hover {
  border-color: var(--splash-primary-color);
}
.ticket-preview-box .card-body .ticket-preview-list .item .buttons-list a:last-child {
  margin-right: 0;
}
.ticket-preview-box .card-body .ticket-preview-list .item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}
.ticket-preview-box .card-body .ticket-preview-list .item:first-child {
  padding-top: 0;
}
.ticket-preview-box .card-body .ticket-preview-list .item .more-conversation {
  left: 50%;
  bottom: -20px;
  padding: 7px 30px;
  position: absolute;
  border-radius: 30px;
  border: 1px solid #E6E6FF;
  transform: translateX(-50%);
}

.contact-information-card .card-head {
  padding-top: 25px;
  padding-left: 30px;
  padding-right: 30px;
  padding-bottom: 20px;
}
.contact-information-card .card-body {
  border-top: 1px dashed #d9e9ef;
  padding-top: 25px;
  padding-left: 30px;
  padding-right: 30px;
  padding-bottom: 25px;
}
.contact-information-card .card-body .info-list {
  margin-top: 20px;
}
.contact-information-card .card-body .info-list li {
  margin-bottom: 10px;
  padding-left: 85px;
}
.contact-information-card .card-body .info-list li span {
  top: 0;
  left: 0;
  position: absolute;
}
.contact-information-card .card-body .info-list li:last-child {
  margin-bottom: 0;
}

.dark .ticket-preview-box .card-body .ticket-preview-list .item {
  border-bottom-color: #45445e;
}
.dark .ticket-preview-box .card-body .ticket-preview-list .item .buttons-list a {
  border-color: #45445e;
}
.dark .ticket-preview-box .card-body .ticket-preview-list .item .buttons-list a:hover {
  border-color: var(--splash-primary-color);
}
.dark .ticket-preview-box .card-body .ticket-preview-list .item .more-conversation {
  border-color: #45445e;
}
.dark .contact-information-card .card-body {
  border-top-color: #45445e;
}

@media only screen and (max-width: 767px) {
  .ticket-preview-box .card-head .buttons-list {
    margin-top: 2px;
  }
  .ticket-preview-box .card-head .buttons-list button {
    padding: 7px 30px 7px 10px;
    margin-right: 3px;
    margin-right: 5px;
    margin-top: 8px;
  }
  .ticket-preview-box .card-head .buttons-list button i {
    right: 10px;
  }
  .ticket-preview-box .card-body .ticket-preview-list .item {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .ticket-preview-box .card-body .ticket-preview-list .item .buttons-list {
    margin-bottom: -8px;
  }
  .ticket-preview-box .card-body .ticket-preview-list .item .buttons-list a {
    margin-bottom: 8px;
  }
  .ticket-preview-box .card-body .ticket-preview-list .item .more-conversation {
    display: none !important;
  }
  .contact-information-card .card-head {
    padding: 15px;
  }
  .contact-information-card .card-body {
    padding: 15px;
  }
  .contact-information-card .card-body .info-list {
    margin-top: 15px;
  }
  .contact-information-card .card-body .info-list li {
    padding-left: 75px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .contact-information-card .card-head {
    padding: 20px;
  }
  .contact-information-card .card-body {
    padding: 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .ticket-preview-box .card-head .buttons-list {
    margin-top: 12px;
  }
  .ticket-preview-box .card-head .buttons-list button {
    padding: 8px 35px 8px 15px;
  }
  .contact-information-card .card-head {
    padding-top: 20px;
    padding-left: 25px;
    padding-right: 25px;
    padding-bottom: 20px;
  }
  .contact-information-card .card-body {
    padding-top: 20px;
    padding-left: 25px;
    padding-right: 25px;
    padding-bottom: 20px;
  }
  .contact-information-card .card-body .info-list {
    margin-top: 15px;
  }
  .contact-information-card .card-body .info-list li {
    padding-left: 80px;
  }
}
.kanban-title-card {
  border-top: 4px solid var(--splash-success-color) !important;
}

.col-xxxl-3:nth-child(2) .kanban-title-card {
  border-top-color: var(--splash-info-color) !important;
}
.col-xxxl-3:nth-child(3) .kanban-title-card {
  border-top-color: var(--splash-primary-color) !important;
}
.col-xxxl-3:nth-child(4) .kanban-title-card {
  border-top-color: var(--splash-warning-color) !important;
}

.kanban-card .card-body p {
  margin-bottom: 10px;
}
.kanban-card .card-body p:last-child {
  margin-bottom: 0;
}
.kanban-card .card-body .users-list div {
  width: 30px;
  height: 30px;
  margin-right: -10px;
  border: 2px solid var(--splash-white-color);
  filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));
}
.kanban-card .card-body .users-list div:last-child {
  margin-right: 0;
}
.kanban-card .card-body .tags-list span {
  margin-right: 5px;
}
.kanban-card .card-body .tags-list span:last-child {
  margin-right: 0;
}
.kanban-card .card-body .info span {
  margin-right: 15px;
  padding-left: 22px;
}
.kanban-card .card-body .info span i {
  left: 0;
  top: 50%;
  line-height: 1;
  font-size: 16px;
  position: absolute;
  transform: translateY(-50%);
}
.kanban-card .card-body .info span:last-child {
  margin-right: 0;
}

.add-another-task-btn {
  border: 0.5px solid #D7D7DE;
  padding: 13px 40px 13px 25px;
}
.add-another-task-btn i {
  transform: translateY(-50%);
  position: absolute;
  margin-left: 8px;
  font-size: 15px;
  line-height: 1;
  top: 50%;
}
.add-another-task-btn:hover {
  background-color: #f8f8f8 !important;
}

.dark .add-another-task-btn {
  border-color: #45445e;
}
.dark .add-another-task-btn:hover {
  background-color: var(--splash-black-color) !important;
}

@media only screen and (max-width: 767px) {
  .add-another-task-btn {
    padding: 12px 40px 12px 25px;
  }
}
.event-card .card-body .date {
  width: 80px;
  border: 1px solid var(--splash-primary-color);
}
.event-card .card-body .date span:first-child {
  padding-top: 5px;
  padding-bottom: 5px;
}
.event-card .card-body .date span:last-child {
  font-size: 28px;
}
.event-card .card-body p {
  margin-bottom: 15px;
}
.event-card .card-body p:last-child {
  margin-bottom: 0;
}
.event-card .card-body .info-list li {
  margin-bottom: 12px;
  padding-left: 100px;
}
.event-card .card-body .info-list li span {
  top: 0;
  left: 0;
  position: absolute;
}
.event-card .card-body .info-list li:last-child {
  margin-bottom: 0;
}
.event-card .card-body .users-list div {
  width: 33px;
  height: 33px;
  margin-right: -10px;
  border: 2px solid var(--splash-white-color);
  filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));
}
.event-card .card-body .users-list div:last-child {
  margin-right: 0;
}
.event-card .card-body .link-btn {
  background: #F2F1F9;
}
.event-card .card-body .link-btn span {
  line-height: 1.3;
}
.event-card .card-body .link-btn span::before {
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  content: "";
  position: absolute;
  transition: var(--transition);
  background: var(--splash-primary-color);
}
.event-card .card-body .link-btn i {
  line-height: 0.01;
  font-size: 20px;
}
.event-card .card-body .link-btn:hover span::before {
  transform: scaleX(0);
}
.event-card .card-body .link-btn.closed {
  background: #F8F8FC;
  pointer-events: none;
}
.event-card .card-body .link-btn.closed span::before {
  display: none;
}

.dark .event-card .card-body .users-list div {
  border-color: #45445e;
}
.dark .event-card .card-body .link-btn {
  background: var(--splash-black-color);
}
.dark .event-card .card-body .link-btn.closed {
  background: #3a3950;
}

@media only screen and (max-width: 767px) {
  .event-card .card-body .date {
    width: 65px;
  }
  .event-card .card-body .date span:last-child {
    font-size: 25px;
  }
  .event-card .card-body .info-list li {
    padding-left: 80px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .event-card .card-body .info-list li {
    padding-left: 85px;
  }
}
.event-details-card .card-body .date {
  width: 80px;
  border: 1px solid var(--splash-primary-color);
}
.event-details-card .card-body .date span:first-child {
  padding-top: 5px;
  padding-bottom: 5px;
}
.event-details-card .card-body .date span:last-child {
  font-size: 28px;
}
.event-details-card .card-body .buttons-list {
  margin-bottom: -10px;
}
.event-details-card .card-body .buttons-list button {
  padding: 12px 46px 12px 25px;
  background: #F2F1F9;
  margin-right: 10px;
  margin-bottom: 10px;
}
.event-details-card .card-body .buttons-list button i {
  top: 50%;
  right: 25px;
  line-height: 1;
  margin-top: 1px;
  position: absolute;
  transform: translateY(-50%);
  transition: var(--transition);
}
.event-details-card .card-body .buttons-list button:hover {
  background-color: var(--splash-primary-color);
  color: var(--splash-white-color) !important;
}
.event-details-card .card-body .buttons-list button:hover i {
  color: var(--splash-white-color) !important;
}
.event-details-card .card-body .buttons-list button:last-child {
  margin-right: 0;
}
.event-details-card .card-body .buttons-list button:last-child {
  padding: 0;
  width: 45px;
  height: 45px;
  line-height: 45px;
  text-align: center;
}
.event-details-card .card-body .buttons-list button:last-child i {
  left: 0;
  right: 0;
}
.event-details-card .card-body p {
  margin-bottom: 15px;
}
.event-details-card .card-body p:last-child {
  margin-bottom: 0;
}
.event-details-card .card-body .info {
  padding-top: 30px;
  padding-left: 30px;
  padding-right: 30px;
  padding-bottom: 5px;
}
.event-details-card .card-body .info .info-card {
  margin-bottom: 25px;
  padding-top: 8px;
  padding-left: 78px;
  padding-bottom: 8px;
}
.event-details-card .card-body .info .info-card .icon {
  top: 50%;
  width: 65px;
  height: 65px;
  font-size: 28px;
  transform: translateY(-50%);
}
.event-details-card .card-body .info .info-card .icon i {
  left: 0;
  top: 50%;
  right: 0;
  line-height: 1;
  margin-top: 1px;
  position: absolute;
  transform: translateY(-50%);
}

.event-speakers-card .card-body ul li {
  border-bottom: 1px dashed #d9e9ef;
  padding-top: 15px;
  padding-bottom: 15px;
}
.event-speakers-card .card-body ul li img {
  border: 2px solid var(--splash-white-color);
  filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));
}
.event-speakers-card .card-body ul li:last-child {
  border-bottom: none;
  padding-bottom: 0;
}
.event-speakers-card .card-body ul li:first-child {
  padding-top: 0;
}

.event-topic-card .card-body ul li {
  margin-bottom: 14px;
  padding-left: 28px;
}
.event-topic-card .card-body ul li i {
  left: 0;
  top: 50%;
  line-height: 1;
  font-size: 18px;
  position: absolute;
  transform: translateY(-50%);
  color: var(--splash-primary-color);
}
.event-topic-card .card-body ul li:last-child {
  margin-bottom: 0;
}

.dark .event-details-card .card-body .buttons-list button {
  background: var(--splash-black-color);
}
.dark .event-details-card .card-body .buttons-list button:hover {
  background-color: var(--splash-primary-color);
}
.dark .event-speakers-card .card-body ul li {
  border-bottom-color: #45445e;
}
.dark .event-speakers-card .card-body ul li img {
  border-color: #45445e;
}

@media only screen and (max-width: 767px) {
  .event-details-card .card-body .date {
    width: 65px;
  }
  .event-details-card .card-body .date span:last-child {
    font-size: 25px;
  }
  .event-details-card .card-body .buttons-list {
    margin-bottom: -15px;
  }
  .event-details-card .card-body .buttons-list button {
    padding: 10px 40px 10px 20px;
    margin-right: 5px;
  }
  .event-details-card .card-body .buttons-list button i {
    right: 20px;
  }
  .event-details-card .card-body .buttons-list button:last-child {
    top: -4px;
    width: 41px;
    height: 41px;
    line-height: 41px;
  }
  .event-details-card .card-body .info {
    padding-top: 20px;
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 5px;
  }
  .event-details-card .card-body .info .info-card {
    margin-bottom: 15px;
    padding-top: 8px;
    padding-left: 78px;
    padding-bottom: 8px;
  }
  .event-speakers-card .card-body ul li {
    padding-top: 10px;
    padding-bottom: 10px;
  }
  .event-topic-card .card-body ul li {
    margin-bottom: 12px;
    padding-left: 24px;
  }
  .event-topic-card .card-body ul li i {
    font-size: 16px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .event-details-card .card-body .buttons-list button:last-child {
    top: 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .event-details-card .card-body .info {
    padding-top: 25px;
    padding-left: 25px;
    padding-right: 25px;
    padding-bottom: 5px;
  }
  .event-details-card .card-body .info .info-card {
    margin-bottom: 20px;
  }
  .event-speakers-card .card-body ul li {
    padding-top: 12px;
    padding-bottom: 12px;
  }
}
.profile-settings-card .ql-container {
  min-height: 150px;
  height: auto;
}
.profile-settings-card .input-group .input-group-text {
  padding: 10px 18px;
  border-right: none;
  background: #F5F4FA;
  border-color: #dedee4;
}
.profile-settings-card .input-group .form-control {
  border-left: none;
}
.profile-settings-card .file-upload {
  border: 1px solid #dedee4;
  padding: 55px 15px;
}
.profile-settings-card .file-upload i {
  line-height: 1;
  font-size: 35px;
  margin-bottom: 5px;
  color: var(--splash-primary-color);
}
.profile-settings-card .file-upload span span::before {
  left: 0;
  right: 0;
  height: 1px;
  content: "";
  bottom: -2px;
  position: absolute;
  background: var(--splash-black-color);
}
.profile-settings-card .file-upload input {
  cursor: pointer;
}
.profile-settings-card .members-list div {
  margin-top: 10px;
  margin-right: 5px;
}
.profile-settings-card .members-list button {
  font-size: 8px;
  margin-left: 3px;
}
.profile-settings-card .members-list button:hover {
  color: red;
}
.profile-settings-card button span {
  line-height: 1.3;
}
.profile-settings-card button span::before {
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  content: "";
  position: absolute;
  transition: var(--transition);
  background: var(--splash-danger-color);
}

.dark .profile-settings-card .input-group .input-group-text {
  background: var(--splash-black-color);
  border-color: #45445e;
}
.dark .profile-settings-card .file-upload {
  border-color: #45445e;
}
.dark .profile-settings-card .file-upload span span::before {
  background: var(--splash-white-color);
}

.starter-card .card-body {
  padding: 100px;
}
.starter-card .card-body h1 {
  font-size: 30px;
  max-width: 650px;
  line-height: 1.5;
  margin-top: 50px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 25px;
}

@media only screen and (max-width: 767px) {
  .starter-card .card-body {
    padding: 25px 15px;
  }
  .starter-card .card-body h1 {
    font-size: 18px;
    max-width: 100%;
    margin-top: 15px;
    margin-bottom: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .starter-card .card-body {
    padding: 50px;
  }
  .starter-card .card-body h1 {
    font-size: 24px;
    max-width: 600px;
    margin-top: 30px;
    margin-bottom: 20px;
  }
}
.authentication-card .card-body {
  padding: 100px 130px;
}
.authentication-card .card-body .icon {
  width: 100px;
  height: 100px;
  font-size: 35px;
  background: #ECF3F2;
}
.authentication-card .card-body .icon i {
  left: 0;
  right: 0;
  top: 50%;
  line-height: 1;
  position: absolute;
  transform: translateY(-50%);
}
.authentication-card .card-body .sub-text {
  max-width: 380px;
  margin-top: 25px;
  margin-left: auto;
  margin-right: auto;
}
.authentication-card .card-body form {
  margin-top: 40px;
}
.authentication-card .card-body form .form-check {
  padding-left: 1.5em;
  position: relative;
  min-height: 16px;
  top: 1.5px;
}
.authentication-card .card-body form .form-check .form-check-input {
  top: -1px;
  width: 16px;
  height: 16px;
  position: relative;
  margin-left: -1.5em;
}
.authentication-card .card-body form .form-check .form-check-input[type=checkbox] {
  border-radius: 50%;
}
.authentication-card .card-body form .forgot-password-btn {
  line-height: 1.3;
}
.authentication-card .card-body form .forgot-password-btn::before {
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  content: "";
  position: absolute;
  transition: var(--transition);
  background: var(--splash-primary-color);
}
.authentication-card .card-body form .forgot-password-btn:hover::before {
  transform: scaleX(0);
}
.authentication-card .card-body form .default-btn {
  padding: 14px 25px;
}
.authentication-card .card-body form .default-btn.with-border {
  background-color: transparent;
  color: var(--splash-primary-color);
  border: 1px solid var(--splash-primary-color);
  padding-top: 13px;
  padding-bottom: 13px;
}
.authentication-card .card-body form .default-btn.with-border:hover {
  background-color: var(--splash-primary-color);
  color: var(--splash-white-color);
}
.authentication-card .card-body form .or::before {
  left: 0;
  right: 0;
  top: 50%;
  z-index: -1;
  content: "";
  height: 1px;
  position: absolute;
  background: #eeeeee;
  transform: translateY(-50%);
}
.authentication-card .card-body form .or span {
  padding-left: 25px;
  padding-right: 25px;
}
.authentication-card .card-body form .socials li {
  margin-left: 5px;
  margin-right: 5px;
}
.authentication-card .card-body form .socials li button {
  width: 30px;
  height: 30px;
  font-size: 17px;
  color: var(--splash-white-color);
  background-color: var(--splash-primary-color);
}
.authentication-card .card-body form .socials li button i {
  left: 0;
  right: 0;
  top: 50%;
  line-height: 1;
  margin-top: -0.5px;
  position: absolute;
  transform: translateY(-50%);
}
.authentication-card .card-body form .socials li button.facebook {
  background: #3B5998;
}
.authentication-card .card-body form .socials li button.google {
  background: #DB3236;
}
.authentication-card .card-body form .socials li button.twitter {
  background: #00ACEE;
}
.authentication-card .card-body form .socials li button.linkedin {
  background: #0072B1;
}
.authentication-card .card-body form .link-btn {
  padding-left: 23px;
}
.authentication-card .card-body form .link-btn i {
  transform: translateY(-50%);
  position: absolute;
  top: 50%;
  left: 0;
}
.authentication-card.email-confirmation-card .card-body .default-btn {
  padding: 14px 25px;
}

.dark .authentication-card .card-body .icon {
  background: var(--splash-black-color);
}
.dark .authentication-card .card-body form .or::before {
  background: #45445e;
}

@media only screen and (max-width: 767px) {
  .authentication-card .card-body {
    padding: 25px 15px;
  }
  .authentication-card .card-body .icon {
    width: 60px;
    height: 60px;
    font-size: 25px;
  }
  .authentication-card .card-body .sub-text {
    max-width: 100%;
    margin-top: 10px;
  }
  .authentication-card .card-body form {
    margin-top: 20px;
  }
  .authentication-card .card-body form .default-btn {
    padding: 13px 25px;
  }
  .authentication-card .card-body form .default-btn.with-border {
    padding-top: 12px;
    padding-bottom: 12px;
  }
  .authentication-card .card-body form .socials li {
    margin-left: 3px;
    margin-right: 3px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .authentication-card .card-body {
    padding: 35px 25px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .authentication-card .card-body {
    padding: 50px 80px;
  }
  .authentication-card .card-body .icon {
    width: 80px;
    height: 80px;
    font-size: 30px;
  }
  .authentication-card .card-body .sub-text {
    margin-top: 12px;
  }
  .authentication-card .card-body form {
    margin-top: 30px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .authentication-card .card-body {
    padding: 70px 100px;
  }
  .authentication-card .card-body .sub-text {
    margin-top: 15px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .authentication-card .card-body {
    padding: 70px 100px;
  }
}
.add-user-card .ql-container {
  min-height: 150px;
  height: auto;
}
.add-user-card .input-group .input-group-text {
  padding: 10px 18px;
  border-right: none;
  background: #F5F4FA;
  border-color: #dedee4;
}
.add-user-card .input-group .form-control {
  border-left: none;
}
.add-user-card .file-upload {
  border: 1px solid #dedee4;
  padding: 55px 15px;
}
.add-user-card .file-upload i {
  line-height: 1;
  font-size: 35px;
  margin-bottom: 5px;
  color: var(--splash-primary-color);
}
.add-user-card .file-upload span span::before {
  left: 0;
  right: 0;
  height: 1px;
  content: "";
  bottom: -2px;
  position: absolute;
  background: var(--splash-black-color);
}
.add-user-card .file-upload input {
  cursor: pointer;
}
.add-user-card .members-list div {
  margin-top: 10px;
  margin-right: 5px;
}
.add-user-card .members-list button {
  font-size: 8px;
  margin-left: 3px;
}
.add-user-card .members-list button:hover {
  color: red;
}
.add-user-card button span {
  line-height: 1.3;
}
.add-user-card button span::before {
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  content: "";
  position: absolute;
  transition: var(--transition);
  background: var(--splash-danger-color);
}

.dark .add-user-card .input-group .input-group-text {
  background: var(--splash-black-color);
  border-color: #45445e;
}
.dark .add-user-card .file-upload {
  border-color: #45445e;
}
.dark .add-user-card .file-upload span span::before {
  background: var(--splash-white-color);
}

.pricing-card .card-body .price div {
  font-size: 28px;
}
.pricing-card .card-body p {
  margin-bottom: 15px;
}
.pricing-card .card-body p:last-child {
  margin-bottom: 0;
}
.pricing-card .card-body ul li {
  margin-bottom: 14px;
  padding-left: 28px;
}
.pricing-card .card-body ul li i {
  left: 0;
  top: 50%;
  line-height: 1;
  font-size: 18px;
  position: absolute;
  transform: translateY(-50%);
  color: var(--splash-primary-color);
}
.pricing-card .card-body ul li i.flaticon-close {
  color: red;
  font-size: 16px;
}
.pricing-card .card-body ul li:last-child {
  margin-bottom: 0;
}
.pricing-card .card-body .link-btn {
  background: #F8F8FC;
}
.pricing-card .card-body .link-btn i {
  line-height: 0.01;
  font-size: 20px;
}
.pricing-card .card-body .link-btn:hover span::before {
  transform: scaleX(0);
}

.dark .pricing-card .card-body .link-btn {
  background: var(--splash-black-color);
}

@media only screen and (max-width: 767px) {
  .pricing-card .card-body ul li {
    margin-bottom: 12px;
    padding-left: 24px;
  }
  .pricing-card .card-body ul li i {
    font-size: 16px;
  }
}
.faq-accordion-card .card-body .accordion .accordion-item {
  margin-bottom: 25px;
}
.faq-accordion-card .card-body .accordion .accordion-item .accordion-button {
  background: #EFEEF9;
  padding-right: 45px;
  line-height: 1.7;
}
.faq-accordion-card .card-body .accordion .accordion-item .accordion-button::after {
  top: 50%;
  right: 20px;
  margin-left: 0;
  position: absolute;
  transform: translateY(-50%) rotate(180deg);
}
.faq-accordion-card .card-body .accordion .accordion-item .accordion-button.collapsed::after {
  transform: translateY(-50%);
}
.faq-accordion-card .card-body .accordion .accordion-item .accordion-body {
  padding-top: 20px;
  padding-left: 20px;
  padding-right: 20px;
}
.faq-accordion-card .card-body .accordion .accordion-item .accordion-body p {
  margin-bottom: 15px;
}
.faq-accordion-card .card-body .accordion .accordion-item .accordion-body p:last-child {
  margin-bottom: 0;
}

.dark .faq-accordion-card .card-body .accordion .accordion-item {
  background-color: transparent;
}
.dark .faq-accordion-card .card-body .accordion .accordion-item .accordion-button {
  background: var(--splash-black-color);
}

@media only screen and (max-width: 767px) {
  .faq-accordion-card .card-body .accordion .accordion-item {
    margin-bottom: 15px;
  }
  .faq-accordion-card .card-body .accordion .accordion-item .accordion-button {
    padding-left: 15px;
    padding-right: 25px;
  }
  .faq-accordion-card .card-body .accordion .accordion-item .accordion-button::after {
    right: 10px;
  }
  .faq-accordion-card .card-body .accordion .accordion-item .accordion-body {
    padding-left: 0;
    padding-right: 0;
    padding-top: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .faq-accordion-card .card-body .accordion .accordion-item {
    margin-bottom: 20px;
  }
  .faq-accordion-card .card-body .accordion .accordion-item .accordion-body {
    padding-left: 0;
    padding-right: 0;
    padding-top: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .faq-accordion-card .card-body .accordion .accordion-item {
    margin-bottom: 20px;
  }
  .faq-accordion-card .card-body .accordion .accordion-item .accordion-body {
    padding-top: 15px;
    padding-left: 15px;
    padding-right: 15px;
  }
}
.error-404-card .card-body {
  padding: 70px;
}
.error-404-card .card-body h1 {
  font-size: 30px;
  line-height: 1.5;
  margin-top: 50px;
  margin-bottom: 35px;
}

@media only screen and (max-width: 767px) {
  .error-404-card .card-body {
    padding: 25px 15px;
  }
  .error-404-card .card-body h1 {
    font-size: 18px;
    margin-top: 15px;
    margin-bottom: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .error-404-card .card-body {
    padding: 50px;
  }
  .error-404-card .card-body h1 {
    font-size: 24px;
    margin-top: 30px;
    margin-bottom: 20px;
  }
}
.maintenance-card .card-body {
  padding: 70px;
}
.maintenance-card .card-body h1 {
  font-size: 20px;
  margin-top: 40px;
  margin-bottom: 12px;
}
.maintenance-card .card-body p {
  max-width: 300px;
}

@media only screen and (max-width: 767px) {
  .maintenance-card .card-body {
    padding: 25px 15px;
  }
  .maintenance-card .card-body h1 {
    font-size: 16px;
    margin-top: 25px;
    margin-bottom: 8px;
  }
  .maintenance-card .card-body p {
    max-width: 100%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .maintenance-card .card-body {
    padding: 50px;
  }
}
.create-social-post-card .card-body .nav.nav-tabs {
  border-bottom: 1px solid #EBEAF4;
  margin-bottom: 30px;
}
.create-social-post-card .card-body .nav.nav-tabs .nav-item {
  margin-right: 50px;
}
.create-social-post-card .card-body .nav.nav-tabs .nav-item .nav-link {
  color: #8E8DA2;
  padding: 0 0 15px 0;
}
.create-social-post-card .card-body .nav.nav-tabs .nav-item .nav-link::before {
  left: 0;
  width: 0;
  height: 2px;
  content: "";
  bottom: -1px;
  position: absolute;
  transition: var(--transition);
  background: var(--splash-primary-color);
}
.create-social-post-card .card-body .nav.nav-tabs .nav-item .nav-link:hover, .create-social-post-card .card-body .nav.nav-tabs .nav-item .nav-link.active {
  color: var(--splash-black-color);
}
.create-social-post-card .card-body .nav.nav-tabs .nav-item .nav-link:hover::before, .create-social-post-card .card-body .nav.nav-tabs .nav-item .nav-link.active::before {
  width: 100%;
}
.create-social-post-card .card-body .nav.nav-tabs .nav-item:last-child {
  margin-right: 0;
}
.create-social-post-card .card-body .tab-content {
  box-shadow: 0px 4px 34px rgba(101, 96, 240, 0.1);
}
.create-social-post-card .card-body .tab-content form .input-post {
  border: none;
  padding: 20px;
  background: #F2F1F9;
}
.create-social-post-card .card-body .tab-content form .input-post::placeholder {
  color: #8E8DA2;
  transition: var(--transition);
}
.create-social-post-card .card-body .tab-content form .input-post:focus::placeholder {
  color: transparent;
}
.create-social-post-card .card-body .tab-content form .buttons-list {
  top: 3px;
}
.create-social-post-card .card-body .tab-content form .buttons-list button {
  font-size: 25px;
  line-height: 0.01;
  margin-right: 10px;
  color: var(--splash-muted-color);
}
.create-social-post-card .card-body .tab-content form .buttons-list button:hover {
  color: var(--splash-primary-color);
}
.create-social-post-card .card-body .tab-content form .buttons-list button:last-child {
  margin-right: 0;
}

.social-post-card .card-body .user-info {
  border-bottom: 1px dashed #D2CFE4;
}
.social-post-card .card-body .user-info img {
  border: 2px solid var(--splash-white-color);
  filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));
}
.social-post-card .card-body p {
  margin-bottom: 15px;
}
.social-post-card .card-body p:last-child {
  margin-bottom: 0;
}
.social-post-card .card-body .post-info {
  margin-top: 20px;
  margin-bottom: 20px;
  padding-top: 20px;
  padding-bottom: 20px;
  border-top: 1px dashed #D2CFE4;
  border-bottom: 1px dashed #D2CFE4;
}
.social-post-card .card-body .post-info ul li {
  margin-right: 20px;
}
.social-post-card .card-body .post-info ul li button {
  padding: 0 0 0 23px;
}
.social-post-card .card-body .post-info ul li button i {
  left: 0;
  top: 50%;
  line-height: 1;
  font-size: 16px;
  position: absolute;
  transform: translateY(-50%);
  transition: var(--transition);
}
.social-post-card .card-body .post-info ul li button:hover i {
  color: var(--splash-primary-color);
}
.social-post-card .card-body .post-info ul li:last-child {
  margin-right: 0;
}
.social-post-card .card-body .post-info .views {
  padding-left: 23px;
}
.social-post-card .card-body .post-info .views i {
  left: 0;
  top: 50%;
  line-height: 1;
  font-size: 16px;
  margin-top: 0.5px;
  position: absolute;
  transform: translateY(-50%);
}
.social-post-card .card-body .write-your-comment {
  padding-right: 220px;
}
.social-post-card .card-body .write-your-comment .write-comment {
  padding: 16px 40px 16px 58px;
  background: #F5F4FA;
  border-radius: 10px;
}
.social-post-card .card-body .write-your-comment .write-comment img {
  top: 50%;
  left: 12px;
  position: absolute;
  transform: translateY(-50%);
  border: 2px solid var(--splash-white-color);
  filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));
}
.social-post-card .card-body .write-your-comment .write-comment .input-comment {
  border: none;
  background-color: transparent;
  border-left: 1px solid #A4A3B0;
  padding-top: 3px;
  padding-left: 10px;
  padding-bottom: 3px;
  padding-right: 10px;
}
.social-post-card .card-body .write-your-comment .write-comment .input-comment::placeholder {
  color: #8E8DA1;
  transition: var(--transition);
}
.social-post-card .card-body .write-your-comment .write-comment .input-comment:focus::placeholder {
  color: transparent;
}
.social-post-card .card-body .write-your-comment .write-comment button {
  top: 50%;
  right: 15px;
  font-size: 25px;
  margin-top: 3px;
  position: absolute;
  transform: translateY(-50%);
  color: var(--splash-muted-color);
}
.social-post-card .card-body .write-your-comment .write-comment button:hover {
  color: var(--splash-primary-color);
}
.social-post-card .card-body .write-your-comment .buttons-list {
  top: 50%;
  right: 0;
  position: absolute;
  transform: translateY(-50%);
}
.social-post-card .card-body .write-your-comment .buttons-list button {
  width: 59px;
  height: 59px;
  font-size: 22px;
  margin-right: 10px;
  border-radius: 10px;
  background: #F5F4FA;
  color: var(--splash-primary-color);
}
.social-post-card .card-body .write-your-comment .buttons-list button i {
  left: 0;
  right: 0;
  top: 50%;
  line-height: 1;
  margin-top: 1px;
  position: absolute;
  transform: translateY(-50%);
}
.social-post-card .card-body .write-your-comment .buttons-list button:last-child {
  margin-right: 0;
}
.social-post-card .card-body .write-your-comment .buttons-list button.active, .social-post-card .card-body .write-your-comment .buttons-list button:hover {
  background-color: var(--splash-primary-color);
  color: var(--splash-white-color);
}

.social-friends-card .card-body ul li {
  border-bottom: 1px dashed #d9e9ef;
  padding-top: 15px;
  padding-bottom: 15px;
}
.social-friends-card .card-body ul li img {
  border: 2px solid var(--splash-white-color);
  filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));
}
.social-friends-card .card-body ul li:last-child {
  border-bottom: none;
  padding-bottom: 0;
}
.social-friends-card .card-body ul li:first-child {
  padding-top: 0;
}

.social-photos-card .card-body .row {
  margin-left: -5px;
  margin-right: -5px;
  margin-bottom: -10px;
}
.social-photos-card .card-body .row .col-3 {
  padding-left: 5px;
  padding-right: 5px;
}

.dark .create-social-post-card .card-body .nav.nav-tabs {
  border-bottom-color: #45445e;
}
.dark .create-social-post-card .card-body .nav.nav-tabs .nav-item .nav-link {
  color: #BCBBC7;
  background-color: transparent !important;
}
.dark .create-social-post-card .card-body .nav.nav-tabs .nav-item .nav-link:hover, .dark .create-social-post-card .card-body .nav.nav-tabs .nav-item .nav-link.active {
  color: var(--splash-white-color);
}
.dark .create-social-post-card .card-body .tab-content form .input-post {
  background: var(--splash-black-color);
}
.dark .create-social-post-card .card-body .tab-content form .input-post::placeholder {
  color: #BCBBC7;
}
.dark .create-social-post-card .card-body .tab-content form .input-post:focus::placeholder {
  color: transparent;
}
.dark .create-social-post-card .card-body .tab-content form .buttons-list button {
  color: #BCBBC7;
}
.dark .create-social-post-card .card-body .tab-content form .buttons-list button:hover {
  color: var(--splash-primary-color);
}
.dark .social-post-card .card-body .user-info {
  border-bottom-color: #45445e;
}
.dark .social-post-card .card-body .user-info img {
  border-color: #45445e;
}
.dark .social-post-card .card-body .post-info {
  border-top-color: #45445e;
  border-bottom-color: #45445e;
}
.dark .social-post-card .card-body .write-your-comment .write-comment {
  background: var(--splash-black-color);
}
.dark .social-post-card .card-body .write-your-comment .write-comment img {
  border-color: #45445e;
}
.dark .social-post-card .card-body .write-your-comment .write-comment .input-comment {
  border-left-color: #45445e;
}
.dark .social-post-card .card-body .write-your-comment .write-comment .input-comment::placeholder {
  color: #BCBBC7;
}
.dark .social-post-card .card-body .write-your-comment .write-comment .input-comment:focus::placeholder {
  color: transparent;
}
.dark .social-post-card .card-body .write-your-comment .write-comment button {
  color: #BCBBC7;
}
.dark .social-post-card .card-body .write-your-comment .write-comment button:hover {
  color: var(--splash-primary-color);
}
.dark .social-post-card .card-body .write-your-comment .buttons-list button {
  background: var(--splash-black-color);
}
.dark .social-post-card .card-body .write-your-comment .buttons-list button.active, .dark .social-post-card .card-body .write-your-comment .buttons-list button:hover {
  background-color: var(--splash-primary-color);
}
.dark .social-friends-card .card-body ul li {
  border-bottom-color: #45445e;
}
.dark .social-friends-card .card-body ul li img {
  border-color: #45445e;
}

@media only screen and (max-width: 767px) {
  .create-social-post-card .card-body .nav.nav-tabs {
    margin-bottom: 15px;
  }
  .create-social-post-card .card-body .nav.nav-tabs .nav-item {
    margin-right: 12px;
    margin-bottom: 10px;
  }
  .create-social-post-card .card-body .nav.nav-tabs .nav-item .nav-link {
    padding: 0;
  }
  .create-social-post-card .card-body .nav.nav-tabs .nav-item .nav-link::before {
    display: none;
  }
  .create-social-post-card .card-body .tab-content form .input-post {
    padding: 12px;
  }
  .create-social-post-card .card-body .tab-content form .buttons-list {
    top: 0;
  }
  .create-social-post-card .card-body .tab-content form .buttons-list button {
    font-size: 20px;
  }
  .social-post-card .card-body .post-info {
    margin-top: 15px;
    margin-bottom: 15px;
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .social-post-card .card-body .post-info ul li {
    margin-right: 12px;
    margin-bottom: 12px;
  }
  .social-post-card .card-body .post-info ul li button {
    padding: 0 0 0 22px;
  }
  .social-post-card .card-body .post-info ul li button i {
    font-size: 15px;
  }
  .social-post-card .card-body .post-info .views {
    padding-left: 22px;
  }
  .social-post-card .card-body .post-info .views i {
    font-size: 15px;
  }
  .social-post-card .card-body .write-your-comment {
    padding-right: 0;
  }
  .social-post-card .card-body .write-your-comment .write-comment {
    padding: 15px 32px 15px 55px;
  }
  .social-post-card .card-body .write-your-comment .write-comment img {
    left: 10px;
  }
  .social-post-card .card-body .write-your-comment .write-comment .input-comment {
    padding-top: 2px;
    padding-bottom: 2px;
  }
  .social-post-card .card-body .write-your-comment .write-comment button {
    right: 10px;
    font-size: 20px;
  }
  .social-post-card .card-body .write-your-comment .buttons-list {
    top: 0;
    margin-top: 12px;
    position: relative;
    transform: translateY(0);
  }
  .social-post-card .card-body .write-your-comment .buttons-list button {
    width: 50px;
    height: 50px;
    font-size: 20px;
    margin-right: 5px;
  }
  .social-post-card .card-body .write-your-comment .buttons-list button i {
    margin-top: 1px;
  }
  .social-friends-card .card-body ul li {
    padding-top: 10px;
    padding-bottom: 10px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .create-social-post-card .card-body .nav.nav-tabs {
    margin-bottom: 25px;
  }
  .create-social-post-card .card-body .nav.nav-tabs .nav-item {
    margin-right: 25px;
  }
  .create-social-post-card .card-body .nav.nav-tabs .nav-item .nav-link {
    padding: 0 0 12px 0;
  }
  .create-social-post-card .card-body .tab-content form .input-post {
    padding: 18px;
  }
  .social-friends-card .card-body ul li {
    padding-top: 12px;
    padding-bottom: 12px;
  }
}
@media only screen and (min-width: 1600px) {
  .social-post-card .card-body .post-info ul li {
    margin-right: 40px;
  }
}
.cover-image-card .card-body .file-upload {
  left: 15px;
  width: 32px;
  height: 32px;
  bottom: 15px;
  font-size: 16px;
  overflow: hidden;
  position: absolute;
}
.cover-image-card .card-body .file-upload i {
  left: 0;
  top: 50%;
  right: 0;
  line-height: 1;
  position: absolute;
  transform: translateY(-50%);
}
.cover-image-card .card-body .file-upload input {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  border-radius: 50%;
  position: absolute;
}
.cover-image-card .card-body .settings-tabs {
  display: block;
}
.cover-image-card .card-body .settings-tabs li {
  flex: unset;
  display: inline-block;
}
.cover-image-card .card-body .settings-tabs li a {
  padding: 11px 45px;
}

.settings-tabs {
  display: flex;
  flex-wrap: wrap;
}
.settings-tabs li {
  flex: 1 0 0%;
  margin-left: 5px;
  margin-right: 5px;
}
.settings-tabs li a {
  background: #F2F1F9;
  padding: 11px 15px;
}
.settings-tabs li a:hover, .settings-tabs li a.active {
  background-color: var(--splash-primary-color);
  color: var(--splash-white-color);
}

.settings-card .card-body form .input-group .input-group-text {
  padding: 10px 18px;
  border-right: none;
  background: #F5F4FA;
  border-color: #dedee4;
}
.settings-card .card-body form .input-group .form-control {
  border-left: none;
}
.settings-card .card-body form .file-upload {
  border: 1px solid #dedee4;
  padding: 55px 15px;
}
.settings-card .card-body form .file-upload i {
  line-height: 1;
  font-size: 35px;
  margin-bottom: 5px;
  color: var(--splash-primary-color);
}
.settings-card .card-body form .file-upload span span::before {
  left: 0;
  right: 0;
  height: 1px;
  content: "";
  bottom: -2px;
  position: absolute;
  background: var(--splash-black-color);
}
.settings-card .card-body form .file-upload input {
  cursor: pointer;
}
.settings-card .card-body form .members-list div {
  margin-top: 10px;
  margin-right: 5px;
}
.settings-card .card-body form .members-list button {
  font-size: 8px;
  margin-left: 3px;
}
.settings-card .card-body form .members-list button:hover {
  color: red;
}
.settings-card .card-body form button span {
  line-height: 1.3;
}
.settings-card .card-body form button span::before {
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  content: "";
  position: absolute;
  transition: var(--transition);
  background: var(--splash-danger-color);
}
.settings-card .card-body form .ql-container {
  height: 150px;
}
.settings-card .card-body .border-top {
  border-top: 1px dashed #D2CFE4 !important;
}
.settings-card .card-body p {
  margin-bottom: 15px;
}
.settings-card .card-body p:last-child {
  margin-bottom: 0;
}

.profile-intro-card .card-body .user-info {
  border-bottom: 1px dashed #D2CFE4;
  padding-bottom: 20px;
  margin-bottom: 20px;
}
.profile-intro-card .card-body .user-info .image img {
  border: 2px solid var(--splash-white-color);
  filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));
}
.profile-intro-card .card-body .user-info .image .file-upload {
  right: 0;
  bottom: 0;
  width: 32px;
  height: 32px;
  font-size: 16px;
  overflow: hidden;
  position: absolute;
  box-shadow: 0px 4px 4px rgba(101, 96, 240, 0.1);
}
.profile-intro-card .card-body .user-info .image .file-upload i {
  left: 0;
  top: 50%;
  right: 0;
  line-height: 1;
  position: absolute;
  transform: translateY(-50%);
}
.profile-intro-card .card-body .user-info .image .file-upload input {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  border-radius: 50%;
  position: absolute;
}
.profile-intro-card .card-body .content {
  padding-right: 45px;
  margin-bottom: 20px;
}
.profile-intro-card .card-body .content button {
  right: 0;
  top: 50%;
  width: 28px;
  height: 28px;
  position: absolute;
  background: #F2F1F9;
  transform: translateY(-50%);
  color: var(--splash-primary-color);
}
.profile-intro-card .card-body .content button i {
  left: 0;
  top: 50%;
  right: 0;
  line-height: 1;
  position: absolute;
  transform: translateY(-50%);
}
.profile-intro-card .card-body .content button:hover {
  background-color: var(--splash-primary-color);
  color: var(--splash-white-color);
}
.profile-intro-card .card-body .info-card {
  margin-left: -5px;
  margin-right: -5px;
}
.profile-intro-card .card-body .info-card .col-sm-4 {
  padding-left: 5px;
  padding-right: 5px;
}
.profile-intro-card .card-body .info-card .col-sm-4:nth-child(2) .info {
  background: #ECF3F2;
}
.profile-intro-card .card-body .info-card .col-sm-4:nth-child(3) .info {
  background: #F3F7F9;
}
.profile-intro-card .card-body .info-card .info {
  background: #F2F1F9;
  margin-top: 10px;
}

.dark .settings-tabs li a {
  background: var(--splash-black-color);
}
.dark .settings-tabs li a:hover, .dark .settings-tabs li a.active {
  background-color: var(--splash-primary-color);
}
.dark .profile-intro-card .card-body .user-info {
  border-bottom-color: #45445e;
}
.dark .profile-intro-card .card-body .user-info .image img {
  border-color: #45445e;
}
.dark .profile-intro-card .card-body .content button {
  background: var(--splash-black-color);
}
.dark .profile-intro-card .card-body .content button:hover {
  background-color: var(--splash-primary-color);
}
.dark .profile-intro-card .card-body .info-card .col-sm-4:nth-child(2) .info {
  background: var(--splash-black-color);
}
.dark .profile-intro-card .card-body .info-card .col-sm-4:nth-child(3) .info {
  background: var(--splash-black-color);
}
.dark .profile-intro-card .card-body .info-card .info {
  background: var(--splash-black-color);
}
.dark .settings-card .card-body form .input-group .input-group-text {
  background: var(--splash-black-color);
  border-color: #45445e;
}
.dark .settings-card .card-body form .file-upload {
  border-color: #45445e;
}
.dark .settings-card .card-body form .file-upload span span::before {
  background: var(--splash-white-color);
}
.dark .settings-card .card-body .border-top {
  border-top-color: #45445e !important;
}

@media only screen and (max-width: 767px) {
  .cover-image-card .card-body .file-upload {
    left: 10px;
    width: 30px;
    height: 30px;
    bottom: 10px;
    font-size: 15px;
  }
  .cover-image-card .card-body .settings-tabs li a {
    padding: 9px 25px;
  }
  .settings-tabs {
    display: block;
    margin-bottom: -10px !important;
  }
  .settings-tabs li {
    flex: unset;
    display: inline-block;
    margin-left: 5px;
    margin-right: 5px;
    margin-bottom: 8px;
  }
  .settings-tabs li a {
    padding: 9px 15px;
  }
  .profile-intro-card .card-body .user-info {
    padding-bottom: 15px;
    margin-bottom: 15px;
  }
  .profile-intro-card .card-body .content {
    padding-right: 35px;
    margin-bottom: 15px;
  }
  .profile-intro-card .card-body .info-card {
    margin-top: -10px;
  }
}
.courses-list-card .card-head .card-buttons .card-btn.active {
  background-color: var(--splash-primary-color) !important;
  color: var(--splash-white-color);
}

.courses-sidebar-filter .title {
  border-bottom: 1px dashed #d9e9ef;
  margin-bottom: 25px;
  padding: 25px 30px;
}
.courses-sidebar-filter .sidebar-item {
  padding-left: 30px;
  padding-right: 30px;
  padding-bottom: 50px;
}
.courses-sidebar-filter .sidebar-item h6 {
  margin-bottom: 20px;
}
.courses-sidebar-filter .sidebar-item .search-box .form-control {
  background: #F5F4FA;
  padding-top: 14px;
  padding-bottom: 14px;
}
.courses-sidebar-filter .sidebar-item .search-box button {
  top: 50%;
  right: 15px;
  line-height: 1;
  margin-top: 1px;
  font-size: 17px;
  position: absolute;
  transform: translateY(-50%);
}
.courses-sidebar-filter .sidebar-item .previous-searches-list {
  margin-bottom: -10px;
}
.courses-sidebar-filter .sidebar-item .previous-searches-list .item {
  padding: 4px 22px 4px 8px;
  background: #F7F7F9;
  border-radius: 2px;
  margin-right: 10px;
  margin-bottom: 10px;
}
.courses-sidebar-filter .sidebar-item .previous-searches-list .item button {
  top: 50%;
  right: 8px;
  line-height: 1;
  color: #8E8DA2;
  font-size: 10px;
  margin-top: 1px;
  position: absolute;
  transform: translateY(-50%);
}
.courses-sidebar-filter .sidebar-item .previous-searches-list .item button:hover {
  color: red;
}
.courses-sidebar-filter .sidebar-item .clear-btn {
  line-height: 1.2;
}
.courses-sidebar-filter .sidebar-item .clear-btn::before {
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  content: "";
  position: absolute;
  transition: var(--transition);
  background: var(--splash-black-color);
}
.courses-sidebar-filter .sidebar-item .clear-btn:hover::before {
  transform: scaleX(0);
}
.courses-sidebar-filter .sidebar-item .categories-list li {
  border-bottom: 1px solid #EEEDF2;
  padding-top: 10px;
  padding-bottom: 10px;
}
.courses-sidebar-filter .sidebar-item .categories-list li:first-child {
  padding-top: 0;
}
.courses-sidebar-filter .sidebar-item .proficiency-list li {
  border-bottom: 1px solid #EEEDF2;
  padding-top: 10px;
  padding-bottom: 10px;
}
.courses-sidebar-filter .sidebar-item .proficiency-list li:first-child {
  padding-top: 0;
}
.courses-sidebar-filter .sidebar-item .see-more-btn {
  line-height: 1.25;
}
.courses-sidebar-filter .sidebar-item .see-more-btn::before {
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  content: "";
  position: absolute;
  transition: var(--transition);
  background: var(--splash-primary-color);
}
.courses-sidebar-filter .sidebar-item .see-more-btn:hover::before {
  transform: scaleX(0);
}
.courses-sidebar-filter .sidebar-item .ratings-list li {
  border-bottom: 1px solid #EEEDF2;
  padding-top: 10px;
  padding-bottom: 10px;
}
.courses-sidebar-filter .sidebar-item .ratings-list li span i {
  top: 1px;
  line-height: 1;
  color: #F3C44C;
  margin-right: 4px;
  position: relative;
}
.courses-sidebar-filter .sidebar-item .ratings-list li:first-child {
  padding-top: 0;
}
.courses-sidebar-filter .sidebar-item .pricing-filter .range-slider {
  position: relative;
  height: 5px;
}
.courses-sidebar-filter .sidebar-item .pricing-filter .range-slider input[type=range] {
  -webkit-appearance: none;
  background: transparent;
  border-radius: 5px;
  position: absolute;
  width: 100%;
  left: 0;
  top: 0;
}
.courses-sidebar-filter .sidebar-item .pricing-filter .range-slider input[type=range]::-webkit-slider-thumb {
  background: var(--splash-primary-color);
  -webkit-appearance: none;
  position: relative;
  border-radius: 50%;
  margin-top: -5px;
  cursor: pointer;
  height: 14px;
  width: 14px;
  z-index: 1;
}
.courses-sidebar-filter .sidebar-item .pricing-filter .range-slider input[type=range]::-webkit-slider-runnable-track {
  width: 100%;
  height: 5px;
  border: none;
  border-radius: 5px;
  background: #f4f4f4;
}
.courses-sidebar-filter .sidebar-item .pricing-filter .price-content {
  margin-top: 12px;
}

.single-course-card .card-body .image .fav {
  color: #EF2929;
  font-size: 18px;
  height: 38px;
  width: 38px;
  right: 15px;
  top: 15px;
}
.single-course-card .card-body .image .fav i {
  left: 0;
  right: 0;
  top: 50%;
  line-height: 1;
  margin-top: 1px;
  position: absolute;
  transform: translateY(-50%);
}
.single-course-card .card-body .content h4 {
  line-height: 1.5;
}
.single-course-card .card-body .content .reviews .rating {
  color: #F3C44C;
}
.single-course-card .card-body .content .reviews .rating i {
  margin-right: 4px;
}
.single-course-card .card-body .content .info li {
  padding-left: 20px;
  margin-right: 20px;
  margin-top: 10px;
}
.single-course-card .card-body .content .info li i {
  color: var(--splash-primary-color);
  transform: translateY(-50%);
  position: absolute;
  line-height: 1;
  top: 50%;
  left: 0;
}
.single-course-card .card-body .content .info li:last-child {
  margin-right: 0;
}
.single-course-card .card-body .content .add-to-cart-btn {
  background-color: transparent;
  padding: 11px 35px 11px 20px;
  border: 1px solid #EDEBF3;
}
.single-course-card .card-body .content .add-to-cart-btn i {
  top: 50%;
  line-height: 1;
  font-size: 16px;
  position: absolute;
  transform: translateY(-50%);
  color: var(--splash-primary-color);
  margin-left: 7px;
}
.single-course-card .card-body .content .add-to-cart-btn:hover {
  background-color: var(--splash-primary-color);
  border-color: var(--splash-primary-color);
  color: var(--splash-white-color);
}
.single-course-card .card-body .content .add-to-cart-btn:hover i {
  color: var(--splash-white-color);
}

.dark .courses-sidebar-filter .title {
  border-bottom-color: #45445e;
}
.dark .courses-sidebar-filter .sidebar-item .search-box .form-control {
  background: var(--splash-black-color);
}
.dark .courses-sidebar-filter .sidebar-item .previous-searches-list .item {
  background: var(--splash-black-color);
}
.dark .courses-sidebar-filter .sidebar-item .previous-searches-list .item button {
  color: #BCBBC7;
}
.dark .courses-sidebar-filter .sidebar-item .clear-btn::before {
  background: var(--splash-white-color);
}
.dark .courses-sidebar-filter .sidebar-item .categories-list li {
  border-bottom-color: #45445e;
}
.dark .courses-sidebar-filter .sidebar-item .proficiency-list li {
  border-bottom-color: #45445e;
}
.dark .courses-sidebar-filter .sidebar-item .ratings-list li {
  border-bottom-color: #45445e;
}
.dark .courses-sidebar-filter .sidebar-item .pricing-filter .range-slider input[type=range]::-webkit-slider-runnable-track {
  background: var(--splash-black-color);
}
.dark .single-course-card .card-body .content .add-to-cart-btn {
  border-color: #45445e;
}
.dark .single-course-card .card-body .content .add-to-cart-btn:hover {
  border-color: var(--splash-primary-color);
}

@media only screen and (max-width: 767px) {
  .courses-sidebar-filter .title {
    margin-bottom: 20px;
    padding: 15px;
  }
  .courses-sidebar-filter .sidebar-item {
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 20px;
  }
  .courses-sidebar-filter .sidebar-item h6 {
    margin-bottom: 15px;
  }
  .courses-sidebar-filter .sidebar-item .search-box .form-control {
    padding-top: 13px;
    padding-bottom: 13px;
  }
  .courses-sidebar-filter .sidebar-item .search-box button {
    font-size: 15px;
  }
  .courses-sidebar-filter .sidebar-item .previous-searches-list {
    margin-bottom: -5px;
  }
  .courses-sidebar-filter .sidebar-item .previous-searches-list .item {
    margin-right: 5px;
    margin-bottom: 5px;
  }
  .courses-sidebar-filter .sidebar-item .categories-list li {
    padding-top: 9px;
    padding-bottom: 9px;
  }
  .courses-sidebar-filter .sidebar-item .proficiency-list li {
    padding-top: 9px;
    padding-bottom: 9px;
  }
  .courses-sidebar-filter .sidebar-item .ratings-list li {
    padding-top: 9px;
    padding-bottom: 9px;
  }
  .single-course-card .card-body .image .fav {
    font-size: 16px;
    height: 35px;
    width: 35px;
  }
  .single-course-card .card-body .content .info li {
    margin-right: 10px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .courses-sidebar-filter .title {
    padding: 20px 25px;
  }
  .courses-sidebar-filter .sidebar-item {
    padding-left: 25px;
    padding-right: 25px;
    padding-bottom: 25px;
  }
  .courses-sidebar-filter .sidebar-item h6 {
    margin-bottom: 15px;
  }
  .single-course-card .card-body .content .info li {
    margin-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .courses-sidebar-filter .title {
    padding: 25px;
  }
  .courses-sidebar-filter .sidebar-item {
    padding-left: 25px;
    padding-right: 25px;
    padding-bottom: 25px;
  }
  .courses-sidebar-filter .sidebar-item h6 {
    margin-bottom: 15px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .courses-sidebar-filter .title {
    padding: 25px;
  }
  .courses-sidebar-filter .sidebar-item {
    padding-left: 25px;
    padding-right: 25px;
    padding-bottom: 25px;
  }
  .courses-sidebar-filter .sidebar-item h6 {
    margin-bottom: 15px;
  }
}
.course-details-card .card-body .bg-gradient {
  background: linear-gradient(92.21deg, #3E39D9 1.38%, #6560F0 100%) !important;
}
.course-details-card .card-body .course-details-header {
  max-width: 600px;
}
.course-details-card .card-body .course-details-header h2 {
  font-size: 30px;
  line-height: 1.4;
}
.course-details-card .card-body .course-details-header p {
  margin-bottom: 10px;
}
.course-details-card .card-body .course-details-header p, .course-details-card .card-body .course-details-header span {
  color: #CFCFD8;
}
.course-details-card .card-body .course-details-header .info .reviews .rating {
  color: #F3C44C;
}
.course-details-card .card-body .course-details-header .info .reviews .rating i {
  font-size: 13px;
  margin-right: 4px;
}
.course-details-card .card-body .course-details-header .info img {
  border: 1px solid var(--splash-white-color);
}
.course-details-card .card-body .course-details-header .buttons-list button {
  border: none;
  padding: 13px 50px;
  background-color: #6560F0;
  color: var(--splash-white-color);
}
.course-details-card .card-body .course-details-header .buttons-list button i {
  transform: translateY(-50%);
  position: absolute;
  margin-top: 1px;
  line-height: 1;
  right: 30px;
  top: 50%;
}
.course-details-card .card-body .course-details-header .buttons-list button:hover {
  background-color: var(--splash-white-color);
  color: var(--splash-black-color);
}
.course-details-card .card-body .course-details-header .buttons-list button.with-icon {
  margin-right: 10px;
  padding: 12px 55px 12px 30px;
  background-color: transparent;
  border: 1px solid var(--splash-white-color);
}
.course-details-card .card-body .course-details-header .buttons-list button.with-icon:hover {
  background-color: var(--splash-white-color);
  color: var(--splash-black-color);
}
.course-details-card .card-body p {
  margin-bottom: 15px;
}
.course-details-card .card-body p:last-child {
  margin-bottom: 0;
}
.course-details-card .card-body .course-info {
  padding-top: 30px;
  padding-left: 40px;
  padding-right: 40px;
  padding-bottom: 5px;
}
.course-details-card .card-body .course-info .info-card {
  margin-bottom: 25px;
  padding-top: 8px;
  padding-left: 78px;
  padding-bottom: 8px;
}
.course-details-card .card-body .course-info .info-card .icon {
  top: 50%;
  width: 65px;
  height: 65px;
  font-size: 28px;
  transform: translateY(-50%);
}
.course-details-card .card-body .course-info .info-card .icon i {
  left: 0;
  top: 50%;
  right: 0;
  line-height: 1;
  margin-top: 1px;
  position: absolute;
  transform: translateY(-50%);
}
.course-details-card .card-body .accordion .accordion-item {
  margin-bottom: 10px;
}
.course-details-card .card-body .accordion .accordion-item .accordion-button {
  padding: 15px 50px 15px 15px;
  box-shadow: 0px 4px 34px rgba(101, 96, 240, 0.1);
}
.course-details-card .card-body .accordion .accordion-item .accordion-button .number {
  width: 30px;
  height: 30px;
  line-height: 30px;
  margin-right: 20px;
}
.course-details-card .card-body .accordion .accordion-item .accordion-button ul li {
  margin-right: 15px;
  padding-left: 20px;
}
.course-details-card .card-body .accordion .accordion-item .accordion-button ul li i {
  left: 0;
  top: 50%;
  line-height: 1;
  margin-top: 0.5px;
  position: absolute;
  transform: translateY(-50%);
}
.course-details-card .card-body .accordion .accordion-item .accordion-button ul li:last-child {
  margin-right: 0;
}
.course-details-card .card-body .accordion .accordion-item .accordion-button::after {
  top: 50%;
  right: 15px;
  position: absolute;
  transform: translateY(-50%) rotate(180deg);
}
.course-details-card .card-body .accordion .accordion-item .accordion-button.collapsed::after {
  transform: translateY(-50%);
}
.course-details-card .card-body .accordion .accordion-item .accordion-body {
  padding: 0 0 15px;
}
.course-details-card .card-body .accordion .accordion-item .accordion-body ul li {
  border-bottom: 1px dashed #D2CFE4;
  padding: 20px;
}
.course-details-card .card-body .accordion .accordion-item .accordion-body ul li a {
  padding-left: 28px;
}
.course-details-card .card-body .accordion .accordion-item .accordion-body ul li a i {
  left: 0;
  top: 50%;
  line-height: 1;
  position: absolute;
  transform: translateY(-50%);
}
.course-details-card .card-body .accordion .accordion-item:last-child {
  margin-bottom: 0;
}
.course-details-card .card-body .what-you-will-learn li {
  margin-bottom: 14px;
  padding-left: 30px;
}
.course-details-card .card-body .what-you-will-learn li i {
  left: 0;
  top: 2px;
  line-height: 1;
  font-size: 18px;
  position: absolute;
  color: var(--splash-primary-color);
}
.course-details-card .card-body .what-you-will-learn li:last-child {
  margin-bottom: 0;
}
.course-details-card .card-body .students-feedback-list li {
  padding-left: 55px;
  border-bottom: 1px dashed #D2CFE4;
  padding-top: 25px;
  padding-bottom: 25px;
}
.course-details-card .card-body .students-feedback-list li img {
  left: 0;
  top: 30px;
  position: absolute;
}
.course-details-card .card-body .students-feedback-list li .reviews .rating {
  color: #F3C44C;
}
.course-details-card .card-body .students-feedback-list li .reviews .rating i {
  font-size: 13px;
}
.course-details-card .card-body .students-feedback-list li:first-child {
  padding-top: 0;
}
.course-details-card .card-body .students-feedback-list li:first-child img {
  top: 5px;
}
.course-details-card .card-body .students-feedback-list li:last-child {
  border-bottom: none;
  padding-bottom: 0;
}
.course-details-card .card-body .show-more-btn {
  border: 1px solid #D2CFE4;
  background-color: transparent;
  color: var(--splash-black-color);
}
.course-details-card .card-body .show-more-btn:hover {
  color: var(--splash-white-color);
  border-color: var(--splash-primary-color);
  background-color: var(--splash-primary-color);
}
.course-details-card .card-body .about-the-author .reviews .rating {
  color: #F3C44C;
}
.course-details-card .card-body .about-the-author .reviews .rating i {
  font-size: 13px;
}
.course-details-card .card-body.lessons-preview-info h2 {
  font-size: 30px;
  line-height: 1.4;
}
.course-details-card .card-body.lessons-preview-info .image .video-btn {
  top: 50%;
  left: 50%;
  width: 90px;
  height: 90px;
  font-size: 25px;
  position: absolute;
  color: var(--splash-primary-color);
  transform: translateX(-50%) translateY(-50%);
}
.course-details-card .card-body.lessons-preview-info .image .video-btn i {
  left: 0;
  top: 50%;
  right: 0;
  line-height: 1;
  margin-top: 1px;
  position: absolute;
  transform: translateY(-50%);
}
.course-details-card .card-body.lessons-preview-info .image .video-btn:hover {
  background-color: var(--splash-primary-color);
  color: var(--splash-white-color);
}
.course-details-card .card-body.lessons-preview-info .course-info {
  padding-left: 30px;
  padding-right: 30px;
}
.course-details-card .card-body.lessons-preview-info .course-info .info-card {
  padding-top: 7px;
  padding-left: 72px;
  padding-bottom: 7px;
}
.course-details-card .card-body.lessons-preview-info .course-info .info-card .icon {
  width: 60px;
  height: 60px;
  font-size: 25px;
}
.course-details-card .course-details-info {
  margin-top: -223px;
}
.course-details-card .course-details-info ul li {
  padding-left: 25px;
  margin-bottom: 8px;
}
.course-details-card .course-details-info ul li i {
  color: var(--splash-primary-color);
  transform: translateY(-50%);
  position: absolute;
  line-height: 1;
  top: 50%;
  left: 0;
}
.course-details-card .course-details-info ul li:last-child {
  margin-bottom: 0;
}

.videoModal .modal-content .btn-close {
  background: unset !important;
  position: absolute;
  font-size: 22px;
  height: auto;
  right: -40px;
  width: auto;
  top: -40px;
  opacity: 1;
}
.videoModal .modal-content .modal-body iframe {
  width: 100%;
  height: 408px;
}

.dark .course-details-card .card-body .accordion .accordion-item .accordion-button {
  box-shadow: unset;
  border-bottom: 1px dashed #45445e !important;
}
.dark .course-details-card .card-body .accordion .accordion-item .accordion-button.bg-white {
  background-color: var(--splash-black-color) !important;
}
.dark .course-details-card .card-body .accordion .accordion-item .accordion-body ul li {
  border-bottom-color: #45445e;
}
.dark .course-details-card .card-body .students-feedback-list li {
  border-bottom-color: #45445e;
}
.dark .course-details-card .card-body .show-more-btn {
  border-color: #45445e;
  color: var(--splash-white-color);
}
.dark .course-details-card .card-body .show-more-btn:hover {
  border-color: var(--splash-primary-color);
}

@media only screen and (max-width: 767px) {
  .course-details-card .card-body .course-details-header {
    max-width: 100%;
  }
  .course-details-card .card-body .course-details-header h2 {
    font-size: 18px;
  }
  .course-details-card .card-body .course-details-header .buttons-list button {
    margin-top: 12px;
    padding: 13px 42px;
  }
  .course-details-card .card-body .course-details-header .buttons-list button i {
    right: 25px;
  }
  .course-details-card .card-body .course-details-header .buttons-list button.with-icon {
    padding: 12px 50px 12px 25px;
  }
  .course-details-card .card-body .course-info {
    padding-top: 20px;
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 5px;
  }
  .course-details-card .card-body .course-info .info-card {
    margin-bottom: 15px;
    padding-left: 75px;
  }
  .course-details-card .card-body .course-info .info-card .icon {
    width: 60px;
    height: 60px;
    font-size: 25px;
  }
  .course-details-card .card-body .accordion .accordion-item .accordion-button {
    padding-right: 30px;
  }
  .course-details-card .card-body .accordion .accordion-item .accordion-button .number {
    width: 25px;
    height: 25px;
    line-height: 25px;
    margin-right: 10px;
  }
  .course-details-card .card-body .accordion .accordion-item .accordion-body ul li {
    padding: 15px;
  }
  .course-details-card .card-body .accordion .accordion-item .accordion-body ul li a {
    padding-left: 25px;
  }
  .course-details-card .card-body .what-you-will-learn li {
    margin-bottom: 12px;
    padding-left: 25px;
  }
  .course-details-card .card-body .what-you-will-learn li i {
    font-size: 16px;
  }
  .course-details-card .card-body br {
    display: none;
  }
  .course-details-card .card-body .students-feedback-list li {
    padding-left: 0;
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .course-details-card .card-body .students-feedback-list li img {
    top: 0;
    position: relative;
    margin-bottom: 12px;
  }
  .course-details-card .card-body.lessons-preview-info h2 {
    font-size: 20px;
  }
  .course-details-card .card-body.lessons-preview-info .image .video-btn {
    width: 60px;
    height: 60px;
    font-size: 22px;
  }
  .course-details-card .card-body.lessons-preview-info .course-info {
    padding-left: 15px;
    padding-right: 15px;
  }
  .course-details-card .card-body.lessons-preview-info .course-info .info-card {
    padding-left: 75px;
  }
  .course-details-card .card-body.lessons-preview-info .course-info .info-card .icon {
    width: 60px;
    height: 60px;
    font-size: 25px;
  }
  .course-details-card .course-details-info {
    margin-top: 0;
  }
  .videoModal .modal-content .btn-close {
    top: -40px;
    right: -5px;
    font-size: 20px;
  }
  .videoModal .modal-content .modal-body iframe {
    height: 154px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .videoModal .modal-content .modal-body iframe {
    height: 282px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .course-details-card .card-body .course-details-header {
    max-width: 100%;
  }
  .course-details-card .card-body .course-details-header h2 {
    font-size: 25px;
  }
  .course-details-card .card-body .course-details-header .buttons-list button {
    padding: 12px 50px;
  }
  .course-details-card .card-body .course-details-header .buttons-list button.with-icon {
    padding: 11px 55px 11px 30px;
  }
  .course-details-card .card-body .course-info {
    padding-top: 25px;
    padding-left: 25px;
    padding-right: 25px;
    padding-bottom: 5px;
  }
  .course-details-card .card-body .course-info .info-card {
    margin-bottom: 20px;
    padding-left: 75px;
  }
  .course-details-card .card-body .course-info .info-card .icon {
    width: 60px;
    height: 60px;
    font-size: 25px;
  }
  .course-details-card .card-body .students-feedback-list li {
    padding-top: 20px;
    padding-bottom: 20px;
  }
  .course-details-card .card-body.lessons-preview-info h2 {
    font-size: 24px;
  }
  .course-details-card .card-body.lessons-preview-info .image .video-btn {
    width: 75px;
    height: 75px;
    font-size: 25px;
  }
  .course-details-card .card-body.lessons-preview-info .course-info {
    padding-left: 25px;
    padding-right: 25px;
  }
  .course-details-card .card-body.lessons-preview-info .course-info .info-card {
    padding-left: 75px;
  }
  .course-details-card .card-body.lessons-preview-info .course-info .info-card .icon {
    width: 60px;
    height: 60px;
    font-size: 25px;
  }
  .course-details-card .course-details-info {
    margin-top: 0;
  }
  .videoModal .modal-dialog {
    max-width: 750px;
  }
  .videoModal .modal-content .btn-close {
    right: -5px;
    top: -40px;
  }
  .videoModal .modal-content .modal-body iframe {
    height: 390px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .course-details-card .card-body .course-details-header {
    max-width: 100%;
  }
  .course-details-card .card-body .course-details-header h2 {
    font-size: 28px;
  }
  .course-details-card .card-body .course-info {
    padding-left: 20px;
    padding-right: 20px;
  }
  .course-details-card .card-body .course-info .info-card {
    padding-left: 75px;
  }
  .course-details-card .card-body .course-info .info-card .icon {
    width: 60px;
    height: 60px;
    font-size: 25px;
  }
  .course-details-card .card-body.lessons-preview-info h2 {
    font-size: 28px;
  }
  .course-details-card .card-body.lessons-preview-info .course-info {
    padding-left: 20px;
    padding-right: 20px;
  }
  .course-details-card .card-body.lessons-preview-info .course-info .info-card {
    padding-left: 75px;
  }
  .course-details-card .card-body.lessons-preview-info .course-info .info-card .icon {
    width: 60px;
    height: 60px;
    font-size: 25px;
  }
  .course-details-card .course-details-info {
    margin-top: 0;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .course-details-card .card-body .course-info {
    padding-left: 25px;
    padding-right: 25px;
  }
  .course-details-card .card-body .course-info .info-card {
    padding-top: 3px;
    padding-left: 58px;
    padding-bottom: 3px;
  }
  .course-details-card .card-body .course-info .info-card .icon {
    width: 50px;
    height: 50px;
    font-size: 25px;
  }
  .course-details-card .course-details-info {
    margin-top: 0;
  }
}
@media only screen and (min-width: 1600px) {
  .course-details-card .course-details-info {
    margin-top: -220px;
  }
}
.edit-course-card .input-group .input-group-text {
  padding: 10px 18px;
  border-right: none;
  background: #F5F4FA;
  border-color: #dedee4;
}
.edit-course-card .input-group .form-control {
  border-left: none;
}
.edit-course-card .file-upload {
  border: 1px solid #dedee4;
  padding: 55px 15px;
}
.edit-course-card .file-upload i {
  line-height: 1;
  font-size: 35px;
  margin-bottom: 5px;
  color: var(--splash-primary-color);
}
.edit-course-card .file-upload span span::before {
  left: 0;
  right: 0;
  height: 1px;
  content: "";
  bottom: -2px;
  position: absolute;
  background: var(--splash-black-color);
}
.edit-course-card .file-upload input {
  cursor: pointer;
}
.edit-course-card .members-list div {
  margin-top: 10px;
  margin-right: 5px;
}
.edit-course-card .members-list button {
  font-size: 8px;
  margin-left: 3px;
}
.edit-course-card .members-list button:hover {
  color: red;
}
.edit-course-card button span {
  line-height: 1.3;
}
.edit-course-card button span::before {
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  content: "";
  position: absolute;
  transition: var(--transition);
  background: var(--splash-danger-color);
}
.edit-course-card .ql-container {
  min-height: 150px;
  height: auto;
}
.edit-course-card .accordion .accordion-item {
  margin-bottom: 10px;
}
.edit-course-card .accordion .accordion-item .accordion-button {
  padding: 15px 50px 15px 15px;
  box-shadow: 0px 4px 34px rgba(101, 96, 240, 0.1);
}
.edit-course-card .accordion .accordion-item .accordion-button .number {
  width: 30px;
  height: 30px;
  line-height: 30px;
  margin-right: 20px;
}
.edit-course-card .accordion .accordion-item .accordion-button ul li {
  margin-right: 15px;
  padding-left: 20px;
}
.edit-course-card .accordion .accordion-item .accordion-button ul li i {
  left: 0;
  top: 50%;
  line-height: 1;
  margin-top: 0.5px;
  position: absolute;
  transform: translateY(-50%);
}
.edit-course-card .accordion .accordion-item .accordion-button ul li:last-child {
  margin-right: 0;
}
.edit-course-card .accordion .accordion-item .accordion-button::after {
  top: 50%;
  right: 15px;
  position: absolute;
  transform: translateY(-50%) rotate(180deg);
}
.edit-course-card .accordion .accordion-item .accordion-button.collapsed::after {
  transform: translateY(-50%);
}
.edit-course-card .accordion .accordion-item .accordion-body {
  padding: 0 0 15px;
}
.edit-course-card .accordion .accordion-item .accordion-body ul li {
  border-bottom: 1px dashed #D2CFE4;
  padding: 20px;
}
.edit-course-card .accordion .accordion-item .accordion-body ul li a {
  padding-left: 28px;
}
.edit-course-card .accordion .accordion-item .accordion-body ul li a i {
  left: 0;
  top: 50%;
  line-height: 1;
  position: absolute;
  transform: translateY(-50%);
}
.edit-course-card .accordion .accordion-item:last-child {
  margin-bottom: 0;
}

.dark .edit-course-card .input-group .input-group-text {
  background: var(--splash-black-color);
  border-color: #45445e;
}
.dark .edit-course-card .file-upload {
  border-color: #45445e;
}
.dark .edit-course-card .file-upload span span::before {
  background: var(--splash-white-color);
}
.dark .edit-course-card .accordion .accordion-item .accordion-button {
  box-shadow: unset;
  border-bottom: 1px dashed #45445e !important;
}
.dark .edit-course-card .accordion .accordion-item .accordion-button.bg-white {
  background-color: var(--splash-black-color) !important;
}
.dark .edit-course-card .accordion .accordion-item .accordion-body ul li {
  border-bottom-color: #45445e;
}

@media only screen and (max-width: 767px) {
  .edit-course-card .accordion .accordion-item .accordion-button {
    padding-right: 30px;
  }
  .edit-course-card .accordion .accordion-item .accordion-button .number {
    width: 25px;
    height: 25px;
    line-height: 25px;
    margin-right: 10px;
  }
  .edit-course-card .accordion .accordion-item .accordion-body ul li {
    padding: 15px;
  }
  .edit-course-card .accordion .accordion-item .accordion-body ul li a {
    padding-left: 25px;
  }
}
.search-result-card .card-body .list li {
  border-top: 1px dashed #d9e9ef;
  padding-top: 16px;
  padding-bottom: 16px;
}
.search-result-card .card-body .list li:last-child {
  border-bottom: 1px dashed #d9e9ef;
}

.dark .search-result-card .card-body .list li {
  border-top-color: #45445e;
}
.dark .search-result-card .card-body .list li:last-child {
  border-bottom-color: #45445e;
}

iframe {
  width: 100%;
  border: none;
  height: 400px;
}

#leafletjs-map-panes, #leafletjs-map-choropleth, #leafletjs-map-layers {
  height: 500px;
}

.leaflet-pane {
  z-index: 1;
}

.terms-conditions-card p {
  margin-bottom: 15px;
}
.terms-conditions-card p strong {
  color: var(--splash-black-color);
}
.terms-conditions-card p:last-child {
  margin-bottom: 0;
}
.terms-conditions-card .h1:not(:first-child), .terms-conditions-card .h2:not(:first-child), .terms-conditions-card .h3:not(:first-child), .terms-conditions-card .h4:not(:first-child), .terms-conditions-card .h5:not(:first-child), .terms-conditions-card .h6:not(:first-child), .terms-conditions-card h1:not(:first-child), .terms-conditions-card h2:not(:first-child), .terms-conditions-card h3:not(:first-child), .terms-conditions-card h4:not(:first-child), .terms-conditions-card h5:not(:first-child), .terms-conditions-card h6:not(:first-child) {
  margin-top: 25px;
}
.terms-conditions-card ul li {
  margin-bottom: 12px;
}
.terms-conditions-card ul li strong {
  color: var(--splash-black-color);
}
.terms-conditions-card ul li:last-child {
  margin-bottom: 0;
}

.dark .terms-conditions-card p strong {
  color: var(--splash-white-color);
}
.dark .terms-conditions-card ul li strong {
  color: var(--splash-white-color);
}

.privacy-policy-card p {
  margin-bottom: 15px;
}
.privacy-policy-card p strong {
  color: var(--splash-black-color);
}
.privacy-policy-card p:last-child {
  margin-bottom: 0;
}
.privacy-policy-card .h1:not(:first-child), .privacy-policy-card .h2:not(:first-child), .privacy-policy-card .h3:not(:first-child), .privacy-policy-card .h4:not(:first-child), .privacy-policy-card .h5:not(:first-child), .privacy-policy-card .h6:not(:first-child), .privacy-policy-card h1:not(:first-child), .privacy-policy-card h2:not(:first-child), .privacy-policy-card h3:not(:first-child), .privacy-policy-card h4:not(:first-child), .privacy-policy-card h5:not(:first-child), .privacy-policy-card h6:not(:first-child) {
  margin-top: 25px;
}
.privacy-policy-card ul li {
  margin-bottom: 12px;
}
.privacy-policy-card ul li strong {
  color: var(--splash-black-color);
}
.privacy-policy-card ul li:last-child {
  margin-bottom: 0;
}

.dark .privacy-policy-card p strong {
  color: var(--splash-white-color);
}
.dark .privacy-policy-card ul li strong {
  color: var(--splash-white-color);
}

.footer-area {
  margin-left: -25px;
  margin-right: -25px;
}
.footer-area .footer-menu li {
  margin-left: 11px;
  margin-right: 11px;
}
.footer-area .footer-menu li a:hover {
  color: var(--splash-primary-color) !important;
}

@media only screen and (max-width: 767px) {
  .footer-area {
    margin-left: -15px;
    margin-right: -15px;
  }
  .footer-area .footer-menu {
    margin-top: 8px;
  }
  .footer-area .footer-menu li {
    margin-left: 7px;
    margin-right: 7px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-area {
    margin-left: -25px;
    margin-right: -25px;
  }
  .footer-area .footer-menu li {
    margin-left: 9px;
    margin-right: 9px;
  }
}
@media only screen and (min-width: 1600px) {
  .footer-area {
    margin-left: -40px;
    margin-right: -40px;
  }
}
@media only screen and (max-width: 767px) {
  .main-content {
    padding-top: 160px;
    padding-left: 15px;
    padding-right: 15px;
  }
  .sidebar-hide .main-content {
    padding-left: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .main-content {
    padding-top: 125px;
    padding-left: 25px;
    padding-right: 25px;
  }
  .sidebar-hide .main-content {
    padding-left: 25px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .main-content {
    padding-top: 125px;
    padding-left: 25px;
    padding-right: 25px;
  }
  .sidebar-hide .main-content {
    padding-left: 25px;
  }
}
@media only screen and (min-width: 1600px) {
  .main-content {
    padding-left: 320px;
    padding-right: 40px;
  }
  .sidebar-hide .main-content {
    padding-left: 100px;
  }
}

/*# sourceMappingURL=style.css.map */
