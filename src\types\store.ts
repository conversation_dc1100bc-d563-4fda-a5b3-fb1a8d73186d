// 用于在店铺列表展示简略信息
export interface storeDetail {
    updateBy: string,
    updateTime: string, // 数据更新日期
    createBy: string,
    createTime: string,
    status: string, // 店铺状态：根据测试结果，'0'表示可用门店，'1'表示营业中（但实际数据中都是'0'）

    storeId:number, // 内部用于索引 接收时产生
    storeName: string, // 店铺名字
    storeType: string, // 门店类型：'2'表示标准店
    storeAddress: string, // 店铺地址
    storeDescription: string,

    businessHours: string, // 营业时间
    contactPhone: string, // 联系电话

    latitude: string,
    longitude: string,

    // params:
}

// 门店状态枚举（根据实际测试结果）
export enum StoreStatus {
    AVAILABLE = '0',  // 可用门店（实际数据中的状态）
    ACTIVE = '1'      // 营业中（理论状态，但实际数据中未使用）
}

// 门店类型枚举
export enum StoreType {
    FLAGSHIP = '1',   // 旗舰店
    STANDARD = '2',   // 标准店
    COMMUNITY = '3'   // 社区店
}
export type storeList = Array<storeDetail>;