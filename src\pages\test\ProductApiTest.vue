<template>
  <div class="product-api-test p-4">
    <div class="container-fluid">
      <h2 class="mb-4">饮品商品管理 API 测试界面</h2>
      
      <!-- 测试控制面板 -->
      <div class="row mb-4">
        <div class="col-md-12">
          <div class="card">
            <div class="card-header">
              <h5>测试控制面板</h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-3">
                  <label class="form-label">门店ID</label>
                  <input v-model.number="testParams.storeId" type="number" class="form-control" placeholder="输入门店ID">
                </div>
                <div class="col-md-3">
                  <label class="form-label">商品ID</label>
                  <input v-model.number="testParams.drinkId" type="number" class="form-control" placeholder="输入商品ID">
                </div>
                <div class="col-md-3">
                  <label class="form-label">原型ID</label>
                  <input v-model.number="testParams.prototypeId" type="number" class="form-control" placeholder="输入原型ID">
                </div>
                <div class="col-md-3">
                  <label class="form-label">页码/页大小</label>
                  <div class="d-flex gap-2">
                    <input v-model.number="testParams.pageNum" type="number" class="form-control" placeholder="页码" min="1">
                    <input v-model.number="testParams.pageSize" type="number" class="form-control" placeholder="页大小" min="1">
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- API测试按钮组 -->
      <div class="row mb-4">
        <div class="col-md-12">
          <div class="card">
            <div class="card-header">
              <h5>API接口测试</h5>
            </div>
            <div class="card-body">
              <div class="row g-3">
                <div class="col-md-4">
                  <button @click="testGetProductList" class="btn btn-primary w-100" :disabled="loading">
                    <i class="flaticon-list me-2"></i>
                    获取商品列表
                  </button>
                </div>
                <div class="col-md-4">
                  <button @click="testGetProductDetail" class="btn btn-info w-100" :disabled="loading || !testParams.drinkId">
                    <i class="flaticon-search me-2"></i>
                    获取商品详情
                  </button>
                </div>
                <div class="col-md-4">
                  <button @click="testGetStoreMenu" class="btn btn-success w-100" :disabled="loading || !testParams.storeId">
                    <i class="flaticon-store me-2"></i>
                    获取门店菜单
                  </button>
                </div>
                <div class="col-md-4">
                  <button @click="testGetProductsByPrototype" class="btn btn-warning w-100" :disabled="loading || !testParams.prototypeId">
                    <i class="flaticon-prototype me-2"></i>
                    根据原型查询商品
                  </button>
                </div>
                <div class="col-md-4">
                  <button @click="testCreateProduct" class="btn btn-secondary w-100" :disabled="loading">
                    <i class="flaticon-plus me-2"></i>
                    测试创建商品
                  </button>
                </div>
                <div class="col-md-4">
                  <button @click="testUpdateProduct" class="btn btn-warning w-100" :disabled="loading || !testParams.drinkId">
                    <i class="flaticon-edit me-2"></i>
                    测试更新商品
                  </button>
                </div>
                <div class="col-md-4">
                  <button @click="testDeleteProduct" class="btn btn-danger w-100" :disabled="loading || !testParams.drinkId">
                    <i class="flaticon-delete me-2"></i>
                    测试删除商品
                  </button>
                </div>
                <div class="col-md-4">
                  <button @click="testExportProducts" class="btn btn-info w-100" :disabled="loading">
                    <i class="flaticon-export me-2"></i>
                    测试导出商品
                  </button>
                </div>
                <div class="col-md-4">
                  <button @click="testGetStoreList" class="btn btn-warning w-100" :disabled="loading">
                    <i class="flaticon-store me-2"></i>
                    测试门店列表
                  </button>
                </div>
                <div class="col-md-4">
                  <button @click="testGetStoreDetail" class="btn btn-info w-100" :disabled="loading || !testParams.storeId">
                    <i class="flaticon-info me-2"></i>
                    测试门店详情
                  </button>
                </div>
                <div class="col-md-4">
                  <button @click="testGetPrototypeDetail" class="btn btn-secondary w-100" :disabled="loading || !testParams.prototypeId">
                    <i class="flaticon-prototype me-2"></i>
                    测试原型详情
                  </button>
                </div>
                <div class="col-md-4">
                  <button @click="testBatchOperations" class="btn btn-dark w-100" :disabled="loading">
                    <i class="flaticon-batch me-2"></i>
                    测试批量操作
                  </button>
                </div>
                <div class="col-md-4">
                  <button @click="testAllApis" class="btn btn-success w-100" :disabled="loading">
                    <i class="flaticon-play me-2"></i>
                    测试所有接口
                  </button>
                </div>
                <div class="col-md-4">
                  <button @click="clearResults" class="btn btn-outline-danger w-100">
                    <i class="flaticon-clear me-2"></i>
                    清空结果
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="text-center mb-4">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">测试中...</span>
        </div>
        <p class="mt-2">正在测试API接口...</p>
      </div>

      <!-- 测试结果展示 -->
      <div class="row">
        <div class="col-md-12">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5>测试结果</h5>
              <div>
                <span class="badge bg-success me-2">成功: {{ successCount }}</span>
                <span class="badge bg-danger">失败: {{ errorCount }}</span>
              </div>
            </div>
            <div class="card-body">
              <div v-if="testResults.length === 0" class="text-center text-muted py-4">
                点击上方按钮开始测试API接口
              </div>
              
              <div v-for="(result, index) in testResults" :key="index" class="mb-4">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <h6 class="mb-0">
                    <span :class="result.success ? 'text-success' : 'text-danger'">
                      {{ result.success ? '✅' : '❌' }}
                    </span>
                    {{ result.title }}
                  </h6>
                  <small class="text-muted">{{ result.timestamp }}</small>
                </div>
                
                <div class="mb-2">
                  <strong>请求URL:</strong> 
                  <code class="text-primary">{{ result.method }} {{ result.url }}</code>
                </div>
                
                <div v-if="result.params" class="mb-2">
                  <strong>请求参数:</strong>
                  <pre class="bg-light p-2 rounded"><code>{{ JSON.stringify(result.params, null, 2) }}</code></pre>
                </div>
                
                <div class="mb-2">
                  <strong>响应状态:</strong>
                  <span :class="result.success ? 'badge bg-success' : 'badge bg-danger'">
                    {{ result.status }}
                  </span>
                  <span class="ms-2 text-muted">耗时: {{ result.duration }}ms</span>
                </div>
                
                <div class="mb-2">
                  <strong>响应数据:</strong>
                  <div class="position-relative">
                    <button @click="copyToClipboard(result.data)" class="btn btn-sm btn-outline-secondary position-absolute top-0 end-0 m-2">
                      复制
                    </button>
                    <pre class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;"><code>{{ JSON.stringify(result.data, null, 2) }}</code></pre>
                  </div>
                </div>
                
                <hr v-if="index < testResults.length - 1">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import serviceAxios from '@/utils/serviceAxios'

// 类型定义
interface TestResult {
  title: string
  method: string
  url: string
  params?: Record<string, unknown>
  success: boolean
  status: string
  data: unknown
  duration: number
  timestamp: string
}

interface TestParams {
  storeId: number
  drinkId: number
  prototypeId: number
  pageNum: number
  pageSize: number
}

interface ApiResponse {
  code?: number
  msg?: string
  data?: unknown
  rows?: unknown[]
  total?: number
}

interface RequestConfig {
  params?: Record<string, unknown>
  data?: Record<string, unknown>
}

// 测试参数
const testParams = reactive<TestParams>({
  storeId: 100,
  drinkId: 148,
  prototypeId: 100,
  pageNum: 1,
  pageSize: 10
})

// 状态管理
const loading = ref(false)
const testResults = ref<TestResult[]>([])
const successCount = ref(0)
const errorCount = ref(0)

// 执行API测试的通用方法
const executeApiTest = async (
  title: string,
  method: string,
  url: string,
  params?: Record<string, unknown>,
  data?: Record<string, unknown>
) => {
  const startTime = Date.now()
  loading.value = true

  try {
    let response: ApiResponse
    const config: RequestConfig = {}

    if (params) {
      config.params = params
    }

    if (data) {
      config.data = data
    }

    switch (method.toUpperCase()) {
      case 'GET':
        response = await serviceAxios.get(url, config)
        break
      case 'POST':
        response = await serviceAxios.post(url, data, config)
        break
      case 'PUT':
        response = await serviceAxios.put(url, data, config)
        break
      case 'DELETE':
        response = await serviceAxios.delete(url, config)
        break
      default:
        throw new Error(`不支持的HTTP方法: ${method}`)
    }

    const duration = Date.now() - startTime
    const result: TestResult = {
      title,
      method: method.toUpperCase(),
      url,
      params: params || data,
      success: true,
      status: '200 OK',
      data: response,
      duration,
      timestamp: new Date().toLocaleTimeString()
    }
    
    testResults.value.unshift(result)
    successCount.value++

  } catch (error: unknown) {
    const apiError = error as { response?: { status?: number; statusText?: string; data?: unknown }; message?: string }
    const duration = Date.now() - startTime
    const result: TestResult = {
      title,
      method: method.toUpperCase(),
      url,
      params: params || data,
      success: false,
      status: apiError.response?.status ? `${apiError.response.status} ${apiError.response.statusText}` : 'Network Error',
      data: apiError.response?.data || apiError.message || 'Unknown error',
      duration,
      timestamp: new Date().toLocaleTimeString()
    }
    
    testResults.value.unshift(result)
    errorCount.value++
  } finally {
    loading.value = false
  }
}

// 1. 获取商品列表
const testGetProductList = () => {
  executeApiTest(
    '获取商品列表',
    'GET',
    '/manager/product/list',
    {
      pageNum: testParams.pageNum,
      pageSize: testParams.pageSize,
      storeId: testParams.storeId || undefined
    }
  )
}

// 2. 获取商品详情
const testGetProductDetail = () => {
  executeApiTest(
    '获取商品详情',
    'GET',
    `/manager/product/${testParams.drinkId}`
  )
}

// 3. 获取门店菜单
const testGetStoreMenu = () => {
  executeApiTest(
    '获取门店菜单',
    'GET',
    `/manager/product/store/${testParams.storeId}`,
    {
      status: '1'
    }
  )
}

// 4. 根据原型查询商品
const testGetProductsByPrototype = () => {
  executeApiTest(
    '根据原型查询商品',
    'GET',
    `/manager/product/prototype/${testParams.prototypeId}`
  )
}

// 测试门店列表
const testGetStoreList = () => {
  executeApiTest(
    '获取门店列表',
    'GET',
    '/manager/store/list',
    {
      pageNum: 1,
      pageSize: 100
    }
  )
}

// 测试门店详情
const testGetStoreDetail = () => {
  executeApiTest(
    '获取门店详情',
    'GET',
    `/manager/store/${testParams.storeId}`
  )
}

// 测试原型详情
const testGetPrototypeDetail = () => {
  executeApiTest(
    '获取原型详情',
    'GET',
    `/manager/prototype/${testParams.prototypeId}`
  )
}

// 5. 测试创建商品
const testCreateProduct = () => {
  const productData = {
    drinkPrototype: testParams.prototypeId,
    storeId: testParams.storeId,
    productPrice: "28.00",
    targetId: "TEST_001",
    status: "1",
    options: JSON.stringify({
      temperature: "ice",
      sugar: "regular",
      size: "medium"
    }),
    remark: "API测试创建的商品"
  }

  executeApiTest(
    '创建商品',
    'POST',
    '/manager/product',
    undefined,
    productData
  )
}

// 6. 测试更新商品
const testUpdateProduct = () => {
  const updateData = {
    drinkId: testParams.drinkId,
    drinkPrototype: testParams.prototypeId,
    storeId: testParams.storeId,
    productPrice: "32.00",
    targetId: "TEST_UPDATE_001",
    status: "1",
    options: JSON.stringify({
      temperature: "hot",
      sugar: "less",
      size: "large"
    }),
    remark: "API测试更新的商品"
  }

  executeApiTest(
    '更新商品',
    'PUT',
    '/manager/product',
    undefined,
    updateData
  )
}

// 7. 测试删除商品
const testDeleteProduct = () => {
  executeApiTest(
    '删除商品',
    'DELETE',
    `/manager/product/${testParams.drinkId}`
  )
}

// 8. 测试导出商品
const testExportProducts = () => {
  const exportData = {
    storeId: testParams.storeId,
    status: "1"
  }

  executeApiTest(
    '导出商品',
    'POST',
    '/manager/product/export',
    undefined,
    exportData
  )
}

// 9. 测试批量操作
const testBatchOperations = () => {
  // 先测试获取多个门店的商品
  const storeIds = [100, 101, 102]

  storeIds.forEach((storeId, index) => {
    setTimeout(() => {
      executeApiTest(
        `批量测试-门店${storeId}商品`,
        'GET',
        `/manager/product/store/${storeId}`,
        { status: '1' }
      )
    }, index * 1000) // 间隔1秒执行，避免并发过多
  })
}

// 10. 测试所有接口
const testAllApis = () => {
  const tests = [
    () => testGetProductList(),
    () => testGetStoreMenu(),
    () => testGetProductsByPrototype(),
    () => testGetProductDetail(),
    () => testExportProducts()
  ]

  // 依次执行测试，间隔1.5秒
  tests.forEach((test, index) => {
    setTimeout(() => {
      test()
    }, index * 1500)
  })
}

// 清空测试结果
const clearResults = () => {
  testResults.value = []
  successCount.value = 0
  errorCount.value = 0
}

// 复制到剪贴板
const copyToClipboard = async (data: unknown) => {
  try {
    await navigator.clipboard.writeText(JSON.stringify(data, null, 2))
    alert('已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    alert('复制失败，请手动复制')
  }
}
</script>

<style scoped>
.product-api-test {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

pre {
  font-size: 0.875rem;
  line-height: 1.4;
}

code {
  color: #e83e8c;
}

.btn:disabled {
  opacity: 0.6;
}

.position-relative pre {
  padding-top: 3rem;
}
</style>
