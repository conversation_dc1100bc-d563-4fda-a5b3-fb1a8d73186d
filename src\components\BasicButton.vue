<!--
@component BaseButton
@description
  基础按钮组件，封装了常用按钮样式（来自已有 SCSS 模板），提供语义化 props 控制类型、尺寸和禁用状态。
  不对现有样式做改动，而是将其“语义封装”以提高代码可读性与复用性。

@usage 示例：
  <BaseButton>默认按钮</BaseButton>
  <BaseButton variant="primary">主按钮</BaseButton>
  <BaseButton variant="outline" size="sm" @click="handleClick">小按钮</BaseButton>
  <BaseButton customClass="btn btn-outline-primary bg-white">自定义 class</BaseButton>

@props
  variant: string [optional]
    类型控制，映射 SCSS 按钮样式类。
    可选值：
      - "default"（默认，映射 .default-btn）
      - "outline"（映射 .default-outline-btn）
      - "primary"（映射 .btn.btn-primary）
      - "link"（映射 .btn.btn-link）
      - "card"（映射 .card-btn）
      - "dot"（映射 .card-dot-btn + .dropdown-toggle）
    默认值："default"

  size: string [optional]
    按钮尺寸。控制 padding/font-size。
    可选值：
      - "sm" → 使用 .btn-sm
      - "lg" → 使用 .btn-lg
      - "md" → 默认，不添加额外尺寸类

  disabled: boolean [optional]
    是否禁用按钮。禁用状态下将阻止点击事件，样式仍由 class 控制。

  customClass: string [optional]
    自定义 class 名，优先级高于 variant/size，用于直接适配 SCSS 模板中已有组合类。

@emits
  click — 点击按钮触发

@slots
  默认插槽：按钮文本或元素
  （支持嵌入图标、svg 或组合内容）

@备注
  - 为了最大限度复用旧模板 SCSS，不推荐在组件中改写样式；
  - 推荐使用 variant/size 控制按钮样式，避免直接写 class；
  - 若需进一步封装 IconButton、LoadingButton 可基于本组件扩展。
-->
<template>
  <button
      :class="classes"
      :disabled="disabled"
      @click="$emit('click')"
  >
    <slot/>
  </button>
</template>
<script setup lang="ts">
import {computed} from 'vue'

const props = defineProps<{
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  size?: 'sm' | 'md' | 'lg';
  hollow?: boolean;
  customClass?: string;
  disabled?: boolean;
}>()

const classes = computed(() => {
  const base: string[] = []
  // 按照 SCSS 中已有 class 来映射
  if (!props.hollow) {
    switch (props.variant) {
      case 'primary':
        base.push('btn', 'btn-primary')
        break
      case 'secondary':
        base.push('btn', 'btn-secondary')
        break
      case 'success':
        base.push('btn', 'btn-success')
        break
      case 'danger':
        base.push('btn', 'btn-danger')
        break
      case 'warning':
        base.push('btn', 'btn-warning')
        break
      case 'info':
        base.push('btn', 'btn-info')
        break
      case 'light':
        base.push('btn', 'btn-light')
        break
      case 'dark':
        base.push('btn', 'btn-dark')
        break
      default:
        base.push('btn', 'btn-primary')
        break
    }
  } else {
    switch (props.variant) {
      case 'primary':
        base.push('btn', 'btn-outline-primary')
        break
      case 'secondary':
        base.push('btn', 'btn-outline-secondary')
        break
      case 'success':
        base.push('btn', 'btn-outline-success')
        break
      case 'danger':
        base.push('btn', 'btn-outline-danger')
        break
      case 'warning':
        base.push('btn', 'btn-outline-warning')
        break
      case 'info':
        base.push('btn', 'btn-outline-info')
        break
      case 'light':
        base.push('btn', 'btn-outline-light')
        break
      case 'dark':
        base.push('btn', 'btn-outline-dark')
        break
      default:
        base.push('btn', 'btn-outline-primary')
        break
    }
  }

// 尺寸
  switch (props.size) {
    case 'sm':
      base.push('btn-sm')
      break
    case 'lg':
      base.push('btn-lg')
      break
    case 'md':
    default:
      break // 默认尺寸(md)
  }
  if (props.customClass) base.push(props.customClass)
  return base
})
</script>
