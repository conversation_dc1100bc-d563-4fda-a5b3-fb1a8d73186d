<template>
  <div class="price-management p-20">
    <!-- 价格管理标签页 -->
    <div class="price-tabs mb-20">
      <ul class="nav nav-pills" id="priceTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button 
            class="nav-link active" 
            id="standard-price-tab" 
            data-bs-toggle="pill" 
            data-bs-target="#standard-price-pane" 
            type="button" 
            role="tab"
            @click="activeSubTab = 'standard'"
          >
            <i class="flaticon-price-tag me-2"></i>
            标准价格管理
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button 
            class="nav-link" 
            id="store-price-tab" 
            data-bs-toggle="pill" 
            data-bs-target="#store-price-pane" 
            type="button" 
            role="tab"
            @click="activeSubTab = 'store'"
          >
            <i class="flaticon-store me-2"></i>
            门店价格管理
          </button>
        </li>

      </ul>
    </div>

    <!-- 标签页内容 -->
    <div class="tab-content" id="priceTabsContent">
      <!-- 标准价格管理 -->
      <div class="tab-pane fade show active" id="standard-price-pane" role="tabpanel">
        <div class="standard-price-section">
          <!-- 操作区域 -->
          <div class="d-flex justify-content-between align-items-center mb-20">
            <div class="search-controls d-flex gap-3">
              <div class="search-input">
                <input 
                  v-model="standardPriceSearch.prototypeName" 
                  type="text" 
                  class="form-control" 
                  placeholder="搜索原型名称"
                  @input="searchStandardPrices"
                />
              </div>
              <div class="category-filter">
                <select v-model="standardPriceSearch.categoryId" class="form-select" @change="searchStandardPrices">
                  <option value="">全部分类</option>
                  <option v-for="category in categoryList" :key="category.groupId" :value="category.groupId">
                    {{ category.groupName }}
                  </option>
                </select>
              </div>
            </div>
            <div class="action-buttons">
              <button @click="searchStandardPrices" class="btn btn-primary">
                搜索
              </button>
            </div>
          </div>

          <!-- 标准价格表格 -->
          <div class="table-responsive">
            <table class="table table-hover">
              <thead class="table-light">
                <tr>
                  <th width="60">
                    <input type="checkbox" v-model="selectAll" @change="toggleSelectAll" class="form-check-input">
                  </th>
                  <th>原型名称</th>
                  <th>分类</th>
                  <th>建议价格</th>
                  <th>门店数量</th>
                  <th>平均售价</th>
                  <th>最后更新</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="item in standardPriceList" :key="item.prototypeId">
                  <td>
                    <input type="checkbox" v-model="selectedItems" :value="item.prototypeId" class="form-check-input">
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <img 
                        v-if="item.prototypeImage" 
                        :src="item.prototypeImage" 
                        alt="原型图片" 
                        class="prototype-image me-2"
                      />
                      <div>
                        <div class="fw-bold">{{ item.prototypeName }}</div>
                        <small class="text-muted">{{ item.prototypeDescription }}</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span class="badge bg-info">{{ getCategoryName(item.drinkGroupId) }}</span>
                  </td>
                  <td>
                    <span class="fw-bold text-success">¥{{ item.standardPrice || '未设置' }}</span>
                  </td>
                  <td>
                    <span class="badge bg-secondary">{{ item.storeCount || 0 }}</span>
                  </td>
                  <td>
                    <span class="text-muted">¥{{ item.averagePrice || '0.00' }}</span>
                  </td>
                  <td>{{ formatDate(item.updateTime) }}</td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      <button @click="editStandardPrice(item)" class="btn btn-outline-primary">
                        编辑
                      </button>
                      <button @click="viewPriceHistory(item)" class="btn btn-outline-info">
                        历史
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- 门店价格管理 -->
      <div class="tab-pane fade" id="store-price-pane" role="tabpanel">
        <div class="store-price-section">
          <!-- 门店选择 -->
          <div class="store-selector mb-20">
            <div class="d-flex align-items-center gap-3">
              <label class="form-label mb-0 fw-bold">选择门店:</label>
              <select v-model="selectedStoreForPrice" class="form-select" style="width: 300px;" @change="loadStorePrices">
                <option value="">请选择门店</option>
                <option v-for="store in storeList" :key="store.storeId" :value="store.storeId">
                  {{ store.storeName }}
                </option>
              </select>

            </div>
          </div>

          <!-- 门店价格表格 -->
          <div v-if="selectedStoreForPrice" class="table-responsive">
            <!-- 加载状态 -->
            <div v-if="loadingStorePrices" class="text-center py-5">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
              </div>
              <p class="mt-2 text-muted">正在加载门店价格数据...</p>
            </div>
            <!-- 数据表格 -->
            <table v-else class="table table-hover">
              <thead class="table-light">
                <tr>
                  <th>商品信息</th>
                  <th>标准价格</th>
                  <th>门店价格</th>
                  <th>价格差异</th>
                  <th>商品选项</th>
                  <th>状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="item in storePriceList" :key="item.drinkId">
                  <td>
                    <div class="fw-bold">{{ item.prototypeName || '未知商品' }}</div>
                    <small class="text-muted">
                      ID: {{ item.drinkId }} | 原型: {{ item.drinkPrototype }} | 目标: {{ item.targetId }}
                    </small>
                  </td>
                  <td>
                    <span class="text-muted">¥{{ item.standardPrice || '未设置' }}</span>
                  </td>
                  <td>
                    <span class="fw-bold text-success">¥{{ item.productPrice || '0.00' }}</span>
                  </td>
                  <td>
                    <span :class="getPriceDifferenceClass(item)">
                      {{ getPriceDifference(item) }}
                    </span>
                  </td>
                  <td>
                    <small class="text-muted" :title="item.options">
                      {{ formatOptions(item.options) }}
                    </small>
                  </td>
                  <td>
                    <span :class="item.status === '1' ? 'badge bg-success' : 'badge bg-danger'">
                      {{ item.status === '1' ? '上架' : '下架' }}
                    </span>
                  </td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      <button @click="editStorePrice(item)" class="btn btn-outline-primary" title="编辑价格">
                        编辑
                      </button>
                      <button @click="viewProductDetail(item)" class="btn btn-outline-info" title="查看详情">
                        查看
                      </button>
                    </div>
                  </td>
                </tr>
                <tr v-if="storePriceList.length === 0 && !loadingStorePrices">
                  <td colspan="7" class="text-center py-4 text-muted">
                    {{ selectedStoreForPrice ? '该门店暂无商品数据' : '请选择门店查看商品价格' }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>


    </div>




  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { format } from 'date-fns'
import serviceAxios from '@/utils/serviceAxios'
import { getDrinkPrototypeList } from '@/utils/api/drink'
import { getStoreMenu, getPrototypeDetailById } from '@/utils/api/product'

// 类型定义
interface CategoryItem {
  groupId: number
  groupName: string
  status: string
}

interface StoreItem {
  storeId: number
  storeName: string
  status: string
}

interface StandardPriceItem {
  prototypeId: number
  prototypeName: string
  prototypeImage?: string
  prototypeDescription?: string
  drinkGroupId: number
  standardPrice?: string
  storeCount?: number
  averagePrice?: string
  updateTime?: string
}

interface StorePriceItem {
  drinkId: number
  drinkPrototype: number
  storeId: number
  productPrice: string
  status: string
  options: string
  targetId: string
  createBy: string
  createTime: string | null
  updateBy: string
  updateTime: string | null
  remark: string | null
  // 前端补充的字段
  prototypeName?: string
  standardPrice?: string
}



// 数据状态
const activeSubTab = ref('standard')
const categoryList = ref<CategoryItem[]>([])
const storeList = ref<StoreItem[]>([])
const standardPriceList = ref<StandardPriceItem[]>([])
const storePriceList = ref<StorePriceItem[]>([])
const selectedStoreForPrice = ref('')
const loadingStorePrices = ref(false)

// 选择相关
const selectAll = ref(false)
const selectedItems = ref<number[]>([])



// 搜索表单
const standardPriceSearch = ref({
  prototypeName: '',
  categoryId: ''
})





// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return format(new Date(dateString), 'yyyy-MM-dd HH:mm')
}

// 获取分类名称
const getCategoryName = (groupId: number) => {
  const category = categoryList.value.find((cat: CategoryItem) => cat.groupId === groupId)
  return category ? category.groupName : '未知分类'
}

// 获取价格差异
const getPriceDifference = (item: StorePriceItem) => {
  if (!item.standardPrice || !item.productPrice) return '-'
  const standardPrice = parseFloat(item.standardPrice)
  const productPrice = parseFloat(item.productPrice)

  if (isNaN(standardPrice) || isNaN(productPrice)) return '-'

  const diff = productPrice - standardPrice
  return diff > 0 ? `+¥${diff.toFixed(2)}` : diff < 0 ? `-¥${Math.abs(diff).toFixed(2)}` : '一致'
}

// 获取价格差异样式类
const getPriceDifferenceClass = (item: StorePriceItem) => {
  if (!item.standardPrice || !item.productPrice) return 'text-muted'
  const standardPrice = parseFloat(item.standardPrice)
  const productPrice = parseFloat(item.productPrice)

  if (isNaN(standardPrice) || isNaN(productPrice)) return 'text-muted'

  const diff = productPrice - standardPrice
  return diff > 0 ? 'text-danger' : diff < 0 ? 'text-success' : 'text-muted'
}

// 获取基础数据
const fetchBaseData = async () => {
  try {
    console.log('🔄 [价格管理] 开始加载基础数据...')

    // 获取分类列表
    const categoryResponse = await serviceAxios.get('/manager/group/list', {
      params: { pageNum: 1, pageSize: 1000 }
    })
    // 过滤出启用状态的分类
    const allCategories = categoryResponse.rows || []
    categoryList.value = allCategories.filter((item: CategoryItem) => item.status === '1')
    console.log(`✅ [价格管理] 分类列表加载成功: 总共${allCategories.length}个分类, 启用${categoryList.value.length}个分类`)

    // 获取门店列表
    const storeResponse = await serviceAxios.get('/manager/store/list', {
      params: { pageNum: 1, pageSize: 1000 }
    })
    // 根据测试结果，门店的status都是'0'，调整筛选逻辑
    const allStores = storeResponse.rows || []
    // 先尝试筛选status='1'的门店，如果没有则使用status='0'的门店
    let activeStores = allStores.filter((item: StoreItem) => item.status === '1')
    if (activeStores.length === 0) {
      console.log('⚠️ [价格管理] 没有找到status=1的门店，使用status=0的门店')
      activeStores = allStores.filter((item: StoreItem) => item.status === '0')
    }
    storeList.value = activeStores
    console.log(`✅ [价格管理] 门店列表加载成功: 总共${allStores.length}个门店, 营业中${storeList.value.length}个门店`)
    console.log('🏪 [价格管理] 营业门店列表:', storeList.value.map(s => `${s.storeId}:${s.storeName}`))
  } catch (error) {
    console.error('❌ [价格管理] 获取基础数据失败:', error)
  }
}

// 搜索标准价格
const searchStandardPrices = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 1000,
      ...standardPriceSearch.value
    }
    const response = await getDrinkPrototypeList(params)
    standardPriceList.value = response.rows || []
  } catch (error) {
    console.error('获取标准价格失败:', error)
  }
}

// 加载门店价格
const loadStorePrices = async () => {
  if (!selectedStoreForPrice.value) return

  loadingStorePrices.value = true
  try {
    console.log(`🔄 [价格管理] 开始加载门店${selectedStoreForPrice.value}的商品价格...`)

    // 使用封装好的API函数获取门店商品数据
    const storeMenuResponse = await getStoreMenu(parseInt(selectedStoreForPrice.value))
    const products = storeMenuResponse.rows || []

    console.log(`🔄 [价格管理] 开始补充${products.length}个商品的原型信息...`)

    // 使用API获取每个商品的原型详细信息
    const enrichedProducts = await Promise.all(
      products.map(async (product: { drinkPrototype: number; [key: string]: unknown }) => {
        try {
          // 获取原型详细信息
          const prototypeDetail = await getPrototypeDetailById(product.drinkPrototype)

          return {
            ...product,
            prototypeName: prototypeDetail?.prototypeName || `原型${product.drinkPrototype}`,
            standardPrice: prototypeDetail?.standardPrice || '0.00'
          } as StorePriceItem
        } catch (error) {
          console.warn(`⚠️ [价格管理] 获取原型${product.drinkPrototype}详细信息失败:`, error)
          return {
            ...product,
            prototypeName: `原型${product.drinkPrototype}`,
            standardPrice: '0.00'
          } as StorePriceItem
        }
      })
    )

    storePriceList.value = enrichedProducts

    console.log(`✅ [价格管理] 门店价格数据加载完成: ${storePriceList.value.length}个商品`)
  } catch (error) {
    console.error('❌ [价格管理] 获取门店价格失败:', error)
    storePriceList.value = []
  } finally {
    loadingStorePrices.value = false
  }
}







// 编辑标准价格
const editStandardPrice = (item: StandardPriceItem) => {
  console.log('编辑标准价格:', item)
}

// 查看价格历史
const viewPriceHistory = (item: StandardPriceItem) => {
  console.log('查看价格历史:', item)
}

// 格式化商品选项
const formatOptions = (options: string) => {
  if (!options) return '无选项'

  // 处理JSON格式的选项
  if (options.startsWith('{')) {
    try {
      const parsed = JSON.parse(options)
      return Object.entries(parsed)
        .map(([key, value]) => `${key}:${value}`)
        .join(', ')
    } catch {
      return options
    }
  }

  // 处理点分隔的选项
  return options.replace(/\./g, ' | ')
}

// 查看商品详情
const viewProductDetail = (item: StorePriceItem) => {
  const detailInfo = `
商品详细信息：

基本信息：
• 商品ID: ${item.drinkId}
• 商品名称: ${item.prototypeName || '未知商品'}
• 原型ID: ${item.drinkPrototype}
• 门店ID: ${item.storeId}
• 目标ID: ${item.targetId}

价格信息：
• 门店价格: ¥${item.productPrice}
• 标准价格: ¥${item.standardPrice || '未设置'}
• 价格差异: ${getPriceDifference(item)}

商品选项：
${formatOptions(item.options)}

状态信息：
• 状态: ${item.status === '1' ? '上架' : '下架'}
• 创建者: ${item.createBy || '未知'}
• 创建时间: ${formatDate(item.createTime)}
• 更新者: ${item.updateBy || '未知'}
• 更新时间: ${formatDate(item.updateTime)}
• 备注: ${item.remark || '无'}
  `.trim()

  alert(detailInfo)
}

// 编辑门店价格
const editStorePrice = (item: StorePriceItem) => {
  console.log('编辑门店价格:', item)
  const newPrice = prompt(`编辑商品价格\n\n商品: ${item.prototypeName || '未知商品'}\n当前价格: ¥${item.productPrice}\n\n请输入新价格:`, item.productPrice)

  if (newPrice && newPrice !== item.productPrice) {
    // 这里可以调用更新价格的API
    console.log(`价格更新: ${item.productPrice} -> ${newPrice}`)
    alert(`价格更新功能待实现\n商品ID: ${item.drinkId}\n新价格: ¥${newPrice}`)
  }
}

// 全选/取消全选
const toggleSelectAll = () => {
  if (selectAll.value) {
    selectedItems.value = standardPriceList.value.map(item => item.prototypeId)
  } else {
    selectedItems.value = []
  }
}

// 监听门店选择变化
watch(selectedStoreForPrice, (newStoreId) => {
  if (newStoreId) {
    loadStorePrices()
  } else {
    storePriceList.value = []
  }
})

// 初始化
onMounted(async () => {
  await fetchBaseData()
  searchStandardPrices()
})
</script>

<style scoped>
.price-management {
  background: #fff;
}

.price-tabs .nav-pills .nav-link {
  color: #495057;
  background: none;
  border: 1px solid #dee2e6;
  margin-right: 10px;
  transition: all 0.2s ease;
}

.price-tabs .nav-pills .nav-link:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.price-tabs .nav-pills .nav-link.active {
  background-color: #f8f9fa;
  border-color: #dee2e6;
  color: #495057;
}

.prototype-image {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

.store-selector {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.search-controls {
  flex-wrap: wrap;
}

.search-input,
.category-filter,
.date-range,
.store-filter {
  min-width: 150px;
}

.table th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
}

.table td {
  vertical-align: middle;
}

@media (max-width: 768px) {
  .search-controls {
    flex-direction: column;
    gap: 10px !important;
  }
  
  .search-input,
  .category-filter,
  .date-range,
  .store-filter {
    min-width: auto;
    width: 100%;
  }
  
  .price-tabs .nav-pills {
    flex-direction: column;
  }
  
  .price-tabs .nav-pills .nav-link {
    margin-right: 0;
    margin-bottom: 5px;
  }
}
</style>
