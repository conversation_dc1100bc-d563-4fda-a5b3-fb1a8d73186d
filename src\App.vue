<script lang="ts">
import {defineComponent} from "vue";

import MainHeader from "./components/layouts/MainHeader.vue";
import MainSidebar from "./components/layouts/MainSidebar.vue";
import MainFooter from "./components/layouts/MainFooter.vue";

export default defineComponent({
  name: "App",
  components: {
    MainHeader,
    MainSidebar,
    MainFooter,
  },
  mounted() {
    document.body.classList.add("bg-body-secondary");
  },
});
</script>

<script lang="ts" setup>
import {useSidebarStore} from "@/store/useSidebarStore";
import {watch} from "vue";
import {useRoute} from "vue-router";
import {computed} from "vue";

const route = useRoute();
const isFullscreen = computed(() => route.meta.layout === "fullscreen");
const isStandalone = computed(() => route.meta.layout === "standalone");
const isAuth = computed(() => route.meta.layout === "auth");
const sidebarStore = useSidebarStore();
watch(()=>sidebarStore.sidebarState,(newVal)=>{
  if(!isFullscreen.value && !isStandalone.value && !isAuth.value){
    if(newVal){
      document.body.classList.remove("sidebar-show");
      document.body.classList.add("sidebar-hide");
    } else{
      document.body.classList.remove("sidebar-hide");
      document.body.classList.add("sidebar-show");
    }
  }
})

</script>

<template>
  <!-- 认证页面布局（登录、注册等） -->
  <template v-if="isAuth">
    <div class="auth-layout">
      <router-view/>
    </div>
  </template>

  <!-- 独立页面布局 -->
  <template v-else-if="isStandalone">
    <router-view/>
  </template>

  <!-- 全屏布局 -->
  <template v-else-if="isFullscreen">
    <div class="fullscreen-layout">
      <div class="main-content-wrapper">
        <router-view/>
      </div>
      <MainFooter/>
    </div>
  </template>

  <!-- 默认布局（带侧边栏） -->
  <template v-else>
    <MainHeader/>
    <MainSidebar/>
    <div class="main-content d-flex flex-column transition overflow-hidden">
      <div class="main-content-wrapper">
        <router-view/>
      </div>
      <MainFooter/>
    </div>
  </template>
</template>
<style scoped>
/* 认证页面布局 */
.auth-layout {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
}

.fullscreen-layout {
  display: flex;
  flex-direction: column; /* 让 main-content-wrapper 和 footer 垂直排列 */
  min-height: 100vh;     /* 确保布局至少占满整个视口高度 */
  width: 100%;
  padding: 0;            /* 关键：移除所有内边距，取消 sidebar 的偏移 */
}
.fullscreen-layout .main-content-wrapper {
  flex-grow: 1;          /* 关键：让这个 wrapper 占据所有可用垂直空间，将 footer 推到底部 */
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center;     /* 垂直居中 */
  width: 100%;
}
/* 确保在普通布局下，这个 wrapper 不影响原有布局 */
.main-content .main-content-wrapper {
  /*
    因为你的 .main-content 已经是 flex-column 布局,
    为了让 router-view 正常填充，也需要让 wrapper 伸展
  */
  flex-grow: 1;
}
</style>
