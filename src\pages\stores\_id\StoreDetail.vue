<script setup lang="ts">
/* eslint-disable */
import {useStoreStore} from "@/store/useStoreStore";
import {useRoute} from "vue-router";
import {useSidebarStore} from "@/store/useSidebarStore";
import {computed, onMounted, onUnmounted, Ref, ref, watch} from "vue";
import {format} from "date-fns";

// 直接在组件中导入 API 调用和所需类型
import {drinkOrderParams, getOrderListByParams, getOrderListByStoreId} from "@/utils/api/order";
import type {TableDataInfo} from "@/types/tableDataInfo";
import type {drinkOrder} from "@/types/order";
import type {queryPageParams} from "@/types/queryPageParams";

// 导入所需子组件
import BreadCrumb from "@/components/layouts/BreadCrumb.vue";
import StatsCard from "@/components/StatsCard.vue";
import {drinkProductionQueueItem, storeDetail} from "@/types";
import {getProductionQueueByStoreId} from "@/utils/api/queue";
import flatPickr from "vue-flatpickr-component";
import {BPagination} from "bootstrap-vue-next";

// --- Pinia 仓库和路由 ---
const storeStore = useStoreStore();
const sidebarStore = useSidebarStore();
const route = useRoute();
// --- 组件内部状态 ---

// 加载状态
const isLoading = ref<boolean>(true);

// 获取当前的店铺信息
const storeId = Number(route.params.id);
const currentStoreInfo: Ref<storeDetail | undefined> = storeStore.findStoreById(storeId);

// 用于存储从API获取的原始订单列表，使用ref使其具有响应性
const totalOrders = ref<drinkOrder[]>([]);
const showSize = 10;
const sortedOrdersByDate = computed(() => {
  return [...totalOrders.value]
      .sort((a, b) => {
        const dateA = new Date(a.paymentTime).getTime();
        const dateB = new Date(b.paymentTime).getTime();
        return dateB - dateA; // 降序
      })
      .slice(0, showSize);
})
// 用于存储所有计算后的统计数据
const orderStats = ref({
  totalSales: 0,
  totalOrders: 0,
  completedOrders: 0,
  pendingOrders: 0,
  cancelledOrders: 0,
  popularDrink: "暂无数据",
});

// 制作队列
const productionQueue = ref<drinkProductionQueueItem[]>([]);
const sortedQueueItemsByDate = computed(() => {
  return productionQueue.value
      .sort((a, b) => {
        const dateA = new Date(a.startTime).getTime();
        const dateB = new Date(b.startTime).getTime();
        return dateB - dateA;
      })
      .slice(0, showSize);
})
const queueItemStats = ref({
  totalItems: 0,
  finishedItems: 0,
  pendingItems: 0,
  failedItems: 0,
})
// List展示内容
const ordersForList = ref<drinkOrder[]>([]);
const sortedOrdersByDateForList = computed(() => {
  return ordersForList.value
      .sort((a, b) => {
        const dateA = new Date(a.startTime).getTime();
        const dateB = new Date(b.startTime).getTime();
        return dateB - dateA;
      })
      .slice(0, showSize);
})
const selectedDate = ref(new Date());
const currentPage = ref(1);
const pageSize = 10;
const totalItemsCount = ref(0);
const flatpickrConfig = {
  dateFormat: "Y-m-d",
  locale: "zh",
  allowInput: true,
  enableTime: false,
}

async function fetchDataForList() {
  try {
    const formattedApiDate = format(selectedDate.value, "yyyy-MM-dd");
    const queryParams: drinkOrderParams = {
      pageNum: currentPage.value,
      pageSize: pageSize,
      queryDate: formattedApiDate,
      storeId: storeId
    }
    const response = await getOrderListByParams(storeId, queryParams);
    ordersForList.value = response.rows;
    totalItemsCount.value = response.total;
  } catch (error) {
    console.error("数据获取失败", error);
  }
}

const handleDateChange = (selectedDate) => {
  // 重新获取数据时会重置页码+获取当天数据
  currentPage.value = 1;
  fetchDataForList();
}
// 侦听切换页数
watch(currentPage,()=>{
  fetchDataForList();
})

/**
 * 在组件内部获取订单数据并进行所有计算
 * 这个函数是组件的核心逻辑
 */
async function fetchAndProcessData() {
  if (currentStoreInfo.value === undefined) {
    console.warn("等待店铺信息加载...");
    return;
  }

  isLoading.value = true;
  try {
    const params: queryPageParams = {pageNum: 1, pageSize: 9999}; // 为了精确统计，获取所有数据
    const orderResponse: TableDataInfo<drinkOrder> = await getOrderListByStoreId(currentStoreInfo.value.storeId, params);
    const queueItemResponse: TableDataInfo<drinkProductionQueueItem> = await getProductionQueueByStoreId(currentStoreInfo.value.storeId, params);


    // 更新组件内的列表状态
    totalOrders.value = orderResponse.rows;
    productionQueue.value = queueItemResponse.rows;

    const totalOrderCount = orderResponse.total;
    const totalQueueItemCount = queueItemResponse.total;

    // --- 在这里执行所有数据计算，并更新 stats 对象 ---

    // 1. 计算总销售额
    const totalSales = totalOrders.value.reduce((sum, order) => {
      return sum + parseFloat(order.totalAmount || '0');
    }, 0);

    // 2. 统计不同状态的订单数量
    const completedOrders = totalOrders.value.filter(o => o.orderStatus === '4').length;
    const cancelledOrders = totalOrders.value.filter(o => o.orderStatus === '5').length;
    const pendingOrders = totalOrderCount - completedOrders - cancelledOrders;

    const finishedItems = productionQueue.value.filter(o => o.queueStatus === '2').length;
    const failedItems = productionQueue.value.filter(o => o.queueStatus === '3').length;
    const pendingItems = totalQueueItemCount - failedItems - finishedItems;

    // 3. 找到最畅销的产品
    let popularDrink = "暂无数据";
    const drinkCounts = new Map<number, number>();
    totalOrders.value.forEach(order => {
      if (order.orderItemsList && Array.isArray(order.orderItemsList)) {
        order.orderItemsList.forEach(item => {
          const prototypeId = item.drinkPrototype;
          drinkCounts.set(prototypeId, (drinkCounts.get(prototypeId) || 0) + 1);
        });
      }
    });

    if (drinkCounts.size > 0) {
      let maxCount = 0;
      let popularDrinkId: number | string = 'N/A';
      drinkCounts.forEach((count, drinkId) => {
        if (count > maxCount) {
          maxCount = count;
          popularDrinkId = drinkId;
        }
      });
      popularDrink = `饮品原型ID: ${popularDrinkId}`;
    }

    // 4. 一次性更新所有统计数据
    orderStats.value = {
      totalSales,
      totalOrders: totalOrderCount,
      completedOrders,
      pendingOrders,
      cancelledOrders,
      popularDrink,
    };

    queueItemStats.value = {
      totalItems: totalQueueItemCount,
      finishedItems,
      pendingItems,
      failedItems,
    }

  } catch (error) {
    console.error("获取店铺数据失败:", error);
    // 如果发生错误，重置所有状态
    totalOrders.value = [];
    orderStats.value = {
      totalSales: 0,
      totalOrders: 0,
      completedOrders: 0,
      pendingOrders: 0,
      cancelledOrders: 0,
      popularDrink: "加载失败"
    };
  } finally {
    isLoading.value = false;
  }
}

function getOrderStatusClass(status: string): string {
  let statusClass;
  switch (status) {
    case "0":
      statusClass = "待支付";
      break;
    case "1":
      statusClass = "已支付";
      break;
    case "2":
      statusClass = "制作中";
      break;
    case "3":
      statusClass = "待取餐";
      break;
    case "4":
      statusClass = "已完成";
      break;
    case "5":
      statusClass = "已取消";
      break;
    default:
      statusClass = "未定义";
  }
  return statusClass;
}

function getQueueItemStatusClass(status: string): string {
  let statusClass;
  switch (status) {
    case "0":
      statusClass = "待制作";
      break;
    case "1":
      statusClass = "制作中";
      break;
    case "2":
      statusClass = "制作完成";
      break;
    case "3":
      statusClass = "制作失败";
      break;
    default:
      statusClass = "未定义";
  }
  return statusClass;
}

async function fetchOrdersByStoreId(storeId: number) {
  try {
    const res = await getOrderListByStoreId(
        storeId,
        {
          pageNum: 1,
          pageSize: 10,
        }
    );
    totalOrders.value = res.rows ?? [];
  } catch (error) {
    console.log("订单数据获取失败", error);
  }
}

// --- 生命周期钩子 ---
onMounted(() => {
  sidebarStore.switchDetailMode();
  fetchOrdersByStoreId(storeId);
  fetchDataForList();
});

onUnmounted(() => {
  sidebarStore.switchInitialMode();
});

// --- 侦听器 ---
// 侦听 activeStore 的变化，这是触发数据获取的入口点
watch(currentStoreInfo, (newStore) => {
  // 确保 newStore 存在并且是当前路由对应的店铺
  if (newStore && newStore.storeId === storeId) {
    fetchAndProcessData();
  }
}, {
  immediate: true // 立即执行一次，以保证组件加载时就能获取数据
});

</script>

<template>
  <div v-if="isLoading">
    <div class="loader">Loading...</div>
  </div>
  <div v-else-if="currentStoreInfo">
<!--    <BreadCrumb :pageTitle="currentStoreInfo.storeName"/>-->
    <div class="card mb-25 border-0 rounded-0 welcome-box">
      <div class="card-body pe-15 pe-sm-20 pe-md-0 pb-0 pt-15 pt-sm-20">
        <div class="row align-items-center">
          <div class="col-lg-6 col-md-6">
            <div class="title position-relative">
              <h3 class="fw-semibold mb-8">
                <span class="fw-bold"> {{ currentStoreInfo.storeName }} </span>
              </h3>
              <span class="d-block text-black-emphasis fs-md-15 fs-lg-16">
                这是今天的门店数据概览。
              </span>
            </div>
            <ul class="ps-0 mb-0 list-unstyled">
              <li
                  class="d-inline-block text-uppercase fw-medium fs-13 text-black-emphasis position-relative"
              >
                今日销量
                <span class="d-block fw-black lh-1 text-black mt-5 mt-md-10">
                {{ orderStats.totalOrders }}
              </span>
              </li>
              <li
                  class="d-inline-block text-uppercase fw-medium fs-13 text-black-emphasis position-relative"
              >
                今日总销售额
                <span class="d-block fw-black lh-1 text-black mt-5 mt-md-10">
                ￥{{ orderStats.totalSales.toFixed(2) }}
              </span>
              </li>
            </ul>
          </div>
          <div class="col-lg-6 col-md-6 text-center mt-15 mt-md-0">
            <img
                src="@/assets/images/welcome/welcome1.png"
                alt="welcome-image"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <StatsCard
          title="今日销售额"
          :value="`¥${orderStats.totalSales.toFixed(2)}`"
          icon="flaticon-idea"
          color="primary"
          sub-text="售出的商品总价"
      />
      <StatsCard
          title="待处理订单"
          :value="orderStats.pendingOrders"
          icon="flaticon-sugar-cubes"
          color="danger"
          sub-text="等待制作完成的订单"
      />
      <StatsCard
          title="最热饮品"
          :value="orderStats.popularDrink"
          icon="flaticon-user-3"
          color="info"
          sub-text="本日最畅销"
      />
      <StatsCard
          title="总订单数"
          :value="orderStats.totalOrders"
          icon="flaticon-sterile-box"
          color="success"
          :progress="orderStats.totalOrders > 0 ? Math.round((orderStats.completedOrders * 1.0 / orderStats.totalOrders) * 100) : 0"
          :progress-text="`${orderStats.completedOrders} 已完成`"
      />
    </div>
    <!--    订单列表-->
    <div class="row">
      <div class="col-sm-12 col-xxl-6">
        <div class="card mb-25 border-0 rounded-0 bg-white letter-spacing ">
          <!--    表头上方功能区-->
          <div
              class="card-head box-shadow bg-white d-md-flex align-items-center justify-content-between p-15 p-sm-20 p-md-25"
          >
            <div class="d-sm-flex align-items-center mt-15 mt-md-3">
              <h3>订单信息</h3>
            </div>
            <flat-pickr
                v-model="selectedDate"
                @on-change="handleDateChange"
                :config="flatpickrConfig"
                placeholder="选择日期"
            />
          </div>
          <div class="card-body p-15 p-sm-20 p-md-25">
            <div v-if="isLoading" class="text-center">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
            <div v-else-if="error" class="alert alert-danger" role="alert">
              {{ error }}
            </div>
            <div v-else class="table-responsive">
              <table class="table text-nowrap align-middle mb-0">
                <thead>
                <tr>
                  <!-- 表头 -->
                  <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0 ps-0">
                    #订单编号
                  </th>
                  <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">
                    订单杯数
                  </th>
                  <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">
                    付款金额
                  </th>
                  <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">
                    订单状态
                  </th>
                  <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">
                    付款时间
                  </th>
                </tr>
                </thead>
                <tbody>
                <tr v-if="sortedOrdersByDateForList === undefined">
                  <td colspan="9" class="text-center text-muted fst-italic">No orders found.</td>
                </tr>
                <!-- 循环制表 -->
                <tr v-for="order in sortedOrdersByDateForList" :key="order.orderId">
                  <td class="shadow-none lh-1 fw-medium text-paragraph">
                    #{{ order.orderId }}
                  </td>
                  <td class="shadow-none lh-1 fw-medium text-paragraph">
                    ×{{ order.orderItemsList.length }}
                  </td>
                  <td class="shadow-none lh-1 fw-medium text-paragraph">
                    ${{ order.totalAmount }}
                  </td>
                  <td class="shadow-none lh-1 fw-medium text-paragraph">
                    {{ getOrderStatusClass(order.orderStatus) }}
                  </td>
                  <td class="shadow-none lh-1 fw-medium">
                    {{ format(new Date(order.paymentTime), 'yyyy-MM-dd HH:mm') }}
                  </td>
                  <td
                      class="shadow-none lh-1 fw-medium text-paragraph text-end pe-0"
                  >
                  </td>
                </tr>
                </tbody>
              </table>
            </div>
            <!-- 分页(目前是静态) -->
            <div
                class="pagination-area d-md-flex mt-15 mt-sm-20 mt-md-25 justify-content-between align-items-center"
            >
              <p class="mb-0 text-paragraph">
                Showing <span class="fw-bold">{{ pageSize }}</span> out of
                <span class="fw-bold">{{ totalItemsCount }}</span> results
              </p>
              <b-pagination
                  v-model="currentPage"
                  :total-rows="totalItemsCount"
                  :per-page="pageSize"
                  aria-controls="my-table"
              />
            </div>
          </div>
        </div>
      </div>
      <!--      制作队列-->
      <div class="col-sm-12 col-xxl-6">
        <div class="card mb-25 border-0 rounded-0 bg-white letter-spacing ">
          <!--    表头上方功能区-->
          <div
              class="card-head box-shadow bg-white d-md-flex align-items-center justify-content-between p-15 p-sm-20 p-md-25"
          >
            <div class="d-sm-flex align-items-center mt-15 mt-md-3">
              <h3>制作队列</h3>
            </div>
          </div>
          <div class="card-body p-15 p-sm-20 p-md-25">
            <div v-if="isLoading" class="text-center">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
            <div v-else-if="error" class="alert alert-danger" role="alert">
              {{ error }}
            </div>
            <div v-else class="table-responsive">
              <table class="table text-nowrap align-middle mb-0">
                <thead>
                <tr>
                  <!-- 表头 -->
                  <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0 ps-0">
                    #队列编号
                  </th>
                  <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">
                    饮品名称
                  </th>
                  <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">
                    制作开始时间
                  </th>
                  <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">
                    制作结束时间
                  </th>
                  <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">
                    付款时间
                  </th>
                </tr>
                </thead>
                <tbody>
                <tr v-if="sortedQueueItemsByDate.length == 0">
                  <td colspan="9" class="text-center text-muted fst-italic">No items found.</td>
                </tr>
                <!-- 循环制表 -->
                <tr v-for="item in sortedQueueItemsByDate" :key="item.queueId">
                  <td class="shadow-none lh-1 fw-medium text-paragraph">
                    #{{ item.queueId }}
                  </td>
                  <td class="shadow-none lh-1 fw-medium text-paragraph">
                    {{ item.drinkName }}
                  </td>
                  <td class="shadow-none lh-1 fw-medium text-paragraph">
                    {{ format(new Date(item.startTime), 'yyyy-MM-dd HH:mm') }}
                  </td>
                  <td class="shadow-none lh-1 fw-medium text-paragraph">
                    {{ format(new Date(item.finishTime), 'yyyy-MM-dd HH:mm') }}
                  </td>
                  <td class="shadow-none lh-1 fw-medium">
                    {{ getQueueItemStatusClass(item.queueStatus) }}
                  </td>
                  <td
                      class="shadow-none lh-1 fw-medium text-paragraph text-end pe-0"
                  >
                  </td>
                </tr>
                </tbody>
              </table>
            </div>
            <!-- 分页(目前是静态) -->
            <div
                class="pagination-area d-md-flex mt-15 mt-sm-20 mt-md-25 justify-content-between align-items-center"
            >
              <p class="mb-0 text-paragraph">
                Showing <span class="fw-bold">{{ showSize }}</span> out of
                <span class="fw-bold">{{ queueItemStats.totalItems }}</span> results
              </p>
              <!-- Add dynamic pagination controls here later if needed -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-else>
    <p>无法加载店铺信息，请检查店铺ID是否正确。</p>
  </div>
</template>