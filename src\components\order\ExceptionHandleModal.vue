<template>
  <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="flaticon-warning text-warning me-2"></i>
            处理异常订单
          </h5>
          <button type="button" class="btn-close" @click="$emit('close')"></button>
        </div>
        
        <div class="modal-body">
          <!-- 订单基本信息 -->
          <div class="card mb-4">
            <div class="card-header">
              <h6 class="mb-0">订单信息</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <table class="table table-sm table-borderless">
                    <tr>
                      <td><strong>订单号:</strong></td>
                      <td>{{ order.orderId }}</td>
                    </tr>
                    <tr>
                      <td><strong>流水号:</strong></td>
                      <td>{{ order.orderReqSeq }}</td>
                    </tr>
                    <tr>
                      <td><strong>下单时间:</strong></td>
                      <td>{{ formatDateTime(order.orderTime) }}</td>
                    </tr>
                    <tr>
                      <td><strong>订单金额:</strong></td>
                      <td class="text-primary"><strong>¥{{ order.totalAmount }}</strong></td>
                    </tr>
                  </table>
                </div>
                <div class="col-md-6">
                  <table class="table table-sm table-borderless">
                    <tr>
                      <td><strong>客户手机:</strong></td>
                      <td>{{ order.userPhone }}</td>
                    </tr>
                    <tr>
                      <td><strong>门店:</strong></td>
                      <td>{{ getStoreName(order.storeId) }}</td>
                    </tr>
                    <tr>
                      <td><strong>订单状态:</strong></td>
                      <td>
                        <span :class="getOrderStatusClass(order.orderStatus)">
                          {{ getOrderStatusName(order.orderStatus) }}
                        </span>
                      </td>
                    </tr>
                    <tr>
                      <td><strong>取餐号:</strong></td>
                      <td>{{ order.pickupNo || '-' }}</td>
                    </tr>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <!-- 异常信息 -->
          <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
              <h6 class="mb-0">异常详情</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label"><strong>异常类型:</strong></label>
                    <div>
                      <span :class="getExceptionTypeClass(order.exceptionType)">
                        {{ getExceptionTypeName(order.exceptionType) }}
                      </span>
                    </div>
                  </div>
                  <div class="mb-3">
                    <label class="form-label"><strong>异常时间:</strong></label>
                    <div>{{ formatDateTime(order.exceptionTime) }}</div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3" v-if="order.timeoutMinutes">
                    <label class="form-label"><strong>超时时长:</strong></label>
                    <div>
                      <span class="badge bg-danger">{{ order.timeoutMinutes }}分钟</span>
                    </div>
                  </div>
                  <div class="mb-3">
                    <label class="form-label"><strong>异常原因:</strong></label>
                    <div class="text-muted">{{ order.exceptionReason }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 处理方案选择 -->
          <div class="card mb-4">
            <div class="card-header">
              <h6 class="mb-0">选择处理方案</h6>
            </div>
            <div class="card-body">
              <div class="row g-3">
                <div class="col-md-6">
                  <label class="form-label"><strong>处理动作:</strong></label>
                  <select v-model="handleForm.action" class="form-select" @change="onActionChange">
                    <option value="">请选择处理动作</option>
                    <option value="remake">重新制作</option>
                    <option value="refund">退款处理</option>
                    <option value="cancel">取消订单</option>
                    <option value="contact">联系客户</option>
                    <option value="complete">手动完成</option>
                  </select>
                </div>
                <div class="col-md-6" v-if="handleForm.action === 'refund'">
                  <label class="form-label"><strong>退款金额:</strong></label>
                  <div class="input-group">
                    <span class="input-group-text">¥</span>
                    <input 
                      type="number" 
                      v-model="handleForm.refundAmount" 
                      class="form-control"
                      :max="parseFloat(order.totalAmount)"
                      step="0.01"
                      placeholder="退款金额"
                    />
                  </div>
                  <small class="text-muted">最大可退款: ¥{{ order.totalAmount }}</small>
                </div>
              </div>
              
              <div class="mt-3">
                <label class="form-label"><strong>处理备注:</strong></label>
                <textarea 
                  v-model="handleForm.remark" 
                  class="form-control" 
                  rows="3"
                  :placeholder="getRemarkPlaceholder()"
                ></textarea>
              </div>

              <!-- 处理建议 -->
              <div v-if="handleForm.action" class="mt-3">
                <div class="alert alert-info">
                  <h6 class="alert-heading">处理建议:</h6>
                  <p class="mb-0">{{ getActionSuggestion(handleForm.action) }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 历史处理记录 -->
          <div class="card" v-if="order.isHandled">
            <div class="card-header bg-success text-white">
              <h6 class="mb-0">历史处理记录</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-2">
                    <strong>处理动作:</strong> {{ getActionName(order.handledAction) }}
                  </div>
                  <div class="mb-2">
                    <strong>处理时间:</strong> {{ formatDateTime(order.handledTime) }}
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-2">
                    <strong>处理人员:</strong> {{ order.handledBy || '-' }}
                  </div>
                  <div class="mb-2">
                    <strong>处理备注:</strong> {{ order.handledRemark || '-' }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('close')">
            取消
          </button>
          <button 
            type="button" 
            class="btn btn-warning" 
            @click="handleException"
            :disabled="!handleForm.action || processing"
          >
            <span v-if="processing" class="spinner-border spinner-border-sm me-2"></span>
            {{ processing ? '处理中...' : '确认处理' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { format } from 'date-fns'
import { 
  handleExceptionOrder,
  type ExceptionOrder,
  type ExceptionAction,
  type HandleExceptionParams
} from '@/utils/api/order'

// Props
const props = defineProps<{
  order: ExceptionOrder
}>()

// Emits
const emit = defineEmits<{
  close: []
  handled: []
}>()

// 数据状态
const processing = ref(false)
const handleForm = ref({
  action: '' as ExceptionAction | '',
  remark: '',
  refundAmount: 0
})

// 处理异常订单
const handleException = async () => {
  if (!handleForm.value.action) return
  
  processing.value = true
  try {
    const params: HandleExceptionParams = {
      orderId: props.order.orderId,
      action: handleForm.value.action as ExceptionAction,
      remark: handleForm.value.remark
    }
    
    if (handleForm.value.action === 'refund' && handleForm.value.refundAmount > 0) {
      params.refundAmount = handleForm.value.refundAmount
    }
    
    await handleExceptionOrder(params)
    console.log(`✅ [异常处理] 订单${props.order.orderId}处理成功`)
    
    emit('handled')
    emit('close')
  } catch (error) {
    console.error('❌ [异常处理] 处理异常订单失败:', error)
  } finally {
    processing.value = false
  }
}

// 动作变更处理
const onActionChange = () => {
  if (handleForm.value.action === 'refund') {
    handleForm.value.refundAmount = parseFloat(props.order.totalAmount)
  }
  handleForm.value.remark = getDefaultRemark(handleForm.value.action as ExceptionAction)
}

// 工具函数
const getStoreName = (storeId: number): string => {
  return `门店${storeId}` // 这里可以从store列表中获取真实名称
}

const getOrderStatusName = (status: string): string => {
  const names: Record<string, string> = {
    '0': '待支付',
    '1': '已支付',
    '2': '制作中',
    '3': '待取餐',
    '4': '已完成',
    '5': '已取消'
  }
  return names[status] || status
}

const getOrderStatusClass = (status: string): string => {
  const classes: Record<string, string> = {
    '0': 'badge bg-warning',
    '1': 'badge bg-info',
    '2': 'badge bg-primary',
    '3': 'badge bg-success',
    '4': 'badge bg-secondary',
    '5': 'badge bg-danger'
  }
  return classes[status] || 'badge bg-secondary'
}

const getExceptionTypeName = (type: string): string => {
  const names: Record<string, string> = {
    timeout_production: '制作超时',
    timeout_pickup: '取餐超时',
    payment_failed: '支付失败',
    production_failed: '制作失败',
    system_error: '系统错误'
  }
  return names[type] || type
}

const getExceptionTypeClass = (type: string): string => {
  const classes: Record<string, string> = {
    timeout_production: 'badge bg-warning',
    timeout_pickup: 'badge bg-info',
    payment_failed: 'badge bg-danger',
    production_failed: 'badge bg-secondary',
    system_error: 'badge bg-dark'
  }
  return classes[type] || 'badge bg-secondary'
}

const getActionName = (action?: string): string => {
  if (!action) return ''
  const names: Record<string, string> = {
    remake: '重新制作',
    refund: '退款',
    cancel: '取消订单',
    contact: '联系客户',
    complete: '手动完成'
  }
  return names[action] || action
}

const getActionSuggestion = (action: ExceptionAction): string => {
  const suggestions: Record<ExceptionAction, string> = {
    remake: '将重新安排制作该订单，建议通知客户预计完成时间。',
    refund: '将为客户办理退款，退款金额可以调整，建议说明退款原因。',
    cancel: '将取消该订单，如已支付将自动退款，建议提前联系客户说明情况。',
    contact: '将标记为需要联系客户，建议记录联系结果和后续处理方案。',
    complete: '将手动标记订单为完成状态，请确保客户已收到商品或服务。'
  }
  return suggestions[action] || ''
}

const getDefaultRemark = (action: ExceptionAction): string => {
  const remarks: Record<ExceptionAction, string> = {
    remake: '由于异常情况，重新安排制作',
    refund: '因异常情况为客户办理退款',
    cancel: '因异常情况取消订单',
    contact: '需要联系客户确认处理方案',
    complete: '异常情况已解决，手动完成订单'
  }
  return remarks[action] || ''
}

const getRemarkPlaceholder = (): string => {
  if (!handleForm.value.action) return '请输入处理备注...'
  return `请详细说明${getActionName(handleForm.value.action as ExceptionAction)}的原因和处理过程...`
}

const formatDateTime = (dateTime?: string): string => {
  if (!dateTime) return '-'
  return format(new Date(dateTime), 'yyyy-MM-dd HH:mm:ss')
}
</script>

<style scoped>
.modal {
  z-index: 1055;
}

.card {
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.table-borderless td {
  border: none;
  padding: 0.25rem 0.5rem;
}

.alert {
  border: none;
  border-radius: 0.375rem;
}

.badge {
  font-size: 0.75em;
}
</style>
