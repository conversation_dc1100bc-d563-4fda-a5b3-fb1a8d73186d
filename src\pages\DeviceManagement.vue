<template>
  <div class="device-management-page">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h1 class="h3 mb-0">设备管理</h1>
        <p class="text-muted mb-0">IoT设备状态监控与制作队列管理</p>
      </div>
      <div class="d-flex gap-2">
        <button 
          @click="switchModule('device')" 
          :class="currentModule === 'device' ? 'btn btn-primary' : 'btn btn-outline-primary'"
        >
          <i class="flaticon-gear me-2"></i>设备状态管理
        </button>
        <button 
          @click="switchModule('queue')" 
          :class="currentModule === 'queue' ? 'btn btn-primary' : 'btn btn-outline-primary'"
        >
          <i class="flaticon-list me-2"></i>制作队列管理
        </button>
        <button 
          @click="switchModule('maintenance')" 
          :class="currentModule === 'maintenance' ? 'btn btn-primary' : 'btn btn-outline-primary'"
        >
          <i class="flaticon-note me-2"></i>维修记录管理
        </button>
      </div>
    </div>




    <!-- 动态组件内容 -->
    <div class="module-content">
      <!-- 设备状态管理模块 -->
      <DeviceStatusDashboard v-if="currentModule === 'device'" />
      
      <!-- 制作队列管理模块 -->
      <ProductionQueueManagement v-if="currentModule === 'queue'" />
      
      <!-- 维修记录管理模块 -->
      <div v-if="currentModule === 'maintenance'" class="card">
        <div class="card-body text-center py-5">
          <i class="flaticon-note text-muted display-1"></i>
          <h4 class="mt-3 mb-3">维修记录管理</h4>
          <p class="text-muted mb-4">
            此功能用于记录设备的维修历史，包括故障描述、维修时间、负责人、处理措施等。
          </p>
          <div class="alert alert-warning">
            <i class="flaticon-warning me-2"></i>
            <strong>功能开发中</strong> - 当前提供的API文档中，没有提供用于"新增、查询、修改"维修记录的接口，该功能暂时无法实现。
          </div>
          <div class="d-flex justify-content-center gap-2">
            <button class="btn btn-outline-primary" disabled>
              <i class="flaticon-plus me-2"></i>新增维修记录
            </button>
            <button class="btn btn-outline-secondary" disabled>
              <i class="flaticon-search-interface-symbol me-2"></i>查询维修记录
            </button>
            <button class="btn btn-outline-info" disabled>
              <i class="flaticon-chart me-2"></i>维修统计
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DeviceStatusDashboard from '@/components/device/DeviceStatusDashboard.vue'
import ProductionQueueManagement from '@/components/device/ProductionQueueManagement.vue'

// 当前模块
const currentModule = ref<'device' | 'queue' | 'maintenance'>('device')



// 切换模块
const switchModule = (module: 'device' | 'queue' | 'maintenance') => {
  currentModule.value = module
  console.log(`🔄 [设备管理] 切换到模块: ${module}`)
}
</script>

<style scoped>
.device-management-page {
  padding: 1.5rem;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.module-card {
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

.module-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.module-card.bg-primary {
  border-color: #0d6efd;
}

.module-card.bg-light:hover {
  border-color: #0d6efd;
  background-color: #e7f1ff !important;
}

.cursor-pointer {
  cursor: pointer;
}

.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.display-6 {
  font-size: 2.5rem;
}

code {
  font-size: 0.875em;
}

.list-unstyled li {
  padding: 0.25rem 0;
}

.alert {
  border: none;
  border-radius: 0.5rem;
}

.module-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .device-management-page {
    padding: 1rem;
  }
  
  .module-card {
    margin-bottom: 1rem;
  }
  
  .d-flex.gap-2 {
    flex-direction: column;
    gap: 0.5rem !important;
  }
  
  .d-flex.gap-2 .btn {
    width: 100%;
  }
}
</style>
