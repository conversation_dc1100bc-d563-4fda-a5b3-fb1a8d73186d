<template>
  <div class="data-center">
    <!-- 面包屑导航 -->
    <BreadCrumb title="数据中心" :breadcrumb="breadcrumb" />

    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-30">
      <h1 class="main-title">数据中心</h1>
      <div class="action-buttons">
        <button @click="refreshData" class="btn btn-outline-secondary me-2">
          <i class="flaticon-refresh me-2"></i>
          刷新数据
        </button>
        <button @click="exportReport" class="btn btn-primary">
          <i class="flaticon-export me-2"></i>
          导出报表
        </button>
      </div>
    </div>

    <!-- 标签页导航 -->
    <div class="card border-0 rounded-0 bg-white box-shadow">
      <div class="card-header bg-light border-bottom">
        <ul class="nav nav-tabs card-header-tabs" id="dataTabs" role="tablist">
          <li class="nav-item" role="presentation">
            <button 
              class="nav-link active" 
              id="dashboard-tab" 
              data-bs-toggle="tab" 
              data-bs-target="#dashboard-pane" 
              type="button" 
              role="tab"
              @click="activeTab = 'dashboard'"
            >
              <i class="flaticon-chart me-2"></i>
              运营看板
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button 
              class="nav-link" 
              id="device-data-tab" 
              data-bs-toggle="tab" 
              data-bs-target="#device-data-pane" 
              type="button" 
              role="tab"
              @click="activeTab = 'device-data'"
            >
              <i class="flaticon-robot me-2"></i>
              设备数据
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button 
              class="nav-link" 
              id="order-report-tab" 
              data-bs-toggle="tab" 
              data-bs-target="#order-report-pane" 
              type="button" 
              role="tab"
              @click="activeTab = 'order-report'"
            >
              <i class="flaticon-shopping-cart me-2"></i>
              订单报表
            </button>
          </li>
        </ul>
      </div>

      <!-- 标签页内容 -->
      <div class="tab-content" id="dataTabsContent">
        <!-- 运营看板 -->
        <div class="tab-pane fade show active" id="dashboard-pane" role="tabpanel">
          <OperationalDashboard />
        </div>

        <!-- 设备数据 -->
        <div class="tab-pane fade" id="device-data-pane" role="tabpanel">
          <DeviceDataAnalysis />
        </div>

        <!-- 订单报表 -->
        <div class="tab-pane fade" id="order-report-pane" role="tabpanel">
          <OrderReportAnalysis />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BreadCrumb from "@/components/layouts/BreadCrumb.vue"
import OperationalDashboard from "@/components/datacenter/OperationalDashboard.vue"
import DeviceDataAnalysis from "@/components/datacenter/DeviceDataAnalysis.vue"
import OrderReportAnalysis from "@/components/datacenter/OrderReportAnalysis.vue"

// 面包屑导航
const breadcrumb = ref([
  { label: '数据中心', url: '/data-center' }
])

// 当前活跃的标签页
const activeTab = ref('dashboard')

// 刷新数据
const refreshData = () => {
  console.log('刷新数据:', activeTab.value)
  // 触发当前活跃标签页的数据刷新
}

// 导出报表
const exportReport = () => {
  console.log('导出报表:', activeTab.value)
  // 根据当前标签页导出相应的报表
}
</script>

<style scoped>
.data-center {
  padding: 20px;
}

.main-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0;
}

.box-shadow {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.nav-tabs .nav-link {
  color: #495057;
  border: none;
  border-bottom: 3px solid transparent;
  background: none;
  padding: 15px 20px;
  font-weight: 500;
}

.nav-tabs .nav-link:hover {
  border-color: transparent;
  background-color: #f8f9fa;
}

.nav-tabs .nav-link.active {
  color: #007bff;
  background-color: #fff;
  border-bottom-color: #007bff;
}

.tab-content {
  padding: 0;
}

.tab-pane {
  min-height: 600px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

@media (max-width: 768px) {
  .data-center {
    padding: 15px;
  }
  
  .main-title {
    font-size: 1.5rem;
  }
  
  .nav-tabs .nav-link {
    padding: 12px 15px;
    font-size: 0.9rem;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 5px;
  }
}
</style>
