<template>
  <div class="system-management">
    <!-- 面包屑导航 -->
    <BreadCrumb title="系统管理" :breadcrumb="breadcrumb" />

    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-30">
      <h1 class="main-title">系统管理</h1>
    </div>

    <!-- 功能模块卡片 -->
    <div class="row">
      <!-- 用户管理 -->
      <div class="col-lg-4 col-md-6 mb-30">
        <div class="card border-0 rounded-0 bg-white h-100 box-shadow">
          <div class="card-body p-30 text-center">
            <div class="module-icon mb-20">
              <i class="flaticon-user text-primary" style="font-size: 3rem;"></i>
            </div>
            <h5 class="fw-bold mb-15 text-black">用户管理</h5>
            <p class="text-muted mb-20">
              管理系统用户账号，包括管理员和商户操作员的新增、编辑、删除和权限分配。
            </p>
            <div class="module-stats mb-20">
              <div class="stat-item">
                <span class="stat-value">{{ userStats.total }}</span>
                <span class="stat-label">个用户</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ userStats.active }}</span>
                <span class="stat-label">活跃用户</span>
              </div>
            </div>
            <button @click="navigateToModule('user')" class="btn w-100 system-card-btn">
              <i class="flaticon-edit me-2"></i>
              管理用户
            </button>
          </div>
        </div>
      </div>

      <!-- 角色权限管理 -->
      <div class="col-lg-4 col-md-6 mb-30">
        <div class="card border-0 rounded-0 bg-white h-100 box-shadow">
          <div class="card-body p-30 text-center">
            <div class="module-icon mb-20">
              <i class="flaticon-shield text-success" style="font-size: 3rem;"></i>
            </div>
            <h5 class="fw-bold mb-15 text-black">角色权限管理</h5>
            <p class="text-muted mb-20">
              定义和管理系统角色，配置角色权限，区分平台级和商户级权限。
            </p>
            <div class="module-stats mb-20">
              <div class="stat-item">
                <span class="stat-value">{{ roleStats.total }}</span>
                <span class="stat-label">个角色</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ roleStats.active }}</span>
                <span class="stat-label">启用中</span>
              </div>
            </div>
            <button @click="navigateToModule('role')" class="btn w-100 system-card-btn">
              <i class="flaticon-edit me-2"></i>
              管理角色
            </button>
          </div>
        </div>
      </div>

      <!-- 系统配置 -->
      <div class="col-lg-4 col-md-6 mb-30">
        <div class="card border-0 rounded-0 bg-white h-100 box-shadow">
          <div class="card-body p-30 text-center">
            <div class="module-icon mb-20">
              <i class="flaticon-settings text-info" style="font-size: 3rem;"></i>
            </div>
            <h5 class="fw-bold mb-15 text-black">系统配置</h5>
            <p class="text-muted mb-20">
              管理系统基础参数配置，包括系统名称、时区、接口配置等。
            </p>
            <div class="module-stats mb-20">
              <div class="stat-item">
                <span class="stat-value">{{ configStats.total }}</span>
                <span class="stat-label">个配置</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ configStats.system }}</span>
                <span class="stat-label">系统配置</span>
              </div>
            </div>
            <button @click="navigateToModule('config')" class="btn w-100 system-card-btn">
              <i class="flaticon-edit me-2"></i>
              系统配置
            </button>
          </div>
        </div>
      </div>



    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import BreadCrumb from "@/components/layouts/BreadCrumb.vue"
import serviceAxios from '@/utils/serviceAxios'

const router = useRouter()

// 面包屑导航
const breadcrumb = ref([
  { label: '系统管理', url: '/system-management' }
])

// 统计数据
const userStats = ref({
  total: 0,
  active: 0
})

const roleStats = ref({
  total: 0,
  active: 0
})

const configStats = ref({
  total: 0,
  system: 0
})

// 导航到具体模块
const navigateToModule = (module: string) => {
  switch (module) {
    case 'user':
      console.log('导航到用户管理')
      break
    case 'role':
      console.log('导航到角色管理')
      break
    case 'config':
      router.push('/system-config')
      break
  }
}



// 获取统计数据
const fetchStatistics = async () => {
  try {
    // 获取用户统计
    const userResponse = await serviceAxios.get('/system/user/list', {
      params: { pageNum: 1, pageSize: 1 }
    })
    userStats.value.total = userResponse.total || 0

    // 获取角色统计
    const roleResponse = await serviceAxios.get('/system/role/list', {
      params: { pageNum: 1, pageSize: 1 }
    })
    roleStats.value.total = roleResponse.total || 0

    // 获取配置统计
    const configResponse = await serviceAxios.get('/system/config/list', {
      params: { pageNum: 1, pageSize: 1 }
    })
    configStats.value.total = configResponse.total || 0

    // 获取商户统计（使用门店数据）
    const storeResponse = await serviceAxios.get('/manager/store/list', {
      params: { pageNum: 1, pageSize: 1 }
    })
    merchantStats.value.total = storeResponse.total || 0

    // 获取营业中的门店
    const openStoreResponse = await serviceAxios.get('/manager/store/open')
    merchantStats.value.active = openStoreResponse.total || 0

  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 初始化
onMounted(() => {
  fetchStatistics()
})
</script>

<style scoped>
.system-management {
  padding: 20px;
}

.module-icon {
  opacity: 0.8;
}

.module-stats {
  display: flex;
  justify-content: space-around;
  padding: 15px 0;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: bold;
  color: #495057;
}

.stat-label {
  font-size: 0.875rem;
  color: #6c757d;
}



.btn-purple {
  background-color: #6f42c1;
  border-color: #6f42c1;
  color: #fff;
}

.btn-purple:hover {
  background-color: #5a32a3;
  border-color: #5a32a3;
}

.text-purple {
  color: #6f42c1 !important;
}

/* 系统管理卡片按钮统一样式 */
.system-card-btn {
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  padding: 12px 24px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;

  /* 统一背景颜色 - 使用项目主色 */
  background-color: var(--splash-primary-color, #6560F0);
  border-color: var(--splash-primary-color, #6560F0);
  color: #ffffff;
}

.system-card-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background-color: var(--splash-primary-active-color, #5a54e6);
  border-color: var(--splash-primary-active-color, #5a54e6);
  color: #ffffff;
}

.system-card-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-color: var(--splash-primary-active-color, #5a54e6);
  border-color: var(--splash-primary-active-color, #5a54e6);
  color: #ffffff;
}

.system-card-btn:focus {
  box-shadow: 0 0 0 0.2rem rgba(101, 96, 240, 0.25);
  background-color: var(--splash-primary-color, #6560F0);
  border-color: var(--splash-primary-color, #6560F0);
  color: #ffffff;
}

.system-card-btn i {
  font-size: 16px;
  margin-right: 8px;
}

/* 确保按钮在卡片底部对齐 */
.card-body {
  display: flex;
  flex-direction: column;
}

.system-card-btn {
  margin-top: auto;
}
</style>
