<template>
  <div class="prototype-management p-20">
    <!-- 搜索和操作区域 -->
    <div class="d-flex justify-content-between align-items-center mb-20">
      <div class="search-controls d-flex gap-3">
        <div class="search-input">
          <input 
            v-model="searchForm.prototypeName" 
            type="text" 
            class="form-control" 
            placeholder="搜索原型名称"
            @input="handleSearch"
          />
        </div>
        <div class="category-filter">
          <select v-model="searchForm.drinkGroupId" class="form-select" @change="handleSearch">
            <option value="">全部分类</option>
            <option v-for="category in categoryList" :key="category.groupId" :value="category.groupId">
              {{ category.groupName }}
            </option>
          </select>
        </div>
        <div class="status-filter">
          <select v-model="searchForm.status" class="form-select" @change="handleSearch">
            <option value="">全部状态</option>
            <option value="0">正常</option>
            <option value="1">停用</option>
          </select>
        </div>
        <button @click="resetSearch" class="btn btn-outline-secondary">
          重置
        </button>
      </div>
      <div class="action-buttons">
        <button @click="showPopularPrototypes" class="btn btn-outline-info me-2">
          查看热门原型
        </button>
        <button @click="showAddModal" class="btn btn-primary">
          新增原型
        </button>
        <button @click="exportData" class="btn btn-outline-secondary ms-2">
          导出
        </button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3 text-muted">正在加载数据...</p>
      </div>

      <!-- 数据表格 -->
      <div v-else class="table-responsive">
        <table class="table table-hover mb-0">
          <thead class="table-light">
            <tr>
              <th width="60">序号</th>
              <th width="150">原型名称</th>
              <th width="120">所属分类</th>
              <th width="100">原型图片</th>
              <th width="100">标签</th>
              <th width="80">排序</th>
              <th width="80">状态</th>
              <th>描述</th>
              <th width="150">创建时间</th>
              <th width="150">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in prototypeList" :key="item.prototypeId">
              <td>{{ (pagination.pageNum - 1) * pagination.pageSize + index + 1 }}</td>
              <td>
                <span class="fw-bold text-dark">{{ item.prototypeName }}</span>
              </td>
              <td>
                <span class="badge bg-info">{{ getCategoryName(item.drinkGroupId) }}</span>
              </td>
              <td>
                <img 
                  v-if="item.prototypeImage" 
                  :src="item.prototypeImage" 
                  alt="原型图片" 
                  class="prototype-image"
                />
                <span v-else class="text-muted">无图片</span>
              </td>
              <td>
                <span v-if="item.prototypeLabel" class="badge bg-warning">{{ item.prototypeLabel }}</span>
                <span v-else class="text-muted">无标签</span>
              </td>
              <td>
                <span class="badge bg-secondary">{{ item.orderNum || 0 }}</span>
              </td>
              <td>
                <span
                  :class="item.status === '1' ? 'badge bg-success' : 'badge bg-danger'"
                >
                  {{ item.status === '1' ? '正常' : '停用' }}
                </span>
              </td>
              <td>
                <span class="text-truncate d-inline-block" style="max-width: 200px;" :title="item.prototypeDescription">
                  {{ item.prototypeDescription || '无描述' }}
                </span>
              </td>
              <td>{{ formatDate(item.createTime) }}</td>
              <td>
                <div class="btn-group btn-group-sm">
                  <button @click="viewProductDistribution(item)" class="btn btn-outline-info" title="查看商品分布">
                    查看
                  </button>
                  <button @click="showEditModal(item)" class="btn btn-outline-primary">
                    编辑
                  </button>
                  <button @click="deletePrototypeItem(item)" class="btn btn-outline-danger">
                    删除
                  </button>
                </div>
              </td>
            </tr>
            <tr v-if="prototypeList.length === 0">
              <td colspan="10" class="text-center py-4 text-muted">
                暂无数据
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.total > 0" class="d-flex justify-content-between align-items-center mt-20">
        <div class="text-muted">
          共 {{ pagination.total }} 条记录，第 {{ pagination.pageNum }} / {{ Math.ceil(pagination.total / pagination.pageSize) }} 页
        </div>
        <b-pagination
          v-model="pagination.pageNum"
          :total-rows="pagination.total"
          :per-page="pagination.pageSize"
          @update:model-value="changePage"
        />
      </div>
    </div>

    <!-- 新增/编辑模态框 -->
    <div class="modal fade" id="prototypeModal" tabindex="-1" ref="prototypeModalRef">
      <div class="modal-dialog modal-xl">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">{{ isEdit ? '编辑原型' : '新增原型' }}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="submitForm">
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label class="form-label">原型名称 <span class="text-danger">*</span></label>
                  <input 
                    v-model="formData.prototypeName" 
                    type="text" 
                    class="form-control" 
                    placeholder="请输入原型名称"
                    required
                  />
                </div>
                <div class="col-md-6 mb-3">
                  <label class="form-label">所属分类 <span class="text-danger">*</span></label>
                  <select v-model="formData.drinkGroupId" class="form-select" required>
                    <option value="">请选择分类</option>
                    <option v-for="category in categoryList" :key="category.groupId" :value="category.groupId">
                      {{ category.groupName }}
                    </option>
                  </select>
                </div>
                <div class="col-md-6 mb-3">
                  <label class="form-label">原型图片</label>
                  <input 
                    v-model="formData.prototypeImage" 
                    type="url" 
                    class="form-control" 
                    placeholder="请输入图片URL"
                  />
                </div>
                <div class="col-md-6 mb-3">
                  <label class="form-label">原型标签</label>
                  <input 
                    v-model="formData.prototypeLabel" 
                    type="text" 
                    class="form-control" 
                    placeholder="如：热销、新品、低糖等"
                  />
                </div>
                <div class="col-md-6 mb-3">
                  <label class="form-label">排序</label>
                  <input 
                    v-model.number="formData.orderNum" 
                    type="number" 
                    class="form-control" 
                    placeholder="数字越小排序越靠前"
                    min="0"
                  />
                </div>
                <div class="col-md-6 mb-3">
                  <label class="form-label">状态</label>
                  <select v-model="formData.status" class="form-select">
                    <option value="0">正常</option>
                    <option value="1">停用</option>
                  </select>
                </div>
                <div class="col-12 mb-3">
                  <label class="form-label">原型描述</label>
                  <textarea 
                    v-model="formData.prototypeDescription" 
                    class="form-control" 
                    rows="3"
                    placeholder="请输入原型描述，如风味特点、制作工艺等"
                  ></textarea>
                </div>
                <div class="col-12 mb-3">
                  <label class="form-label">默认选项 (JSON格式)</label>
                  <textarea 
                    v-model="formData.defaultOptions" 
                    class="form-control" 
                    rows="3"
                    placeholder='如：{"temperature": "hot", "sugar": "normal", "ice": "regular"}'
                  ></textarea>
                  <div class="form-text">请输入有效的JSON格式，定义默认的温度、糖度、冰块等选项</div>
                </div>
                <div class="col-12 mb-3">
                  <label class="form-label">备注</label>
                  <textarea 
                    v-model="formData.remark" 
                    class="form-control" 
                    rows="2"
                    placeholder="请输入备注信息"
                  ></textarea>
                </div>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" @click="submitForm" class="btn btn-primary" :disabled="submitting">
              <span v-if="submitting" class="spinner-border spinner-border-sm me-2"></span>
              {{ isEdit ? '更新' : '创建' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { format } from 'date-fns'
import { Modal } from 'bootstrap'
import { BPagination } from 'bootstrap-vue-next'
import {
  getDrinkPrototypeList,
  getEnabledDrinkGroups,
  createPrototype,
  updatePrototype,
  deletePrototype,
  getPopularPrototypes,
  exportPrototypeList,
  type PrototypeQueryParams
} from '@/utils/api/drink'
import { drinkPrototype, drinkGroup } from '@/types/drink'
import serviceAxios from '@/utils/serviceAxios'

// 数据状态
const loading = ref(false)
const submitting = ref(false)
const prototypeList = ref<drinkPrototype[]>([])
const categoryList = ref<drinkGroup[]>([])
const isEdit = ref(false)
const prototypeModalRef = ref<HTMLElement>()
let prototypeModal: Modal | null = null

// 搜索表单
const searchForm = ref({
  prototypeName: '',
  drinkGroupId: '',
  status: ''
})

// 分页信息
const pagination = ref({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 表单数据
const formData = ref({
  prototypeId: null,
  prototypeName: '',
  drinkGroupId: '',
  prototypeDescription: '',
  prototypeImage: '',
  prototypeLabel: '',
  orderNum: 0,
  status: '0',
  defaultOptions: '',
  remark: ''
})

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return format(new Date(dateString), 'yyyy-MM-dd HH:mm')
}

// 获取分类名称
const getCategoryName = (groupId: number) => {
  const category = categoryList.value.find((cat: drinkGroup) => cat.groupId === groupId)
  return category ? category.groupName : '未知分类'
}

// 获取分类列表
const fetchCategoryList = async () => {
  try {
    const response = await getEnabledDrinkGroups()
    categoryList.value = response.rows || []
  } catch (error) {
    console.error('获取分类列表失败:', error)
    categoryList.value = []
  }
}

// 获取原型列表
const fetchPrototypeList = async () => {
  loading.value = true
  try {
    const params: PrototypeQueryParams = {
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize,
      prototypeName: searchForm.value.prototypeName || undefined,
      drinkGroupId: searchForm.value.drinkGroupId ? Number(searchForm.value.drinkGroupId) : undefined,
      status: searchForm.value.status || undefined
    }

    const response = await getDrinkPrototypeList(params)
    prototypeList.value = response.rows || []
    pagination.value.total = response.total || 0
  } catch (error) {
    console.error('获取原型列表失败:', error)
    prototypeList.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.value.pageNum = 1
  fetchPrototypeList()
}

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    prototypeName: '',
    drinkGroupId: '',
    status: ''
  }
  pagination.value.pageNum = 1
  fetchPrototypeList()
}

// 分页处理
const changePage = (page: number) => {
  pagination.value.pageNum = page
  fetchPrototypeList()
}

// 显示新增模态框
const showAddModal = () => {
  isEdit.value = false
  formData.value = {
    prototypeId: null,
    prototypeName: '',
    drinkGroupId: '',
    prototypeDescription: '',
    prototypeImage: '',
    prototypeLabel: '',
    orderNum: 0,
    status: '0',
    defaultOptions: '',
    remark: ''
  }
  prototypeModal?.show()
}

// 显示编辑模态框
const showEditModal = (item: drinkPrototype) => {
  isEdit.value = true
  formData.value = {
    prototypeId: item.prototypeId,
    prototypeName: item.prototypeName,
    drinkGroupId: item.drinkGroupId,
    prototypeDescription: item.prototypeDescription || '',
    prototypeImage: item.prototypeImage || '',
    prototypeLabel: item.prototypeLabel || '',
    orderNum: item.orderNum || 0,
    status: item.status,
    defaultOptions: item.defaultOptions || '',
    remark: item.remark || ''
  }
  prototypeModal?.show()
}

// 提交表单
const submitForm = async () => {
  if (!formData.value.prototypeName.trim()) {
    alert('请输入原型名称')
    return
  }
  
  if (!formData.value.drinkGroupId) {
    alert('请选择所属分类')
    return
  }
  
  // 验证JSON格式
  if (formData.value.defaultOptions) {
    try {
      JSON.parse(formData.value.defaultOptions)
    } catch (error) {
      alert('默认选项必须是有效的JSON格式')
      return
    }
  }
  
  submitting.value = true
  try {
    if (isEdit.value) {
      await updatePrototype(formData.value as drinkPrototype)
    } else {
      await createPrototype(formData.value)
    }

    prototypeModal?.hide()
    fetchPrototypeList()
    alert(isEdit.value ? '更新成功' : '创建成功')
  } catch (error) {
    console.error('提交失败:', error)
    alert('操作失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 查看热门原型
const showPopularPrototypes = async () => {
  try {
    const popularList = await getPopularPrototypes(10)

    if (popularList.length === 0) {
      alert('暂无热门原型数据')
      return
    }

    // 显示热门原型信息
    const popularNames = popularList.map((item: drinkPrototype) => item.prototypeName).join('、')
    alert(`热门原型：${popularNames}`)
  } catch (error) {
    console.error('获取热门原型失败:', error)
    alert('获取热门原型失败，请重试')
  }
}

// 查看商品分布
const viewProductDistribution = async (item: drinkPrototype) => {
  try {
    const response = await serviceAxios.get(`/manager/product/prototype/${item.prototypeName}`)
    const productList = response.rows || response.data || []

    if (productList.length === 0) {
      alert(`原型"${item.prototypeName}"暂无商品分布`)
      return
    }

    // 统计门店分布
    const storeMap = new Map()
    productList.forEach((product: { storeName?: string }) => {
      const storeName = product.storeName || '未知门店'
      storeMap.set(storeName, (storeMap.get(storeName) || 0) + 1)
    })

    const distributionInfo = Array.from(storeMap.entries())
      .map(([storeName, count]) => `${storeName}: ${count}个商品`)
      .join('\n')

    alert(`原型"${item.prototypeName}"的商品分布：\n\n${distributionInfo}\n\n总计：${productList.length}个商品`)
  } catch (error) {
    console.error('获取商品分布失败:', error)
    alert('获取商品分布失败，请重试')
  }
}

// 删除原型
const deletePrototypeItem = async (item: drinkPrototype) => {
  if (!confirm(`确定要删除原型"${item.prototypeName}"吗？`)) return

  try {
    await deletePrototype(String(item.prototypeId))
    fetchPrototypeList()
    alert('删除成功')
  } catch (error) {
    console.error('删除失败:', error)
    alert('删除失败，请重试')
  }
}

// 导出数据
const exportData = async () => {
  try {
    const params: PrototypeQueryParams = {
      pageNum: 1,
      pageSize: 10000,
      prototypeName: searchForm.value.prototypeName || undefined,
      drinkGroupId: searchForm.value.drinkGroupId ? Number(searchForm.value.drinkGroupId) : undefined,
      status: searchForm.value.status || undefined
    }

    const blob = await exportPrototypeList(params)

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `饮品原型列表_${new Date().toISOString().slice(0, 10)}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    alert('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    alert('导出失败，请重试')
  }
}

// 初始化
onMounted(async () => {
  await nextTick()
  if (prototypeModalRef.value) {
    prototypeModal = new Modal(prototypeModalRef.value)
  }
  await fetchCategoryList()
  fetchPrototypeList()
})
</script>

<style scoped>
.prototype-management {
  background: #fff;
}

.search-controls {
  flex-wrap: wrap;
}

.search-input {
  min-width: 200px;
}

.category-filter,
.status-filter {
  min-width: 120px;
}

.prototype-image {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 4px;
}

.table th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
}

.table td {
  vertical-align: middle;
}

.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
}

@media (max-width: 768px) {
  .search-controls {
    flex-direction: column;
    gap: 10px !important;
  }
  
  .search-input,
  .category-filter,
  .status-filter {
    min-width: auto;
    width: 100%;
  }
  
  .action-buttons {
    margin-top: 10px;
  }
}
</style>
