import serviceAxios from "@/utils/serviceAxios";

// 队列状态枚举（匹配实际API）
export enum QueueStatus {
  PENDING = '0',       // 待制作
  IN_PROGRESS = '1',   // 制作中
  COMPLETED = '2',     // 已完成
  FAILED = '3',        // 失败
  CANCELLED = '4'      // 已取消
}

// 队列优先级
export enum QueuePriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent'
}

// 制作队列项（匹配实际API返回格式）
export interface ProductionQueue {
  createBy: string;
  createTime: string;
  updateBy: string;
  updateTime: string;
  remark: string | null;
  queueId: number;          // 队列ID
  orderId: number;          // 订单ID
  orderReqSeq: string;      // 订单流水号
  storeId: number;          // 门店ID
  targetKioskId: string;    // 目标设备ID
  targetDrinkId: string;    // 目标饮品ID
  drinkName: string;        // 饮品名称
  drinkOptions: string;     // 饮品选项
  quantity: number;         // 数量
  queueStatus: string;      // 队列状态 ("0"=待制作, "1"=制作中, "2"=已完成, "3"=失败)
  priority: number;         // 优先级
  estimatedDuration: number; // 预计制作时间(秒)
  startTime: string | null; // 开始时间
  finishTime: string | null; // 完成时间
  failureReason: string | null; // 失败原因
  commandId: string | null; // 命令ID
}

// 队列查询参数
export interface QueueQueryParams {
  pageNum?: number;
  pageSize?: number;
  status?: QueueStatus;
  queueStatus?: string;  // 添加queueStatus参数
  storeId?: number;
  deviceId?: string;
  orderId?: string;
  startTime?: string;
  endTime?: string;
  priority?: QueuePriority;
}

// 队列操作参数
export interface QueueOperationParams {
  queueId: string;
  operator?: string;
  reason?: string;
  remark?: string;
}

// API响应类型（匹配实际API格式）
export interface QueueListResponse {
  code: number;
  msg: string;
  total: number;
  rows: ProductionQueue[];
}

export interface QueueDetailResponse {
  code: number;
  msg: string;
  data: ProductionQueue;
}

export interface QueueMonitorResponse {
  code: number;
  msg: string;
  data: {
    totalQueues: number;
    pendingQueues: number;
    inProgressQueues: number;
    completedToday: number;
    failedToday: number;
    averageTime: number;
  };
}

// ==================== 制作队列查询 API ====================

/**
 * 获取完整的制作队列列表
 * 作为主要的数据来源
 */
export function getProductionQueueList(params?: QueueQueryParams): Promise<QueueListResponse> {
  return serviceAxios.get("/manager/production-queue/list", { params });
}

/**
 * 按状态筛选制作队列
 * @param queueStatus 队列状态 (pending, in_progress, completed, failed)
 */
export function getQueuesByStatus(queueStatus: QueueStatus, params?: QueueQueryParams): Promise<QueueListResponse> {
  return serviceAxios.get(`/manager/production-queue/status/${queueStatus}`, { params });
}

/**
 * 按设备查询制作队列
 * @param kioskId 设备ID
 */
export function getQueuesByDevice(kioskId: string, params?: QueueQueryParams): Promise<QueueListResponse> {
  return serviceAxios.get(`/manager/production-queue/device/${kioskId}`, { params });
}

/**
 * 按门店查询制作队列
 * @param storeId 门店ID
 */
export function getQueuesByStore(storeId: number, params?: QueueQueryParams): Promise<QueueListResponse> {
  return serviceAxios.get(`/manager/production-queue/store/${storeId}`, { params });
}

/**
 * 获取队列详细信息
 * @param queueId 队列ID
 */
export function getQueueDetail(queueId: string): Promise<QueueDetailResponse> {
  return serviceAxios.get(`/manager/production-queue/${queueId}`);
}

/**
 * 获取实时监控数据
 * 用于驱动监控大屏，展示关键指标
 */
export function getQueueMonitorData(): Promise<QueueMonitorResponse> {
  return serviceAxios.get("/manager/production-queue/monitor");
}

// ==================== 异常队列处理 API ====================

/**
 * 重置队列
 * 将某个失败或卡顿的队列恢复到初始状态，以便重新制作
 * @param queueId 队列ID
 */
export function resetQueue(queueId: string, params?: QueueOperationParams): Promise<{
  code: number;
  msg: string;
}> {
  return serviceAxios.put(`/manager/production-queue/${queueId}/reset`, params);
}

/**
 * 标记队列失败
 * 当确认订单无法完成时，手动将其状态标记为失败
 * @param queueId 队列ID
 */
export function markQueueFailed(queueId: string, params?: QueueOperationParams): Promise<{
  code: number;
  msg: string;
}> {
  return serviceAxios.put(`/manager/production-queue/${queueId}/fail`, params);
}

/**
 * 手动启动队列
 * 如果一个订单处于"待制作"但没有自动开始，可手动触发
 * @param queueId 队列ID
 */
export function startQueue(queueId: string, params?: QueueOperationParams): Promise<{
  code: number;
  msg: string;
}> {
  return serviceAxios.post(`/manager/production-queue/start/${queueId}`, params);
}

/**
 * 手动完成队列
 * 在某些特殊情况下，如果设备已完成但状态未上报，可手动标记完成
 * @param queueId 队列ID
 */
export function completeQueue(queueId: string, params?: QueueOperationParams): Promise<{
  code: number;
  msg: string;
}> {
  return serviceAxios.put(`/manager/production-queue/${queueId}/complete`, params);
}

/**
 * 清理设备状态
 * 强力操作，用于解决设备级别的软件或流程卡死问题
 * @param kioskId 设备ID
 */
export function clearDeviceStatus(kioskId: string, params?: {
  operator?: string;
  reason?: string;
}): Promise<{
  code: number;
  msg: string;
}> {
  return serviceAxios.post(`/manager/production-queue/device-status/${kioskId}/clear`, params);
}

// ==================== 批量操作 API ====================

/**
 * 批量重置队列
 * @param queueIds 队列ID数组
 */
export function batchResetQueues(queueIds: string[], params?: {
  operator?: string;
  reason?: string;
}): Promise<{
  code: number;
  msg: string;
  data: {
    successCount: number;
    failedCount: number;
    errors: string[];
  };
}> {
  return serviceAxios.post("/manager/production-queue/batch/reset", {
    queueIds,
    ...params
  });
}

/**
 * 批量标记失败
 * @param queueIds 队列ID数组
 */
export function batchMarkFailed(queueIds: string[], params?: {
  operator?: string;
  reason?: string;
}): Promise<{
  code: number;
  msg: string;
  data: {
    successCount: number;
    failedCount: number;
    errors: string[];
  };
}> {
  return serviceAxios.post("/manager/production-queue/batch/fail", {
    queueIds,
    ...params
  });
}

// ==================== 实用工具函数 ====================

/**
 * 获取队列状态显示名称
 */
export function getQueueStatusName(status: QueueStatus | string): string {
  const statusNames: Record<string, string> = {
    '0': '待制作',
    '1': '制作中',
    '2': '已完成',
    '3': '失败',
    '4': '已取消'
  };
  return statusNames[status as string] || status;
}

/**
 * 获取队列状态样式类
 */
export function getQueueStatusClass(status: QueueStatus | string): string {
  const statusClasses: Record<string, string> = {
    '0': 'badge bg-warning',
    '1': 'badge bg-primary',
    '2': 'badge bg-success',
    '3': 'badge bg-danger',
    '4': 'badge bg-secondary'
  };
  return statusClasses[status as string] || 'badge bg-light';
}

/**
 * 获取优先级显示名称
 */
export function getQueuePriorityName(priority: QueuePriority): string {
  const priorityNames = {
    [QueuePriority.LOW]: '低',
    [QueuePriority.NORMAL]: '普通',
    [QueuePriority.HIGH]: '高',
    [QueuePriority.URGENT]: '紧急'
  };
  return priorityNames[priority] || priority;
}

/**
 * 获取优先级样式类
 */
export function getQueuePriorityClass(priority: QueuePriority): string {
  const priorityClasses = {
    [QueuePriority.LOW]: 'badge bg-light',
    [QueuePriority.NORMAL]: 'badge bg-info',
    [QueuePriority.HIGH]: 'badge bg-warning',
    [QueuePriority.URGENT]: 'badge bg-danger'
  };
  return priorityClasses[priority] || 'badge bg-light';
}

// ==================== 运维操作 API ====================

/**
 * 手动启动队列
 * @param queueId 队列ID
 */
export function manualStartQueue(queueId: string): Promise<{
  code: number;
  msg: string;
  data?: unknown;
}> {
  return serviceAxios.post(`/manager/production-queue/start/${queueId}`);
}
