// 挂载配置主程序
import {createApp} from "vue";
import App from "./App.vue";
import {router} from "./router/"
import {createBootstrap} from "bootstrap-vue-next";
import VueApexCharts from "vue3-apexcharts";
import {QuillEditor} from '@vueup/vue-quill'

import {createPinia} from "pinia";
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import { useAuthStore } from "@/store/useAuthStore";

import Vue3Prism from 'vue3-prism/lib/Vue3Prism.common.js'

// bootstrap
import "bootstrap/dist/js/bootstrap.bundle.js"
import "bootstrap/dist/css/bootstrap.css";
import "bootstrap-vue-next/dist/bootstrap-vue-next.css";

import "swiper/css";
import "swiper/css/bundle";
import 'flatpickr/dist/flatpickr.css';
import '@vueup/vue-quill/dist/vue-quill.snow.css';
import '@vueup/vue-quill/dist/vue-quill.bubble.css';
import "vue3-prism/lib/Vue3Prism.css"

import "./assets/custom.scss";

const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);
const app = createApp(App).use(pinia).use(router);

app.use(VueApexCharts);
app.use(createBootstrap());
app.component('QuillEditor', QuillEditor)
    .use(Vue3Prism)

// 初始化认证状态
const initApp = async () => {
    const authStore = useAuthStore()

    // 如果有token，尝试获取用户信息
    if (authStore.token) {
        try {
            await authStore.initAuth()
        } catch (error) {
            console.error('初始化认证状态失败:', error)
        }
    }

    // 挂载应用
    app.mount("#app")
}

// 启动应用
initApp()
