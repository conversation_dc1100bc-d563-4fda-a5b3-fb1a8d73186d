.accordion {
    --bs-accordion-btn-padding-x: 20px;
    --bs-accordion-btn-padding-y: 15px;
    --bs-accordion-btn-icon-width: 12px;
    --bs-accordion-border-color: #eeeeee;
    --bs-accordion-active-bg: #EFEEF9;
    --bs-accordion-active-color: var(--splash-primary-color);
}
.accordion-item {
    margin-bottom: 20px;

    &:last-child {
        margin-bottom: 0;
    }
}
.accordion-button {
    text-align: start;
}

// Dark Mode
.dark {
    .accordion {
        --bs-accordion-border-color: #45445e;
        --bs-accordion-bg: var(--splash-black-color);
        --bs-accordion-active-bg: #34334a
    }
    .accordion-button {
        &::after {
            filter: invert(1);
        }
    }
}