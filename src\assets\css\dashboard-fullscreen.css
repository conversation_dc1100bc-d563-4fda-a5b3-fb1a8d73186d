/* 全屏大屏专用样式 - 适合大屏显示和远距离观看 */

/* 页面标题样式 - 大屏版本 */
.main-title {
  font-size: 3.5rem;
  color: var(--splash-primary-color);
  margin-bottom: 0;
  font-family: 'Source Han Serif', serif;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 日期控制区域 - 大屏版本 */
.date-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: flex-end;
}

.quick-date-buttons {
  display: flex;
  gap: 12px;
}

.quick-btn {
  padding: 12px 24px;
  border: 2px solid var(--splash-primary-color);
  background: var(--splash-white-color);
  color: var(--splash-primary-color);
  border-radius: 8px;
  font-weight: 600;
  font-family: 'Source Han Serif', serif;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 18px;
}

.quick-btn:hover {
  background: var(--splash-primary-color);
  color: var(--splash-white-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(101, 96, 240, 0.3);
}

.quick-btn.active {
  background: var(--splash-primary-color);
  color: var(--splash-white-color);
  box-shadow: 0 4px 16px rgba(101, 96, 240, 0.4);
}

.time-selector {
  min-width: 350px;
}

.date-picker {
  border: 2px solid #d1d5db;
  border-radius: 10px;
  padding: 16px 20px;
  font-family: 'Source Han Serif', serif;
  font-size: 18px;
  background: var(--splash-white-color);
  color: var(--bs-body-color);
  width: 100%;
}

.date-picker::placeholder {
  color: var(--splash-secondary-color);
}

.date-picker:focus {
  border-color: var(--splash-primary-color);
  box-shadow: 0 0 0 4px rgba(101, 96, 240, 0.15);
  outline: none;
}

/* 核心指标样式 - 大屏版本 */
.metric-item {
  padding: 20px 0;
}

.metric-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.metric-label {
  font-size: 18px;
  color: var(--splash-secondary-color);
  font-weight: 600;
  margin: 0;
  font-family: 'Source Han Serif', serif;
}

.metric-value-container {
  display: flex;
  align-items: baseline;
  gap: 6px;
  justify-content: center;
}

.metric-value {
  font-size: 42px;
  font-weight: 800;
  color: #000000;
  font-family: 'Source Han Serif', serif;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.metric-value.revenue {
  color: var(--splash-success-color);
}

.metric-value.orders {
  color: var(--splash-info-color);
}

.metric-value.cups {
  color: var(--splash-warning-color);
}

/* 统一单位颜色 - 大屏版本 */
.metric-unit {
  font-size: 20px;
  font-weight: 600;
  font-family: 'Source Han Serif', serif;
}

.currency {
  color: var(--splash-success-color);
}

.order-unit {
  color: var(--splash-info-color);
}

.cup-unit {
  color: var(--splash-warning-color);
}

/* 明星产品网格样式 - 大屏版本 */
.star-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.product-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 2px solid #d1d5db;
}

.product-item:last-child {
  border-bottom: none;
}

.product-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  width: 100%;
}

.product-name-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.product-rank {
  background: transparent;
  color: #000000;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 800;
  flex-shrink: 0;
  font-family: 'Source Han Serif', serif;
}

.product-name {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.product-sales-container {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.product-sales {
  font-size: 20px;
  font-weight: 700;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.product-unit {
  font-size: 16px;
  font-weight: 600;
  font-family: 'Source Han Serif', serif;
}

/* 门店排行样式 - 大屏版本 */
.store-ranking {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.store-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 2px solid #d1d5db;
}

.store-item:last-child {
  border-bottom: none;
}

.store-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  width: 100%;
}

.store-name-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.store-rank {
  background: transparent;
  color: #000000;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 800;
  flex-shrink: 0;
  font-family: 'Source Han Serif', serif;
}

.store-name {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.store-revenue-container {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.store-revenue {
  font-size: 20px;
  font-weight: 700;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.store-unit {
  font-size: 16px;
  font-weight: 600;
  font-family: 'Source Han Serif', serif;
}

/* 产品排行样式 - 大屏版本 */
.product-ranking {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.product-rank-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 2px solid #d1d5db;
}

.product-rank-item:last-child {
  border-bottom: none;
}

.product-rank-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  width: 100%;
}

.product-rank-name-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.product-rank-number {
  background: transparent;
  color: #000000;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 800;
  flex-shrink: 0;
  font-family: 'Source Han Serif', serif;
}

.product-rank-name {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.product-rank-cups-container {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.product-rank-cups {
  font-size: 20px;
  font-weight: 700;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.product-rank-unit {
  font-size: 16px;
  font-weight: 600;
  font-family: 'Source Han Serif', serif;
}

/* 汇总统计样式 - 大屏版本 */
.summary-stats {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-bottom: 2px solid #f0f1f3;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  font-size: 18px;
  color: var(--splash-secondary-color);
  font-weight: 600;
  font-family: 'Source Han Serif', serif;
}

.summary-value-container {
  display: flex;
  align-items: baseline;
  gap: 6px;
}

.summary-value {
  font-size: 28px;
  font-weight: 800;
  color: #000000;
  font-family: 'Source Han Serif', serif;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.summary-unit {
  font-size: 18px;
  font-weight: 600;
  font-family: 'Source Han Serif', serif;
}

.growth-positive {
  color: var(--splash-success-color);
}

/* 图表容器样式 - 大屏版本 */
.chart-container {
  margin-top: 16px;
}

/* 卡片样式调整 - 大屏版本 */
.card {
  margin-bottom: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.card-body {
  padding: 25px;
}

/* 图标样式 - 大屏版本 */
.card-body h5 i {
  font-size: 24px !important;
}

/* 响应式设计 - 大屏版本 */
@media (max-width: 1200px) {
  .star-products-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }

  .main-title {
    font-size: 3rem;
  }

  .metric-value {
    font-size: 36px;
  }
}

@media (max-width: 768px) {
  .star-products-grid {
    grid-template-columns: 1fr;
  }

  .main-title {
    font-size: 2.5rem;
  }

  .time-selector {
    min-width: 280px;
    margin-top: 15px;
  }

  .date-controls {
    align-items: stretch;
  }

  .quick-date-buttons {
    justify-content: center;
  }

  .metric-value {
    font-size: 28px;
  }

  .quick-btn {
    padding: 10px 18px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 2rem;
  }

  .metric-value {
    font-size: 24px;
  }

  .quick-btn {
    padding: 8px 14px;
    font-size: 14px;
  }

  .product-name, .store-name, .product-rank-name {
    font-size: 16px;
  }

  .product-sales, .store-revenue, .product-rank-cups {
    font-size: 18px;
  }
}
