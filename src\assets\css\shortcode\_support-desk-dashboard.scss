// Welcome Support Desk
.welcome-support-desk-box {
    h2 {
        font-size: 25px;
    }
    .list {
        margin-top: 30px;

        h4 {
            font-size: 20px;
        }
        &.row {
            --bs-gutter-x: 10px;
        }
    }
}

// Time Duration
.time-duration-box {
    .card-body {
        padding: 40px 20px;

        .icon {
            z-index: 1;
            font-size: 50px;
            margin-bottom: 25px;

            &::before {
                background: #F3F7F9;
                border-radius: 10px;
                position: absolute;
                height: 40px;
                content: "";
                width: 40px;
                z-index: -1;
                left: 25px;
                top: 25px;
            }
        }
        .content {
            margin-bottom: 40px;

            h2 {
                font-size: 25px;
            }
        }
        .chart {
            top: 15px;
            right: 20px;
            max-width: 150px;
            position: absolute;
        }
    }
}

// Number of Tickets
.number-of-tickets-box {
    .info {
        margin-top: 20px;

        li {
            margin: {
                left: 10px;
                right: 10px;
            };
            &:first-child {
                margin-left: 0;
            }
            &:last-child {
                margin-right: 0;
            }
        }
        h4 {
            font-size: 22px;
        }
    }
}

// Top Performer Help Agent
.top-performer-box {
    .list {
        li {
            border-bottom: 1px dashed #d9e9ef;
            padding-bottom: 17px;
            margin-bottom: 17px;
            
            img {
                width: 50px;
                height: 50px;
            }
            .title {
                margin-left: 10px;
            }
            &:last-child {
                margin-bottom: 0;
                padding-bottom: 0;
                border-bottom: none;
            }
        }
    }
}

// Unresolved Tickets By Priority
.unresolved-tickets-box {
    .list {
        li {
            border-bottom: 1px dashed #d9e9ef;
            margin-bottom: 20px;
            padding: {
                left: 18px;
                bottom: 18px;
            };
            &:last-child {
                margin-bottom: 0;
                padding-bottom: 0;
                border-bottom: none;
            }
        }
    }
}

// Customer Satisfaction
.customer-satisfaction-box {
    .list {
        border-top: 1px dashed #d9e9ef;
        padding-top: 21px;
        margin-top: 21px;

        li {
            margin: {
                left: 10px;
                right: 10px;
            };
            h4 {
                font-size: 20px;
            }
            &:first-child {
                margin-left: 0;
            }
            &:last-child {
                margin-right: 0;
            }
        }
    }
}

// Ticket Grid
.ticket-grid-box {
    .item {
        padding: 20px;
        margin-bottom: 10px;
        border: 1px solid #d9e9ef;

        .form-select {
            line-height: 1.5;
            border-color: var(--bs-border-color);
            padding: 0.375rem 2.25rem 0.375rem 0.75rem;
            background: {
                position: right 0.75rem center;
                size: 16px 12px;
            };
            &:focus {
                border-color: var(--splash-primary-color);
            }
        }
        &:hover {
            border-color: #F5F4FA;
            background: #F5F4FA;
        }
        &:last-child {
            margin-bottom: 0;
        }
    }
}

// Dark Mode
.dark {

    .time-duration-box {
        .card-body {
            .icon {
                &::before {
                    background: var(--splash-black-color);
                }
            }
        }
    }

    .top-performer-box {
        .list {
            li {
                border-bottom-color: #45445e;
            }
        }
    }

    .unresolved-tickets-box {
        .list {
            li {
                border-bottom-color: #45445e;
            }
        }
    }

    .customer-satisfaction-box {
        .list {
            border-top-color: #45445e;
        }
    }

    .ticket-grid-box {
        .item {
            border-color: #45445e;
    
            .form-select {
                border-color: #45445e;
            }
            &:hover {
                border-color: var(--splash-black-color);
                background: var(--splash-black-color);
            }
        }
    }

}

@media only screen and (max-width : 767px) {

    // Welcome Support Desk
    .welcome-support-desk-box {
        h2 {
            font-size: 20px;
        }
        .list {
            margin-top: 10px;

            h4 {
                font-size: 18px;
            }
        }
    }

    // Time Duration
    .time-duration-box {
        .card-body {
            padding: 15px;

            .icon {
                font-size: 40px;
                margin-bottom: 20px;

                &::before {
                    height: 35px;
                    width: 35px;
                    left: 15px;
                    top: 15px;
                }
            }
            .content {
                margin-bottom: 15px;

                h2 {
                    font-size: 18px;
                }
            }
            .chart {
                top: -10px;
                right: 15px;
            }
        }
    }

    // Number of Tickets
    .number-of-tickets-box {
        .info {
            margin-top: 0;
    
            li {
                margin-top: 15px;
            }
        }
    }

    // Top Performer Help Agent
    .top-performer-box {
        .list {
            li {
                padding-bottom: 15px;
                margin-bottom: 15px;
            }
        }
    }

    // Unresolved Tickets By Priority
    .unresolved-tickets-box {
        .list {
            text-align: center;

            li {
                display: inline-block;
                border-bottom: none;
                margin-bottom: 0;
                padding: {
                    left: 5px;
                    bottom: 5px;
                };
            }
        }
    }

    // Customer Satisfaction
    .customer-satisfaction-box {
        .list {
            padding-top: 15px;
            margin-top: 15px;
        }
    }

}

@media only screen and (min-width : 768px) and (max-width : 991px) {

    // Welcome Support Desk
    .welcome-support-desk-box {
        .list {
            margin-top: 20px;
        }
    }

    // Time Duration
    .time-duration-box {
        .card-body {
            .content {
                margin-bottom: 20px;
            }
        }
    }

    // Top Performer Help Agent
    .top-performer-box {
        .list {
            li {
                padding-bottom: 15px;
                margin-bottom: 15px;
            }
        }
    }

    // Unresolved Tickets By Priority
    .unresolved-tickets-box {
        .list {
            li {
                padding-left: 10px;
            }
        }
    }

}

@media only screen and (min-width : 992px) and (max-width : 1199px) {

    // Time Duration
    .time-duration-box {
        .card-body {
            .content {
                margin-bottom: 25px;
            }
            p {
                max-width: 330px;
            }
        }
    }

    // Top Performer Help Agent
    .top-performer-box {
        .list {
            li {
                padding-bottom: 15px;
                margin-bottom: 15px;
            }
        }
    }

}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {

    // Time Duration
    .time-duration-box {
        .card-body {
            .content {
                margin-bottom: 25px;
            }
            p {
                max-width: 330px;
            }
        }
    }

    // Top Performer Help Agent
    .top-performer-box {
        .list {
            li {
                padding-bottom: 15px;
                margin-bottom: 15px;
            }
        }
    }

}

@media only screen and (min-width: 1600px) {

    // Welcome Support Desk
    .welcome-support-desk-box {
        h2 {
            font-size: 30px;
        }
        .content {
            padding-right: 20px;
        }
        .list {
            margin-top: 30px;

            h4 {
                font-size: 24px;
            }
        }
    }

    // Time Duration
    .time-duration-box {
        .card-body {
            padding: 37px 30px;

            .icon {
                font-size: 55px;
            }
            .content {
                margin-bottom: 40px;

                span {
                    font-size: 16px;
                }
                h2 {
                    font-size: 30px;
                }
            }
        }
    }

    // Number of Tickets
    .number-of-tickets-box {
        .info {
            margin-top: 32px;
    
            li {
                margin: {
                    left: 30px;
                    right: 30px;
                };
            }
            h4 {
                font-size: 24px;
            }
        }
    }

    // Top Performer Help Agent
    .top-performer-box {
        .list {
            li {
                padding-bottom: 15.6px;
                margin-bottom: 15.6px;
                
                img {
                    width: 55px;
                    height: 55px;
                }
                .title {
                    margin-left: 15px;
                }
            }
        }
    }

    // Customer Satisfaction
    .customer-satisfaction-box {
        .list {
            padding-top: 20px;
            margin-top: 20px;

            li {
                margin: {
                    left: 20px;
                    right: 20px;
                };
                h4 {
                    font-size: 24px;
                }
            }
        }
    }

}