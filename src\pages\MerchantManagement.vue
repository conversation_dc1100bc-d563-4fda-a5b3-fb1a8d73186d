<template>
  <div class="merchant-management">
    <!-- 面包屑导航 -->
    <BreadCrumb pageTitle="商户管理" />

    <!-- 操作栏 -->
    <div class="d-flex justify-content-between align-items-center mb-25">
      <div class="refresh">
        <button
          class="transition bg-transparent p-0 border-0"
          @click="refreshStoreList"
          aria-label="刷新">
          <i class="flaticon-refresh"></i>
        </button>
      </div>

      <div class="d-flex gap-2">
        <button @click="handleExport" class="btn btn-outline-primary btn-sm">
          <i class="flaticon-download me-2"></i>
          导出数据
        </button>
        <button @click="showAddModal" class="btn btn-primary btn-sm">
          <i class="flaticon-plus me-2"></i>
          新增商户
        </button>
      </div>
    </div>

    <!-- 统计卡片 - 使用标准组件 -->
    <div class="row mb-25">
      <StatsCard
        title="总商户数"
        :value="stats.total"
        icon="flaticon-store"
        color="primary"
      />
      <StatsCard
        title="营业中"
        :value="stats.openStores"
        icon="flaticon-check"
        color="success"
      />
      <StatsCard
        title="暂停营业"
        :value="stats.inactive"
        icon="flaticon-pause"
        color="danger"
      />
      <StatsCard
        title="活跃商户"
        :value="stats.active"
        icon="flaticon-location"
        color="info"
      />
    </div>

    <!-- 搜索和筛选 -->
    <div class="card border-0 rounded-0 bg-white box-shadow mb-25">
      <div class="card-body p-15 p-sm-20 p-md-25 p-lg-30">
        <div class="row g-3">
          <div class="col-md-3">
            <label class="form-label">商户名称</label>
            <input
              type="text"
              class="form-control"
              placeholder="请输入商户名称"
              v-model="queryParams.storeName"
              @keyup.enter="handleQuery"
            />
          </div>
          <div class="col-md-3">
            <label class="form-label">商户地址</label>
            <input
              type="text"
              class="form-control"
              placeholder="请输入商户地址"
              v-model="queryParams.storeAddress"
              @keyup.enter="handleQuery"
            />
          </div>
          <div class="col-md-2">
            <label class="form-label">商户类型</label>
            <select class="form-select" v-model="queryParams.storeType">
              <option value="">全部类型</option>
              <option value="直营店">直营店</option>
              <option value="加盟店">加盟店</option>
            </select>
          </div>
          <div class="col-md-2">
            <label class="form-label">营业状态</label>
            <select class="form-select" v-model="queryParams.status">
              <option value="">全部状态</option>
              <option value="1">营业中</option>
              <option value="0">暂停营业</option>
            </select>
          </div>
          <div class="col-md-2 d-flex align-items-end gap-2">
            <button @click="handleQuery" class="btn btn-primary btn-sm">
              <i class="flaticon-search me-1"></i>
              搜索
            </button>
            <button @click="resetQuery" class="btn btn-outline-secondary btn-sm">
              <i class="flaticon-refresh me-1"></i>
              重置
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 商户列表 -->
    <div class="card border-0 rounded-0 bg-white box-shadow">
      <div class="card-body p-15 p-sm-20 p-md-25 p-lg-30">
        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>
                  <input type="checkbox" v-model="isSelectAll" @change="handleSelectAll" />
                </th>
                <th>商户名称</th>
                <th>商户地址</th>
                <th>商户类型</th>
                <th>联系电话</th>
                <th>营业时间</th>
                <th>营业状态</th>
                <th>描述</th>
                <th>创建时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="store in storeList" :key="store.storeId">
                <td>
                  <input
                    type="checkbox"
                    :value="store.storeId"
                    v-model="selectedIds"
                  />
                </td>
                <td class="fw-bold">{{ store.storeName }}</td>
                <td>{{ store.storeAddress }}</td>
                <td>{{ store.storeType || '-' }}</td>
                <td>{{ store.contactPhone }}</td>
                <td>{{ store.businessHours || '-' }}</td>
                <td>
                  <span
                    :class="['badge', store.status === '1' ? 'bg-success' : 'bg-danger']"
                  >
                    {{ store.status === '1' ? '营业中' : '暂停营业' }}
                  </span>
                </td>
                <td>{{ store.storeDescription || '-' }}</td>
                <td>{{ formatDate(store.createTime) }}</td>
                <td>
                  <div class="btn-group btn-group-sm">
                    <button
                      @click="handleView(store)"
                      class="btn btn-outline-primary btn-sm"
                      title="查看详情"
                    >
                      <i class="flaticon-eye"></i>
                    </button>
                    <button
                      @click="handleEdit(store)"
                      class="btn btn-outline-warning btn-sm"
                      title="编辑"
                    >
                      <i class="flaticon-edit"></i>
                    </button>
                    <button
                      @click="handleStatusChange(store)"
                      :class="['btn', 'btn-sm', store.status === '1' ? 'btn-outline-danger' : 'btn-outline-success']"
                      :title="store.status === '1' ? '暂停营业' : '开始营业'"
                    >
                      <i :class="store.status === '1' ? 'flaticon-close' : 'flaticon-check'"></i>
                    </button>
                    <button
                      @click="handleDelete(store)"
                      class="btn btn-outline-danger btn-sm"
                      title="删除"
                    >
                      <i class="flaticon-delete"></i>
                    </button>
                  </div>
                </td>
              </tr>
              <tr v-if="storeList.length === 0">
                <td colspan="10" class="text-center py-4 text-muted">
                  <i class="flaticon-info me-2"></i>
                  暂无商户数据
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div class="d-flex justify-content-between align-items-center mt-20" v-if="total > 0">
          <div class="text-muted">
            共 {{ total }} 条记录，第 {{ queryParams.pageNum }} / {{ Math.ceil(total / queryParams.pageSize) }} 页
          </div>
          <nav>
            <ul class="pagination pagination-sm mb-0">
              <li class="page-item" :class="{ disabled: queryParams.pageNum <= 1 }">
                <button class="page-link" @click="changePage(queryParams.pageNum - 1)">上一页</button>
              </li>
              <li 
                v-for="page in getPageNumbers()" 
                :key="page"
                class="page-item" 
                :class="{ active: page === queryParams.pageNum }"
              >
                <button class="page-link" @click="changePage(page)">{{ page }}</button>
              </li>
              <li class="page-item" :class="{ disabled: queryParams.pageNum >= Math.ceil(total / queryParams.pageSize) }">
                <button class="page-link" @click="changePage(queryParams.pageNum + 1)">下一页</button>
              </li>
            </ul>
          </nav>
        </div>

        <!-- 批量操作 -->
        <div class="d-flex gap-2 mt-20" v-if="selectedIds.length > 0">
          <button @click="handleBatchDelete" class="btn btn-danger btn-sm">
            <i class="flaticon-delete me-2"></i>
            批量删除 ({{ selectedIds.length }})
          </button>
        </div>
      </div>
    </div>

    <!-- 商户编辑模态框 -->
    <StoreEditModal
      v-model:visible="showEditModal"
      :store-data="editingStore"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  getMerchantStoreList,
  updateMerchantStore,
  deleteMerchantStore,
  deleteMerchantStoreBatch,
  exportMerchantStoreData,
  type StoreInfo,
  type StoreQueryParams,
  type StoreFormData
} from '@/utils/api/merchant'

import StatsCard from '@/components/StatsCard.vue'
import BreadCrumb from '@/components/layouts/BreadCrumb.vue'
import StoreEditModal from '@/components/merchant/StoreEditModal.vue'

const router = useRouter()



// 查询参数
const queryParams = reactive<StoreQueryParams>({
  storeName: '',
  storeAddress: '',
  status: '',
  storeType: ''
})

// 数据状态
const loading = ref(false)
const storeList = ref<StoreInfo[]>([])
const total = ref(0)
const selectedIds = ref<number[]>([])

// 模态框状态
const showEditModal = ref(false)
const editingStore = ref<StoreInfo | null>(null)

// 统计数据
const stats = ref({
  total: 0,
  active: 0,
  inactive: 0,
  openStores: 0
})

// 计算属性
const isSelectAll = computed({
  get: () => storeList.value.length > 0 && selectedIds.value.length === storeList.value.length,
  set: (value: boolean) => {
    if (value) {
      selectedIds.value = storeList.value.map(item => item.storeId!).filter(id => id !== undefined)
    } else {
      selectedIds.value = []
    }
  }
})

// 获取商户列表
const getStoreListData = async () => {
  loading.value = true
  try {
    const response = await getMerchantStoreList(queryParams)
    storeList.value = response.rows || []
    total.value = response.total || 0
    // 获取列表后立即计算统计数据
    getStatsData()
  } catch (error) {
    console.error('获取商户列表失败:', error)
    // ElMessage.error('获取商户列表失败')
    console.log('获取商户列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const getStatsData = async () => {
  try {
    // 基于现有商户列表计算统计数据
    const total = storeList.value.length
    const openStores = storeList.value.filter(store => store.status === '1').length
    const inactive = storeList.value.filter(store => store.status === '0').length
    const active = openStores // 活跃商户等于营业中的商户

    stats.value = {
      total,
      openStores,
      inactive,
      active
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 搜索
const handleQuery = () => {
  getStoreListData()
}

// 重置搜索
const resetQuery = () => {
  queryParams.storeName = ''
  queryParams.storeAddress = ''
  queryParams.status = ''
  queryParams.storeType = ''
  getStoreListData()
}

// 分页
const changePage = () => {
  // 注意：真实API可能不支持分页，这里保留分页逻辑以备后用
  getStoreListData()
}

// 获取页码数组
const getPageNumbers = () => {
  // 简化分页逻辑，因为真实API可能不支持分页
  return [1]
}

// 全选处理
const handleSelectAll = () => {
  if (isSelectAll.value) {
    selectedIds.value = storeList.value.map(item => item.storeId!).filter(id => id !== undefined)
  } else {
    selectedIds.value = []
  }
}

// 查看详情
const handleView = (store: StoreInfo) => {
  // 路由到商户详情页面
  router.push(`/stores/${store.storeId}`)
}

// 刷新列表
const refreshStoreList = () => {
  getStoreListData()
}

// 新增商户
const showAddModal = () => {
  editingStore.value = null
  showEditModal.value = true
}

// 编辑商户
const handleEdit = (store: StoreInfo) => {
  editingStore.value = store
  showEditModal.value = true
}

// 编辑成功回调
const handleEditSuccess = () => {
  showEditModal.value = false
  editingStore.value = null
  getStoreListData()
}

// 状态切换
const handleStatusChange = async (store: StoreInfo) => {
  const newStatus = store.status === '1' ? '0' : '1'
  const action = newStatus === '1' ? '开始营业' : '暂停营业'

  if (confirm(`确定要${action}商户"${store.storeName}"吗？`)) {
    try {
      // 使用标准的更新API，只更新状态字段
      const updateData: StoreFormData = {
        storeId: store.storeId!,
        storeName: store.storeName,
        storeAddress: store.storeAddress,
        contactPhone: store.contactPhone,
        status: newStatus,
        storeDescription: store.storeDescription,
        storeType: store.storeType,
        businessHours: store.businessHours,
        remark: store.remark
      }

      await updateMerchantStore(updateData)
      alert(`${action}成功`)
      getStoreListData()
    } catch (error) {
      console.error('状态切换失败:', error)
      alert(`${action}失败`)
    }
  }
}

// 删除商户
const handleDelete = async (store: StoreInfo) => {
  if (confirm(`确定要删除商户"${store.storeName}"吗？此操作不可恢复！`)) {
    try {
      await deleteMerchantStore(store.storeId!)
      alert('删除成功')
      getStoreListData()
    } catch (error) {
      console.error('删除失败:', error)
      alert('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (confirm(`确定要删除选中的 ${selectedIds.value.length} 个商户吗？此操作不可恢复！`)) {
    try {
      await deleteMerchantStoreBatch(selectedIds.value)
      alert('批量删除成功')
      selectedIds.value = []
      getStoreListData()
    } catch (error) {
      console.error('批量删除失败:', error)
      alert('批量删除失败')
    }
  }
}

// 导出数据
const handleExport = async () => {
  try {
    const blob = await exportMerchantStoreData(queryParams)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `商户数据_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    alert('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    alert('导出失败')
  }
}



// 格式化日期
const formatDate = (dateString?: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 初始化
onMounted(() => {
  getStoreListData()
})
</script>

<style scoped>
.merchant-management {
  padding: 20px;
}

.main-title {
  color: #2c3e50;
  font-weight: 700;
  margin: 0;
}

.stats-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}

.table th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
}

.table td {
  vertical-align: middle;
}

.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.pagination .page-link {
  color: #6560F0;
  border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
  background-color: #6560F0;
  border-color: #6560F0;
}

.pagination .page-link:hover {
  color: #5a54e6;
  background-color: #e9ecef;
  border-color: #dee2e6;
}
</style>
