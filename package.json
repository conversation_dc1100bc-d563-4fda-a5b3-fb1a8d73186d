{"name": "adlash", "version": "1.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@fullcalendar/core": "^6.1.8", "@fullcalendar/daygrid": "^6.1.8", "@fullcalendar/timegrid": "^6.1.8", "@popperjs/core": "^2.11.8", "@vue-leaflet/vue-leaflet": "^0.10.1", "@vueup/vue-quill": "^1.2.0", "apexcharts": "^4.5.0", "axios": "^1.4.0", "chart.js": "^4.5.0", "core-js": "^3.8.3", "date-fns": "^4.1.0", "leaflet": "^1.9.4", "lodash": "^4.17.21", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "quill-blot-formatter": "^1.0.5", "quill-image-uploader": "^1.3.0", "swiper": "^10.1.0", "vue": "^3.3.4", "vue-flatpickr-component": "^11.0.3", "vue-router": "^4.2.4", "vue3-apexcharts": "^1.8.0", "vue3-circle-progress": "^1.0.7", "vue3-prism": "^0.1.4", "vue3-tree": "^0.11.5"}, "devDependencies": {"@bootstrap-vue-next/nuxt": "^0.28.6", "@types/bootstrap": "^5.2.10", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-typescript": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/eslint-config-typescript": "^11.0.0", "bootstrap": "^5.3.5", "bootstrap-vue-next": "^0.28.6", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.20.0", "sass": "^1.64.1", "sass-loader": "^13.0.2", "typescript": "~4.9.5"}}