<template>
  <div class="store-business-dashboard">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="text-center py-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-3 text-muted">正在加载业务数据...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="alert alert-danger" role="alert">
      <i class="flaticon-warning me-2"></i>
      {{ error }}
    </div>

    <!-- 主要内容 -->
    <div v-else>
      <!-- 时间选择器区域 -->
      <div class="card mb-25 border-0 rounded-0 bg-white box-shadow">
        <div class="card-body p-20">
          <div class="d-flex align-items-center justify-content-between">
            <div class="d-flex align-items-center">
              <i class="flaticon-calendar me-10 text-primary" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">业务数据查询</h5>
            </div>
            <div class="d-flex align-items-center">
              <label class="form-label fw-medium text-black-emphasis mb-0 me-3">查询日期：</label>
              <flat-pickr
                v-model="filterCriteria.selectedDate"
                :config="flatpickrConfig"
                class="form-control date-picker"
                placeholder="选择日期"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 核心指标区 -->
      <div class="row mb-30">

        <div class="col-lg-4 col-md-6 mb-20">
          <div class="card border-0 rounded-0 bg-white h-100 box-shadow">
            <div class="card-body p-20 text-center">
              <div class="metric-item">
                <div class="metric-content">
                  <span class="metric-label">营业额</span>
                  <div class="metric-value-container">
                    <span class="metric-value revenue">{{ businessStats.totalRevenue.toFixed(2) }}</span>
                    <span class="metric-unit currency">¥</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-20">
          <div class="card border-0 rounded-0 bg-white h-100 box-shadow">
            <div class="card-body p-20 text-center">
              <div class="metric-item">
                <div class="metric-content">
                  <span class="metric-label">订单数</span>
                  <div class="metric-value-container">
                    <span class="metric-value orders">{{ businessStats.totalOrders }}</span>
                    <span class="metric-unit order-unit">单</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-20">
          <div class="card border-0 rounded-0 bg-white h-100 box-shadow">
            <div class="card-body p-20 text-center">
              <div class="metric-item">
                <div class="metric-content">
                  <span class="metric-label">杯数</span>
                  <div class="metric-value-container">
                    <span class="metric-value cups">{{ businessStats.totalItems }}</span>
                    <span class="metric-unit cup-unit">杯</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
      </div>
    </div>

    <!-- 主体分析区 -->
    <div class="row">
      <!-- 左栏内容 -->
      <div class="col-lg-6 mb-25">
        <!-- 全部饮品数据 -->
        <div class="card border-0 rounded-0 bg-white mb-25 box-shadow">
          <div class="card-body p-20">
            <div class="d-flex align-items-center justify-content-between mb-20">
              <div class="d-flex align-items-center">
                <i class="flaticon-chart me-10 text-primary" style="font-size: 20px;"></i>
                <h5 class="fw-bold mb-0 text-black">全部饮品数据</h5>
              </div>
              <span class="badge bg-primary text-white">{{ allProducts.length }} 种</span>
            </div>
            <div class="all-products">
              <div v-if="isLoading" class="text-center text-muted py-3">
                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                正在加载饮品数据...
              </div>
              <div v-else-if="allProducts.length === 0" class="text-center text-muted py-3">
                <i class="flaticon-warning me-2"></i>
                暂无饮品数据
              </div>
              <div v-else class="products-list">
                <div v-for="product in allProducts" :key="product.id" class="product-item">
                  <div class="product-info">
                    <div class="product-name-container">
                      <div class="product-rank">{{ product.rank }}</div>
                      <span class="product-name">{{ product.name }}</span>
                    </div>
                    <div class="product-sales-container">
                      <span class="product-sales">{{ product.sales }}</span>
                      <span class="product-unit cup-unit">杯</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 高峰购买时段 -->
        <div class="card border-0 rounded-0 bg-white mb-25 box-shadow">
          <div class="card-body p-20">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-clock me-10 text-info" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">高峰购买时段</h5>
            </div>
            <div class="peak-hours">
              <div v-if="isLoading" class="text-center text-muted py-3">
                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                正在分析高峰时段...
              </div>
              <div v-else-if="peakHours.length === 0" class="text-center text-muted py-3">
                <i class="flaticon-warning me-2"></i>
                暂无高峰时段数据
              </div>
              <div v-else>
                <div v-for="hour in peakHours" :key="hour.time" class="hour-item" :class="{ 'peak-highlight': hour.isPeak }">
                  <div class="hour-time">{{ hour.time }}</div>
                  <div class="hour-sales-container">
                    <span class="hour-sales">{{ hour.sales }}</span>
                    <span class="hour-unit order-unit">单</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 全饮品杯数统计 -->
        <div class="card border-0 rounded-0 bg-white box-shadow">
          <div class="card-body p-20">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-drink me-10 text-success" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">全饮品杯数统计</h5>
            </div>
            <div class="beverage-stats">
              <div v-if="isLoading" class="text-center text-muted py-3">
                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                正在统计饮品数据...
              </div>
              <div v-else-if="beverageStats.length === 0" class="text-center text-muted py-3">
                <i class="flaticon-warning me-2"></i>
                暂无饮品统计数据
              </div>
              <div v-else>
                <div v-for="beverage in beverageStats" :key="beverage.type" class="beverage-item">
                  <div class="beverage-type">{{ beverage.type }}</div>
                  <div class="beverage-count-container">
                    <span class="beverage-count">{{ beverage.count }}</span>
                    <span class="beverage-unit cup-unit">杯</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右栏内容 -->
      <div class="col-lg-6 mb-25">
        <!-- 分析笔记区域 -->
        <div class="card border-0 rounded-0 bg-white mb-25 box-shadow">
          <div class="card-body p-20">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-note me-10 text-secondary" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">分析笔记</h5>
            </div>
            <div class="notes-content">
              <div v-if="isLoading" class="text-center text-muted py-3">
                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                正在生成分析笔记...
              </div>
              <div v-else-if="analysisNotes.length === 0" class="text-center text-muted py-3">
                <i class="flaticon-warning me-2"></i>
                暂无分析数据
              </div>
              <div v-else>
                <p
                  v-for="(note, index) in analysisNotes"
                  :key="index"
                  class="text-muted"
                  :class="{ 'mb-10': index < analysisNotes.length - 1, 'mb-0': index === analysisNotes.length - 1 }"
                >
                  • {{ note }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- 营业额时段统计 -->
        <div class="card border-0 rounded-0 bg-white box-shadow">
          <div class="card-body p-20">
            <div class="d-flex align-items-center mb-20">
              <i class="flaticon-chart me-10 text-primary" style="font-size: 20px;"></i>
              <h5 class="fw-bold mb-0 text-black">营业额时段统计 <span class="chart-note">（折线图）</span></h5>
            </div>
            <div class="chart-container">
              <apexchart
                  type="line"
                  height="200"
                  :options="revenueChartOptions"
                  :series="revenueChartSeries"
                  class="chart"
                  id="revenueTimeChart"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    </div> <!-- 结束主要内容的条件渲染 -->
  </div> <!-- 结束 store-business-dashboard -->
</template>

<script setup lang="ts">
import { ref, onMounted, computed, reactive, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useStoreStore } from '@/store/useStoreStore'
import { storeToRefs } from 'pinia'
import { format } from 'date-fns'
import type { storeDetail } from '@/types/store'
import type { drinkOrder } from '@/types/order'
import type { drinkProductionQueueItem } from '@/types'
import { getOrderListByParams, type drinkOrderParams } from '@/utils/api/order'
import { getProductionQueueList, type QueueQueryParams } from '@/utils/api/queue'
import { getProductDetailById, getPrototypeDetailById } from '@/utils/api/product'
import flatPickr from 'vue-flatpickr-component'
import 'flatpickr/dist/flatpickr.css'

// 定义属性
interface Props {
  storeId?: number
  store?: storeDetail | null
}

const props = defineProps<Props>()

// 获取路由和store
const route = useRoute()
const storeStore = useStoreStore()
const { selectedStore, stores } = storeToRefs(storeStore)

// 加载状态
const isLoading = ref<boolean>(true)
const error = ref<string | null>(null)

// 核心筛选元数据
const filterCriteria = reactive({
  storeId: Number(route.params.id) || props.storeId || 0,
  selectedDate: new Date(),
})

// 统一的数据存储
const businessData = reactive<{
  orders: drinkOrder[]
  queueItems: drinkProductionQueueItem[]
}>({
  orders: [],
  queueItems: [],
})

// 获取当前门店ID
const currentStoreId = computed(() => {
  return props.storeId || Number(route.params.id) || filterCriteria.storeId
})

// 从URL参数或props获取门店ID并设置选中门店
onMounted(async () => {
  let storeId: string | number | undefined

  // 优先使用props中的storeId，其次使用路由参数
  if (props.storeId) {
    storeId = props.storeId.toString()
  } else if (route.query.storeId) {
    storeId = route.query.storeId as string
  } else if (route.params.id) {
    storeId = route.params.id as string
  }

  if (storeId) {
    filterCriteria.storeId = Number(storeId)

    if (!selectedStore.value) {
      // 如果stores为空，先获取门店列表
      if (stores.value.length === 0) {
        await storeStore.fetchStore()
      }
      // 根据ID找到对应门店并设置为选中状态
      const store = stores.value.find(s => s.storeId.toString() === storeId)
      if (store) {
        storeStore.selectStore(store)
      }
    }

    // 获取业务数据
    await fetchBusinessData()
  }
})



// 数据获取函数
async function fetchBusinessData() {
  if (!currentStoreId.value) {
    console.warn("门店ID无效，无法获取数据。")
    return
  }

  isLoading.value = true
  error.value = null

  try {
    const formattedApiDate: string = format(filterCriteria.selectedDate, "yyyy-MM-dd")
    console.log(`🔍 [业务看板] 开始获取门店${currentStoreId.value}的业务数据，查询日期: ${formattedApiDate}`)

    const queryParamsDrinkOrders: drinkOrderParams = {
      pageNum: 1,
      pageSize: 9999,
      queryDate: formattedApiDate,
      storeId: currentStoreId.value,
      orderTimeSort: "1",
    }

    const queryParamsProductionQueue: QueueQueryParams = {
      pageNum: 1,
      pageSize: 9999,
      queryDate: formattedApiDate,
      storeId: currentStoreId.value,
      orderTimeSort: "1",
    }

    console.log(`📞 [业务看板] 调用订单API参数:`, queryParamsDrinkOrders)
    console.log(`📞 [业务看板] 调用制作队列API参数:`, queryParamsProductionQueue)

    const [orderResponse, queueItemResponse] = await Promise.all([
      getOrderListByParams(queryParamsDrinkOrders),
      getProductionQueueList(queryParamsProductionQueue)
    ])

    console.log(`✅ [业务看板] 订单API返回: ${orderResponse.rows?.length || 0}条订单`)
    console.log(`✅ [业务看板] 制作队列API返回: ${queueItemResponse.rows?.length || 0}条制作项`)

    businessData.orders = orderResponse.rows ?? []
    businessData.queueItems = queueItemResponse.rows ?? []

    // 处理数据并更新各个统计
    await processBusinessData()

  } catch (err) {
    console.error("获取业务数据失败:", err)
    error.value = "数据加载失败，请稍后重试。"
    businessData.orders = []
    businessData.queueItems = []
  } finally {
    isLoading.value = false
  }
}

// 全部饮品数据（100%基于API数据）
const allProducts = ref([])

// 数据处理函数
async function processBusinessData() {
  // 处理全部饮品数据
  await calculateAllProducts()

  // 处理高峰时段数据
  calculatePeakHours()

  // 处理饮品统计数据
  calculateBeverageStats()

  // 处理营业额图表数据
  calculateRevenueChart()

  // 生成分析笔记
  calculateAnalysisNotes()
}

// 计算全部饮品数据
async function calculateAllProducts() {
  if (!businessData.orders || businessData.orders.length === 0) {
    allProducts.value = []
    return
  }

  try {
    const productQuantities = new Map<number, number>()

    // 统计每个产品的销量
    for (const order of businessData.orders) {
      let parsedItems
      try {
        parsedItems = JSON.parse(order.orderItems)
      } catch (e) {
        continue
      }
      if (!Array.isArray(parsedItems)) continue

      for (const item of parsedItems) {
        if (item && item.productId) {
          const productId = Number(item.productId)
          const quantity = Number(item.quantity) || 1
          productQuantities.set(productId, (productQuantities.get(productId) || 0) + quantity)
        }
      }
    }

    // 获取产品详情并按销量排序
    const productDetails = []
    for (const [productId, quantity] of productQuantities.entries()) {
      try {
        const detail = await getProductDetailById(productId)
        if (detail && detail.drinkPrototype) {
          const prototypeDetail = await getPrototypeDetailById(detail.drinkPrototype)
          productDetails.push({
            id: productId,
            name: prototypeDetail?.prototypeName || `产品${productId}`,
            sales: quantity,
            rank: 0
          })
        }
      } catch (err) {
        console.error(`获取产品${productId}详情失败:`, err)
      }
    }

    // 按销量排序并设置排名（显示所有产品）
    productDetails.sort((a, b) => b.sales - a.sales)
    allProducts.value = productDetails.map((item, index) => ({
      ...item,
      rank: index + 1
    }))
  } catch (err) {
    console.error("计算全部饮品数据失败:", err)
  }
}

// 高峰时段数据（100%基于API数据）
const peakHours = ref([])

// 饮品统计数据（100%基于API数据）
const beverageStats = ref([])

// 计算高峰时段
function calculatePeakHours() {
  console.log(`📊 [业务看板] 开始计算高峰时段，订单数: ${businessData.orders?.length || 0}`)

  if (!businessData.orders || businessData.orders.length === 0) {
    console.log(`⚠️ [业务看板] 无订单数据，清空高峰时段`)
    peakHours.value = []
    return
  }

  const hourlyStats = new Map<string, { sales: number, count: number }>()

  // 按小时统计订单
  for (const order of businessData.orders) {
    const orderTime = new Date(order.orderTime)
    const hour = orderTime.getHours()
    const timeSlot = `${hour.toString().padStart(2, '0')}:00-${(hour + 1).toString().padStart(2, '0')}:00`

    const sales = parseFloat(order.totalAmount || '0')
    const current = hourlyStats.get(timeSlot) || { sales: 0, count: 0 }
    hourlyStats.set(timeSlot, {
      sales: current.sales + sales,
      count: current.count + 1
    })
  }

  // 转换为数组并找出高峰时段
  const hourlyArray = Array.from(hourlyStats.entries()).map(([time, data]) => ({
    time,
    sales: data.sales,
    count: data.count,
    isPeak: false
  }))

  // 按销售额排序，只取前4个销量最高的时段
  hourlyArray.sort((a, b) => b.sales - a.sales)
  const top4Hours = hourlyArray.slice(0, 4)

  // 标记销量最高的时段（第一个）为高峰，其他保持普通样式
  if (top4Hours.length > 0) {
    top4Hours[0].isPeak = true // 只有销量最高的时段使用放大样式
  }

  // 按时间重新排序显示
  top4Hours.sort((a, b) => a.time.localeCompare(b.time))
  peakHours.value = top4Hours

  console.log(`✅ [业务看板] 高峰时段计算完成，共${peakHours.value.length}个时段`)
}

// 计算饮品统计
function calculateBeverageStats() {
  console.log(`📊 [业务看板] 开始计算饮品统计，订单数: ${businessData.orders?.length || 0}`)

  if (!businessData.orders || businessData.orders.length === 0) {
    console.log(`⚠️ [业务看板] 无订单数据，清空饮品统计`)
    beverageStats.value = []
    return
  }

  const categoryStats = new Map<string, number>()

  // 这里可以根据实际的产品分类逻辑进行统计
  // 暂时使用简化的分类逻辑
  for (const order of businessData.orders) {
    let parsedItems
    try {
      parsedItems = JSON.parse(order.orderItems)
    } catch (e) {
      continue
    }
    if (!Array.isArray(parsedItems)) continue

    for (const item of parsedItems) {
      if (item && item.productName) {
        const productName = item.productName.toLowerCase()
        let category = '其他'

        if (productName.includes('咖啡') || productName.includes('拿铁') || productName.includes('美式')) {
          category = '咖啡类'
        } else if (productName.includes('茶') || productName.includes('奶茶')) {
          category = '茶饮类'
        } else if (productName.includes('果汁') || productName.includes('鲜榨')) {
          category = '果汁类'
        }

        const quantity = Number(item.quantity) || 1
        categoryStats.set(category, (categoryStats.get(category) || 0) + quantity)
      }
    }
  }

  beverageStats.value = Array.from(categoryStats.entries()).map(([type, count]) => ({
    type,
    count
  }))

  console.log(`✅ [业务看板] 饮品统计计算完成，共${beverageStats.value.length}个分类`)
}

// 营业额时段统计图表数据（100%基于API数据）
const revenueChartSeries = ref([
  {
    name: '营业额',
    data: []
  }
])

// 分析笔记数据（100%基于API数据）
const analysisNotes = ref([])

// 计算营业额图表数据
function calculateRevenueChart() {
  console.log(`📊 [业务看板] 开始计算营业额图表，订单数: ${businessData.orders?.length || 0}`)

  if (!businessData.orders || businessData.orders.length === 0) {
    console.log(`⚠️ [业务看板] 无订单数据，清空营业额图表`)
    revenueChartSeries.value = [{
      name: '营业额',
      data: []
    }]
    return
  }

  const hourlyRevenue = new Array(24).fill(0)

  // 按小时统计营业额
  for (const order of businessData.orders) {
    const orderTime = new Date(order.orderTime)
    const hour = orderTime.getHours()
    const revenue = parseFloat(order.totalAmount || '0')
    hourlyRevenue[hour] += revenue
  }

  // 只显示有数据的时段（通常是营业时间）
  const businessHours = []
  for (let i = 7; i <= 22; i++) { // 假设营业时间是7:00-22:00
    businessHours.push(Math.round(hourlyRevenue[i]))
  }

  revenueChartSeries.value = [{
    name: '营业额',
    data: businessHours
  }]

  console.log(`✅ [业务看板] 营业额图表计算完成，数据点: ${businessHours.length}个`)
}

// 计算分析笔记
function calculateAnalysisNotes() {
  console.log(`📊 [业务看板] 开始生成分析笔记，订单数: ${businessData.orders?.length || 0}`)

  if (!businessData.orders || businessData.orders.length === 0) {
    console.log(`⚠️ [业务看板] 无订单数据，显示暂无数据提示`)
    analysisNotes.value = ['今日暂无数据']
    return
  }

  const notes = []

  try {
    // 1. 营业额分析（需要昨日数据对比，这里简化处理）
    const todayRevenue = businessData.orders.reduce((sum, order) => {
      return sum + parseFloat(order.totalAmount || '0')
    }, 0)

    if (todayRevenue > 0) {
      notes.push(`今日营业额为¥${todayRevenue.toFixed(2)}`)
    }

    // 2. 饮品类别分析
    const categoryStats = new Map<string, number>()
    let totalItems = 0

    for (const order of businessData.orders) {
      let parsedItems
      try {
        parsedItems = JSON.parse(order.orderItems)
      } catch (e) {
        continue
      }
      if (!Array.isArray(parsedItems)) continue

      for (const item of parsedItems) {
        if (item && item.productName) {
          const productName = item.productName.toLowerCase()
          let category = '其他'

          if (productName.includes('咖啡') || productName.includes('拿铁') || productName.includes('美式')) {
            category = '咖啡类'
          } else if (productName.includes('茶') || productName.includes('奶茶')) {
            category = '茶饮类'
          } else if (productName.includes('果汁') || productName.includes('鲜榨')) {
            category = '果汁类'
          }

          const quantity = Number(item.quantity) || 1
          categoryStats.set(category, (categoryStats.get(category) || 0) + quantity)
          totalItems += quantity
        }
      }
    }

    // 找出占比最高的饮品类别
    if (categoryStats.size > 0) {
      let maxCategory = ''
      let maxCount = 0
      let maxPercentage = 0

      for (const [category, count] of categoryStats.entries()) {
        if (count > maxCount) {
          maxCount = count
          maxCategory = category
          maxPercentage = totalItems > 0 ? Math.round((count / totalItems) * 100) : 0
        }
      }

      if (maxCategory && maxPercentage > 0) {
        notes.push(`${maxCategory}饮品销量占比最高，达${maxPercentage}%`)
      }
    }

    // 3. 高峰时段分析
    const hourlyStats = new Map<number, number>()

    for (const order of businessData.orders) {
      const orderTime = new Date(order.orderTime)
      const hour = orderTime.getHours()
      hourlyStats.set(hour, (hourlyStats.get(hour) || 0) + 1)
    }

    if (hourlyStats.size > 0) {
      let peakHour = 0
      let maxOrders = 0

      for (const [hour, count] of hourlyStats.entries()) {
        if (count > maxOrders) {
          maxOrders = count
          peakHour = hour
        }
      }

      if (maxOrders > 0) {
        const peakPeriod = `${peakHour}:00-${peakHour + 1}:00`
        notes.push(`${peakPeriod}为销售高峰期，共${maxOrders}单`)
      }
    }

    // 4. 热门产品建议
    const productQuantities = new Map<string, number>()

    for (const order of businessData.orders) {
      let parsedItems
      try {
        parsedItems = JSON.parse(order.orderItems)
      } catch (e) {
        continue
      }
      if (!Array.isArray(parsedItems)) continue

      for (const item of parsedItems) {
        if (item && item.productName) {
          const quantity = Number(item.quantity) || 1
          productQuantities.set(item.productName, (productQuantities.get(item.productName) || 0) + quantity)
        }
      }
    }

    if (productQuantities.size > 0) {
      const sortedProducts = Array.from(productQuantities.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 3) // 取前3名

      if (sortedProducts.length > 0) {
        const topProduct = sortedProducts[0]
        notes.push(`建议增加"${topProduct[0]}"等热门产品库存`)
      }
    }

    // 如果没有生成任何分析，显示基础信息
    if (notes.length === 0) {
      notes.push('今日有订单数据，但分析信息不足')
    }

  } catch (err) {
    console.error('生成分析笔记失败:', err)
    notes.push('数据分析出现异常，请稍后重试')
  }

  analysisNotes.value = notes
  console.log(`✅ [业务看板] 分析笔记生成完成，共${notes.length}条`)
}

// 监听门店ID变化
watch(() => currentStoreId.value, (newStoreId) => {
  if (newStoreId && newStoreId !== filterCriteria.storeId) {
    filterCriteria.storeId = newStoreId
    fetchBusinessData()
  }
})

// 日期选择器配置
const flatpickrConfig = {
  dateFormat: "Y-m-d",
  locale: "zh",
  allowInput: true,
  enableTime: false,
  maxDate: new Date(), // 不能选择未来日期
}

// 监听日期变化
watch(() => filterCriteria.selectedDate, () => {
  fetchBusinessData()
})

// 计算统计数据
const businessStats = computed(() => {
  const totalOrders = businessData.orders.length
  const totalRevenue = businessData.orders.reduce((sum, order) => {
    return sum + parseFloat(order.totalAmount || '0')
  }, 0)
  const totalItems = businessData.queueItems.length
  const completedOrders = businessData.orders.filter(o => o.orderStatus === '4').length

  console.log(`📊 [业务看板] 统计计算结果:`)
  console.log(`   - 订单数: ${totalOrders}`)
  console.log(`   - 营业额: ¥${totalRevenue}`)
  console.log(`   - 杯数: ${totalItems}`)
  console.log(`   - 完成订单: ${completedOrders}`)

  return {
    totalOrders,
    totalRevenue,
    totalItems,
    completedOrders,
    completionRate: totalOrders > 0 ? Math.round((completedOrders / totalOrders) * 100) : 0
  }
})

// 营业额时段统计图表配置
const revenueChartOptions = ref({
  chart: {
    type: 'line',
    height: 200,
    toolbar: {
      show: false
    },
    fontFamily: 'Source Han Serif, serif'
  },
  colors: ['#6560F0'],
  stroke: {
    curve: 'smooth',
    width: 3
  },
  markers: {
    size: 6,
    colors: ['#6560F0'],
    strokeColors: '#fff',
    strokeWidth: 2,
    hover: {
      size: 8
    }
  },
  grid: {
    borderColor: '#f0f1f3',
    strokeDashArray: 3,
    xaxis: {
      lines: {
        show: true
      }
    },
    yaxis: {
      lines: {
        show: true
      }
    }
  },
  xaxis: {
    categories: ['7:00', '8:00', '9:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00'],
    labels: {
      style: {
        colors: '#6c757d',
        fontSize: '11px',
        fontFamily: 'Source Han Serif, serif'
      }
    },
    axisBorder: {
      show: false
    },
    axisTicks: {
      show: false
    }
  },
  yaxis: {
    labels: {
      style: {
        colors: '#6c757d',
        fontSize: '11px',
        fontFamily: 'Source Han Serif, serif'
      },
      formatter: function (value: number) {
        return '¥' + value
      }
    }
  },
  tooltip: {
    theme: 'light',
    style: {
      fontSize: '12px',
      fontFamily: 'Source Han Serif, serif'
    },
    y: {
      formatter: function (value: number) {
        return '¥' + value
      }
    }
  },
  dataLabels: {
    enabled: false
  }
})


</script>

<style scoped>
.store-business-dashboard {
  font-family: 'Source Han Serif', serif;
}

.store-business-dashboard * {
  font-family: 'Source Han Serif', serif;
}

.main-title {
  font-size: 2.5rem;
  color: var(--splash-primary-color);
  margin-bottom: 0;
}

/* 核心指标样式 */
.metric-item {
  padding: 10px 0;
}

.metric-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.metric-label {
  font-size: 14px;
  color: var(--splash-secondary-color);
  font-weight: 500;
  margin: 0;
}

.metric-value-container {
  display: flex;
  align-items: baseline;
  gap: 4px;
  justify-content: center;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: #000000;
}

.metric-value.strikethrough {
  text-decoration: line-through;
  color: #6c757d;
  font-size: 20px;
}

.metric-value.revenue {
  color: var(--splash-success-color);
}

.metric-value.orders {
  color: var(--splash-info-color);
}

.metric-value.cups {
  color: var(--splash-warning-color);
}

/* 统一单位颜色 */
.metric-unit {
  font-size: 16px;
  font-weight: 500;
}

.currency {
  color: var(--splash-success-color);
}

.order-unit {
  color: var(--splash-info-color);
}

.cup-unit {
  color: var(--splash-warning-color);
}

.unit {
  font-size: 16px;
  color: #6c757d;
  margin-left: 4px;
}

/* 全部饮品数据样式 */
.all-products {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.products-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.product-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #d1d5db;
}

.product-item:last-child {
  border-bottom: none;
}

.product-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  width: 100%;
}

.product-name-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.product-rank {
  background: transparent;
  color: #000000;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 700;
  flex-shrink: 0;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
}

.product-sales-container {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.product-sales {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
}

.product-unit {
  font-size: 12px;
  font-weight: 500;
}

/* 高峰时段样式 */
.peak-hours {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.hour-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.hour-item.peak-highlight {
  padding: 18px 20px;
  background-color: #f8f9fa;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.hour-item.peak-highlight .hour-time {
  font-weight: 600;
  font-size: 15px;
}

.hour-item.peak-highlight .hour-sales {
  font-weight: 700;
  font-size: 18px;
}

.hour-item.peak-highlight .hour-unit {
  font-weight: 600;
}

.hour-time {
  font-size: 13px;
  color: var(--splash-secondary-color);
  font-weight: 500;
}

.hour-sales-container {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.hour-sales {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
}

.hour-unit {
  font-size: 12px;
  font-weight: 500;
}

/* 饮品统计样式 */
.beverage-stats {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.beverage-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #d1d5db;
}

.beverage-item:last-child {
  border-bottom: none;
}

.beverage-type {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
}

.beverage-count-container {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.beverage-count {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
}

.beverage-unit {
  font-size: 12px;
  font-weight: 500;
}

/* 分析笔记样式 */
.notes-content p {
  font-size: 13px;
  line-height: 1.6;
}

/* 图表样式 */
.chart-note {
  font-size: 12px;
  color: #6c757d;
  font-weight: 400;
}

.chart-container {
  height: 200px;
  position: relative;
}

.chart-container .chart {
  font-family: 'Source Han Serif', serif;
}

/* 通用样式 */
.card.box-shadow {
  box-shadow: 0px 4px 34px rgba(101, 96, 240, 0.1);
  transition: box-shadow 0.3s ease;
}

.card.box-shadow:hover {
  box-shadow: 0px 6px 40px rgba(101, 96, 240, 0.15);
}

h5 {
  color: #000000;
}

.btn-primary {
  font-family: 'Source Han Serif', serif;
  background-color: var(--splash-primary-color);
  border-color: var(--splash-primary-color);
}

.btn-primary:hover {
  background-color: var(--splash-primary-active-color);
  border-color: var(--splash-primary-active-color);
  transform: translateY(-1px);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .main-title {
    font-size: 2rem;
  }
  
  .metric-value {
    font-size: 24px;
  }
  
  .peak-hours {
    grid-template-columns: 1fr;
  }
  
  .product-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .chart-container {
    height: 150px;
  }
}

@media (max-width: 576px) {
  .main-title {
    font-size: 1.75rem;
  }
  
  .metric-value {
    font-size: 20px;
  }
  
  .product-sales,
  .hour-sales,
  .beverage-count {
    font-size: 14px;
  }
}

/* 日期选择器样式 */
.date-picker {
  width: 160px;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  border: 1px solid #e3f2fd;
  border-radius: 6px;
  padding: 8px 12px;
  background-color: #fff;
  transition: all 0.2s ease;
}

.date-picker:focus {
  border-color: #6560F0;
  box-shadow: 0 0 0 0.2rem rgba(101, 96, 240, 0.25);
  outline: none;
}

.date-picker:hover {
  border-color: #6560F0;
}

/* 响应式日期选择器 */
@media (max-width: 768px) {
  .date-picker {
    width: 140px;
    font-size: 13px;
    padding: 6px 10px;
  }
}

@media (max-width: 576px) {
  .date-picker {
    width: 120px;
    font-size: 12px;
    padding: 5px 8px;
  }
}
</style>
