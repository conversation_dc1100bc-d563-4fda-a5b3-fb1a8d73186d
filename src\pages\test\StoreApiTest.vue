<template>
  <div class="container-fluid py-4">
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">门店API测试工具</h5>
          </div>
          <div class="card-body">
            <!-- 测试按钮 -->
            <div class="row g-3 mb-4">
              <div class="col-md-4">
                <button @click="testStoreList" class="btn btn-primary w-100" :disabled="loading">
                  <i class="flaticon-store me-2"></i>
                  测试门店列表API
                </button>
              </div>
              <div class="col-md-4">
                <div class="input-group">
                  <input 
                    type="number" 
                    v-model="testStoreId" 
                    class="form-control" 
                    placeholder="门店ID"
                  />
                  <button @click="testStoreDetail" class="btn btn-info" :disabled="loading || !testStoreId">
                    <i class="flaticon-info me-2"></i>
                    测试门店详情
                  </button>
                </div>
              </div>
              <div class="col-md-4">
                <button @click="clearResults" class="btn btn-outline-secondary w-100">
                  <i class="flaticon-refresh me-2"></i>
                  清空结果
                </button>
              </div>
            </div>

            <!-- 加载状态 -->
            <div v-if="loading" class="text-center py-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">测试中...</span>
              </div>
              <p class="mt-2 text-muted">正在测试API...</p>
            </div>

            <!-- 测试结果 -->
            <div v-if="testResults.length > 0" class="mt-4">
              <h6>测试结果</h6>
              <div class="accordion" id="testResultsAccordion">
                <div 
                  v-for="(result, index) in testResults" 
                  :key="index"
                  class="accordion-item"
                >
                  <h2 class="accordion-header">
                    <button 
                      class="accordion-button collapsed" 
                      type="button" 
                      data-bs-toggle="collapse" 
                      :data-bs-target="`#collapse${index}`"
                    >
                      <span :class="result.success ? 'text-success' : 'text-danger'">
                        <i :class="result.success ? 'flaticon-check' : 'flaticon-close'"></i>
                        {{ result.title }}
                      </span>
                      <small class="text-muted ms-2">{{ result.timestamp }}</small>
                    </button>
                  </h2>
                  <div 
                    :id="`collapse${index}`" 
                    class="accordion-collapse collapse"
                    data-bs-parent="#testResultsAccordion"
                  >
                    <div class="accordion-body">
                      <div class="row">
                        <div class="col-md-6">
                          <h6>请求信息</h6>
                          <table class="table table-sm">
                            <tr>
                              <td><strong>方法:</strong></td>
                              <td>{{ result.method }}</td>
                            </tr>
                            <tr>
                              <td><strong>URL:</strong></td>
                              <td><code>{{ result.url }}</code></td>
                            </tr>
                            <tr v-if="result.params">
                              <td><strong>参数:</strong></td>
                              <td><pre class="small">{{ JSON.stringify(result.params, null, 2) }}</pre></td>
                            </tr>
                          </table>
                        </div>
                        <div class="col-md-6">
                          <h6>响应信息</h6>
                          <table class="table table-sm">
                            <tr>
                              <td><strong>状态:</strong></td>
                              <td>
                                <span :class="result.success ? 'badge bg-success' : 'badge bg-danger'">
                                  {{ result.success ? '成功' : '失败' }}
                                </span>
                              </td>
                            </tr>
                            <tr v-if="result.responseTime">
                              <td><strong>响应时间:</strong></td>
                              <td>{{ result.responseTime }}ms</td>
                            </tr>
                            <tr v-if="result.dataCount !== undefined">
                              <td><strong>数据条数:</strong></td>
                              <td>{{ result.dataCount }}</td>
                            </tr>
                          </table>
                        </div>
                      </div>
                      
                      <!-- 响应数据 -->
                      <div class="mt-3">
                        <h6>响应数据</h6>
                        <div class="bg-light p-3 rounded">
                          <pre class="small mb-0" style="max-height: 400px; overflow-y: auto;">{{ JSON.stringify(result.data, null, 2) }}</pre>
                        </div>
                      </div>
                      
                      <!-- 错误信息 -->
                      <div v-if="result.error" class="mt-3">
                        <h6 class="text-danger">错误信息</h6>
                        <div class="alert alert-danger">
                          <pre class="mb-0">{{ result.error }}</pre>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 门店数据分析 -->
            <div v-if="storeAnalysis" class="mt-4">
              <h6>门店数据分析</h6>
              <div class="row g-3">
                <div class="col-md-3">
                  <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                      <h4>{{ storeAnalysis.total }}</h4>
                      <p class="mb-0">总门店数</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card bg-success text-white">
                    <div class="card-body text-center">
                      <h4>{{ storeAnalysis.active }}</h4>
                      <p class="mb-0">营业中</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                      <h4>{{ storeAnalysis.inactive }}</h4>
                      <p class="mb-0">暂停营业</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card bg-info text-white">
                    <div class="card-body text-center">
                      <h4>{{ storeAnalysis.statusValues.length }}</h4>
                      <p class="mb-0">状态类型</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="mt-3">
                <h6>状态分布</h6>
                <div class="table-responsive">
                  <table class="table table-sm">
                    <thead>
                      <tr>
                        <th>状态值</th>
                        <th>门店数量</th>
                        <th>门店列表</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="status in storeAnalysis.statusDistribution" :key="status.value">
                        <td><code>{{ status.value }}</code></td>
                        <td>{{ status.count }}</td>
                        <td>
                          <small>{{ status.stores.map(s => s.storeName).join(', ') }}</small>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getStoreList, getStoreByStoreId } from '@/utils/api/store'
import type { storeDetail } from '@/types'

// 测试结果类型定义
interface TestResult {
  title: string
  method: string
  url: string
  params?: Record<string, unknown>
  success: boolean
  data?: unknown
  error?: string
  dataCount?: number
  responseTime: number
  timestamp: string
}

// 门店分析类型定义
interface StoreAnalysis {
  total: number
  active: number
  inactive: number
  statusValues: string[]
  statusDistribution: Array<{
    value: string
    count: number
    stores: storeDetail[]
  }>
}

// 数据状态
const loading = ref(false)
const testStoreId = ref<number>()
const testResults = ref<TestResult[]>([])
const storeAnalysis = ref<StoreAnalysis | null>(null)

// 测试门店列表API
const testStoreList = async () => {
  loading.value = true
  const startTime = Date.now()
  
  try {
    console.log('🔄 [门店API测试] 开始测试门店列表API...')
    
    const params = { pageNum: 1, pageSize: 1000 }
    const response = await getStoreList(params)
    const endTime = Date.now()
    
    console.log('✅ [门店API测试] 门店列表API测试成功:', response)
    
    // 分析门店数据
    const stores = response.rows || []
    analyzeStoreData(stores)
    
    // 记录测试结果
    testResults.value.unshift({
      title: '门店列表API测试',
      method: 'GET',
      url: '/manager/store/list',
      params,
      success: true,
      data: response,
      dataCount: stores.length,
      responseTime: endTime - startTime,
      timestamp: new Date().toLocaleString()
    })
    
  } catch (error) {
    const endTime = Date.now()
    console.error('❌ [门店API测试] 门店列表API测试失败:', error)
    
    testResults.value.unshift({
      title: '门店列表API测试',
      method: 'GET',
      url: '/manager/store/list',
      params: { pageNum: 1, pageSize: 1000 },
      success: false,
      error: error instanceof Error ? error.message : String(error),
      responseTime: endTime - startTime,
      timestamp: new Date().toLocaleString()
    })
  } finally {
    loading.value = false
  }
}

// 测试门店详情API
const testStoreDetail = async () => {
  if (!testStoreId.value) return
  
  loading.value = true
  const startTime = Date.now()
  
  try {
    console.log(`🔄 [门店API测试] 开始测试门店详情API: ${testStoreId.value}`)
    
    const response = await getStoreByStoreId(testStoreId.value)
    const endTime = Date.now()
    
    console.log('✅ [门店API测试] 门店详情API测试成功:', response)
    
    testResults.value.unshift({
      title: `门店详情API测试 (ID: ${testStoreId.value})`,
      method: 'GET',
      url: `/manager/store/${testStoreId.value}`,
      success: true,
      data: response,
      responseTime: endTime - startTime,
      timestamp: new Date().toLocaleString()
    })
    
  } catch (error) {
    const endTime = Date.now()
    console.error('❌ [门店API测试] 门店详情API测试失败:', error)
    
    testResults.value.unshift({
      title: `门店详情API测试 (ID: ${testStoreId.value})`,
      method: 'GET',
      url: `/manager/store/${testStoreId.value}`,
      success: false,
      error: error instanceof Error ? error.message : String(error),
      responseTime: endTime - startTime,
      timestamp: new Date().toLocaleString()
    })
  } finally {
    loading.value = false
  }
}

// 分析门店数据
const analyzeStoreData = (stores: storeDetail[]) => {
  const statusMap = new Map<string, storeDetail[]>()
  
  stores.forEach(store => {
    const status = store.status || 'undefined'
    if (!statusMap.has(status)) {
      statusMap.set(status, [])
    }
    statusMap.get(status)!.push(store)
  })
  
  const statusDistribution = Array.from(statusMap.entries()).map(([value, stores]) => ({
    value,
    count: stores.length,
    stores
  }))
  
  storeAnalysis.value = {
    total: stores.length,
    active: statusMap.get('1')?.length || 0,
    inactive: statusMap.get('0')?.length || 0,
    statusValues: Array.from(statusMap.keys()),
    statusDistribution
  }
  
  console.log('📊 [门店API测试] 门店数据分析:', storeAnalysis.value)
}

// 清空结果
const clearResults = () => {
  testResults.value = []
  storeAnalysis.value = null
}
</script>

<style scoped>
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}

.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.accordion-button:not(.collapsed) {
  background-color: #f8f9fa;
}
</style>
