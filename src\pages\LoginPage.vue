<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-content">
        <div class="login-card">
          <div class="card-body">
            <!-- Logo和标题 -->
            <div class="text-center mb-4">
              <div class="logo-container mx-auto mb-3">
                <img src="@/assets/images/favicon.png" alt="logo" class="logo-image"/>
              </div>
              <h3 class="fw-bold text-dark">创界智联</h3>
              <p class="sub-text text-muted">咖啡店智能管理系统</p>
            </div>

            <!-- 登录表单 -->
            <form @submit.prevent="handleLogin">
              <!-- 用户名 -->
              <div class="mb-3">
                <label for="username" class="form-label">用户名</label>
                <input
                  id="username"
                  v-model="loginForm.username"
                  type="text"
                  class="form-control"
                  :class="{ 'is-invalid': errors.username }"
                  placeholder="请输入用户名"
                  required
                />
                <div v-if="errors.username" class="invalid-feedback">
                  {{ errors.username }}
                </div>
              </div>

              <!-- 密码 -->
              <div class="mb-3">
                <label for="password" class="form-label">密码</label>
                <div class="position-relative">
                  <input
                    id="password"
                    v-model="loginForm.password"
                    :type="showPassword ? 'text' : 'password'"
                    class="form-control"
                    :class="{ 'is-invalid': errors.password }"
                    placeholder="请输入密码"
                    required
                  />
                  <button
                    type="button"
                    class="btn btn-link position-absolute end-0 top-50 translate-middle-y pe-3"
                    @click="togglePassword"
                  >
                    <i :class="showPassword ? 'flaticon-eye-slash' : 'flaticon-eye'"></i>
                  </button>
                </div>
                <div v-if="errors.password" class="invalid-feedback">
                  {{ errors.password }}
                </div>
              </div>



              <!-- 记住我 -->
              <div class="mb-3">
                <div class="form-check">
                  <input
                    id="remember"
                    v-model="loginForm.remember"
                    type="checkbox"
                    class="form-check-input"
                  />
                  <label for="remember" class="form-check-label">
                    记住我
                  </label>
                </div>
              </div>

              <!-- 错误提示 -->
              <div v-if="authStore.error" class="alert alert-danger" role="alert">
                <i class="flaticon-warning me-2"></i>
                {{ authStore.error }}
              </div>

              <!-- 登录按钮 -->
              <button
                type="submit"
                class="btn btn-primary w-100 py-2"
                :disabled="authStore.isLoading"
              >
                <span v-if="authStore.isLoading" class="spinner-border spinner-border-sm me-2" role="status">
                  <span class="visually-hidden">Loading...</span>
                </span>
                {{ authStore.isLoading ? '登录中...' : '登录' }}
              </button>
            </form>

            <!-- 底部链接 -->
            <div class="text-center mt-4">
              <p class="text-muted small">
                © 2025 创界智联. All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/store/useAuthStore'

// 路由和状态管理
const router = useRouter()
const authStore = useAuthStore()

// 表单数据
const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

// 表单验证错误
const errors = reactive({
  username: '',
  password: ''
})

// 界面状态
const showPassword = ref(false)

// 切换密码显示
const togglePassword = () => {
  showPassword.value = !showPassword.value
}



// 表单验证
const validateForm = (): boolean => {
  // 重置错误
  errors.username = ''
  errors.password = ''

  let isValid = true

  // 验证用户名
  if (!loginForm.username.trim()) {
    errors.username = '请输入用户名'
    isValid = false
  } else if (loginForm.username.length < 2) {
    errors.username = '用户名至少2个字符'
    isValid = false
  }

  // 验证密码
  if (!loginForm.password) {
    errors.password = '请输入密码'
    isValid = false
  } else if (loginForm.password.length < 6) {
    errors.password = '密码至少6个字符'
    isValid = false
  }

  return isValid
}

// 处理登录
const handleLogin = async () => {
  // 表单验证
  if (!validateForm()) {
    return
  }

  try {
    // 调用登录接口
    await authStore.login({
      username: loginForm.username,
      password: loginForm.password
    })

    // 登录成功，跳转到首页
    router.push('/')
  } catch (error) {
    console.error('登录失败:', error)
  }
}


</script>

<style scoped>
/* 重置所有样式，确保完全独立 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.login-page {
  width: 100vw;
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
  position: relative;
}

.login-container {
  width: 100%;
  max-width: 450px;
  padding: 20px;
}

.login-content {
  width: 100%;
}

.login-card {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #dadce0;
}

.card-body {
  padding: 40px;
}

/* Logo和标题样式 */
.logo-container {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-image {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

h3 {
  color: #2c3e50;
  font-weight: 700;
  margin-bottom: 8px;
}

.sub-text {
  color: #6c757d;
  font-size: 14px;
  margin-bottom: 0;
}

/* 表单样式 */
.form-label {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 8px;
}

.form-control {
  border: 1px solid #dadce0;
  border-radius: 4px;
  padding: 12px 16px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: white;
}

.form-control:focus {
  border-color: #4285f4;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
  outline: none;
}

.form-control.is-invalid {
  border-color: #dc3545;
}

.invalid-feedback {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
}

/* 密码切换按钮 */
.btn-link {
  border: none !important;
  color: #6c757d;
  text-decoration: none;
  background: none;
  padding: 0;
}

.btn-link:hover {
  color: #495057;
}



/* 复选框样式 */
.form-check-input {
  border: 1px solid #dadce0;
}

.form-check-input:checked {
  background-color: #4285f4;
  border-color: #4285f4;
}

.form-check-label {
  color: #2c3e50;
  font-size: 14px;
}

/* 按钮样式 */
.btn-primary {
  background: #4285f4;
  border: none;
  font-weight: 500;
  padding: 12px 24px;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.2s ease;
  letter-spacing: 0.25px;
}

.btn-primary:hover {
  background: #3367d6;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.btn-primary:disabled {
  background: #dadce0;
  color: #5f6368;
  box-shadow: none;
}

/* 加载动画 */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* 错误提示 */
.alert {
  border-radius: 8px;
  border: none;
  font-size: 14px;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
}

/* 底部文字 */
.text-muted.small {
  color: #6c757d !important;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 576px) {
  .login-container {
    padding: 15px;
  }

  .card-body {
    padding: 30px 20px;
  }

  .logo-container {
    width: 60px;
    height: 60px;
  }

  .logo-image {
    width: 45px;
    height: 45px;
  }

  h3 {
    font-size: 1.5rem;
  }
}
</style>