<template>
  <div class="col-xxl-3 col-sm-6 mb-25">
    <div class="card  border-0 rounded-0 bg-white stats-item h-100">
      <div class="card-body p-15 p-sm-20 p-md-25 p-lg-30 letter-spacing">
        <div class="d-flex align-items-center justify-content-between">
          <div
              class="icon position-relative rounded-1 text-center"
              :class="`text-${color}`"
          >
            <i :class="icon"></i>
          </div>
          <div class="title text-end">
            <span class="d-block fw-bold text-dark-emphasis mb-5">
              {{ title }}
            </span>
            <div class="d-flex align-items-center justify-content-end">
              <h3 class="fw-black mb-0 me-10">{{ value }}</h3>

              <span v-if="change" class="fw-bold text-badge d-inline-block" :class="change.startsWith('+') ? 'text-success' : 'text-danger'">
                <i
                    :class="change.startsWith('+') ? 'flaticon-up-arrow' : 'flaticon-down-arrow'"
                    class="fs-11 lh-1 position-relative top-1"
                ></i>
                {{ change.substring(1) }}
              </span>
            </div>
            <span v-if="subText" class="d-block fw-medium text-dark-emphasis mt-5">
              {{ subText }}
            </span>
          </div>
        </div>
        <template v-if="progress !== undefined">
          <div class="mt-15 mt-md-25 mb-5 mb-md-8 d-flex justify-content-between align-items-center">
            <span class="fw-medium text-secondary">
              {{ progressText }}
            </span>
            <span class="fw-semibold" :class="`text-${color}`">{{ progress }}%</span>
          </div>
          <div
              class="progress"
              role="progressbar"
              :aria-valuenow="progress"
              aria-valuemin="0"
              aria-valuemax="100"
          >
            <div class="progress-bar" :class="`bg-${color}`" :style="{ width: progress + '%' }"></div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  title: { type: String, required: true },
  value: { type: [String, Number], required: true },
  icon: { type: String, required: true },
  color: { type: String, default: 'primary' },
  subText: { type: String, default: null },
  progress: { type: Number, default: undefined },
  progressText: { type: String, default: '' },

  // ▼▼▼ 您缺失的关键属性定义 ▼▼▼
  change: { type: String, default: null },
});
</script>