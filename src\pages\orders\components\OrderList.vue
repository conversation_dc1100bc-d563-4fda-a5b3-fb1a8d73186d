<template>
  <div class="card mb-25 border-0 rounded-0 bg-white letter-spacing">
<!--    表头上方功能区-->
    <div
        class="card-head box-shadow bg-white d-md-flex align-items-center justify-content-between p-15 p-sm-20 p-md-25"
    >
      <!-- 搜索框 -->
      <form class="search-box position-relative" @submit.prevent="handleSearch">
        <input
            type="text"
            class="form-control shadow-none text-black rounded-0 border-0"
            placeholder="Search order by ID, Name, Customer..."
            v-model="searchTerm"
        />
        <button
            type="submit"
            class="bg-transparent text-primary transition p-0 border-0"
        >
          <i class="flaticon-search-interface-symbol"></i>
        </button>
      </form>
      <!-- 一些操作按钮 -->
      <div class="d-sm-flex align-items-center mt-15 mt-md-0">
        <BasicButton
            variant="primary"
            size="md"
            custom-class="me-10"
            @click="updateOrderList"
        >
          更新数据
        </BasicButton>
        <button
            @click="exportOrders"
            class="default-outline-btn position-relative transition fw-medium text-black pt-10 pb-10 ps-25 pe-25 pt-md-11 pb-md-11 ps-md-30 pe-md-30 rounded-1 bg-transparent fs-md-15 fs-lg-16 d-inline-block mt-10 mt-md-0"
            type="button"
        >
          Export
          <i class="flaticon-file-1 position-relative ms-5 top-2 fs-15"></i>
        </button>
      </div>
    </div>
    <div class="card-body p-15 p-sm-20 p-md-25">
      <div v-if="isLoading" class="text-center">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>
      <div v-else-if="error" class="alert alert-danger" role="alert">
        {{ error }}
      </div>
      <div v-else class="table-responsive">
        <table class="table text-nowrap align-middle mb-0">
          <thead>
          <tr>
            <!-- 表头 -->
            <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0 ps-0">
              #订单编号
            </th>
            <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">
              订单内容
            </th>
            <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">
              客户信息
            </th>
            <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">
              付款金额
            </th>
            <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">
              下单时间
            </th>
            <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">
              下单门店
            </th>
            <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0">
              订单状态
            </th>
            <th scope="col" class="text-uppercase fw-medium shadow-none text-body-tertiary fs-13 pt-0 text-end pe-0">
              更多操作
            </th>
          </tr>
          </thead>
          <tbody>
          <tr v-if="filteredOrders.length === 0">
            <td colspan="9" class="text-center text-muted fst-italic">No orders found.</td>
          </tr>
          <!-- 循环制表 -->
          <tr v-for="order in filteredOrders" :key="order.id">
            <td class="shadow-none lh-1 fw-medium text-paragraph">
              #{{ order.id }}
            </td>
            <td class="shadow-none lh-1 fw-medium text-paragraph">
              {{ order.customer }}
            </td>
            <td class="shadow-none lh-1 fw-medium text-paragraph">
              ${{ order.price.toFixed(2) }} <!-- Basic price formatting -->
            </td>
            <td class="shadow-none lh-1 fw-medium text-paragraph">
              {{ order.date}} <!-- Date formatting -->
            </td>
            <td class="shadow-none lh-1 fw-medium text-paragraph">
              {{ order.vendor }}
            </td>
            <td class="shadow-none lh-1 fw-medium">
              <!-- 实时状态标记 -->
              <span :class="getOrderStatusClass(order.orderStatus)">
                   {{ order.orderStatus }}
                 </span>
            </td>
            <td
                class="shadow-none lh-1 fw-medium text-paragraph text-end pe-0"
            >
              <!-- 更多操作的下拉选框 -->
              <div class="dropdown">
                <button
                    class="dropdown-toggle lh-1 bg-transparent border-0 shadow-none p-0 transition"
                    type="button"
                    data-bs-toggle="dropdown"
                    aria-expanded="false"
                >
                  <i class="flaticon-dots"></i>
                </button>
                <ul class="dropdown-menu">
                  <li>
                    <a
                        class="dropdown-item d-flex align-items-center"
                        href="#" @click.prevent="viewOrder(order)"
                    ><i
                        class="flaticon-view lh-1 me-8 position-relative top-1"
                    ></i>
                      View</a
                    >
                  </li>
                  <li>
                    <a
                        class="dropdown-item d-flex align-items-center"
                        href="#" @click.prevent="editOrder(order)"
                    ><i
                        class="flaticon-pen lh-1 me-8 position-relative top-1"
                    ></i>
                      Edit</a
                    >
                  </li>
                  <li>
                    <a
                        class="dropdown-item d-flex align-items-center"
                        href="#" @click.prevent="deleteOrder(order)"
                    ><i
                        class="flaticon-delete lh-1 me-8 position-relative top-1"
                    ></i>
                      Delete</a
                    >
                  </li>
                </ul>
              </div>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
      <!-- 分页(目前是静态) -->
      <div
          class="pagination-area d-md-flex mt-15 mt-sm-20 mt-md-25 justify-content-between align-items-center"
      >
        <p class="mb-0 text-paragraph">
          Showing <span class="fw-bold">{{ filteredOrders.length }}</span> out of
          <span class="fw-bold">{{ totalOrders }}</span> results
          <span v-if="searchTerm"> (filtered)</span>
        </p>
        <!-- Add dynamic pagination controls here later if needed -->
        <nav class="mt-15 mt-md-0">
          <ul class="pagination mb-0">
            <li class="page-item disabled"> <!-- Add logic later -->
              <a class="page-link" href="#" aria-label="Previous">
                <i class="flaticon-chevron-1"></i>
              </a>
            </li>
            <li class="page-item active"><a class="page-link" href="#">1</a></li>
            <li class="page-item"><a class="page-link" href="#">2</a></li>
            <li class="page-item"><a class="page-link" href="#">3</a></li>
            <li class="page-item">
              <a class="page-link" href="#" aria-label="Next">
                <i class="flaticon-chevron"></i>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable */
import {type orderInfomation, orderArray} from "@/types/order";
import {useOrderStore} from "@/store/useOrderStore";
import {computed, ref} from "vue";
import {storeToRefs} from "pinia";
import BasicButton from "@/components/BasicButton.vue";
// 订单信息
const orderStore = useOrderStore();
const {orders} = storeToRefs(orderStore);
// 搜索关键词
const searchTerm = ref<string>("");

// 筛选的订单,使用计算属性(根据orders计算出筛选的订单)
// 匹配订单含有的所有属性
const filteredOrders = computed<orderArray>(() => {
  const term = searchTerm.value.trim().toLowerCase();
  if (!term) {
    return orders.value;
  }
  return orders.value.filter(order => {
    return Object.values(order).some(value => {
      const stringValue = String(value).toLowerCase();
      return stringValue.includes(term);
    })
  })
})

function getOrderStatusClass(status: string) {
  const classTag: string[] = [];
  classTag.push("badge");
  return classTag;
}
// 从服务器获取新的数据
function updateOrderList(){
  // 获取当前选中的门店ID，如果没有选中则使用默认值
  const storeId = 1; // 这里应该从某个地方获取当前门店ID
  orderStore.fetchOrdersByStore(storeId);
}

</script>

<style scoped>
/* Add any component-specific styles here if needed */
.product-title img {
  object-fit: cover; /* Ensure product images don't distort */
  border-radius: 4px; /* Optional: slightly rounded corners for images */
}

/* Make status badges slightly more prominent if needed */
.badge {
  padding: 0.4em 0.7em;
  font-size: 0.8rem;
  font-weight: 600;
}

/* You might need to adjust dropdown positioning depending on your CSS framework version */
.dropdown-menu {
  /* Potential adjustments if needed */
}

/* Style for no results row */
.fst-italic {
  font-style: italic !important;
}
</style>
