.cover-image-card {
    .card-body {
        .file-upload {
            left: 15px;
            width: 32px;
            height: 32px;
            bottom: 15px;
            font-size: 16px;
            overflow: hidden;
            position: absolute;

            i {
                left: 0;
                top: 50%;
                right: 0;
                line-height: 1;
                position: absolute;
                transform: translateY(-50%);
            }
            input {
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                opacity: 0;
                border-radius: 50%;
                position: absolute;
            }
        }
        .settings-tabs {
            display: block;
        
            li {
                flex: unset;
                display: inline-block;
                
                a {
                    padding: 11px 45px;
                }
            }
        }
    }
}
.settings-tabs {
    display: flex;
    flex-wrap: wrap;

    li {
        flex: 1 0 0%;
        margin: {
            left: 5px;
            right: 5px;
        };
        a {
            background: #F2F1F9;
            padding: 11px 15px;

            &:hover, &.active {
                background-color: var(--splash-primary-color);
                color: var(--splash-white-color);
            }
        }
    }
}
.settings-card {
    .card-body {
        form {
            .input-group {
                .input-group-text {
                    padding: 10px 18px;
                    border-right: none;
                    background: #F5F4FA;
                    border-color: #dedee4;
                }
                .form-control {
                    border-left: none;
                }
            }
            .file-upload {
                border: 1px solid #dedee4;
                padding: 55px 15px;
        
                i {
                    line-height: 1;
                    font-size: 35px;
                    margin-bottom: 5px;
                    color: var(--splash-primary-color);
                }
                span {
                    span {
                        &::before {
                            left: 0;
                            right: 0;
                            height: 1px;
                            content: '';
                            bottom: -2px;
                            position: absolute;
                            background: var(--splash-black-color);
                        }
                    }
                }
                input {
                    cursor: pointer;
                }
            }
            .members-list {
                div {
                    margin: {
                        top: 10px;
                        right: 5px;
                    };
                }
                button {
                    font-size: 8px;
                    margin-left: 3px;
        
                    &:hover {
                        color: red;
                    }
                }
            }
            button {
                span {
                    line-height: 1.3;
        
                    &::before {
                        left: 0;
                        right: 0;
                        bottom: 0;
                        height: 1px;
                        content: '';
                        position: absolute;
                        transition: var(--transition);
                        background: var(--splash-danger-color);
                    }
                }
            }
            .ql-container {
                height: 150px;
            }
        }
        .border-top {
            border-top: 1px dashed #D2CFE4 !important;
        }
        p {
            margin-bottom: 15px;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}
.profile-intro-card {
    .card-body {
        .user-info {
            border-bottom: 1px dashed #D2CFE4;
            padding-bottom: 20px;
            margin-bottom: 20px;

            .image {
                img {
                    border: 2px solid var(--splash-white-color);
                    filter: drop-shadow(0px 4px 4px rgba(101, 96, 240, 0.1));
                }
                .file-upload {
                    right: 0;
                    bottom: 0;
                    width: 32px;
                    height: 32px;
                    font-size: 16px;
                    overflow: hidden;
                    position: absolute;
                    box-shadow: 0px 4px 4px rgba(101, 96, 240, 0.1);

                    i {
                        left: 0;
                        top: 50%;
                        right: 0;
                        line-height: 1;
                        position: absolute;
                        transform: translateY(-50%);
                    }
                    input {
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        opacity: 0;
                        border-radius: 50%;
                        position: absolute;
                    }
                }
            }
        }
        .content {
            padding-right: 45px;
            margin-bottom: 20px;

            button {
                right: 0;
                top: 50%;
                width: 28px;
                height: 28px;
                position: absolute;
                background: #F2F1F9;
                transform: translateY(-50%);
                color: var(--splash-primary-color);

                i {
                    left: 0;
                    top: 50%;
                    right: 0;
                    line-height: 1;
                    position: absolute;
                    transform: translateY(-50%);
                }
                &:hover {
                    background-color: var(--splash-primary-color);
                    color: var(--splash-white-color);
                }
            }
        }
        .info-card {
            margin: {
                left: -5px;
                right: -5px;
            };
            .col-sm-4 {
                padding: {
                    left: 5px;
                    right: 5px;
                };
                &:nth-child(2) {
                    .info {
                        background: #ECF3F2;
                    } 
                }
                &:nth-child(3) {
                    .info {
                        background: #F3F7F9;
                    } 
                }
            }
            .info {
                background: #F2F1F9;
                margin-top: 10px;
            }
        }
    }
}

// Dark Mode
.dark {
    .settings-tabs {
        li {
            a {
                background: var(--splash-black-color);
    
                &:hover, &.active {
                    background-color: var(--splash-primary-color);
                }
            }
        }
    }
    .profile-intro-card {
        .card-body {
            .user-info {
                border-bottom-color: #45445e;

                .image {
                    img {
                        border-color: #45445e;
                    }
                }
            }
            .content {
                button {
                    background: var(--splash-black-color);

                    &:hover {
                        background-color: var(--splash-primary-color);
                    }
                }
            }
            .info-card {
                .col-sm-4 {
                    &:nth-child(2) {
                        .info {
                            background: var(--splash-black-color);
                        } 
                    }
                    &:nth-child(3) {
                        .info {
                            background: var(--splash-black-color);
                        } 
                    }
                }
                .info {
                    background: var(--splash-black-color);
                }
            }
        }
    }
    .settings-card {
        .card-body {
            form {
                .input-group {
                    .input-group-text {
                        background: var(--splash-black-color);
                        border-color: #45445e;
                    }
                }
                .file-upload {
                    border-color: #45445e;
            
                    span {
                        span {
                            &::before {
                                background: var(--splash-white-color);
                            }
                        }
                    }
                }
            }
            .border-top {
                border-top-color: #45445e !important;
            }
        }
    }
}

@media only screen and (max-width : 767px) {

    .cover-image-card {
        .card-body {
            .file-upload {
                left: 10px;
                width: 30px;
                height: 30px;
                bottom: 10px;
                font-size: 15px;
            }
            .settings-tabs {
                li {
                    a {
                        padding: 9px 25px;
                    }
                }
            }
        }
    }
    .settings-tabs {
        display: block;
        margin-bottom: -10px !important;
    
        li {
            flex: unset;
            display: inline-block;
            margin: {
                left: 5px;
                right: 5px;
                bottom: 8px;
            };
            a {
                padding: 9px 15px;
            }
        }
    }
    .profile-intro-card {
        .card-body {
            .user-info {
                padding-bottom: 15px;
                margin-bottom: 15px;
            }
            .content {
                padding-right: 35px;
                margin-bottom: 15px;
            }
            .info-card {
                margin-top: -10px;
            }
        }
    }

}