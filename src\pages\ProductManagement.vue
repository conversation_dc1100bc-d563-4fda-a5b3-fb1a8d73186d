<template>
  <div class="product-management">
    <!-- 面包屑导航 -->
    <BreadCrumb title="商品管理" :breadcrumb="breadcrumb" />

    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-30">
      <h1 class="main-title">商品管理</h1>
      <div class="action-buttons">
        <button @click="refreshData" class="btn btn-outline-secondary me-2">
          刷新数据
        </button>
        <button @click="showQuickAdd" class="btn btn-primary">
          快速添加
        </button>
      </div>
    </div>

    <!-- 标签页导航 -->
    <div class="card border-0 rounded-0 bg-white box-shadow">
      <div class="card-header bg-light border-bottom">
        <ul class="nav nav-tabs card-header-tabs" id="productTabs" role="tablist">

          <li class="nav-item" role="presentation">
            <button
              class="nav-link active"
              id="prototype-tab"
              data-bs-toggle="tab"
              data-bs-target="#prototype-pane"
              type="button"
              role="tab"
              @click="activeTab = 'prototype'"
            >
              <i class="flaticon-coffee-cup me-2"></i>
              饮品原型管理
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button 
              class="nav-link" 
              id="product-tab" 
              data-bs-toggle="tab" 
              data-bs-target="#product-pane" 
              type="button" 
              role="tab"
              @click="activeTab = 'product'"
            >
              <i class="flaticon-shopping-cart me-2"></i>
              商品管理
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button 
              class="nav-link" 
              id="store-menu-tab" 
              data-bs-toggle="tab" 
              data-bs-target="#store-menu-pane" 
              type="button" 
              role="tab"
              @click="activeTab = 'store-menu'"
            >
              <i class="flaticon-menu me-2"></i>
              门店菜单管理
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button 
              class="nav-link" 
              id="price-tab" 
              data-bs-toggle="tab" 
              data-bs-target="#price-pane" 
              type="button" 
              role="tab"
              @click="activeTab = 'price'"
            >
              <i class="flaticon-price-tag me-2"></i>
              价格管理
            </button>
          </li>
        </ul>
      </div>

      <!-- 标签页内容 -->
      <div class="tab-content" id="productTabsContent">
        <!-- 饮品原型管理 -->
        <div class="tab-pane fade show active" id="prototype-pane" role="tabpanel">
          <PrototypeManagement />
        </div>

        <!-- 商品管理 -->
        <div class="tab-pane fade" id="product-pane" role="tabpanel">
          <ProductItemManagement />
        </div>

        <!-- 门店菜单管理 -->
        <div class="tab-pane fade" id="store-menu-pane" role="tabpanel">
          <StoreMenuManagement />
        </div>

        <!-- 价格管理 -->
        <div class="tab-pane fade" id="price-pane" role="tabpanel">
          <PriceManagement />
        </div>
      </div>
    </div>

    <!-- 快速添加模态框 -->
    <div class="modal fade" id="quickAddModal" tabindex="-1" ref="quickAddModalRef">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">快速添加</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <div class="d-grid gap-2">

              <button @click="quickAddAction('prototype')" class="btn btn-outline-success">
                新增饮品原型
              </button>
              <button @click="quickAddAction('product')" class="btn btn-outline-info">
                新增商品
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import BreadCrumb from "@/components/layouts/BreadCrumb.vue"

import PrototypeManagement from "@/components/product/PrototypeManagement.vue"
import ProductItemManagement from "@/components/product/ProductItemManagement.vue"
import StoreMenuManagement from "@/components/product/StoreMenuManagement.vue"
import PriceManagement from "@/components/product/PriceManagement.vue"
import { Modal } from 'bootstrap'

// 面包屑导航
const breadcrumb = ref([
  { label: '商品管理', url: '/product-management' }
])

// 当前活跃的标签页
const activeTab = ref('prototype')
const quickAddModalRef = ref<HTMLElement>()
let quickAddModal: Modal | null = null

// 刷新数据
const refreshData = () => {
  // 触发当前活跃标签页的数据刷新
  console.log('刷新数据:', activeTab.value)
}

// 显示快速添加模态框
const showQuickAdd = () => {
  quickAddModal?.show()
}

// 快速添加操作
const quickAddAction = (type: string) => {
  quickAddModal?.hide()
  
  // 切换到对应的标签页并触发添加操作
  switch (type) {

    case 'prototype': {
      activeTab.value = 'prototype'
      const prototypeTab = document.getElementById('prototype-tab')
      prototypeTab?.click()
      break
    }
    case 'product': {
      activeTab.value = 'product'
      const productTab = document.getElementById('product-tab')
      productTab?.click()
      break
    }
  }
}

// 初始化
onMounted(async () => {
  await nextTick()
  if (quickAddModalRef.value) {
    quickAddModal = new Modal(quickAddModalRef.value)
  }
})
</script>

<style scoped>
.product-management {
  padding: 20px;
}

.main-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0;
}

.box-shadow {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.nav-tabs .nav-link {
  color: #495057;
  border: none;
  border-bottom: 3px solid transparent;
  background: none;
  padding: 15px 20px;
  font-weight: 500;
}

.nav-tabs .nav-link:hover {
  border-color: transparent;
  background-color: #f8f9fa;
}

.nav-tabs .nav-link.active {
  color: #007bff;
  background-color: #fff;
  border-bottom-color: #007bff;
}

.tab-content {
  padding: 0;
}

.tab-pane {
  min-height: 600px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

@media (max-width: 768px) {
  .product-management {
    padding: 15px;
  }
  
  .main-title {
    font-size: 1.5rem;
  }
  
  .nav-tabs .nav-link {
    padding: 12px 15px;
    font-size: 0.9rem;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 5px;
  }
}
</style>
