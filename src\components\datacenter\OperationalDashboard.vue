<template>
  <div class="operational-dashboard p-20">
    <!-- 时间筛选器 -->
    <div class="time-filter-section mb-20">
      <div class="d-flex justify-content-between align-items-center">
        <h5 class="mb-0">运营数据看板</h5>
        <div class="time-controls d-flex gap-3">
          <select v-model="timeRange" class="form-select" style="width: 150px;" @change="handleTimeRangeChange">
            <option value="today">今日</option>
            <option value="week">本周</option>
            <option value="month">本月</option>
            <option value="quarter">本季度</option>
            <option value="year">本年</option>
            <option value="custom">自定义</option>
          </select>
          <div v-if="timeRange === 'custom'" class="d-flex gap-2">
            <input v-model="customStartDate" type="date" class="form-control" style="width: 150px;">
            <input v-model="customEndDate" type="date" class="form-control" style="width: 150px;">
            <button @click="applyCustomRange" class="btn btn-primary">应用</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="text-center py-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
      </div>
      <p class="mt-2 text-muted">正在获取数据...</p>
    </div>

    <!-- 错误提示 -->
    <div v-else-if="error" class="alert alert-danger" role="alert">
      <i class="flaticon-warning me-2"></i>
      {{ error }}
      <button @click="fetchDashboardData" class="btn btn-outline-danger btn-sm ms-2">重试</button>
    </div>

    <!-- 核心指标卡片 -->
    <div v-else class="metrics-cards mb-30">
      <div class="row">
        <div class="col-lg-3 col-md-6 mb-20">
          <div class="metric-card">
            <div class="metric-icon">
              <i class="flaticon-chart text-primary"></i>
            </div>
            <div class="metric-content">
              <h3 class="metric-value">{{ dashboardData.totalRevenue }}</h3>
              <p class="metric-label">总营收 (元)</p>
              <span class="metric-change positive">+{{ dashboardData.revenueGrowth }}%</span>
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-20">
          <div class="metric-card">
            <div class="metric-icon">
              <i class="flaticon-shopping-cart text-success"></i>
            </div>
            <div class="metric-content">
              <h3 class="metric-value">{{ dashboardData.totalOrders }}</h3>
              <p class="metric-label">订单总数</p>
              <span class="metric-change positive">+{{ dashboardData.ordersGrowth }}%</span>
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-20">
          <div class="metric-card">
            <div class="metric-icon">
              <i class="flaticon-user text-info"></i>
            </div>
            <div class="metric-content">
              <h3 class="metric-value">{{ dashboardData.avgOrderValue }}</h3>
              <p class="metric-label">客单价 (元)</p>
              <span class="metric-change negative">{{ dashboardData.avgOrderChange }}%</span>
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-20">
          <div class="metric-card">
            <div class="metric-icon">
              <i class="flaticon-store text-warning"></i>
            </div>
            <div class="metric-content">
              <h3 class="metric-value">{{ dashboardData.activeStores }}</h3>
              <p class="metric-label">活跃门店</p>
              <span class="metric-change neutral">{{ dashboardData.storeChange }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="row">
        <!-- 销售趋势图 -->
        <div class="col-lg-8 mb-30">
          <div class="chart-card">
            <div class="chart-header">
              <h6 class="chart-title">销售趋势分析</h6>
              <div class="chart-controls">
                <button 
                  v-for="period in ['日', '周', '月']" 
                  :key="period"
                  @click="changeTrendPeriod(period)"
                  :class="['btn', 'btn-sm', trendPeriod === period ? 'btn-primary' : 'btn-outline-secondary']"
                >
                  {{ period }}
                </button>
              </div>
            </div>
            <div class="chart-container">
              <apexchart
                v-if="chartsReady"
                type="line"
                height="280"
                :options="salesTrendChartOptions"
                :series="salesTrendChartSeries"
                class="chart"
              />
              <div v-else class="chart-loading">
                <div class="text-center py-5">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                  </div>
                  <p class="mt-2 text-muted">图表加载中...</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 营收占比饼图 -->
        <div class="col-lg-4 mb-30">
          <div class="chart-card">
            <div class="chart-header">
              <h6 class="chart-title">门店营收占比</h6>
            </div>
            <div class="chart-container">
              <apexchart
                v-if="chartsReady"
                type="donut"
                height="280"
                :options="revenueDistributionChartOptions"
                :series="revenueDistributionChartSeries"
                class="chart"
              />
              <div v-else class="chart-loading">
                <div class="text-center py-5">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                  </div>
                  <p class="mt-2 text-muted">图表加载中...</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 用户画像分析 -->
        <div class="col-lg-6 mb-30">
          <div class="chart-card">
            <div class="chart-header">
              <h6 class="chart-title">用户画像分析</h6>
            </div>
            <div class="user-profile-content">
              <div class="profile-section">
                <h6>性别分布</h6>
                <div class="gender-distribution">
                  <div class="gender-item female">
                    <div class="gender-icon">
                      ♀
                    </div>
                    <div class="gender-content">
                      <div class="gender-label">女性</div>
                      <div class="gender-percentage">{{ genderDistribution.female }}%</div>
                    </div>
                  </div>
                  <div class="gender-item male">
                    <div class="gender-icon">
                      ♂
                    </div>
                    <div class="gender-content">
                      <div class="gender-label">男性</div>
                      <div class="gender-percentage">{{ genderDistribution.male }}%</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="profile-section">
                <h6>年龄分布</h6>
                <div class="age-distribution">
                  <div v-for="age in dashboardData.ageDistribution" :key="age.range" class="age-item">
                    <span class="age-label">{{ age.range }}</span>
                    <div class="age-bar">
                      <div class="age-progress" :style="{ width: age.percentage + '%' }"></div>
                    </div>
                    <span class="age-value">{{ age.percentage }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 热门商品排行 -->
        <div class="col-lg-6 mb-30">
          <div class="chart-card ranking-card">
            <div class="chart-header">
              <div class="ranking-header">
                <i class="flaticon-trophy text-warning"></i>
                <h6 class="chart-title">热门商品排行</h6>
              </div>
            </div>
            <div class="ranking-list">
              <!-- 空状态 -->
              <div v-if="!isLoading && dashboardData.popularProducts.length === 0" class="empty-state">
                <i class="flaticon-warning me-2"></i>
                暂无热门商品数据
              </div>
              <!-- 商品列表 -->
              <div v-else v-for="(product, index) in dashboardData.popularProducts" :key="product.id" class="ranking-item">
                <div class="rank-number" :class="getRankClass(index + 1)">{{ index + 1 }}</div>
                <div class="product-info">
                  <div class="product-name">{{ product.name }}</div>
                  <div class="product-metrics">
                    <div class="product-sales">
                      <span class="sales-value">{{ product.sales }}</span>
                      <span class="sales-unit">杯</span>
                    </div>
                    <div class="product-revenue">
                      <span class="revenue-value">¥{{ product.revenue }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import serviceAxios from '@/utils/serviceAxios'
import { format } from 'date-fns'
import type { drinkOrder } from '@/types/order'

// 加载状态和错误处理
const isLoading = ref(false)
const error = ref<string | null>(null)
const chartsReady = ref(false)

// 时间筛选
const timeRange = ref('today')
const customStartDate = ref('')
const customEndDate = ref('')
const trendPeriod = ref('日')

// 仪表板数据
const dashboardData = ref({
  totalRevenue: '0',
  revenueGrowth: '0',
  totalOrders: '0',
  ordersGrowth: '0',
  avgOrderValue: '0',
  avgOrderChange: '0',
  activeStores: '0',
  storeChange: '0',
  ageDistribution: [] as Array<{ range: string; percentage: number }>,
  popularProducts: [] as Array<{ id: number; name: string; sales: number; revenue: string }>
})

// 处理时间范围变化
const handleTimeRangeChange = () => {
  if (timeRange.value !== 'custom') {
    fetchDashboardData()
  }
}

// 应用自定义时间范围
const applyCustomRange = () => {
  if (customStartDate.value && customEndDate.value) {
    // 验证日期范围
    const start = new Date(customStartDate.value)
    const end = new Date(customEndDate.value)

    if (start > end) {
      alert('开始日期不能晚于结束日期')
      return
    }

    console.log(`🔄 应用自定义时间范围: ${customStartDate.value} 到 ${customEndDate.value}`)

    // 重置图表状态，确保重新加载
    chartsReady.value = false

    fetchDashboardData().then(() => {
      setTimeout(() => {
        chartsReady.value = true
      }, 200)
    })
  } else {
    alert('请选择开始日期和结束日期')
  }
}

// 改变趋势图周期
const changeTrendPeriod = (period: string) => {
  trendPeriod.value = period
  updateSalesTrendChart()
}

// 销售趋势图配置
const salesTrendChartSeries = ref([
  {
    name: '销售额',
    data: [12000, 15000, 13000, 18000, 16000, 22000, 20000]
  }
])

const salesTrendChartOptions = ref({
  chart: {
    type: 'line',
    height: 280,
    toolbar: {
      show: false
    },
    fontFamily: 'Source Han Serif, serif',
    background: 'transparent',
    animations: {
      enabled: true,
      easing: 'easeinout',
      speed: 800
    },
    dropShadow: {
      enabled: true,
      color: '#000',
      top: 18,
      left: 7,
      blur: 10,
      opacity: 0.1
    }
  },
  colors: ['#6560F0'], // 使用项目主色
  fill: {
    type: 'gradient',
    gradient: {
      shade: 'light',
      type: 'vertical',
      shadeIntensity: 0.5,
      gradientToColors: ['#726eed'], // 使用项目主色的激活态
      inverseColors: false,
      opacityFrom: 0.8,
      opacityTo: 0.1,
      stops: [0, 100]
    }
  },
  stroke: {
    curve: 'smooth',
    width: 4,
    lineCap: 'round'
  },
  dataLabels: {
    enabled: false
  },
  markers: {
    size: 6,
    colors: ['#6560F0'], // 使用项目主色
    strokeColors: '#fff',
    strokeWidth: 2,
    hover: {
      size: 8
    }
  },
  xaxis: {
    categories: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    labels: {
      style: {
        colors: '#64748b',
        fontSize: '13px',
        fontFamily: 'Source Han Serif, serif',
        fontWeight: 500
      }
    },
    axisBorder: {
      show: false
    },
    axisTicks: {
      show: false
    }
  },
  yaxis: {
    labels: {
      style: {
        colors: '#64748b',
        fontSize: '13px',
        fontFamily: 'Source Han Serif, serif',
        fontWeight: 500
      },
      formatter: function (value: number) {
        return '¥' + value.toLocaleString()
      }
    }
  },
  grid: {
    borderColor: '#e2e8f0',
    strokeDashArray: 0,
    xaxis: {
      lines: {
        show: false
      }
    },
    yaxis: {
      lines: {
        show: true
      }
    },
    padding: {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0
    }
  },
  tooltip: {
    theme: 'light',
    style: {
      fontSize: '15px',
      fontFamily: 'Source Han Serif, serif'
    },
    custom: function({series, seriesIndex, dataPointIndex, w}) {
      return '<div style="padding: 12px 16px; background: #ffffff; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); border: 1px solid #e5e7eb;">' +
        '<div style="font-weight: 600; color: #6560F0; margin-bottom: 6px; font-size: 16px;">' + w.globals.labels[dataPointIndex] + '</div>' +
        '<div style="color: #374151; font-size: 14px;">' +
        '<span style="color: #6b7280;">销售额: </span>' +
        '<span style="font-weight: 600; color: #111827;">¥' + series[seriesIndex][dataPointIndex].toLocaleString() + '</span>' +
        '</div>' +
        '</div>'
    }
  }
})

// 营收分布图配置 - 使用真实门店数据
const revenueDistributionChartSeries = ref<number[]>([])

const revenueDistributionChartOptions = ref({
  chart: {
    type: 'donut',
    height: 280,
    fontFamily: 'Source Han Serif, serif',
    animations: {
      enabled: true,
      easing: 'easeinout',
      speed: 800
    }
  },
  labels: [] as string[],
  // 使用项目主题色彩，营收数据使用商务色调
  colors: ['#6560F0', '#06b48a', '#1FB1E6', '#F3C44C', '#8e8da2'],
  legend: {
    position: 'bottom',
    fontSize: '14px',
    fontFamily: 'Source Han Serif, serif',
    fontWeight: 500,
    labels: {
      colors: '#2b2a3f' // 使用项目主文字色
    },
    markers: {
      width: 12,
      height: 12,
      radius: 6
    }
  },
  plotOptions: {
    pie: {
      donut: {
        size: '65%',
        labels: {
          show: true,
          name: {
            show: true,
            fontSize: '16px',
            fontFamily: 'Source Han Serif, serif',
            fontWeight: 600,
            color: '#2b2a3f'
          },
          value: {
            show: true,
            fontSize: '24px',
            fontFamily: 'Source Han Serif, serif',
            fontWeight: 700,
            color: '#2b2a3f',
            formatter: function (val: string) {
              return val + '%'
            }
          },
          total: {
            show: true,
            showAlways: false,
            label: '总营收',
            fontSize: '16px',
            fontFamily: 'Source Han Serif, serif',
            fontWeight: 600,
            color: '#2b2a3f'
          }
        }
      }
    }
  },
  dataLabels: {
    enabled: false
  },
  tooltip: {
    theme: 'light',
    style: {
      fontSize: '15px',
      fontFamily: 'Source Han Serif, serif'
    },
    custom: function({series, seriesIndex, w}) {
      const colors = ['#6560F0', '#06b48a', '#1FB1E6', '#F3C44C', '#8e8da2']
      return '<div style="padding: 12px 16px; background: #ffffff; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); border: 1px solid #e5e7eb;">' +
        '<div style="font-weight: 600; color: ' + colors[seriesIndex] + '; margin-bottom: 6px; font-size: 16px;">' + w.globals.labels[seriesIndex] + '</div>' +
        '<div style="color: #374151; font-size: 14px;">' +
        '<span style="color: #6b7280;">营收占比: </span>' +
        '<span style="font-weight: 600; color: #111827;">' + series[seriesIndex] + '%</span>' +
        '</div>' +
        '</div>'
    }
  },
  states: {
    hover: {
      filter: {
        type: 'lighten',
        value: 0.1
      }
    },
    active: {
      allowMultipleDataPointsSelection: false,
      filter: {
        type: 'darken',
        value: 0.1
      }
    }
  }
})

// 性别分布数据 - 暂无API，不显示假数据
const genderDistribution = ref({
  female: 0,
  male: 0
})

// 数据处理工具函数
const dataUtils = {
  // 计算增长率
  calculateGrowthRate: (current: number, previous: number): string => {
    if (previous === 0) return '0'
    const rate = ((current - previous) / previous) * 100
    return rate > 0 ? `+${rate.toFixed(1)}` : rate.toFixed(1)
  },

  // 获取日期范围
  getDateRange: (days: number) => {
    const endDate = new Date()
    const startDate = new Date()
    startDate.setDate(endDate.getDate() - days + 1)
    return {
      startDate: format(startDate, 'yyyy-MM-dd'),
      endDate: format(endDate, 'yyyy-MM-dd')
    }
  },

  // 处理销售历史数据
  processSalesHistory: (salesData: Record<string, unknown>, period: string) => {
    const entries = Object.entries(salesData)
    if (entries.length === 0) return { labels: [], data: [] }

    const labels: string[] = []
    const data: number[] = []

    entries.forEach(([date, item]) => {
      switch (period) {
        case '日':
          labels.push(format(new Date(date), 'HH:mm'))
          break
        case '周':
          labels.push(['周日', '周一', '周二', '周三', '周四', '周五', '周六'][new Date(date).getDay()])
          break
        case '月':
          labels.push(format(new Date(date), 'M月'))
          break
        default:
          labels.push(date)
      }
      data.push(parseFloat(item.totalAmount || 0))
    })

    return { labels, data }
  }
}

// 基于真实订单数据计算热门商品
const calculatePopularProducts = async (orders: drinkOrder[]) => {
  const productStats: Record<string, {
    id: string;
    name: string;
    sales: number;
    revenue: number
  }> = {}

  // 统计每个商品的销量和营收
  orders.forEach(order => {
    if (!order.orderItems) return

    try {
      const items = JSON.parse(order.orderItems)
      items.forEach((item: Record<string, unknown>) => {
        const productId = String(item.productId || item.prototypeId || 'unknown')
        const productName = String(item.productName || item.prototypeName || `商品${productId}`)
        const quantity = parseInt(String(item.quantity)) || 1
        const price = parseFloat(String(item.price || item.unitPrice)) || 0
        const itemRevenue = quantity * price

        if (!productStats[productId]) {
          productStats[productId] = {
            id: productId,
            name: productName,
            sales: 0,
            revenue: 0
          }
        }

        productStats[productId].sales += quantity
        productStats[productId].revenue += itemRevenue
      })
    } catch (error) {
      console.warn('解析订单商品失败:', error)
    }
  })

  // 按销量排序，取前5名
  const topProducts = Object.values(productStats)
    .sort((a, b) => b.sales - a.sales)
    .slice(0, 5)

  // 获取真实的商品名称
  console.log(`🔍 开始获取${topProducts.length}个热门商品的真实名称`)
  const productsWithRealNames = []

  for (const product of topProducts) {
    let realName = product.name

    try {
      // 尝试获取商品详情
      const productId = parseInt(product.id)
      console.log(`🔍 处理商品ID: ${productId}, 当前名称: ${product.name}`)

      if (!isNaN(productId)) {
        const { getProductDetailById, getPrototypeDetailById } = await import('@/utils/api/product')

        const productDetail = await getProductDetailById(productId)
        if (productDetail && productDetail.drinkPrototype) {
          console.log(`📦 商品${productId}关联原型ID: ${productDetail.drinkPrototype}`)
          const prototypeDetail = await getPrototypeDetailById(productDetail.drinkPrototype)
          if (prototypeDetail && prototypeDetail.prototypeName) {
            realName = prototypeDetail.prototypeName
            console.log(`✅ 获取商品${productId}真实名称: ${realName}`)
          } else {
            console.warn(`⚠️ 原型${productDetail.drinkPrototype}没有名称信息`)
          }
        } else {
          console.warn(`⚠️ 商品${productId}没有关联原型`)
        }
      }
    } catch (error) {
      console.warn(`❌ 获取商品${product.id}真实名称失败:`, error)
    }

    productsWithRealNames.push({
      id: parseInt(product.id) || 0,
      name: realName,
      sales: product.sales,
      revenue: product.revenue.toLocaleString()
    })
  }

  return productsWithRealNames
}

// 计算门店营收占比
const calculateStoreRevenueDistribution = async (stores: Record<string, unknown>[]) => {
  try {
    console.log('🏪 开始计算门店营收占比，门店数量:', stores.length)

    if (!stores || stores.length === 0) {
      console.warn('⚠️ 没有门店数据，跳过营收占比计算')
      revenueDistributionChartOptions.value.labels = ['暂无数据']
      revenueDistributionChartSeries.value = [1]
      return
    }

    const { startDate, endDate } = getDateRange()
    const storeRevenueData: { name: string; revenue: number }[] = []

    // 获取每个门店的营收数据
    for (const store of stores) {
      const storeId = store.storeId as number
      const storeName = store.storeName as string

      console.log(`🔍 处理门店: ID=${storeId}, 名称=${storeName}`)

      try {
        let revenue = 0

        if (timeRange.value === 'custom' && startDate && endDate) {
          // 自定义时间范围：累计多天的营收
          const start = new Date(startDate)
          const end = new Date(endDate)

          for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
            const dateStr = format(d, 'yyyy-MM-dd')
            try {
              const response = await serviceAxios.get('/manager/store/sales-stat', {
                params: {
                  storeId,
                  date: dateStr
                }
              })
              revenue += parseFloat(response.data?.totalAmount || '0')
            } catch (error) {
              console.warn(`获取门店${storeId}在${dateStr}的营收数据失败:`, error)
            }
          }
        } else {
          // 单日查询
          const response = await serviceAxios.get('/manager/store/sales-stat', {
            params: {
              storeId,
              date: startDate
            }
          })
          revenue = parseFloat(response.data?.totalAmount || '0')
        }

        storeRevenueData.push({
          name: storeName || `门店${storeId}`,
          revenue
        })
      } catch (error) {
        console.warn(`获取门店${storeId}营收数据失败:`, error)
        storeRevenueData.push({
          name: storeName || `门店${storeId}`,
          revenue: 0
        })
      }
    }

    // 按营收排序，取前5名
    storeRevenueData.sort((a, b) => b.revenue - a.revenue)
    const top5Stores = storeRevenueData.slice(0, 5)

    // 计算其他门店的总营收
    const otherStoresRevenue = storeRevenueData.slice(5).reduce((sum, store) => sum + store.revenue, 0)

    // 更新图表数据
    const labels: string[] = []
    const series: number[] = []

    top5Stores.forEach(store => {
      labels.push(store.name)
      series.push(store.revenue)
    })

    // 如果有其他门店，添加"其他"分类
    if (otherStoresRevenue > 0) {
      labels.push('其他')
      series.push(otherStoresRevenue)
    }

    // 更新图表配置 - 强制响应式更新
    revenueDistributionChartSeries.value = [...series]
    revenueDistributionChartOptions.value = {
      ...revenueDistributionChartOptions.value,
      labels: [...labels]
    }

    console.log('✅ 门店营收占比计算完成:', { labels, series })
    console.log('📊 图表配置更新:', revenueDistributionChartOptions.value.labels)
  } catch (error) {
    console.error('计算门店营收占比失败:', error)
    // 失败时清空数据
    revenueDistributionChartOptions.value.labels = []
    revenueDistributionChartSeries.value = []
  }
}

// 获取仪表板数据
const fetchDashboardData = async () => {
  if (isLoading.value) return

  isLoading.value = true
  error.value = null

  try {
    // 计算查询日期范围
    const { startDate, endDate } = getDateRange()

    // 构建订单查询参数
    const orderParams: Record<string, unknown> = {
      pageNum: 1,
      pageSize: 9999
    }

    // 根据时间范围类型选择不同的查询方式
    if (timeRange.value === 'custom' && startDate && endDate) {
      // 自定义时间范围：使用时间范围查询
      orderParams.orderTimeBegin = `${startDate} 00:00:00`
      orderParams.orderTimeEnd = `${endDate} 23:59:59`
      console.log(`📅 自定义时间范围查询: ${startDate} 到 ${endDate}`)
    } else {
      // 其他时间范围：使用单日查询
      orderParams.queryDate = startDate
      console.log(`📅 单日查询: ${startDate}`)
    }

    // 并行获取所有需要的数据
    const [
      ordersResponse,
      storesResponse
    ] = await Promise.all([
      // 获取订单数据
      serviceAxios.get('/manager/order/list', {
        params: orderParams
      }),
      // 获取门店数据
      serviceAxios.get('/manager/store/list', {
        params: {
          pageNum: 1,
          pageSize: 9999
        }
      })
    ])

    // 处理订单数据
    const orders = ordersResponse.rows || []
    const totalOrderCount = ordersResponse.total || 0

    // 计算总营收
    const totalRevenue = orders.reduce((sum: number, order: drinkOrder) => {
      return sum + parseFloat(order.totalAmount || '0')
    }, 0)

    // 计算平均客单价
    const avgOrderValue = totalOrderCount > 0 ? totalRevenue / totalOrderCount : 0

    // 处理门店数据
    const activeStores = storesResponse.total || 0

    // 基于真实订单数据统计热门商品
    const popularProducts = await calculatePopularProducts(orders)

    // 计算门店营收占比
    await calculateStoreRevenueDistribution(storesResponse.rows || [])

    // 计算增长率（与昨天对比）
    const yesterdayDate = new Date(startDate)
    yesterdayDate.setDate(yesterdayDate.getDate() - 1)

    let revenueGrowth = '0'
    let ordersGrowth = '0'
    let avgOrderChange = '0'

    try {
      // 获取昨天的数据进行对比
      const yesterdayOrdersResponse = await serviceAxios.get('/manager/order/list', {
        params: {
          pageNum: 1,
          pageSize: 9999,
          queryDate: format(yesterdayDate, 'yyyy-MM-dd')
        }
      })

      const yesterdayOrders = yesterdayOrdersResponse.rows || []
      const yesterdayRevenue = yesterdayOrders.reduce((sum: number, order: drinkOrder) => {
        return sum + parseFloat(order.totalAmount || '0')
      }, 0)
      const yesterdayOrderCount = yesterdayOrders.length
      const yesterdayAvgOrderValue = yesterdayOrderCount > 0 ? yesterdayRevenue / yesterdayOrderCount : 0

      // 计算增长率
      revenueGrowth = dataUtils.calculateGrowthRate(totalRevenue, yesterdayRevenue)
      ordersGrowth = dataUtils.calculateGrowthRate(totalOrderCount, yesterdayOrderCount)
      avgOrderChange = dataUtils.calculateGrowthRate(avgOrderValue, yesterdayAvgOrderValue)

    } catch (error) {
      console.warn('获取昨天数据失败，无法计算增长率:', error)
    }

    // 更新仪表板数据
    dashboardData.value = {
      totalRevenue: totalRevenue.toLocaleString(),
      revenueGrowth, // 真实的增长率数据
      totalOrders: totalOrderCount.toString(),
      ordersGrowth, // 真实的增长率数据
      avgOrderValue: avgOrderValue.toFixed(1),
      avgOrderChange, // 真实的增长率数据
      activeStores: activeStores.toString(),
      storeChange: '0', // 门店变化需要专门的API
      ageDistribution: [], // 暂无用户画像API，不显示假数据
      popularProducts
    }

    updateSalesTrendChart()

  } catch (err) {
    console.error('获取仪表板数据失败:', err)
    error.value = '获取数据失败，请稍后重试'
  } finally {
    isLoading.value = false
  }
}

// 获取日期范围
const getDateRange = () => {
  const today = new Date()
  let startDate = ''
  let endDate = format(today, 'yyyy-MM-dd')

  switch (timeRange.value) {
    case 'today':
      startDate = format(today, 'yyyy-MM-dd')
      break
    case 'yesterday': {
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)
      startDate = format(yesterday, 'yyyy-MM-dd')
      endDate = format(yesterday, 'yyyy-MM-dd')
      break
    }
    case 'week': {
      const weekStart = new Date(today)
      weekStart.setDate(weekStart.getDate() - 7)
      startDate = format(weekStart, 'yyyy-MM-dd')
      break
    }
    case 'month': {
      const monthStart = new Date(today)
      monthStart.setDate(monthStart.getDate() - 30)
      startDate = format(monthStart, 'yyyy-MM-dd')
      break
    }
    case 'quarter': {
      // 本季度：计算当前季度的开始日期
      const currentQuarter = Math.floor(today.getMonth() / 3)
      const quarterStart = new Date(today.getFullYear(), currentQuarter * 3, 1)
      startDate = format(quarterStart, 'yyyy-MM-dd')
      console.log(`📅 本季度查询: ${startDate} 到 ${endDate}`)
      break
    }
    case 'year': {
      // 本年：从今年1月1日开始
      const yearStart = new Date(today.getFullYear(), 0, 1)
      startDate = format(yearStart, 'yyyy-MM-dd')
      console.log(`📅 本年查询: ${startDate} 到 ${endDate}`)
      break
    }
    case 'custom':
      startDate = customStartDate.value
      endDate = customEndDate.value
      break
    default:
      startDate = format(today, 'yyyy-MM-dd')
  }

  return { startDate, endDate }
}

// 获取排名样式类
const getRankClass = (rank: number) => {
  if (rank === 1) return 'rank-first'
  if (rank === 2) return 'rank-second'
  if (rank === 3) return 'rank-third'
  return 'rank-normal'
}

// 获取销售历史数据 - 使用单日销售统计API
const fetchSalesHistory = async (storeId: number, days: number) => {
  try {
    const salesData: Record<string, { totalAmount: number; orderCount: number; totalCups: number }> = {}

    // 获取指定天数的历史数据
    for (let i = 0; i < days; i++) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      const dateStr = format(date, 'yyyy-MM-dd')

      try {
        const response = await serviceAxios.get('/manager/store/sales-stat', {
          params: {
            storeId,
            date: dateStr
          }
        })

        salesData[dateStr] = {
          totalAmount: parseFloat(response.data?.totalAmount || '0'),
          orderCount: parseInt(response.data?.orderCount || '0'),
          totalCups: parseInt(response.data?.totalCups || '0')
        }
      } catch (error) {
        console.warn(`获取${dateStr}销售数据失败:`, error)
        salesData[dateStr] = { totalAmount: 0, orderCount: 0, totalCups: 0 }
      }
    }

    return salesData
  } catch (error) {
    console.error('获取销售历史数据失败:', error)
    return {}
  }
}

// 更新销售趋势图
const updateSalesTrendChart = async () => {
  try {
    // 获取第一个门店的ID作为默认门店
    const storesResponse = await serviceAxios.get('/manager/store/list', {
      params: { pageNum: 1, pageSize: 1 }
    })

    const firstStore = storesResponse.rows?.[0]
    if (!firstStore) {
      console.warn('没有找到门店数据，无法显示销售趋势')
      // 清空图表数据
      salesTrendChartOptions.value.xaxis.categories = []
      salesTrendChartSeries.value = [{ name: '销售额', data: [] }]
      return
    }

    const storeId = firstStore.storeId
    let salesData: Record<string, unknown> = {}
    let labels: string[] = []
    let data: number[] = []

    // 根据周期获取不同的数据
    switch (trendPeriod.value) {
      case '日': {
        // 获取指定日期范围的订单数据，按小时统计
        const { startDate, endDate } = getDateRange()

        // 构建订单查询参数
        const orderParams: Record<string, unknown> = {
          pageNum: 1,
          pageSize: 9999,
          storeId: storeId
        }

        if (timeRange.value === 'custom' && startDate && endDate) {
          // 自定义时间范围：使用时间范围查询
          orderParams.orderTimeBegin = `${startDate} 00:00:00`
          orderParams.orderTimeEnd = `${endDate} 23:59:59`
        } else {
          // 其他时间范围：使用单日查询
          orderParams.queryDate = startDate
        }

        const orderResponse = await serviceAxios.get('/manager/order/list', {
          params: orderParams
        })

        const orders = orderResponse.rows || []
        labels = ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']

        // 按时段统计营业额
        const hourlyData = new Array(7).fill(0) // 7个时段
        orders.forEach((order: Record<string, unknown>) => {
          try {
            const orderTime = new Date(String(order.orderTime))
            const hour = orderTime.getHours()
            const amount = parseFloat(String(order.totalAmount || 0))

            let timeSlot = 0
            if (hour >= 0 && hour < 4) timeSlot = 0      // 00:00-04:00
            else if (hour >= 4 && hour < 8) timeSlot = 1  // 04:00-08:00
            else if (hour >= 8 && hour < 12) timeSlot = 2 // 08:00-12:00
            else if (hour >= 12 && hour < 16) timeSlot = 3 // 12:00-16:00
            else if (hour >= 16 && hour < 20) timeSlot = 4 // 16:00-20:00
            else if (hour >= 20 && hour < 24) timeSlot = 5 // 20:00-24:00
            else timeSlot = 6                             // 24:00

            hourlyData[timeSlot] += amount
          } catch (error) {
            console.warn('处理订单时间失败:', error)
          }
        })

        data = hourlyData
        break
      }

      case '周': {
        // 获取最近7天数据
        salesData = await fetchSalesHistory(storeId, 7)
        labels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

        // 将日期数据按周几排序
        const weekData = new Array(7).fill(0)
        Object.entries(salesData).forEach(([dateStr, item]) => {
          const date = new Date(dateStr)
          const dayOfWeek = date.getDay() // 0=周日, 1=周一, ..., 6=周六
          const weekIndex = dayOfWeek === 0 ? 6 : dayOfWeek - 1 // 转换为0=周一, 6=周日
          weekData[weekIndex] += item.totalAmount
        })

        data = weekData
        break
      }

      case '月': {
        // 获取最近6个月数据
        const monthlyData: number[] = []
        const monthLabels: string[] = []

        for (let i = 5; i >= 0; i--) {
          const date = new Date()
          date.setMonth(date.getMonth() - i)
          const monthStr = format(date, 'M月')
          monthLabels.push(monthStr)

          // 获取该月的数据（简化为获取该月第一天的数据）
          const monthFirstDay = format(new Date(date.getFullYear(), date.getMonth(), 1), 'yyyy-MM-dd')
          try {
            const response = await serviceAxios.get('/manager/store/sales-stat', {
              params: {
                storeId,
                date: monthFirstDay
              }
            })
            monthlyData.push(parseFloat(response.data?.totalAmount || '0'))
          } catch (error) {
            console.warn(`获取${monthStr}数据失败:`, error)
            monthlyData.push(0)
          }
        }

        labels = monthLabels
        data = monthlyData
        break
      }

      case '季': {
        // 本季度：按月显示当前季度的数据
        const currentQuarter = Math.floor(new Date().getMonth() / 3)
        const quarterMonths = []
        const quarterData = []

        // 获取当前季度的3个月
        for (let i = 0; i < 3; i++) {
          const monthIndex = currentQuarter * 3 + i
          const date = new Date(new Date().getFullYear(), monthIndex, 1)
          const monthStr = format(date, 'M月')
          quarterMonths.push(monthStr)

          try {
            const response = await serviceAxios.get('/manager/store/sales-stat', {
              params: {
                storeId,
                date: format(date, 'yyyy-MM-dd')
              }
            })
            quarterData.push(parseFloat(response.data?.totalAmount || '0'))
          } catch (error) {
            console.warn(`获取${monthStr}数据失败:`, error)
            quarterData.push(0)
          }
        }

        labels = quarterMonths
        data = quarterData
        break
      }

      case '年': {
        // 本年：按季度显示今年的数据
        const yearlyData = []
        const yearLabels = ['第一季度', '第二季度', '第三季度', '第四季度']

        for (let quarter = 0; quarter < 4; quarter++) {
          let quarterTotal = 0

          // 获取该季度3个月的数据
          for (let month = 0; month < 3; month++) {
            const monthIndex = quarter * 3 + month
            const date = new Date(new Date().getFullYear(), monthIndex, 1)

            try {
              const response = await serviceAxios.get('/manager/store/sales-stat', {
                params: {
                  storeId,
                  date: format(date, 'yyyy-MM-dd')
                }
              })
              quarterTotal += parseFloat(response.data?.totalAmount || '0')
            } catch (error) {
              console.warn(`获取${format(date, 'M月')}数据失败:`, error)
            }
          }

          yearlyData.push(quarterTotal)
        }

        labels = yearLabels
        data = yearlyData
        break
      }

      default:
        // 不支持的时间周期，清空数据
        salesTrendChartOptions.value.xaxis.categories = []
        salesTrendChartSeries.value = [{ name: '销售额', data: [] }]
        return
    }

    salesTrendChartOptions.value.xaxis.categories = labels
    salesTrendChartSeries.value = [{
      name: '销售额',
      data: data
    }]

  } catch (error) {
    console.error('更新销售趋势图失败:', error)
    // API失败时清空数据，不使用假数据
    salesTrendChartOptions.value.xaxis.categories = []
    salesTrendChartSeries.value = [{ name: '销售额', data: [] }]
  }
}



// 初始化
onMounted(async () => {
  // 等待 DOM 完全渲染
  await nextTick()

  // 先获取数据，再显示图表
  await fetchDashboardData()

  // 延迟一小段时间确保数据已加载完成
  setTimeout(() => {
    chartsReady.value = true
  }, 200)
})
</script>

<style scoped>
.operational-dashboard {
  background: #fff;
}

.time-filter-section {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.metrics-cards .metric-card {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.metrics-cards .metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.metric-card {
  display: flex;
  align-items: center;
}

.metric-icon {
  margin-right: 15px;
}

.metric-icon i {
  font-size: 2.5rem;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 5px;
  color: #2c3e50;
}

.metric-label {
  color: #6c757d;
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.metric-change {
  font-size: 0.8rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
}

.metric-change.positive {
  color: #28a745;
  background: #d4edda;
}

.metric-change.negative {
  color: #dc3545;
  background: #f8d7da;
}

.metric-change.neutral {
  color: #6c757d;
  background: #e9ecef;
}

.chart-card {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  height: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e9ecef;
}

.chart-title {
  font-weight: 600;
  color: #2b2a3f; /* 使用项目主文字色 */
  margin-bottom: 0;
  font-family: 'Source Han Serif', serif;
  font-size: 16px;
}

.chart-controls {
  display: flex;
  gap: 5px;
}

.chart-container {
  position: relative;
  height: 300px;
}

.chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 250px;
}

.chart-loading .spinner-border {
  width: 2rem;
  height: 2rem;
}

/* 自定义工具提示样式 */
:global(.custom-tooltip) {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

:global(.custom-tooltip .tooltip-title) {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
  margin-bottom: 6px;
}

:global(.custom-tooltip .tooltip-content) {
  display: flex;
  align-items: center;
  gap: 8px;
}

:global(.custom-tooltip .tooltip-label) {
  font-size: 13px;
  color: #6b7280;
}

:global(.custom-tooltip .tooltip-value) {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
}

.user-profile-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.profile-section h6 {
  font-weight: 600;
  color: #2b2a3f; /* 使用项目主文字色 */
  margin-bottom: 12px;
  font-family: 'Source Han Serif', serif;
  font-size: 15px;
}

/* 性别分布区域样式 */
.gender-distribution {
  display: flex;
  gap: 16px;
  margin-top: 8px;
}

.gender-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
  border: 2px solid transparent;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.gender-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.12);
}

.gender-item.female {
  background: linear-gradient(135deg, rgba(239, 41, 41, 0.1), rgba(241, 66, 27, 0.05));
  border-color: rgba(239, 41, 41, 0.2);
}

.gender-item.female:hover {
  border-color: rgba(239, 41, 41, 0.4);
  background: linear-gradient(135deg, rgba(239, 41, 41, 0.15), rgba(241, 66, 27, 0.08));
}

.gender-item.male {
  background: linear-gradient(135deg, rgba(101, 96, 240, 0.1), rgba(114, 110, 237, 0.05));
  border-color: rgba(101, 96, 240, 0.2);
}

.gender-item.male:hover {
  border-color: rgba(101, 96, 240, 0.4);
  background: linear-gradient(135deg, rgba(101, 96, 240, 0.15), rgba(114, 110, 237, 0.08));
}

.gender-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #ffffff;
  font-weight: 600;
}

.female .gender-icon {
  background: linear-gradient(135deg, #EF2929, #F1421B);
  box-shadow: 0 4px 12px rgba(239, 41, 41, 0.3);
}

.male .gender-icon {
  background: linear-gradient(135deg, #6560F0, #726eed);
  box-shadow: 0 4px 12px rgba(101, 96, 240, 0.3);
}

.gender-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.gender-label {
  font-size: 16px;
  font-weight: 600;
  color: #2b2a3f;
  font-family: 'Source Han Serif', serif;
}

.gender-percentage {
  font-size: 24px;
  font-weight: 700;
  font-family: 'Source Han Serif', serif;
}

.female .gender-percentage {
  color: #EF2929;
}

.male .gender-percentage {
  color: #6560F0;
}

.age-distribution {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.age-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.age-label {
  min-width: 85px;
  font-size: 14px;
  color: #2b2a3f; /* 使用项目主文字色 */
  font-family: 'Source Han Serif', serif;
  font-weight: 500;
}

.age-bar {
  flex: 1;
  height: 10px;
  background: #f2f1f9; /* 使用项目背景色 */
  border-radius: 5px;
  overflow: hidden;
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

.age-progress {
  height: 100%;
  background: linear-gradient(90deg, #6560F0, #726eed); /* 使用项目主色渐变 */
  transition: width 0.4s ease;
  border-radius: 5px;
}

.age-value {
  min-width: 45px;
  text-align: right;
  font-size: 14px;
  font-weight: 600;
  color: #6560F0; /* 使用项目主色 */
  font-family: 'Source Han Serif', serif;
}

/* 排行榜样式 - 参考大屏看板设计 */
.ranking-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.ranking-header i {
  font-size: 1.2rem;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 10px;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 0;
  border-bottom: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-item:hover {
  background: #f8f9fa;
  border-radius: 6px;
  padding-left: 8px;
  padding-right: 8px;
}

.rank-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.9rem;
  flex-shrink: 0;
  font-family: 'Source Han Serif', serif;
}

.rank-first {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #b8860b;
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

.rank-second {
  background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
  color: #666;
  box-shadow: 0 2px 8px rgba(192, 192, 192, 0.3);
}

.rank-third {
  background: linear-gradient(135deg, #cd7f32, #daa520);
  color: #8b4513;
  box-shadow: 0 2px 8px rgba(205, 127, 50, 0.3);
}

.rank-normal {
  background: #f8f9fa;
  color: #495057;
  border: 1px solid #e9ecef;
}

.product-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-name {
  font-weight: 600;
  color: #2c3e50;
  font-family: 'Source Han Serif', serif;
  font-size: 0.95rem;
}

.product-metrics {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.product-sales,
.product-revenue {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.sales-value,
.revenue-value {
  font-weight: 600;
  color: #2c3e50;
  font-family: 'Source Han Serif', serif;
}

.sales-unit {
  font-size: 0.8rem;
  color: #6c757d;
  font-weight: 500;
}

.product-revenue {
  color: #28a745;
}

.revenue-value {
  color: #28a745;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #6c757d;
  font-size: 14px;
  text-align: center;
  min-height: 120px;
}

@media (max-width: 768px) {
  .metrics-cards .col-lg-3 {
    margin-bottom: 15px;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .user-profile-content {
    gap: 15px;
  }

  /* 性别分布响应式 */
  .gender-distribution {
    flex-direction: column;
    gap: 12px;
  }

  .gender-item {
    padding: 14px;
    gap: 10px;
  }

  .gender-icon {
    width: 42px;
    height: 42px;
    font-size: 18px;
  }

  .gender-label {
    font-size: 15px;
  }

  .gender-percentage {
    font-size: 22px;
  }

  /* 排行榜响应式 */
  .ranking-item {
    padding: 10px 0;
    gap: 12px;
  }

  .rank-number {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }

  .product-name {
    font-size: 0.9rem;
  }

  .product-metrics {
    gap: 2px;
  }

  .sales-value,
  .revenue-value {
    font-size: 0.9rem;
  }

  .sales-unit {
    font-size: 0.75rem;
  }
}

@media (max-width: 576px) {
  /* 性别分布小屏幕优化 */
  .gender-item {
    padding: 12px;
    gap: 8px;
  }

  .gender-icon {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }

  .gender-label {
    font-size: 14px;
  }

  .gender-percentage {
    font-size: 20px;
  }

  .ranking-item {
    flex-direction: row;
    align-items: flex-start;
    padding: 8px 0;
  }

  .product-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .product-metrics {
    align-items: flex-start;
    width: 100%;
  }

  .rank-number {
    width: 24px;
    height: 24px;
    font-size: 0.75rem;
  }
}
</style>
