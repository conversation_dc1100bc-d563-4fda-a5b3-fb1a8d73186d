import serviceAxios from "@/utils/serviceAxios";
import type { TableDataInfo } from "@/types/tableDataInfo";

// 门店信息接口（基于真实API）
export interface StoreInfo {
  storeId?: number
  storeName: string
  storeAddress: string
  storeDescription?: string
  storeType?: string
  contactPhone: string
  businessHours?: string
  status: string // 营业状态
  createTime?: string
  updateTime?: string
  createBy?: string
  updateBy?: string
  remark?: string
  params?: Record<string, unknown>
}

// 门店查询参数（基于真实API）
export interface StoreQueryParams {
  businessHours?: string
  contactPhone?: string
  createBy?: string
  createTime?: string
  latitude?: string
  longitude?: string
  params?: Record<string, unknown>
  remark?: string
  searchValue?: string
  status?: string
  storeAddress?: string
  storeDescription?: string
  storeId?: number
  storeName?: string
  storeType?: string
  updateBy?: string
  updateTime?: string
}

// 门店创建/更新参数（基于真实API）
export interface StoreFormData {
  storeId?: number
  storeName: string
  storeAddress: string
  storeDescription?: string
  storeType?: string
  contactPhone: string
  businessHours?: string
  status: string
  remark?: string
  params?: Record<string, unknown>
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string
}

/**
 * 获取门店管理列表（基于真实API）
 */
export function getMerchantStoreList(params: StoreQueryParams): Promise<TableDataInfo<StoreInfo>> {
  return serviceAxios.get('/manager/store/list', { params })
}

/**
 * 获取门店管理详细信息（基于真实API）
 */
export function getMerchantStoreDetail(storeId: number): Promise<{ data: StoreInfo }> {
  return serviceAxios.get(`/manager/store/${storeId}`)
}

/**
 * 新增门店（基于真实API）
 */
export function createMerchantStore(data: StoreFormData): Promise<void> {
  return serviceAxios.post('/manager/store', data)
}

/**
 * 修改门店信息（基于真实API）
 */
export function updateMerchantStore(data: StoreFormData): Promise<void> {
  return serviceAxios.put('/manager/store', data)
}

/**
 * 获取营业中的门店列表（基于真实API）
 */
export function getMerchantOpenStores(): Promise<TableDataInfo<StoreInfo>> {
  return serviceAxios.get('/manager/store/open')
}

/**
 * 删除门店（扩展API，可能需要后端支持）
 */
export function deleteMerchantStore(storeId: number): Promise<void> {
  return serviceAxios.delete(`/manager/store/${storeId}`)
}



/**
 * 批量删除门店（扩展API，可能需要后端支持）
 */
export function deleteMerchantStoreBatch(storeIds: number[]): Promise<void> {
  return serviceAxios.delete('/manager/store/batch', { data: { storeIds } })
}

/**
 * 导出门店数据（扩展API，可能需要后端支持）
 */
export function exportMerchantStoreData(params: StoreQueryParams): Promise<Blob> {
  return serviceAxios.get('/manager/store/export', {
    params,
    responseType: 'blob'
  })
}

/**
 * 获取门店统计信息（扩展API，可能需要后端支持）
 */
export function getMerchantStoreStats(): Promise<{
  total: number
  active: number
  inactive: number
  openStores: number
}> {
  return serviceAxios.get('/manager/store/statistics')
}
