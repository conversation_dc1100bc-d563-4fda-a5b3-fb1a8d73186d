<template>
  <div class="device-status-dashboard">
    <!-- 页面标题和控制 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h2 class="mb-0">设备状态监控</h2>
        <p class="text-muted mb-0">实时监控所有IoT设备的运行状态</p>

      </div>
      <div class="d-flex gap-2">
        <button @click="toggleAutoRefresh" :class="autoRefreshClass" :disabled="loading">
          <i class="flaticon-refresh me-2"></i>
          {{ autoRefresh ? '停止自动刷新' : '开启自动刷新' }}
        </button>
        <button @click="refreshDevices" class="btn btn-primary" :disabled="loading">
          <i class="flaticon-refresh me-2"></i>刷新
        </button>
      </div>
    </div>

    <!-- 设备统计卡片 -->
    <div class="row g-3 mb-4" v-if="statistics">
      <div class="col-md-2">
        <div class="card bg-light border-0 device-status-card">
          <div class="card-body text-center py-3">
            <h4 class="mb-2 text-dark">{{ statistics.totalDevices }}</h4>
            <p class="mb-0 d-flex align-items-center justify-content-center">
              <span class="status-dot bg-primary me-2"></span>
              <span class="text-muted">总设备数</span>
            </p>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="card bg-light border-0 device-status-card">
          <div class="card-body text-center py-3">
            <h4 class="mb-2 text-dark">{{ statistics.onlineDevices }}</h4>
            <p class="mb-0 d-flex align-items-center justify-content-center">
              <span class="status-dot bg-success me-2"></span>
              <span class="text-muted">在线设备</span>
            </p>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="card bg-light border-0 device-status-card">
          <div class="card-body text-center py-3">
            <h4 class="mb-2 text-dark">{{ statistics.offlineDevices || 0 }}</h4>
            <p class="mb-0 d-flex align-items-center justify-content-center">
              <span class="status-dot bg-secondary me-2"></span>
              <span class="text-muted">离线设备</span>
            </p>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="card bg-light border-0 device-status-card">
          <div class="card-body text-center py-3">
            <h4 class="mb-2 text-dark">{{ statistics.busyDevices }}</h4>
            <p class="mb-0 d-flex align-items-center justify-content-center">
              <span class="status-dot bg-warning me-2"></span>
              <span class="text-muted">忙碌设备</span>
            </p>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="card bg-light border-0 device-status-card">
          <div class="card-body text-center py-3">
            <h4 class="mb-2 text-dark">{{ statistics.errorDevices }}</h4>
            <p class="mb-0 d-flex align-items-center justify-content-center">
              <span class="status-dot bg-danger me-2"></span>
              <span class="text-muted">故障设备</span>
            </p>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="card bg-light border-0 device-status-card">
          <div class="card-body text-center py-3">
            <h4 class="mb-2 text-dark">{{ statistics.totalDevices > 0 ? Math.round((statistics.onlineDevices / statistics.totalDevices) * 100) : 0 }}%</h4>
            <p class="mb-0 d-flex align-items-center justify-content-center">
              <span class="status-dot bg-info me-2"></span>
              <span class="text-muted">在线率</span>
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="card mb-4">
      <div class="card-body">
        <div class="row g-3">
          <!-- 设备类型筛选 -->
          <div class="col-md-3">
            <label class="form-label">设备类型</label>
            <select v-model="filterForm.deviceType" class="form-select" @change="handleFilter">
              <option value="">全部类型</option>
              <option value="kiosk">咖啡机</option>
              <option value="robot_arm">机械臂</option>
              <option value="ice_maker">制冰机</option>
              <option value="grinder">研磨机</option>
              <option value="dispenser">分配器</option>
            </select>
          </div>
          
          <!-- 设备状态筛选 -->
          <div class="col-md-2">
            <label class="form-label">设备状态</label>
            <select v-model="filterForm.status" class="form-select" @change="handleFilter">
              <option value="">全部状态</option>
              <option value="online">在线</option>
              <option value="offline">离线</option>
              <option value="busy">忙碌中</option>
              <option value="error">故障</option>
              <option value="maintenance">维护中</option>
            </select>
          </div>
          
          <!-- 门店筛选 -->
          <div class="col-md-2">
            <label class="form-label">门店</label>
            <select v-model="filterForm.storeId" class="form-select" @change="handleFilter">
              <option value="">全部门店</option>
              <option v-for="store in storeList" :key="store.storeId" :value="store.storeId">
                {{ store.storeName }}
              </option>
            </select>
          </div>
          
          <!-- 搜索框 -->
          <div class="col-md-3">
            <label class="form-label">搜索</label>
            <div class="input-group">
              <input
                type="text"
                class="form-control"
                placeholder="搜索设备名称、ID..."
                v-model="filterForm.searchValue"
                @keyup.enter="handleFilter"
              />
              <button class="btn btn-outline-secondary" type="button" @click="handleFilter">
                <i class="flaticon-search-interface-symbol"></i>
              </button>
            </div>
          </div>
          
          <!-- 重置按钮 -->
          <div class="col-md-2">
            <label class="form-label">&nbsp;</label>
            <button @click="resetFilter" class="btn btn-outline-secondary w-100 d-block">
              <i class="flaticon-refresh me-2"></i>重置
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 设备列表 -->
    <div class="card">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">
            设备列表
            <span class="badge bg-primary ms-2">{{ filteredDevices.length }}</span>
          </h5>
          <div class="d-flex gap-2">
            <button @click="switchView('card')" :class="viewMode === 'card' ? 'btn btn-primary btn-sm' : 'btn btn-outline-primary btn-sm'">
              <i class="flaticon-grid"></i> 卡片视图
            </button>
            <button @click="switchView('table')" :class="viewMode === 'table' ? 'btn btn-primary btn-sm' : 'btn btn-outline-primary btn-sm'">
              <i class="flaticon-list"></i> 列表视图
            </button>
          </div>
        </div>
      </div>
      <div class="card-body">
        <!-- 加载状态 -->
        <div v-if="loading" class="text-center py-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
          <p class="mt-2 text-muted">正在加载设备信息...</p>
        </div>

        <!-- 卡片视图 -->
        <div v-else-if="viewMode === 'card' && filteredDevices.length > 0" class="row g-3">
          <div v-for="device in filteredDevices" :key="device.deviceId" class="col-md-4 col-lg-3">
            <div class="card device-card" :class="getDeviceCardClass(device.status)" @click="viewDeviceDetail(device)">
              <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                  <h6 class="card-title mb-0">{{ device.deviceName }}</h6>
                  <span :class="getDeviceStatusClass(device.status)">
                    {{ getDeviceStatusName(device.status) }}
                  </span>
                </div>
                <p class="card-text text-muted small mb-2">
                  <i class="flaticon-gear me-1"></i>{{ getDeviceTypeName(device.deviceType) }}
                </p>
                <p class="card-text text-muted small mb-2">
                  <i class="flaticon-store me-1"></i>{{ getStoreName(device.storeId) }}
                </p>
                <p class="card-text text-muted small mb-0">
                  <i class="flaticon-time me-1"></i>{{ formatDateTime(device.lastOnlineTime) }}
                </p>
                <div class="mt-2">
                  <button 
                    @click.stop="viewDeviceDetail(device)"
                    class="btn btn-sm btn-outline-info me-1"
                  >
                    详情
                  </button>
                  <button 
                    v-if="device.status === 'error'"
                    @click.stop="clearDeviceError(device)"
                    class="btn btn-sm btn-outline-warning"
                  >
                    清除错误
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 表格视图 -->
        <div v-else-if="viewMode === 'table' && filteredDevices.length > 0" class="table-responsive">
          <table class="table table-hover">
            <thead class="table-light">
              <tr>
                <th>设备信息</th>
                <th>类型</th>
                <th>门店</th>
                <th>状态</th>
                <th>最后在线时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="device in filteredDevices" :key="device.deviceId" :class="getDeviceRowClass(device.status)">
                <td>
                  <div>
                    <strong>{{ device.deviceName }}</strong>
                    <br>
                    <small class="text-muted">{{ device.deviceId }}</small>
                  </div>
                </td>
                <td>
                  <span class="badge bg-info">{{ getDeviceTypeName(device.deviceType) }}</span>
                </td>
                <td>{{ getStoreName(device.storeId) }}</td>
                <td>
                  <span :class="getDeviceStatusClass(device.status)">
                    {{ getDeviceStatusName(device.status) }}
                  </span>
                </td>
                <td>
                  <small>{{ formatDateTime(device.lastOnlineTime) }}</small>
                </td>
                <td>
                  <div class="btn-group btn-group-sm">
                    <button 
                      @click="viewDeviceDetail(device)"
                      class="btn btn-outline-info"
                      title="查看详情"
                    >
                      详情
                    </button>
                    <button 
                      v-if="device.status === 'error'"
                      @click="clearDeviceError(device)"
                      class="btn btn-outline-warning"
                      title="清除错误"
                    >
                      清除错误
                    </button>
                    <button 
                      @click="toggleMaintenanceMode(device)"
                      class="btn btn-outline-secondary"
                      :title="device.status === 'maintenance' ? '退出维护' : '进入维护'"
                    >
                      {{ device.status === 'maintenance' ? '退出维护' : '维护' }}
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 空状态 -->
        <div v-else class="text-center py-5">
          <i class="flaticon-gear text-muted" style="font-size: 3rem;"></i>
          <h5 class="mt-3">暂无设备数据</h5>
          <p class="text-muted">请检查筛选条件或刷新页面</p>
        </div>
      </div>
    </div>

    <!-- 设备详情弹窗 -->
    <DeviceDetailModal
      v-if="showDetailModal"
      :device="selectedDevice"
      @close="showDetailModal = false"
      @refresh="refreshDevices"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { format } from 'date-fns'
import {
  getAllDevices,
  getDeviceStatistics,
  getDeviceTypeName,
  getDeviceStatusName,
  getDeviceStatusClass,
  clearDeviceError as clearDeviceErrorAPI,
  setDeviceMaintenanceMode
} from '@/utils/api/device'
import { getStoreList } from '@/utils/api/store'
import type { Device, DeviceStatistics } from '@/types/device'
import type { storeDetail } from '@/types'
import DeviceDetailModal from './DeviceDetailModal.vue'

// 数据状态
const loading = ref(false)
const deviceList = ref<Device[]>([])
const storeList = ref<storeDetail[]>([])
const statistics = ref<DeviceStatistics | null>(null)

// UI状态
const viewMode = ref<'card' | 'table'>('card')
const autoRefresh = ref(false)
const refreshInterval = ref<number | null>(null)
const showDetailModal = ref(false)
const selectedDevice = ref<Device | null>(null)

// 筛选表单
const filterForm = ref({
  deviceType: '',
  status: '',
  storeId: '',
  searchValue: ''
})

// 计算属性
const autoRefreshClass = computed(() => {
  return autoRefresh.value ? 'btn btn-success' : 'btn btn-outline-success'
})

const filteredDevices = computed(() => {
  let devices = deviceList.value

  // 按设备类型筛选
  if (filterForm.value.deviceType) {
    devices = devices.filter(device => device.deviceType === filterForm.value.deviceType)
  }

  // 按状态筛选
  if (filterForm.value.status) {
    devices = devices.filter(device => device.status === filterForm.value.status)
  }

  // 按门店筛选
  if (filterForm.value.storeId) {
    devices = devices.filter(device => device.storeId === Number(filterForm.value.storeId))
  }

  // 按搜索值筛选
  if (filterForm.value.searchValue) {
    const searchValue = filterForm.value.searchValue.toLowerCase()
    devices = devices.filter(device => 
      device.deviceName.toLowerCase().includes(searchValue) ||
      device.deviceId.toLowerCase().includes(searchValue)
    )
  }

  return devices
})

// 获取设备列表
const fetchDevices = async () => {
  loading.value = true
  try {
    console.log('🔄 [设备监控] 开始加载设备列表...')

    // 使用修复后的getAllDevices方法
    const allDevicesResponse = await getAllDevices({ pageNum: 1, pageSize: 1000 })

    if (allDevicesResponse.code === 200) {
      deviceList.value = allDevicesResponse.data || []
      console.log(`✅ [设备监控] 设备列表加载成功: ${deviceList.value.length}个设备`)
    } else {
      console.error('❌ [设备监控] API返回错误:', allDevicesResponse.msg)
      deviceList.value = []
    }
  } catch (error) {
    console.error('❌ [设备监控] 获取设备列表失败:', error)
    deviceList.value = []
  } finally {
    loading.value = false
  }
}

// 获取设备统计
const fetchStatistics = async () => {
  try {
    const response = await getDeviceStatistics()
    if (response.code === 200) {
      statistics.value = response.data
      console.log('✅ [设备监控] 设备统计加载成功:', statistics.value)
    } else {
      console.error('❌ [设备监控] 设备统计API返回错误:', response.msg)
      statistics.value = null
    }
  } catch (error) {
    console.error('❌ [设备监控] 获取设备统计失败:', error)
    statistics.value = null
  }
}

// 获取门店列表
const fetchStoreList = async () => {
  try {
    const response = await getStoreList({ pageNum: 1, pageSize: 1000 })
    storeList.value = (response.rows || []).filter((store: storeDetail) => store.status === '0')
    console.log(`✅ [设备监控] 门店列表加载成功: ${storeList.value.length}个门店`)
  } catch (error) {
    console.error('❌ [设备监控] 获取门店列表失败:', error)
  }
}

// 刷新设备数据
const refreshDevices = async () => {
  await Promise.all([
    fetchDevices(),
    fetchStatistics()
  ])
}

// 切换自动刷新
const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  
  if (autoRefresh.value) {
    refreshInterval.value = window.setInterval(() => {
      refreshDevices()
    }, 15000) // 每15秒刷新一次
    console.log('🔄 [设备监控] 开启自动刷新 (15秒间隔)')
  } else {
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
      refreshInterval.value = null
    }
    console.log('⏹️ [设备监控] 停止自动刷新')
  }
}

// 切换视图模式
const switchView = (mode: 'card' | 'table') => {
  viewMode.value = mode
}

// 筛选处理
const handleFilter = () => {
  console.log('🔍 [设备监控] 应用筛选条件:', filterForm.value)
}

// 重置筛选
const resetFilter = () => {
  filterForm.value = {
    deviceType: '',
    status: '',
    storeId: '',
    searchValue: ''
  }
}

// 查看设备详情
const viewDeviceDetail = (device: Device) => {
  selectedDevice.value = device
  showDetailModal.value = true
}

// 清除设备错误
const clearDeviceError = async (device: Device) => {
  try {
    await clearDeviceErrorAPI(device.deviceId)
    console.log(`✅ [设备监控] 设备${device.deviceName}错误已清除`)
    refreshDevices()
  } catch (error) {
    console.error('❌ [设备监控] 清除设备错误失败:', error)
  }
}

// 切换维护模式
const toggleMaintenanceMode = async (device: Device) => {
  try {
    const maintenance = device.status !== 'maintenance'
    await setDeviceMaintenanceMode(device.deviceId, maintenance)
    console.log(`✅ [设备监控] 设备${device.deviceName}${maintenance ? '进入' : '退出'}维护模式`)
    refreshDevices()
  } catch (error) {
    console.error('❌ [设备监控] 切换维护模式失败:', error)
  }
}

// 工具函数
const getStoreName = (storeId: number): string => {
  const store = storeList.value.find(s => s.storeId === storeId)
  return store?.storeName || `门店${storeId}`
}

const getDeviceCardClass = (status: string): string => {
  const classes = {
    'online': 'border-success',
    'offline': 'border-secondary',
    'busy': 'border-warning',
    'error': 'border-danger',
    'maintenance': 'border-info'
  }
  return classes[status as keyof typeof classes] || ''
}

const getDeviceRowClass = (status: string): string => {
  const classes = {
    'error': 'table-danger',
    'offline': 'table-secondary',
    'maintenance': 'table-info'
  }
  return classes[status as keyof typeof classes] || ''
}

const formatDateTime = (dateTime: string): string => {
  if (!dateTime) return '-'
  return format(new Date(dateTime), 'MM-dd HH:mm')
}

// 生命周期
onMounted(async () => {
  console.log('🚀 [设备监控] 开始初始化...')
  await fetchStoreList()
  await refreshDevices()
  console.log('✅ [设备监控] 初始化完成')
})

onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
})
</script>

<style scoped>
.device-status-dashboard {
  padding: 1rem;
}

.device-card {
  cursor: pointer;
  transition: all 0.2s ease;
  border-width: 2px;
}

.device-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border: 1px solid rgba(0, 0, 0, 0.125);
}

/* 设备状态卡片统一样式 */
.device-status-card {
  height: 100px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.device-status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.device-status-card .card-body {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

/* 状态圆点样式 */
.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  flex-shrink: 0;
}

.table th {
  font-weight: 600;
  border-bottom: 2px solid #dee2e6;
  background-color: #f8f9fa;
}

.badge {
  font-size: 0.75em;
}

.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}
</style>
