.profile-settings-card {
    .ql-container {
        min-height: 150px;
        height: auto;
    }
    .input-group {
        .input-group-text {
            padding: 10px 18px;
            border-right: none;
            background: #F5F4FA;
            border-color: #dedee4;
        }
        .form-control {
            border-left: none;
        }
    }
    .file-upload {
        border: 1px solid #dedee4;
        padding: 55px 15px;

        i {
            line-height: 1;
            font-size: 35px;
            margin-bottom: 5px;
            color: var(--splash-primary-color);
        }
        span {
            span {
                &::before {
                    left: 0;
                    right: 0;
                    height: 1px;
                    content: '';
                    bottom: -2px;
                    position: absolute;
                    background: var(--splash-black-color);
                }
            }
        }
        input {
            cursor: pointer;
        }
    }
    .members-list {
        div {
            margin: {
                top: 10px;
                right: 5px;
            };
        }
        button {
            font-size: 8px;
            margin-left: 3px;

            &:hover {
                color: red;
            }
        }
    }
    button {
        span {
            line-height: 1.3;

            &::before {
                left: 0;
                right: 0;
                bottom: 0;
                height: 1px;
                content: '';
                position: absolute;
                transition: var(--transition);
                background: var(--splash-danger-color);
            }
        }
    }
}

// Dark Mode
.dark {
    .profile-settings-card {
        .input-group {
            .input-group-text {
                background: var(--splash-black-color);
                border-color: #45445e;
            }
        }
        .file-upload {
            border-color: #45445e;
    
            span {
                span {
                    &::before {
                        background: var(--splash-white-color);
                    }
                }
            }
        }
    }
}