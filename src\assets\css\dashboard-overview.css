/* 自定义容器样式 - 避免框架main-content的padding约束 */
.dashboard-overview {
  padding: 15px 20px;
  min-height: calc(100vh - 140px);
  background: var(--splash-body-bg);
}

/* 优化行间距 */
.dashboard-overview .row {
  margin-bottom: 20px;
}

.dashboard-overview .row:last-child {
  margin-bottom: 0;
}

/* 优化标题区间距 */
.dashboard-overview .d-flex.justify-content-between.align-items-center {
  margin-bottom: 25px;
}

/* 页面标题样式 */
.main-title {
  font-size: 1.8rem;
  color: var(--splash-primary-color);
  margin-bottom: 0;
  font-family: 'Source Han Serif', serif;
  font-weight: 600;
}

/* 日期控制区域 */
.date-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-end;
}

.quick-date-buttons {
  display: flex;
  gap: 6px;
}

.quick-btn {
  padding: 6px 12px;
  border: 1px solid var(--splash-primary-color);
  background: var(--splash-white-color);
  color: var(--splash-primary-color);
  border-radius: 4px;
  font-weight: 500;
  font-family: 'Source Han Serif', serif;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 13px;
}

.quick-btn:hover {
  background: var(--splash-primary-color);
  color: var(--splash-white-color);
  transform: translateY(-1px);
}

.quick-btn.active {
  background: var(--splash-primary-color);
  color: var(--splash-white-color);
  box-shadow: 0 2px 6px rgba(101, 96, 240, 0.2);
}

.time-selector {
  min-width: 220px;
}

.date-picker {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 8px 12px;
  font-family: 'Source Han Serif', serif;
  font-size: 14px;
  background: var(--splash-white-color);
  color: var(--bs-body-color);
  width: 100%;
}

.date-picker::placeholder {
  color: var(--splash-secondary-color);
}

.date-picker:focus {
  border-color: var(--splash-primary-color);
  box-shadow: 0 0 0 2px rgba(101, 96, 240, 0.1);
  outline: none;
}


.metric-item {
  padding: 15px 0;
}

.metric-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.metric-label {
  font-size: 13px;
  color: var(--splash-secondary-color);
  font-weight: 500;
  margin: 0;
  font-family: 'Source Han Serif', serif;
}

.metric-value-container {
  display: flex;
  align-items: baseline;
  gap: 3px;
  justify-content: center;
}

.metric-value {
  font-size: 22px;
  font-weight: 700;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.metric-value.revenue {
  color: var(--splash-success-color);
}

.metric-value.orders {
  color: var(--splash-info-color);
}

.metric-value.cups {
  color: var(--splash-warning-color);
}

/* 统一单位颜色 */
.metric-unit {
  font-size: 14px;
  font-weight: 500;
  font-family: 'Source Han Serif', serif;
}

.currency {
  color: var(--splash-success-color);
}

.order-unit {
  color: var(--splash-info-color);
}

.cup-unit {
  color: var(--splash-warning-color);
}

/* 明星产品网格样式 - 紧凑版本 */
.star-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 10px;
}

.product-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e5e7eb;
}

.product-item:last-child {
  border-bottom: none;
}

.product-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  width: 100%;
}

.product-name-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.product-rank {
  background: transparent;
  color: #000000;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 700;
  flex-shrink: 0;
  font-family: 'Source Han Serif', serif;
}

.product-name {
  font-size: 13px;
  font-weight: 500;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.product-sales-container {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.product-sales {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.product-unit {
  font-size: 11px;
  font-weight: 500;
  font-family: 'Source Han Serif', serif;
}

/* 门店排行样式 - 紧凑版本 */
.store-ranking {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.store-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e5e7eb;
}

.store-item:last-child {
  border-bottom: none;
}

.store-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  width: 100%;
}

.store-name-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.store-rank {
  background: transparent;
  color: #000000;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 700;
  flex-shrink: 0;
  font-family: 'Source Han Serif', serif;
}

.store-name {
  font-size: 13px;
  font-weight: 500;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.store-revenue-container {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.store-revenue {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.store-unit {
  font-size: 11px;
  font-weight: 500;
  font-family: 'Source Han Serif', serif;
}

/* 产品排行样式 - 紧凑版本 */
.product-ranking {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.product-rank-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e5e7eb;
}

.product-rank-item:last-child {
  border-bottom: none;
}

.product-rank-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  width: 100%;
}

.product-rank-name-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.product-rank-number {
  background: transparent;
  color: #000000;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 700;
  flex-shrink: 0;
  font-family: 'Source Han Serif', serif;
}

.product-rank-name {
  font-size: 13px;
  font-weight: 500;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.product-rank-cups-container {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.product-rank-cups {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.product-rank-unit {
  font-size: 11px;
  font-weight: 500;
  font-family: 'Source Han Serif', serif;
}

/* 汇总统计样式 - 紧凑版本 */
.summary-stats {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f1f3;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  font-size: 13px;
  color: var(--splash-secondary-color);
  font-weight: 500;
  font-family: 'Source Han Serif', serif;
}

.summary-value-container {
  display: flex;
  align-items: baseline;
  gap: 3px;
}

.summary-value {
  font-size: 16px;
  font-weight: 700;
  color: #000000;
  font-family: 'Source Han Serif', serif;
}

.summary-unit {
  font-size: 12px;
  font-weight: 500;
  font-family: 'Source Han Serif', serif;
}

.growth-positive {
  color: var(--splash-success-color);
}

/* 图表容器样式 - 紧凑版本 */
.chart-container {
  margin-top: 8px;
  width: 100%;
  overflow: hidden;
}

/* 全宽图表优化 */
.row .col-12 .chart-container {
  margin-top: 10px;
}

.row .col-12 .chart-container .chart {
  width: 100% !important;
}

/* 卡片样式调整 */
.card {
  margin-bottom: 20px;
}

.card-body {
  padding: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-overview {
    padding: 12px 15px;
    min-height: calc(100vh - 120px);
  }

  .dashboard-overview .row {
    margin-bottom: 15px;
  }

  .dashboard-overview .d-flex.justify-content-between.align-items-center {
    margin-bottom: 20px;
  }

  .star-products-grid {
    grid-template-columns: 1fr;
  }

  .main-title {
    font-size: 1.5rem;
  }

  .time-selector {
    min-width: 180px;
    margin-top: 10px;
  }

  .date-controls {
    align-items: stretch;
  }

  .quick-date-buttons {
    justify-content: center;
  }

  .metric-value {
    font-size: 18px;
  }

  .card-body {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .dashboard-overview {
    padding: 8px 12px;
    min-height: calc(100vh - 100px);
  }

  .dashboard-overview .row {
    margin-bottom: 12px;
  }

  .dashboard-overview .d-flex.justify-content-between.align-items-center {
    margin-bottom: 15px;
  }

  .main-title {
    font-size: 1.3rem;
  }

  .metric-value {
    font-size: 16px;
  }

  .quick-btn {
    padding: 5px 10px;
    font-size: 12px;
  }
}
