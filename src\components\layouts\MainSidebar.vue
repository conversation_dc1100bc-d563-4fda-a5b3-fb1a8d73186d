<script lang="ts">
import {defineComponent} from "vue";

export default defineComponent({
  name: "MainSidebar",
});
</script>
<script lang="ts" setup>
import {useSidebarStore} from "@/store/useSidebarStore";

const sidebarStore = useSidebarStore();
</script>
<template>
  <div
      :class="[
      'sidebar-area position-fixed start-0 top-0 bg-black h-100vh transition',
      { active: sidebarStore.sidebarState },
    ]"
      id="sidebar-area"
  >
    <!--    顶部商标区-->
    <div class="logo position-absolute start-0 end-0 top-0 bg-black">
      <router-link
          to="/"
          class="d-flex align-items-center text-white text-decoration-none"
      >
        <img src="../../assets/images/favicon.png" alt="logo-icon"/>
        <span class="fw-bold ms-10">创界智联</span>
      </router-link>
      <!--      一个分割线-->
      <div class="border-bottom"></div>
      <!--      按钮,用来固定侧边栏状态-->
      <button
          class="sidebar-burger-menu position-absolute lh-1 bg-transparent p-0 border-0"
          @click="sidebarStore.toggleSidebar()"
      >
        <i class="ph-duotone ph-caret-double-right"></i>
      </button>
    </div>
    <!--    菜单-->
    <div class="sidebar-menu">
      <!--      侧边栏开始，这个ul是第一级-->
      <ul
          class="sidebar-navbar-nav ps-0 mb-0 list-unstyled accordion"
          id="sidebarNavAccordion"
      >
        <!-- 总览和工作台 -->
        <li v-if="sidebarStore.navMode === 'initial'"
            class="sidebar-nav-item accordion-item bg-transparent border-0 rounded-0"
        >
          <router-link
              to="/overview-workspace"
              class="accordion-button rounded-0 shadow-none bg-transparent d-block text-decoration-none"
          >
            <i class="flaticon-more-1"></i>
            <span class="title">总览和工作台</span>
          </router-link>
        </li>



        <!-- 订单管理 -->
        <li v-if="sidebarStore.navMode === 'initial'"
            class="sidebar-nav-item accordion-item bg-transparent border-0 rounded-0"
        >
          <router-link
              to="/orders"
              class="accordion-button rounded-0 shadow-none bg-transparent d-block text-decoration-none"
          >
            <i class="flaticon-shopping-cart"></i>
            <span class="title">订单管理</span>
          </router-link>
        </li>

        <!-- 门店管理 -->
        <li v-if="sidebarStore.navMode === 'initial'"
            class="sidebar-nav-item accordion-item bg-transparent border-0 rounded-0"
        >
          <router-link
              to="/stores"
              class="accordion-button rounded-0 shadow-none bg-transparent d-block text-decoration-none"
          >
            <i class="flaticon-store"></i>
            <span class="title">门店管理</span>
          </router-link>
        </li>

        <!-- 商户管理 -->
        <li v-if="sidebarStore.navMode === 'initial'"
            class="sidebar-nav-item accordion-item bg-transparent border-0 rounded-0"
        >
          <router-link
              to="/merchant-management"
              class="accordion-button rounded-0 shadow-none bg-transparent d-block text-decoration-none"
          >
            <i class="flaticon-user-1"></i>
            <span class="title">商户管理</span>
          </router-link>
        </li>

        <!-- 商品管理 -->
        <li v-if="sidebarStore.navMode === 'initial'"
            class="sidebar-nav-item accordion-item bg-transparent border-0 rounded-0"
        >
          <router-link
              to="/product-management"
              class="accordion-button rounded-0 shadow-none bg-transparent d-block text-decoration-none"
          >
            <i class="flaticon-coffee-cup"></i>
            <span class="title">商品管理</span>
          </router-link>
        </li>

        <!-- 数据中心 -->
        <li v-if="sidebarStore.navMode === 'initial'"
            class="sidebar-nav-item accordion-item bg-transparent border-0 rounded-0"
        >
          <router-link
              to="/data-center"
              class="accordion-button rounded-0 shadow-none bg-transparent d-block text-decoration-none"
          >
            <i class="flaticon-analytics"></i>
            <span class="title">数据中心</span>
          </router-link>
        </li>

        <!-- 设备运维 -->
        <li v-if="sidebarStore.navMode === 'initial'"
            class="sidebar-nav-item accordion-item bg-transparent border-0 rounded-0"
        >
          <router-link
              to="/device-management"
              class="accordion-button rounded-0 shadow-none bg-transparent d-block text-decoration-none"
          >
            <i class="flaticon-robot"></i>
            <span class="title">设备运维</span>
          </router-link>
        </li>

        <!-- 系统配置 -->
        <li v-if="sidebarStore.navMode === 'initial'"
            class="sidebar-nav-item accordion-item bg-transparent border-0 rounded-0"
        >
          <router-link
              to="/system-management"
              class="accordion-button rounded-0 shadow-none bg-transparent d-block text-decoration-none"
          >
            <i class="flaticon-settings"></i>
            <span class="title">系统配置</span>
          </router-link>
        </li>

        <!-- 大屏看板 -->
        <li v-if="sidebarStore.navMode === 'initial'"
            class="sidebar-nav-item accordion-item bg-transparent border-0 rounded-0"
        >
          <router-link
              to="/all-stores-fullscreen"
              class="accordion-button rounded-0 shadow-none bg-transparent d-block text-decoration-none"
          >
            <i class="flaticon-monitor"></i>
            <span class="title">大屏看板</span>
          </router-link>
        </li>
<!--        显示模式二-->
        <li v-if="sidebarStore.navMode === 'detail'"
            class="sidebar-nav-item accordion-item bg-transparent border-0 rounded-0"
        >
          <!--          折叠头用a配置一个href="#"模拟可点击内容，data-bs-target配置要折叠的元素 #元素名id-->
          <!--          折叠主体 主体用div配置对应id 如果绑定父类 可以实现父类下只能展开一个折叠内容 显然 ul是一个本次父类-->
          <a
              href="#"
              class="accordion-button rounded-0 shadow-none bg-transparent d-block"
              data-bs-toggle="collapse"
              data-bs-target="#sidebarCollapse-Store"
          >
            <i class="flaticon-more-1"></i>
            <span class="title">店铺信息</span>
          </a>
          <div
              id="sidebarCollapse-Store"
              class="accordion-collapse collapse show">
            <!--           class 信息 绑定父类，实现最多展开1个折叠内容 data-bs-parent="#sidebarNavAccordion"-->
            <div class="accordion-body">
              <ul class="sidebar-sub-menu ps-0 mb-0 list-unstyled">
                <li class="sidebar-sub-menu-item">
                  <router-link
                      to="/dashboard"
                      class="sidebar-sub-menu-link"
                  >
                    总览
                  </router-link>
                </li>
              </ul>
            </div>
            <div class="accordion-body">
              <ul class="sidebar-sub-menu ps-0 mb-0 list-unstyled">
                <li class="sidebar-sub-menu-item">
                  <router-link
                      to="/datalist"
                      class="sidebar-sub-menu-link"
                  >
                    DataList
                  </router-link>
                </li>
              </ul>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>