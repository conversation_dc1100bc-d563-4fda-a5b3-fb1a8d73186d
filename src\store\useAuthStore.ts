import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import serviceAxios from '@/utils/serviceAxios'

// 用户信息接口
export interface UserInfo {
  userId: number
  username: string
  realName?: string
  email?: string
  phone?: string
  avatar?: string
  roles?: string[]
  permissions?: string[]
}

// 登录请求参数
export interface LoginParams {
  username: string
  password: string
}

// 登录响应数据
export interface LoginResponse {
  token: string
  user: UserInfo
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>('')
  const userInfo = ref<UserInfo | null>(null)
  const isLoading = ref<boolean>(false)
  const error = ref<string>('')

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)
  const hasPermission = computed(() => (permission: string) => {
    return userInfo.value?.permissions?.includes(permission) || false
  })
  const hasRole = computed(() => (role: string) => {
    return userInfo.value?.roles?.includes(role) || false
  })



  // 用户登录
  const login = async (loginParams: LoginParams): Promise<void> => {
    isLoading.value = true
    error.value = ''

    try {
      const response = await serviceAxios.post('/login', loginParams)
      const loginData = response as unknown as LoginResponse

      // 存储token和用户信息
      token.value = loginData.token
      userInfo.value = loginData.user

      console.log('登录成功:', userInfo.value)
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : '登录失败'
      error.value = errorMessage
      console.error('登录失败:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 获取用户信息
  const getUserInfo = async (): Promise<UserInfo> => {
    if (!token.value) {
      throw new Error('未登录')
    }

    try {
      const response = await serviceAxios.get('/getInfo')
      const userData = response as unknown as UserInfo
      userInfo.value = userData
      return userData
    } catch (err) {
      console.error('获取用户信息失败:', err)
      // 如果获取用户信息失败，清除登录状态
      logout()
      throw err
    }
  }

  // 用户登出
  const logout = async (): Promise<void> => {
    try {
      // 调用登出接口
      if (token.value) {
        await serviceAxios.post('/logout')
      }
    } catch (err) {
      console.error('登出接口调用失败:', err)
    } finally {
      // 清除本地状态
      token.value = ''
      userInfo.value = null
      error.value = ''
    }
  }

  // 刷新token
  const refreshToken = async (): Promise<void> => {
    if (!token.value) {
      throw new Error('未登录')
    }

    try {
      const response = await serviceAxios.post('/refresh')
      const tokenData = response as unknown as { token: string }
      token.value = tokenData.token
    } catch (err) {
      console.error('刷新token失败:', err)
      // 刷新失败，清除登录状态
      logout()
      throw err
    }
  }

  // 初始化认证状态（应用启动时调用）
  const initAuth = async (): Promise<void> => {
    if (token.value) {
      try {
        await getUserInfo()
      } catch (err) {
        console.error('初始化认证状态失败:', err)
        // 初始化失败，清除登录状态
        logout()
      }
    }
  }

  // 检查token是否有效
  const checkTokenValid = (): boolean => {
    return !!token.value && !!userInfo.value
  }

  return {
    // 状态
    token,
    userInfo,
    isLoading,
    error,
    
    // 计算属性
    isLoggedIn,
    hasPermission,
    hasRole,
    
    // 方法
    login,
    logout,
    getUserInfo,
    refreshToken,
    initAuth,
    checkTokenValid
  }
}, {
  persist: {
    key: 'auth-data',
    storage: localStorage
  }
})
