<template>
  <div class="overview-workspace">
    <!-- 面包屑导航 -->
    <BreadCrumb title="总览和工作台" :breadcrumb="breadcrumb" />

    <!-- 数据总览看板组件 -->
    <DashboardWidget />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BreadCrumb from "@/components/layouts/BreadCrumb.vue"
import DashboardWidget from "@/components/DashboardWidget.vue"

// 面包屑导航
const breadcrumb = ref([
  { label: '总览和工作台', url: '/overview-workspace' }
])






</script>

<style scoped>
.overview-workspace {
  padding: 20px;
}






</style>
