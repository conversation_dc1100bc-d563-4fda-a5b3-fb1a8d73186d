// API响应类型定义

// 基础响应结构
export interface BaseApiResponse {
    code: number;
    msg: string;
}

// 列表类型API响应 (如 /manager/prototype/popular, /manager/store/list)
export interface ListApiResponse<T = unknown> extends BaseApiResponse {
    rows: T[];
    total: number;
}

// 单个对象API响应 (如 /manager/product/{id})
export interface DataApiResponse<T = unknown> extends BaseApiResponse {
    data: T;
}

// 统计类API响应 (如 /manager/store/sales-stat)
export interface SalesStatsResponse extends BaseApiResponse {
    totalAmount: string;
    orderCount: number;
    totalCups: number;
}

// 设备信息响应
export interface DeviceInfoResponse extends BaseApiResponse {
    data?: DeviceInfo[];
    rows?: DeviceInfo[];
    [key: string]: unknown;
}

// 设备状态响应
export interface DeviceStatusResponse extends BaseApiResponse {
    data?: Record<string, unknown>;
    [key: string]: unknown;
}

// 饮品原型信息
export interface DrinkPrototype {
    prototypeId: number;
    drinkGroupId: number;
    prototypeName: string;
    prototypeDescription: string;
    prototypeImage: string;
    prototypeLabel: string;
    orderNum: number;
    status: string;
    defaultOptions?: Record<string, unknown>;
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
}

// 热门饮品原型API响应
export type PopularPrototypesResponse = ListApiResponse<DrinkPrototype>;

// 门店信息
export interface StoreInfo {
    storeId: number;
    storeName: string;
    storeType: string;
    storeAddress: string;
    storeDescription: string;
    businessHours: string;
    contactPhone: string;
    latitude: string;
    longitude: string;
    status: string;
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
}

// 门店列表API响应
export type StoreListResponse = ListApiResponse<StoreInfo>;

// 设备信息
export interface DeviceInfo {
    deviceId: string;
    deviceName: string;
    deviceType: string;
    storeId: number;
    status: string;
    [key: string]: unknown;
}

// 设备列表API响应
export type DeviceListResponse = ListApiResponse<DeviceInfo>;

// 联合类型，涵盖所有可能的响应格式
export type ApiResponse<T = unknown> =
    | ListApiResponse<T>
    | DataApiResponse<T>
    | SalesStatsResponse
    | DeviceInfoResponse
    | DeviceStatusResponse
    | (BaseApiResponse & { [key: string]: unknown });

// 类型守卫函数
export function isListResponse<T>(response: unknown): response is ListApiResponse<T> {
    return Boolean(
        response &&
        typeof response === 'object' &&
        response !== null &&
        'rows' in response &&
        Array.isArray((response as ListApiResponse<T>).rows)
    );
}

export function isDataResponse<T>(response: unknown): response is DataApiResponse<T> {
    return Boolean(
        response &&
        typeof response === 'object' &&
        response !== null &&
        'data' in response
    );
}

export function isSalesStatsResponse(response: unknown): response is SalesStatsResponse {
    return Boolean(
        response &&
        typeof response === 'object' &&
        response !== null &&
        'totalAmount' in response
    );
}

// 辅助函数：安全地提取数据
export function extractData<T>(response: ApiResponse<T>): T[] | T | null {
    if (isListResponse<T>(response)) {
        return response.rows;
    }
    if (isDataResponse<T>(response)) {
        return response.data;
    }
    return null;
}

// 辅助函数：安全地提取列表数据
export function extractListData<T>(response: ApiResponse<T>): T[] {
    if (isListResponse<T>(response)) {
        return response.rows;
    }
    return [];
}

// 辅助函数：安全地提取单个数据
export function extractSingleData<T>(response: ApiResponse<T>): T | null {
    if (isDataResponse<T>(response)) {
        return response.data;
    }
    return null;
}
