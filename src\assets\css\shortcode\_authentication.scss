.authentication-card {
    .card-body {
        padding: 100px 130px;

        .icon {
            width: 100px;
            height: 100px;
            font-size: 35px;
            background: #ECF3F2;

            i {
                left: 0;
                right: 0;
                top: 50%;
                line-height: 1;
                position: absolute;
                transform: translateY(-50%);
            }
        }
        .sub-text {
            max-width: 380px;
            margin: {
                top: 25px;
                left: auto;
                right: auto;
            };
        }
        form {
            margin-top: 40px;

            .form-check {
                padding-left: 1.5em;
                position: relative;
                min-height: 16px;
                top: 1.5px;

                .form-check-input {
                    top: -1px;
                    width: 16px;
                    height: 16px;
                    position: relative;
                    margin-left: -1.5em;

                    &[type=checkbox] {
                        border-radius: 50%;
                    }
                }
            }
            .forgot-password-btn {
                line-height: 1.3;

                &::before {
                    left: 0;
                    right: 0;
                    bottom: 0;
                    height: 1px;
                    content: '';
                    position: absolute;
                    transition: var(--transition);
                    background: var(--splash-primary-color);
                }
                &:hover {
                    &::before {
                        transform: scaleX(0);
                    }
                }
            }
            .default-btn {
                padding: 14px 25px;

                &.with-border {
                    background-color: transparent;
                    color: var(--splash-primary-color);
                    border: 1px solid var(--splash-primary-color);
                    padding: {
                        top: 13px;
                        bottom: 13px;
                    };
                    &:hover {
                        background-color: var(--splash-primary-color);
                        color: var(--splash-white-color);
                    }
                }
            }
            .or {
                &::before {
                    left: 0;
                    right: 0;
                    top: 50%;
                    z-index: -1;
                    content: '';
                    height: 1px;
                    position: absolute;
                    background: #eeeeee;
                    transform: translateY(-50%);
                }
                span {
                    padding: {
                        left: 25px;
                        right: 25px;
                    };
                }
            }
            .socials {
                li {
                    margin: {
                        left: 5px;
                        right: 5px;
                    };
                    button {
                        width: 30px;
                        height: 30px;
                        font-size: 17px;
                        color: var(--splash-white-color);
                        background-color: var(--splash-primary-color);

                        i {
                            left: 0;
                            right: 0;
                            top: 50%;
                            line-height: 1;
                            margin-top: -.5px;
                            position: absolute;
                            transform: translateY(-50%);
                        }
                        &.facebook {
                            background: #3B5998;
                        }
                        &.google {
                            background: #DB3236;
                        }
                        &.twitter {
                            background: #00ACEE;
                        }
                        &.linkedin {
                            background: #0072B1;
                        }
                    }
                }
            }
            .link-btn {
                padding-left: 23px;

                i {
                    transform: translateY(-50%);
                    position: absolute;
                    top: 50%;
                    left: 0;
                }
            }
        }
    }
    &.email-confirmation-card {
        .card-body {
            .default-btn {
                padding: 14px 25px;
            }
        }
    }
}

// Dark Mode
.dark {
    .authentication-card {
        .card-body {
            .icon {
                background: var(--splash-black-color);
            }
            form {
                .or {
                    &::before {
                        background: #45445e;
                    }
                }
            }
        }
    }
}

@media only screen and (max-width : 767px) {

    .authentication-card {
        .card-body {
            padding: 25px 15px;
    
            .icon {
                width: 60px;
                height: 60px;
                font-size: 25px;
            }
            .sub-text {
                max-width: 100%;
                margin-top: 10px;
            }
            form {
                margin-top: 20px;
    
                .default-btn {
                    padding: 13px 25px;
    
                    &.with-border {
                        padding: {
                            top: 12px;
                            bottom: 12px;
                        };
                    }
                }
                .socials {
                    li {
                        margin: {
                            left: 3px;
                            right: 3px;
                        };
                    }
                }
            }
        }
    }

}

@media only screen and (min-width : 576px) and (max-width : 767px) {

    .authentication-card {
        .card-body {
            padding: 35px 25px;
        }
    }

}

@media only screen and (min-width : 768px) and (max-width : 991px) {

    .authentication-card {
        .card-body {
            padding: 50px 80px;
    
            .icon {
                width: 80px;
                height: 80px;
                font-size: 30px;
            }
            .sub-text {
                margin-top: 12px;
            }
            form {
                margin-top: 30px;
            }
        }
    }

}

@media only screen and (min-width : 992px) and (max-width : 1199px) {

    .authentication-card {
        .card-body {
            padding: 70px 100px;

            .sub-text {
                margin-top: 15px;
            }
        }
    }

}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {

    .authentication-card {
        .card-body {
            padding: 70px 100px;
        }
    }

}